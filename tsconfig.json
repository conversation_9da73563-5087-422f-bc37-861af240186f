{"extends": "@react-native/typescript-config", "exclude": ["node_modules", "android", "dist", "ios", "__tests__", "assets"], "compilerOptions": {"jsx": "react", "lib": ["es2015", "DOM"], "baseUrl": ".", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "sourceMap": true, "allowJs": true, "noEmit": true, "paths": {"*": ["src/*"], "config": ["src/config"], "json": ["src/json/*"], "utils": ["src/utils/*"], "assets": ["assets/*"], "components": ["src/components/*"], "reducers": ["src/reducers/*"], "screens": ["src/screens/*"], "navigation": ["src/navigation/*"], "requests": ["src/requests/*"], "styles": ["src/styles/*"]}}}