import {sendRequestMTM} from './api';
import {get} from 'lodash';
import {getCountry} from 'utils/commonVariable';
import { COUNTRY_CODE } from 'utils/constant';

//#region Poll Question
export const getCoachProfile = async coachTag => {
  try {
    const request = await sendRequestMTM({
      url: `/cms/coach-tag/${coachTag}?count=50`,
      method: 'GET',
    });
    return get(request, 'data', []);
  } catch (error) {
    throw error;
  }
};

export const productDataList = async productIds => {
  try {
    const country = getCountry() ? getCountry() : COUNTRY_CODE.USA;

    const request = await sendRequestMTM({
      url: `/ecom/products?productIds=${productIds}&country=${country}`,
      method: 'GET',
    });
    return get(request, 'data.data', {});
  } catch (error) {
    throw error;
  }
};

export const getTourorStories = async country => {
  try {
    const request = await sendRequestMTM({
      url: `/tour-story/app?country=${country}`,
      method: 'GET',
    });
    return get(request, 'data', []);
  } catch (error) {
    throw error;
  }
};

export const updateUserTourorStories = async id => {
  try {
    const request = await sendRequestMTM({
      url: `/tour-story/user`,
      method: 'POST',
      data: {
        tourStoryId: id,
      },
    });
    return get(request, 'data', []);
  } catch (error) {
    throw error;
  }
};
