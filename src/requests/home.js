import {sendRequestMTM} from './api';
import {get} from 'lodash';

const HOMETYPE = {
  THE_DAILY: 'THE_DAILY',
  HOME_PERK: 'HOME_PERK',
  HOME_POINTS: 'HOME_POINTS',
  JUST_IN_CLUBHOUSE: 'JUST_IN_CLUBHOUSE',
};

const HOMESTATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};
//#region Poll Question
export const getPollQuestions = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/poll-questions`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getUserCurrentQuestion = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/poll-questions/user`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getLastSummary = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/poll-questions/user/last-summary`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const postAnswer = async data => {
  try {
    const request = await sendRequestMTM({
      url: `/user-answers`,
      method: 'POST',
      data: {
        ...data,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getQuestionById = async questionId => {
  try {
    const request = await sendRequestMTM({
      url: `/poll-questions/${questionId}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getAnswersPercentage = async questionId => {
  try {
    const request = await sendRequestMTM({
      url: `/poll-questions/percent/${questionId}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getTheDaily = async data => {
  try {
    const params = {
      ...data,
      type: HOMETYPE.THE_DAILY,
      sort: true,
      status: HOMESTATUS.ACTIVE,
    };
    const request = await sendRequestMTM({
      url: `/tiles-widget`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getHomePerks = async data => {
  try {
    const params = {
      ...data,
      type: HOMETYPE.HOME_PERK,
      sort: true,
      status: HOMESTATUS.ACTIVE,
    };
    const request = await sendRequestMTM({
      url: `/tiles-widget`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getHomePoints = async data => {
  try {
    const params = {
      ...data,
      type: HOMETYPE.HOME_POINTS,
      sort: true,
      status: HOMESTATUS.ACTIVE,
    };
    const request = await sendRequestMTM({
      url: `/tiles-widget`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getHomeProduct = async headers => {
  try {
    const request = await sendRequestMTM({
      url: `/home-product/products`,
      method: 'GET',
      headers,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getJustIn = async data => {
  try {
    const params = {
      ...data,
      type: HOMETYPE.JUST_IN_CLUBHOUSE,
      sort: true,
      status: HOMESTATUS.ACTIVE,
    };
    const request = await sendRequestMTM({
      url: `/tiles-widget`,
      method: 'GET',
      params,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
