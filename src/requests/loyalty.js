import { COUNTRY_CODE } from 'utils/constant';
import {sendRequestMTM} from './api';
import {get} from 'lodash';

export const getUserTier = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/loyalty/user/tier`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getUserPoints = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/loyalty/user/points`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const addPointsNotification = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/loyalty/point/notification`,
      method: 'POST',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const createLoyaltyUser = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/loyalty/user`,
      method: 'POST',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getActionTrackRound = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/loyalty/action/play`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getTierList = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/loyalty/tierlist/all`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getUserCompletedActions = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/loyalty/user/actions`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getTierBenefits = async (
  country = COUNTRY_CODE.USA,
  currentTier = 'par',
) => {
  try {
    const request = await sendRequestMTM({
      url: `/content/tiers?country=${country}&level=${currentTier}`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
