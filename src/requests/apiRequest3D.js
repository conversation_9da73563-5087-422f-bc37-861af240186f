import axios from 'axios';
// import constants from './constants';
import CryptoJS from 'crypto-js';
import moment from 'moment';
import * as crapto from 'crypto-js';

const config = {
  host: 'https://api-connect.igolf.com/',
  apiKey: 'qataPJ1u88ymdfs',
  secretKey: '6hhNafpzWSZ_Q0HyrD7wn_WZfHwp6p',
};

let apiParams = {
  apiKey: config.apiKey,
  apiVersion: '1.1',
  signatureVersion: '2.0',
  signatureMethod: 'HmacSHA256',
  responseFormat: 'JSON',
};

function UserException(message) {
  this.message = message;
  this.name = 'UserException';
}

export async function postApi(action, payload, cancel) {
  if (config.apiKey === '' || config.secretKey === '') {
    throw new UserException('Please define your apiKey and secretKey');
  }
  const timestamp = () => {
    const o = new Date();
    const a = o.getTimezoneOffset();
    return (
      String(o.getFullYear()).slice(-2) +
      pad2(o.getMonth() + 1) +
      pad2(o.getDate()) +
      pad2(o.getHours()) +
      pad2(o.getMinutes()) +
      pad2(o.getSeconds()) +
      (a > 0 ? '-' : '-') +
      pad2(Math.floor(Math.abs(a) / 60)) +
      pad2(Math.abs(a) % 60)
    );
  };

  const pad2 = e => {
    return (e < 10 ? '0' : '') + e;
  };

  const i = `${action}/${config.apiKey}/${apiParams.apiVersion}/${
    apiParams.signatureVersion
  }/${apiParams.signatureMethod}/${timestamp()}/JSON`;

  const signature = crapto
    .HmacSHA256(i, config.secretKey)
    .toString(crapto.enc.Base64url)
    .replace(/\//g, '_')
    .replace(/\+/g, '-')
    .replace(/=/g, '');

  let arr = [];
  let object = {
    // action: action,
    apiKey: config.apiKey,
    appVersion: '1.1',
    apiVersion: '2.0',
    signatureVersion: 'HmacSHA256',
    signatureMethod: signature,
    timestamp: timestamp(),
    responseFormat: 'JSON',
  };

  for (const key in object) {
    if (Object.hasOwnProperty.call(object, key)) {
      arr.push(object[key]);
    }
  }
  const url = arr.join('/');
  return await axios.post(
    `${config.host}rest/action/${action}/${url}`,
    payload,
    cancel,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: '',
      },
    },
  );
}

export async function getApi(action, obj, header) {
  const timestamp = () => {
    const o = new Date();
    const a = o.getTimezoneOffset();
    return (
      String(o.getFullYear()).slice(-2) +
      pad2(o.getMonth() + 1) +
      pad2(o.getDate()) +
      pad2(o.getHours()) +
      pad2(o.getMinutes()) +
      pad2(o.getSeconds()) +
      (a > 0 ? '-' : '-') +
      pad2(Math.floor(Math.abs(a) / 60)) +
      pad2(Math.abs(a) % 60)
    );
  };

  const pad2 = e => {
    return (e < 10 ? '0' : '') + e;
  };

  // let timestamp = timestamp()

  const i = `${action}/${config.apiKey}/${apiParams.apiVersion}/${
    apiParams.signatureVersion
  }/${apiParams.signatureMethod}/${timestamp()}/JSON`;

  const signature = crapto
    .HmacSHA256(i, config.secretKey)
    .toString(crapto.enc.Base64url)
    .replace(/\//g, '_')
    .replace(/\+/g, '-')
    .replace(/=/g, '');

  let arr = [];
  let object = {
    action: action,
    apiKey: config.apiKey,
    appVersion: '1.1',
    apiVersion: '2.0',
    signatureVersion: 'HmacSHA256',
    signatureMethod: signature,
    timestamp: timestamp(),
    responseFormat: 'JSON',
  };

  for (const key in object) {
    if (Object.hasOwnProperty.call(object, key)) {
      arr.push(object[key]);
    }
  }
  const url = arr.join('/');

  console.log('GetApi : ', `${config.host}/${action}/${url}`);

  return;

  return await axios.get(`${constants.BASE_URL}/${action}/${url}`, {
    headers: {
      Accept: header.Accept,
      'Content-Type': header.contenttype,
      // 'x-access-token': `${header.authorization}`,
      Authorization: 'Bearer' + ' ' + header.authorization,
    },
  });
}

export async function getAWSApi(url) {
  console.log('url', url);
  return await axios.get(
    url
  );
}
