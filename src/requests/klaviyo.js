import {sendRequestMTM} from './api';
import {get} from 'lodash';

export const triggerViewedCategory = async categoryType => {
  try {
    const request = await sendRequestMTM({
      url: `/tiles-widget/trigger/view-category`,
      method: 'POST',
      data: {
        type: categoryType, // Marquee / Promotion / ProductCarousel /Mainstays / ProductCatalog
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const triggerViewedProduct = async data => {
  try {
    const request = await sendRequestMTM({
      url: `/tiles-widget/trigger/view-product`,
      method: 'POST',
      data,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const triggerActiveOnSite = async data => {
  try {
    const request = await sendRequestMTM({
      url: `/tiles-widget/trigger/active-on-site`,
      method: 'POST',
      data,
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
