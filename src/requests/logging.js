import {get} from 'lodash';
import axios from 'axios';
import {decryptClientIDMyTM, getConfig} from 'config/env';

export const writeLogToDB = async data => {
  const API_URL = await getConfig('MYTM_API_URL');
  try {
    const request = await axios({
      url: `${API_URL}/logging/save`,
      method: 'POST',
      headers: {
        clientId: decryptClientIDMyTM(),
      },
      data,
    });
    return get(request, 'data', {});
  } catch (error) {
    console.log('Error while writing log to server', error);
  }
};
