import {sendRequestMTM} from './api';
import {get} from 'lodash';

export const getEntryCard = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/game-profiles/entry-card`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getGolferQuestionnaire = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/game-profiles/questionnaire`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};

export const getQuestionList = async () => {
  try {
    const request = await sendRequestMTM({
      url: `/game-profile-questions`,
      method: 'GET',
    });
    return get(request, 'data', {});
  } catch (error) {
    throw error;
  }
};
