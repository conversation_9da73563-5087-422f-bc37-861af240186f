import {get} from 'lodash';
import {sendRequestMTM} from './api';
export const getModuleCacheData = async country => {
  try {
    const request = await sendRequestMTM({
      url: `/api-versions/check`,
      method: 'GET',
      params: {
        country,
      },
    });
    return get(request, 'data', {});
  } catch (error) {
    console.log('error get api version check', error?.message);
    return {
      country: null,
      features: [],
    };
  }
};
