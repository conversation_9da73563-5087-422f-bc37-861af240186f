import {combineReducers, createSlice} from '@reduxjs/toolkit';

const initialState = {
  country: null,
  version: null,
  data: [],
};

const perksSlice = createSlice({
  name: 'perks',
  initialState: initialState,
  reducers: {
    clearDataPerks() {
      return initialState;
    },
    updateDataPerks(state, action) {
      return action.payload;
    },
  },
});

const tourTrashSlice = createSlice({
  name: 'tourTrash',
  initialState: initialState,
  reducers: {
    clearDataTourTrash() {
      return initialState;
    },
    updateDataTourTrash(state, action) {
      return action.payload;
    },
  },
});

export const {clearDataPerks, updateDataPerks} = perksSlice.actions;
export const {clearDataTourTrash, updateDataTourTrash} = tourTrashSlice.actions;

export default combineReducers({
  perks: perksSlice.reducer,
  tourTrash: tourTrashSlice.reducer,
});
