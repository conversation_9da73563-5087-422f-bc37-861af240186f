import {createSlice} from '@reduxjs/toolkit';
export const initialStateLoyalty = {
  tier: {
    currentTier: 'PAR',
    nextTier: 'BIRDIE',
    spendAmountToNextTier: 0,
    spendAmountInTier: 0,
    currentTierPurchaseRatio: 1,
  },
  points: {
    availablePoints: 0,
    creditsToCurrencyValue: 0,
    pointsToExpire: 0,
    pointsToExpireDate: '',
    creditsToCurrencyRatio: 0.5,
    totalSpent: 0,
  },
  tierAll: [
    {
      tierMilestone: {
        tierThreshold: '2501',
        extendedAttribute: [],
        actionSeries: '',
      },
    },
    {
      tierMilestone: {
        tierThreshold: '750',
        extendedAttribute: [],
        actionSeries: '',
      },
    },
    {
      tierMilestone: {
        tierThreshold: '0',
        extendedAttribute: [],
        actionSeries: '',
      },
    },
  ],
  completedActions: [],
  rewardProducts: [],
  loyaltyActions: {
    country: null,
    version: null,
    data: [],
  },
};

const loyaltySlice = createSlice({
  name: 'loyalty',
  initialState: initialStateLoyalty,
  reducers: {
    clearLoyalty() {
      return initialStateLoyalty;
    },
    updateLoyaltyData(state, action) {
      const payload = {...state, ...action.payload};
      return payload;
    },
  },
});

export const {clearLoyalty, updateLoyaltyData} = loyaltySlice.actions;

export default loyaltySlice.reducer;
