import {createSlice} from '@reduxjs/toolkit';
export const initialStateTrackingOrders = {
  orders: [],
};

const trackingOrdersSlice = createSlice({
  name: 'trackingOrders',
  initialState: initialStateTrackingOrders,
  reducers: {
    clearTrackingOrders() {
      return initialStateTrackingOrders;
    },
    updateTrackingOrdersData(state, action) {
      const payload = {...state, ...action.payload};
      return payload;
    },
  },
});

export const {clearTrackingOrders, updateTrackingOrdersData} =
  trackingOrdersSlice.actions;

export default trackingOrdersSlice.reducer;
