import {createSlice} from '@reduxjs/toolkit';
import {combineReducers} from 'redux';

const initialState = {
  country: null,
  version: null,
  data: [],
};
const homeDailySlice = createSlice({
  name: 'homeDaily',
  initialState,
  reducers: {
    updateDailyData(state, action) {
      return action.payload;
    },
    resetDailyData() {
      return initialState;
    },
  },
});

const homeProductsSlice = createSlice({
  name: 'homeProducts',
  initialState,
  reducers: {
    updateHomeProductsData(state, action) {
      return action.payload;
    },
    resetHomeProductsData() {
      return initialState;
    },
  },
});

const coachProfileSlice = createSlice({
  name: 'coachProfile',
  initialState,
  reducers: {
    updateCoachProfileData(state, action) {
      return action.payload;
    },
    resetCoachProfileData() {
      return initialState;
    },
  },
});

const playerProfileSlice = createSlice({
  name: 'playerProfile',
  initialState,
  reducers: {
    updatePlayerProfileData(state, action) {
      return action.payload;
    },
    resetPlayerProfileData() {
      return initialState;
    },
  },
});

const customTextsSlice = createSlice({
  name: 'customTexts',
  initialState,
  reducers: {
    updateAppTextData(state, action) {
      return action.payload;
    },
    resetAppTextData() {
      return initialState;
    },
  },
});

const productTilesSlice = createSlice({
  name: 'productTiles',
  initialState,
  reducers: {
    updateProductTilesData(state, action) {
      return action.payload;
    },
    resetProductTilesData() {
      return initialState;
    },
  },
});

const promotionTileSlice = createSlice({
  name: 'promotionTile',
  initialState,
  reducers: {
    updatePromotionTileData(state, action) {
      return action.payload;
    },
    resetPromotionTileData() {
      return initialState;
    },
  },
});

const moreForYouSlice = createSlice({
  name: 'moreForYou',
  initialState,
  reducers: {
    updateMoreForYouData(state, action) {
      return action.payload;
    },
    resetMoreForYouData() {
      return initialState;
    },
  },
});

const shopCategoriesSlice = createSlice({
  name: 'shopCategories',
  initialState,
  reducers: {
    updateShopCategoriesData(state, action) {
      return action.payload;
    },
    resetShopCategoriesData() {
      return initialState;
    },
  },
});

const mainstaysSlice = createSlice({
  name: 'mainstays',
  initialState,
  reducers: {
    updateMainstaysData(state, action) {
      return action.payload;
    },
    resetMainstaysData() {
      return initialState;
    },
  },
});

const discountItemsSlice = createSlice({
  name: 'mainstays',
  initialState,
  reducers: {
    updateDiscountItemsData(state, action) {
      return action.payload;
    },
    resetDiscountItemsData() {
      return initialState;
    },
  },
});

const finalTileSlice = createSlice({
  name: 'finalTile',
  initialState,
  reducers: {
    updateFinalTileData(state, action) {
      return action.payload;
    },
    resetFinalTileData() {
      return initialState;
    },
  },
});

const justInTileSlice = createSlice({
  name: 'justInTile',
  initialState,
  reducers: {
    updateJustInTileData(state, action) {
      return action.payload;
    },
    resetJustInTileData() {
      return initialState;
    },
  },
});

const entertainmentSlice = createSlice({
  name: 'entertainment',
  initialState,
  reducers: {
    updateEntertainmentData(state, action) {
      return action.payload;
    },
    resetEntertainmentData() {
      return initialState;
    },
  },
});

const ecomProductsSlice = createSlice({
  name: 'ecomProducts',
  initialState,
  reducers: {
    updateEcomProductsData(state, action) {
      const incomingData = action.payload?.data || [];
      const existingData = state.data || [];

      const mergedMap = new Map();

      // Add existing data first
      for (const item of existingData) {
        if (item?.data?.id) {
          mergedMap.set(item.data.id, item);
        }
      }

      // Overwrite or add new data
      for (const item of incomingData) {
        if (item?.data?.id) {
          mergedMap.set(item.data.id, item);
        }
      }

      // Update state with merged data
      state.data = Array.from(mergedMap.values());
    },
    resetEcomProductsData() {
      return initialState;
    },
    cleanEcomProducts(state) {
      const ONE_WEEK = 7 * 24 * 60 * 60 * 1000;
      const now = Date.now();

      state.data = state.data.filter(item => {
        return item?.cachedAt && now - item.cachedAt <= ONE_WEEK;
      });
    },
  },
});

const collectionListSlice = createSlice({
  name: 'collectionList',
  initialState,
  reducers: {
    updateCollectionListData(state, action) {
      return action.payload;
    },
    resetCollectionListData() {
      return initialState;
    },
  },
});

const tourStorySlice = createSlice({
  name: 'tourStory',
  initialState: {...initialState, data: {}},
  reducers: {
    updateTourStoryData(state, action) {
      return action.payload;
    },
    resetTourStory() {
      return {...initialState, data: {}};
    },
  },
});

const gearListSlice = createSlice({
  name: 'gearList',
  initialState,
  reducers: {
    updateGearListData(state, action) {
      return action.payload;
    },
    resetGearListData() {
      return initialState;
    },
  },
});

const DrillVideosSlice = createSlice({
  name: 'drillVideos',
  initialState,
  reducers: {
    updateDrillVideosData(state, action) {
      return action.payload;
    },
    resetDrillVideosData() {
      return initialState;
    },
  },
});

export const {updateDailyData, resetDailyData} = homeDailySlice.actions;
export const {updateHomeProductsData, resetHomeProductsData} =
  homeProductsSlice.actions;
export const {updateCoachProfileData, resetCoachProfileData} =
  coachProfileSlice.actions;
export const {updatePlayerProfileData, resetPlayerProfileData} =
  playerProfileSlice.actions;
export const {updateAppTextData, resetAppTextData} = customTextsSlice.actions;
export const {updateProductTilesData, resetProductTilesData} =
  productTilesSlice.actions;
export const {updatePromotionTileData, resetPromotionTileData} =
  promotionTileSlice.actions;
export const {updateMoreForYouData, resetMoreForYouData} =
  moreForYouSlice.actions;
export const {updateShopCategoriesData, resetShopCategoriesData} =
  shopCategoriesSlice.actions;
export const {updateMainstaysData, resetMainstaysData} = mainstaysSlice.actions;
export const {updateDiscountItemsData, resetDiscountItemsData} =
  discountItemsSlice.actions;
export const {updateFinalTileData, resetFinalTileData} = finalTileSlice.actions;
export const {updateJustInTileData, resetJustInTileData} =
  justInTileSlice.actions;
export const {updateEntertainmentData, resetEntertainmentData} =
  entertainmentSlice.actions;
export const {updateCollectionListData, resetCollectionListData} =
  collectionListSlice.actions;
export const {
  updateEcomProductsData,
  resetEcomProductsData,
  cleanEcomProducts,
} = ecomProductsSlice.actions;
export const {updateTourStoryData, resetTourStory} = tourStorySlice.actions;
export const {updateGearListData, resetGearListData} = gearListSlice.actions;
export const {updateDrillVideosData, resetDrillVideosData} =
  DrillVideosSlice.actions;

export default combineReducers({
  homeDaily: homeDailySlice.reducer,
  homeProducts: homeProductsSlice.reducer,
  coachProfile: coachProfileSlice.reducer,
  playerProfile: playerProfileSlice.reducer,
  customTexts: customTextsSlice.reducer,
  productTiles: productTilesSlice.reducer,
  promotionTile: promotionTileSlice.reducer,
  moreForYou: moreForYouSlice.reducer,
  shopCategories: shopCategoriesSlice.reducer,
  mainstays: mainstaysSlice.reducer,
  discountItems: discountItemsSlice.reducer,
  finalTile: finalTileSlice.reducer,
  justInTile: justInTileSlice.reducer,
  entertainment: entertainmentSlice.reducer,
  collectionList: collectionListSlice.reducer,
  ecomProducts: ecomProductsSlice.reducer,
  tourStory: tourStorySlice.reducer,
  gearList: gearListSlice.reducer,
  drillVideos: DrillVideosSlice.reducer,
});
