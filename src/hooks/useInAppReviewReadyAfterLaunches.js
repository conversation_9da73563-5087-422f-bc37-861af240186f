import {useEffect, useRef, useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';

const LAUNCH_COUNT_KEY = 'app_launch_count';
const REQUIRED_LAUNCHES = 5;

export const useInAppReviewReadyAfterLaunches = () => {
  const [readyToReview, setReadyToReview] = useState(false);
  const timeoutRef = useRef();

  useEffect(() => {
    const checkAndWait = async () => {
      let launchCount = parseInt(
        (await AsyncStorage.getItem(LAUNCH_COUNT_KEY)) || '0',
        10,
      );
      // Get current date
      const today = moment();
      // AsyncStorage.removeItem('ratingExpiresIn');
      // Get stored expiration time from previous rating attempt
      const expireTime = await AsyncStorage.getItem('ratingExpiresIn');
      if (expireTime) {
        const expiresIn = moment(expireTime);

        // If expired, start counting to 5
        if (today.isAfter(expiresIn)) {
          launchCount += 1;
        } else {
          //if not yet expired, reset the launch count to 0
          launchCount = 0;
        }
      } else {
        launchCount += 1;
      }

      await AsyncStorage.setItem(LAUNCH_COUNT_KEY, launchCount.toString());
      // console.log('launchCount', launchCount);
      if (launchCount >= REQUIRED_LAUNCHES) {
        timeoutRef.current = setTimeout(() => {
          setReadyToReview(true);
        }, 60 * 1000); // delay 60s
      }
    };

    checkAndWait();
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return readyToReview;
};
