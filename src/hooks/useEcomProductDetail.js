import {useSelector, useDispatch} from 'react-redux';
import {updateEcomProductsData} from 'reducers/dataCache';
import {getProductDetails} from 'requests/ecom';
import {cloneDeep} from 'lodash';

export const ONE_DAY = 24 * 60 * 60 * 1000; // milliseconds in 1 day
export const useEcomProductDetail = () => {
  const dispatch = useDispatch();
  const ecomProductsCache = useSelector(state => state.dataCache?.ecomProducts);
  const getEcomProductDetail = async productId => {
    try {
      // Get current cache on each call to ensure latest data
      const currentCache = cloneDeep(ecomProductsCache);
      const cacheItem = currentCache?.data?.find(
        prd => prd.data?.id === productId,
      );

      const isExpired = !cacheItem || Date.now() - cacheItem.cachedAt > ONE_DAY;

      if (isExpired) {
        const productDetails = await getProductDetails(productId);
        // console.log('Get product FROM ECOM', productDetails?.id);
        if (productDetails) {
          const {
            id,
            _v,
            _type,
            brand,
            currency,
            image_groups,
            valid_from,
            valid_to,
            name,
            price_per_unit,
            price,
            step_quantity,
            unit_quantity,
            primary_category_id,
            variants,
          } = productDetails;

          const newCacheData = [];

          const newItem = {
            data: {
              id,
              _v,
              _type,
              brand,
              currency,
              image_groups,
              valid_from,
              valid_to,
              name,
              price_per_unit,
              price,
              step_quantity,
              unit_quantity,
              primary_category_id,
              variants,
            },
            cachedAt: Date.now(),
          };

          newCacheData.push(newItem);
          dispatch(updateEcomProductsData({data: newCacheData}));
          return newItem.data;
        }
      } else {
        // console.log('Get product from cache', cacheItem?.data?.id);
        return cacheItem?.data;
      }
    } catch (error) {
      console.error('getEcomProductDetail error', error);
      return null;
    }
  };

  const getMultipleEcomProductDetails = async productIds => {
    try {
      const currentCache = cloneDeep(ecomProductsCache);
      const newCacheData = [];

      const results = [];

      for (const productId of productIds) {
        const cacheItem = currentCache.data.find(
          prd => prd.data?.id === productId,
        );
        const isExpired =
          !cacheItem || Date.now() - cacheItem.cachedAt > ONE_DAY;
        if (isExpired) {
          const productDetails = await getProductDetails(productId);
          // console.log('Get product FROM ECOM(Multi)', productDetails?.id);

          if (productDetails) {
            const {
              id,
              _v,
              _type,
              brand,
              currency,
              image_groups,
              valid_from,
              valid_to,
              name,
              price_per_unit,
              price,
              step_quantity,
              unit_quantity,
              primary_category_id,
              variants,
            } = productDetails;

            const newItem = {
              data: {
                id,
                _v,
                _type,
                brand,
                currency,
                image_groups,
                valid_from,
                valid_to,
                name,
                price_per_unit,
                price,
                step_quantity,
                unit_quantity,
                primary_category_id,
                variants,
              },
              cachedAt: Date.now(),
            };

            newCacheData.push(newItem);
            results.push(newItem.data);
          } else {
            results.push(null);
          }
        } else {
          // console.log('Get product from cache(multi)', cacheItem?.data?.id);
          results.push(cacheItem?.data);
        }
      }

      // Only dispatch once after processing all products
      dispatch(updateEcomProductsData({data: newCacheData}));
      return results;
    } catch (error) {
      console.error('getMultipleEcomProductDetails error', error);
      return [];
    }
  };

  return {getEcomProductDetail, getMultipleEcomProductDetails};
};
