import React from 'react';
import ProgressCircleCustom from 'components/ProgressCircleCustom';
import {Platform, View, StyleSheet} from 'react-native';
import Text from 'components/Text';

const CircleChartSmall = ({value, color, style, backgroundColor}) => {
  return value === 0 ? (
    <View style={[styles.notApplicableCircle, style]}>
      <View
        style={{
          flexDirection: 'row',
        }}
      >
        <Text white size={14} style={{marginLeft: 4}}>
          {value}
        </Text>
        <Text
          MonoFont
          size={8}
          style={{
            fontWeight: '500',
            marginTop: Platform.OS === 'ios' ? 2 : 4,
          }}
          white
        >
          %
        </Text>
      </View>
    </View>
  ) : (
    <ProgressCircleCustom
      value={value}
      maxValue={100}
      size={50}
      colorProgress={color}
      endText={''}
      duration={400}
      border={7}
      SFPro
      customEndText={
        <Text
          MonoFont
          size={8}
          style={{
            fontWeight: '500',
            marginTop: Platform.OS === 'ios' ? 2 : 4,
          }}
          black
        >
          %
        </Text>
      }
      textStyle={{
        fontSize: 14,
      }}
      style={style}
      backgroundColor={backgroundColor}
    />
  );
};

const styles = StyleSheet.create({
  notApplicableCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#cccccc',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 4,
  },
});

export default CircleChartSmall;
