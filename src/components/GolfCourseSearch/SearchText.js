import React, {useRef} from 'react';
import PropTypes from 'prop-types';
import {View, TextInput, TouchableOpacity, StyleSheet} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';

import {GREY} from '../../config';
import appStyles from 'styles/global';

const SearchText = ({
  style,
  placeholder,
  onChangeText,
  defaultValue,
  leftIcon,
  rightIcon,
  clearInput,
  mode,
  secureTextEntry,
  autoCapitalize,
  keyboardType,
  returnKeyType,
  disabled,
  autoCorrect,
  textContentType,
  stylesInput,
  styleLeftIcon,
  placeholderTextColor,
  value,
  onFocus,
  onSubmitEditing,
  inputProps,
  refInput,
}) => {
  const inputRef = useRef(null);
  return (
    <View
      style={[
        appStyles.pHMd,
        appStyles.row,
        appStyles.hCenter,
        styles.textInputContainer,
      ].concat(style)}
    >
      {leftIcon ? (
        <Icon
          name={leftIcon}
          color={GREY}
          size={wp('4%')}
          style={appStyles.mRSm}
          {...styleLeftIcon}
        />
      ) : null}

      <TextInput
        style={[
          appStyles.sm,
          appStyles.flex,
          {flexWrap: 'wrap'},
          mode === 'light' ? styles.textInputLight : styles.textInput,
          {...stylesInput},
        ]}
        placeholderTextColor={placeholderTextColor ?? GREY}
        placeholder={placeholder}
        onChangeText={value => onChangeText(value.trimStart())}
        defaultValue={defaultValue}
        secureTextEntry={secureTextEntry}
        autoCapitalize={autoCapitalize}
        keyboardType={keyboardType}
        returnKeyType={returnKeyType || 'done'}
        disabled={disabled}
        underlineColorAndroid="transparent"
        autoCorrect={autoCorrect}
        textContentType={textContentType}
        value={value}
        ref={ref => {
          inputRef.current = ref;
          if (refInput) {
            refInput(ref);
          }
        }}
        onSubmitEditing={onSubmitEditing}
        onFocus={onFocus}
        {...inputProps}
      />

      {/* Clear input text */}
      {rightIcon ? (
        <TouchableOpacity
          style={[appStyles.pLSm, {marginRight: -10}]}
          onPress={() => {
            inputRef?.current?.clear?.();
            clearInput();
          }}
          disabled={disabled}
        >
          <Icon name={rightIcon} color={GREY} size={wp('4%')} />
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

SearchText.propTypes = {
  placeholder: PropTypes.string,
  onChangeText: PropTypes.func,
  defaultValue: PropTypes.string,
  clearInput: PropTypes.func,
  mode: PropTypes.string,
  secureTextEntry: PropTypes.bool,
  autoCapitalize: PropTypes.string,
  keyboardType: PropTypes.string,
  returnKeyType: PropTypes.string,
  autoCorrect: PropTypes.bool,
};

SearchText.defaultProps = {
  placeholder: '',
  onChangeText: () => {},
  defaultValue: '',
  clearInput: () => {},
  mode: '',
  secureTextEntry: false,
  autoCapitalize: 'sentences',
  keyboardType: 'default',
  autoCorrect: true,
  returnKeyType: null,
};

const styles = StyleSheet.create({
  textInputContainer: {
    borderRadius: wp('20%'),
    borderWidth: 1,
    borderColor: GREY,
    height: hp('6%'),
  },
  textInput: {
    color: 'white',
  },
  textInputLight: {
    color: GREY,
  },
});

export default SearchText;
