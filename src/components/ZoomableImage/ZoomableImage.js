import React, {useEffect, useState} from 'react';
import {Image} from 'react-native';
import {
  PanGestureHandler,
  PinchGestureHandler,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';

const ZoomableImage = ({source, style, imageStyle}) => {
  const scale = useSharedValue(1);
  const currentScale = useSharedValue(1);
  const currentTranslateX = useSharedValue(0);
  const currentTranslateY = useSharedValue(0);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  let {boxWidth, boxHeight} = imageStyle;
  const [size, setSize] = useState({
    width: boxWidth,
    height: boxHeight,
  });

  useEffect(() => {
    if (source?.uri && boxWidth && boxHeight) {
      Image.getSize(source?.uri, (w, h) => {
        const ratio = w / h;
        let width = w;
        let height = h;
        if (boxWidth > 0 && ratio > boxWidth / boxHeight) {
          height = boxHeight;
          width = ratio * height;
        } else if (ratio <= boxWidth / boxHeight && boxHeight > 0) {
          width = boxWidth;
          height = width / ratio;
        }
        setSize({
          width,
          height,
        });
      });
    }
  }, [boxHeight, boxWidth, source?.uri]);

  const pinchHandler = useAnimatedGestureHandler({
    onActive: event => {
      scale.value = currentScale.value * event.scale;
    },
    onEnd: event => {
      currentScale.value = currentScale.value * event.scale;
    },
  });

  const panHandler = useAnimatedGestureHandler({
    onActive: event => {
      translateX.value =
        currentTranslateX.value + event.translationX / currentScale.value;
      translateY.value =
        currentTranslateY.value + event.translationY / currentScale.value;
    },
    onEnd: event => {
      currentTranslateX.value =
        currentTranslateX.value + event.translationX / currentScale.value;
      currentTranslateY.value =
        currentTranslateY.value + event.translationY / currentScale.value;
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {scale: scale.value},
        {translateX: translateX.value},
        {translateY: translateY.value},
      ],
    };
  });
  return (
    <PanGestureHandler
      onGestureEvent={panHandler}
      minPointers={1}
      maxPointers={1}
    >
      <Animated.View
        style={[
          {
            justifyContent: 'center',
            alignItems: 'center',
          },
          style,
        ]}
      >
        <PinchGestureHandler
          onGestureEvent={pinchHandler}
          minPointers={2}
          maxPointers={2}
        >
          <Animated.View
            style={[
              {
                justifyContent: 'center',
                alignItems: 'center',
              },
              style,
            ]}
          >
            <Animated.Image source={source} style={[animatedStyle, size]} />
          </Animated.View>
        </PinchGestureHandler>
      </Animated.View>
    </PanGestureHandler>
  );
};

export default ZoomableImage;
