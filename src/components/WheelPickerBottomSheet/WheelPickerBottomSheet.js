import React, {useEffect, useState} from 'react';
import Picker from '@gregfrench/react-native-wheel-picker';
import appStyles from 'styles/global';
import {
  View,
  Platform,
  TouchableOpacity,
  TouchableWithoutFeedback,
  StyleSheet,
  Image,
} from 'react-native';
import BottomSheet from 'reanimated-bottom-sheet';
import Text from 'components/Text';
import TickIcon from 'assets/imgs/tick-icon.png';
import CancelIcon from 'assets/imgs/cancel-icon.png';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import { convertDistanceFromYards } from 'utils/convert';

const WheelPickerBottomSheet = React.forwardRef((props, sheetRef) => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [innerPickerValue, setInnerPickerValue] = useState(props.currentValue);
  const [innerDateValue, setInnerDateValue] = useState(
    props.type === 'date' ? props.currentValue : null,
  );

  useEffect(() => {
    return () => {
      showBottomTab();
    };
  }, []);

  useEffect(() => {
    if (props.type === 'date') {
      setInnerDateValue(props.currentValue);
    } else {
      setInnerPickerValue(props.currentValue);
    }
  }, [props.currentValue]);

  const hideBottomTab = () => {
    props.navigation
      ?.getParent()
      ?.setOptions({tabBarStyle: {display: 'none'}, tabBarVisible: false});
  };
  const showBottomTab = () => {
    props.navigation
      ?.getParent()
      ?.setOptions({tabBarStyle: undefined, tabBarVisible: undefined});
  };

  const closeBottomSheet = () => {
    sheetRef.current?.snapTo(1); // Close bottom sheet
  };

  const handleExpression = (
    item,
    expression = '',
    propsArray = [],
    labelReplaceConditions = [],
    shouldHideNumberZero = false,
  ) => {
    try {
      if (
        propsArray.length > 1 &&
        (item[propsArray[1]] === null ||
          item[propsArray[1]] === undefined ||
          (shouldHideNumberZero && item[propsArray[1]] === 0))
      ) {
        expression = '{1}';
      }
      for (let index = 0; index < propsArray.length; index++) {
        const prop = propsArray[index];
        if (prop === 'ydsTotal') {
          expression = expression.replace(
            `{${index + 1}}`,
            convertDistanceFromYards({
              distanceInYards: item[prop],
              userUnit: props.userDistanceUnit,
            }).value,
          );
        } else {
          expression = expression.replace(`{${index + 1}}`, item[prop]);
        }
      }
      for (let index = 0; index < labelReplaceConditions.length; index++) {
        const replaceCondition = labelReplaceConditions[index];
        expression = expression.replace(
          replaceCondition?.searchStr,
          replaceCondition?.replaceStr,
        );
      }
      return expression;
    } catch (error) {
      console.log(error.message);
    }
  };

  const onClosePickerSheet = () => {
    if (props.type === 'date') {
      setInnerDateValue(props.currentValue);
    } else {
      setInnerPickerValue(props.currentValue);
    }
    closeBottomSheet();
  };

  const onSelectValue = () => {
    props.onChange?.(props.type === 'date' ? innerDateValue : innerPickerValue);
    closeBottomSheet();
  };

  const renderWheelPicker = () => {
    return (
      <View
        style={[
          appStyles.row,
          Platform.OS === 'android'
            ? {
                flex: 1,
                justifyContent: 'center',
                marginLeft: 45,
                marginRight: 10,
              }
            : {marginLeft: 45},
          {marginLeft: 0},
        ]}
      >
        <Picker
          style={appStyles.flex}
          lineColor={'#cccccc'}
          itemStyle={{color: '#000000', fontSize: 17}}
          selectedValue={props.options.indexOf(innerPickerValue)}
          onValueChange={index => setInnerPickerValue(props.options[index])}
        >
          {props.options.map((option, i) => (
            <Picker.Item
              key={
                props.type === 'text' ? option : option[props.keyProp || 'key']
              }
              label={
                props.type === 'text'
                  ? option
                  : props.labelExpression
                  ? handleExpression(
                      option,
                      props.labelExpression,
                      props.labelPropArray,
                      props.labelReplaceConditions,
                      props.shouldHideNumberZero,
                    )
                  : option[props.labelProp] || 'label'
              }
              value={i}
            />
          ))}
        </Picker>
      </View>
    );
  };

  const renderDatePicker = () => {
    return (
      <DatePicker
        style={[appStyles.flex]}
        date={
          innerDateValue?.length
            ? new Date(innerDateValue)
            : props.defaultValue && props.defaultValue?.length > 0
            ? new Date(props.defaultValue)
            : new Date()
        }
        mode="date"
        minimumDate={props?.minimumDate ? new Date(props?.minimumDate) : ''}
        maximumDate={props.allowFutureDates ? '' : new Date()}
        onDateChange={dateSelect => {
          setInnerDateValue(moment(dateSelect).toISOString());
        }}
        androidVariant="iosClone"
        locale="en"
      />
    );
  };

  return (
    <>
      {showOverlay ? (
        <TouchableWithoutFeedback onPress={onClosePickerSheet}>
          <View style={styles.overlayView} />
        </TouchableWithoutFeedback>
      ) : null}
      <BottomSheet
        ref={sheetRef}
        snapPoints={
          props?.sheetsProps?.snapPoints
            ? props?.sheetsProps?.snapPoints
            : [320, 0]
        }
        borderRadius={46}
        initialSnap={1}
        onCloseEnd={() => {
          setShowOverlay(false);
          showBottomTab();
        }}
        onOpenStart={() => {
          setShowOverlay(true);
          hideBottomTab();
        }}
        renderContent={() => (
          <View style={styles.sheetContentContainerView}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <TouchableOpacity onPress={onClosePickerSheet}>
                <Image source={CancelIcon} style={{width: 24, height: 24}} />
              </TouchableOpacity>
              <Text
                black
                size={12}
                Din79Font
                style={{fontWeight: '700', flex: 1, textAlign: 'center'}}
              >
                {props.pickerTitle}
              </Text>
              <TouchableOpacity onPress={onSelectValue}>
                <Image source={TickIcon} style={{width: 24, height: 24}} />
              </TouchableOpacity>
            </View>
            {props.type === 'date'
              ? renderDatePicker()
              : props.options?.length > 0
              ? renderWheelPicker()
              : null}
          </View>
        )}
      />
    </>
  );
});

const styles = StyleSheet.create({
  overlayView: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(17, 17, 17, 0.3)',
    zIndex: 100,
  },
  sheetContentContainerView: {
    backgroundColor: 'white',
    height: '100%',
    alignItems: 'center',
    paddingVertical: 25,
    zIndex: 999,
    paddingHorizontal: 26,
  },
});

export default WheelPickerBottomSheet;
