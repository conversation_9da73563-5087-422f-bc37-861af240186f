import Text from 'components/Text';
import React from 'react';
import {Image, Platform, TouchableOpacity, View} from 'react-native';
import BG_BottomTab from 'assets/imgs/bottomtab/bg_bottom_tab.png';
import Ic_clubhouse_black from 'assets/imgs/bottomtab/ic_clubhouse_black.svg';
import Ic_clubhouse_white from 'assets/imgs/bottomtab/ic_clubhouse_white.svg';
import Ic_house_black from 'assets/imgs/bottomtab/ic_house_black.svg';
import Ic_house_white from 'assets/imgs/bottomtab/ic_house_white.svg';
import Ic_play_black from 'assets/imgs/bottomtab/ic_play_black.svg';
import Ic_play_white from 'assets/imgs/bottomtab/ic_play_white.svg';
import Ic_rewards_black from 'assets/imgs/bottomtab/ic_rewards_black.svg';
import Ic_rewards_white from 'assets/imgs/bottomtab/ic_rewards_white.svg';
import Ic_shop_black from 'assets/imgs/bottomtab/ic_shop_black.svg';
import Ic_shop_white from 'assets/imgs/bottomtab/ic_shop_white.svg';
import Animated, {
  useSharedValue,
  withTiming,
  useAnimatedStyle,
} from 'react-native-reanimated';
import LinearGradient from 'react-native-linear-gradient';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {
  BOTTOM_BAR_HEIGHT,
  checkMainCountry,
  isSmallScreenIphone,
} from 'utils/constant';
import {useSelector} from 'react-redux';

const SCREEN_WIDTH = wp(100);
const BOTTOM_TAB_HORIZONTAL_PADDING = 8; //It matches the left padding next to the black Tab Icon Holding of BG_BottomTab. See the image for more
const height = BOTTOM_BAR_HEIGHT;
const REAL_TAB_WIDTH = wp(100) - BOTTOM_TAB_HORIZONTAL_PADDING * 2;
const STANDARD_TAB_COUNT = 5; //Because the BG_BottomTab is designed for 5 tabs
const BottomTabCustomize = ({state, descriptors, navigation}) => {
  const user = useSelector(reduxState => reduxState.user);
  const isMainCountry = checkMainCountry(user?.userCountry);
  const homePageIndex = 0;
  const playPageIndex = isMainCountry ? 2 : 1;
  const clubHousePageIndex = isMainCountry ? 1 : 2;
  const shopPageIndex = 3;
  const rewardsPageIndex = 4;
  const offset = useSharedValue(0);
  const ACCTUAL_TAB_COUNT = state.routes?.length;
  const widthItem = REAL_TAB_WIDTH / ACCTUAL_TAB_COUNT;
  React.useEffect(() => {
    const value =
      widthItem * state.index +
      REAL_TAB_WIDTH *
        (1 / (ACCTUAL_TAB_COUNT * 2) - 1 / (STANDARD_TAB_COUNT * 2));
    offset.value = withTiming(value, {
      duration: 300,
    });
  }, [state.index, ACCTUAL_TAB_COUNT]);

  const animatedTabBGStyles = useAnimatedStyle(() => {
    return {
      left: offset.value - SCREEN_WIDTH,
    };
  });
  const onPress = (route, isFocused, indexSelected) => {
    const event = navigation.emit({
      type: 'tabPress',
      target: route.key,
      canPreventDefault: true,
    });

    if (!isFocused && !event.defaultPrevented) {
      navigation.navigate(route.name, route.params);
    }
  };
  const getIcon = (index, indexSelected) => {
    switch (index) {
      case 0:
        if (
          indexSelected === playPageIndex ||
          indexSelected === shopPageIndex
        ) {
          return {icon: <Ic_house_black />, color: '#000'};
        } else {
          return {icon: <Ic_house_white />, color: '#fff'};
        }
      case 1:
        // If is main country, the order is: Home -> Play -> ClubHouse
        if (checkMainCountry(user?.userCountry)) {
          if (indexSelected === rewardsPageIndex) {
            return {icon: <Ic_clubhouse_white />, color: '#fff'};
          } else {
            return {icon: <Ic_clubhouse_black />, color: '#000'};
          }
        } else {
          if (
            indexSelected === homePageIndex ||
            indexSelected === shopPageIndex
          ) {
            return {icon: <Ic_play_black />, color: '#000'};
          } else {
            return {icon: <Ic_play_white />, color: '#fff'};
          }
        }
      case 2:
        // If is main country, the order is: Home -> Play -> ClubHouse
        if (checkMainCountry(user?.userCountry)) {
          if (
            indexSelected === homePageIndex ||
            indexSelected === shopPageIndex
          ) {
            return {icon: <Ic_play_black />, color: '#000'};
          } else {
            return {icon: <Ic_play_white />, color: '#fff'};
          }
        } else {
          if (indexSelected === rewardsPageIndex) {
            return {icon: <Ic_clubhouse_white />, color: '#fff'};
          } else {
            return {icon: <Ic_clubhouse_black />, color: '#000'};
          }
        }
      case 3:
        if (
          indexSelected === homePageIndex ||
          indexSelected === playPageIndex
        ) {
          return {icon: <Ic_shop_black />, color: '#000'};
        } else {
          return {icon: <Ic_shop_white />, color: '#fff'};
        }
      default:
        if (indexSelected === clubHousePageIndex) {
          return {icon: <Ic_rewards_white />, color: '#fff'};
        } else {
          return {icon: <Ic_rewards_black />, color: '#000'};
        }
    }
  };

  const getBackgroundColor = indexSelected => {
    if (
      indexSelected === clubHousePageIndex ||
      indexSelected === rewardsPageIndex
    ) {
      return '#fff';
    }
    return '#000';
  };

  const renderItem = (label, index, indexSelected) => {
    const {color, icon} = getIcon(index, indexSelected);
    return (
      <View
        key={label}
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: 40,
          minWidth: 40,
          height: 40,
          marginTop: 2,
          backgroundColor:
            index === indexSelected
              ? getBackgroundColor(indexSelected)
              : 'transparent',
        }}
      >
        {icon}
        {index !== indexSelected && (
          <Text size={8} Din79Font weight={700} style={{color}}>
            {label.toUpperCase()}
          </Text>
        )}
      </View>
    );
  };
  const bottomValue =
    isSmallScreenIphone() || Platform.OS === 'android'
      ? 62 - BOTTOM_BAR_HEIGHT
      : 84 - BOTTOM_BAR_HEIGHT;
  return (
    <View
      style={{
        flexDirection: 'row',
        width: SCREEN_WIDTH,
        height: height,
        backgroundColor: 'transparent',
        position: 'absolute',
        bottom: bottomValue < 0 ? bottomValue : 0,
        paddingHorizontal: 8,
      }}
    >
      <View
        style={{width: SCREEN_WIDTH * 2, height: height, position: 'absolute'}}
      >
        <LinearGradient
          colors={[
            'rgba(0, 0, 0, 0)',
            'rgba(0, 0, 0, 0.36)',
            'rgba(0, 0, 0, 1)',
          ]}
          style={{
            height,
            width: SCREEN_WIDTH,
            position: 'absolute',
            opacity: 0.5,
          }}
        />
        <Animated.View
          style={[
            {
              width: SCREEN_WIDTH * 3,
              height,
              flexDirection: 'row',
              left: -SCREEN_WIDTH,
              position: 'absolute',
            },
            animatedTabBGStyles,
          ]}
        >
          <View
            style={{
              backgroundColor:
                state.index === clubHousePageIndex ||
                state.index === rewardsPageIndex
                  ? '#000'
                  : '#fff',
              width: SCREEN_WIDTH,
              height,
            }}
          />
          <Image
            source={BG_BottomTab}
            style={{
              width: SCREEN_WIDTH,
              height,
              tintColor:
                state.index === clubHousePageIndex ||
                state.index === rewardsPageIndex
                  ? '#000'
                  : '#fff',
            }}
          />
          <View
            style={{
              backgroundColor:
                state.index === clubHousePageIndex ||
                state.index === rewardsPageIndex
                  ? '#000'
                  : '#fff',
              width: SCREEN_WIDTH,
              height,
            }}
          />
        </Animated.View>
      </View>
      {state.routes.map((route, index) => {
        const {options} = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
            ? options.title
            : route.name;

        const isFocused = state.index === index;
        return (
          <TouchableOpacity
            key={label + 'Touch'}
            accessibilityRole="button"
            accessibilityState={isFocused ? {selected: true} : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarTestID}
            onPress={() => onPress(route, isFocused, index)}
            style={{
              flex: 1,
              alignItems: 'center',
            }}
          >
            {renderItem(label, index, state.index)}
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default BottomTabCustomize;
