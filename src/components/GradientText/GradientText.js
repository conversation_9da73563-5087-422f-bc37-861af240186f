import React from 'react';
import Text from 'components/Text';
import LinearGradient from 'react-native-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';

const GradientText = ({
  children,
  style,
  colors = ['#FFD836', '#998220'],
  ...otherProps
}) => {
  return (
    <MaskedView
      maskElement={
        <Text style={[style, {backgroundColor: 'transparent'}]} {...otherProps}>
          {children}
        </Text>
      }
    >
      <LinearGradient
        colors={colors}
        start={{x: 0, y: 0}}
        end={{x: 0.7, y: 0}}
        style={{flex: 1}}
      >
        <Text style={[style, {opacity: 0}]} {...otherProps}>
          {children}
        </Text>
      </LinearGradient>
    </MaskedView>
  );
};

export default GradientText;
