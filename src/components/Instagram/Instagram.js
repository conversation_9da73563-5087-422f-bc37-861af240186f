import React from 'react';
import {
  View,
  StyleSheet,
  Image,
  Text,
  ScrollView,
  Linking,
  TouchableOpacity,
} from 'react-native';
import {InstagramVideo} from './Video';
import {
  numberWithSpace,
  getPostTime,
  backgroundColorDarkMode,
  backgroundColorLightMode,
  textColorDarkMode,
  textColorLightMode,
} from './utils';
import {InteractionRow} from './InteractionRow';
import {getLikeTranslation} from './translations';
import {getPostInformation} from 'requests/instagram';
import {t} from 'i18next';

export const Instagram = props => {
  const {id, language, darkMode, containerBorderRadius} = props;
  const [instagramPostData, setInstagramPostData] = React.useState(null);
  const [carouselCurrentIndex, setCarouselCurrentIndex] = React.useState(0);
  const styles = stylings(darkMode);

  React.useEffect(() => {
    getPostInformation(id).then(response => setInstagramPostData(response));
  }, [id]);
  if (!instagramPostData) {
    return null;
  }

  function nFormatter(num, digits) {
    const lookup = [
      {value: 1, symbol: ''},
      {value: 1e3, symbol: 'k'},
      {value: 1e6, symbol: 'M'},
      {value: 1e9, symbol: 'G'},
      {value: 1e12, symbol: 'T'},
      {value: 1e15, symbol: 'P'},
      {value: 1e18, symbol: 'E'},
    ];
    const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
    let item = lookup
      .slice()
      .reverse()
      .find(function (item) {
        return num >= item.value;
      });
    return item
      ? (num / item.value).toFixed(digits).replace(rx, '$1') + item.symbol
      : '0';
  }

  const numberOfItems = instagramPostData?.content?.length;
  const isCarousel = numberOfItems > 1;
  return (
    <View style={styles.container(containerBorderRadius)}>
      <TouchableOpacity
        onPress={() => Linking.openURL(`https://www.instagram.com/p/${id}/`)}
        style={[styles.header]}
      >
        <Image
          source={{uri: instagramPostData?.ownerPicture}}
          style={styles.profilePicture}
        />
        <View>
          <View style={{flexDirection: 'row'}}>
            <Text style={styles.headerUsername}>
              {instagramPostData?.ownerName}
            </Text>
            {instagramPostData?.ownerIsVerified ? (
              <Image
                source={require('./assets/verified.png')}
                style={styles.verified}
              />
            ) : null}
          </View>
          <Text
            style={{marginLeft: 8, fontSize: 12, color: 'gray'}}
          >{`${nFormatter(
            instagramPostData?.ownerFollower,
            1,
          )} followers`}</Text>
        </View>
        <View style={{flex: 1}} />
        <View
          style={{
            backgroundColor: 'rgb(0, 147, 240)',
            borderRadius: 4,
            paddingHorizontal: 12,
            paddingVertical: 8,
          }}
        >
          <Text style={{color: 'white', fontSize: 16, fontWeight: 'bold'}}>
            {t('view_profile')}
          </Text>
        </View>
      </TouchableOpacity>
      <ScrollView
        horizontal
        contentContainerStyle={{
          width: instagramPostData?.content?.length * 100 + '%',
        }}
        showsHorizontalScrollIndicator={false}
        bounces={isCarousel}
        onMomentumScrollEnd={({nativeEvent}) => {
          setCarouselCurrentIndex(
            Math.round(
              (nativeEvent.contentOffset.x / nativeEvent.contentSize.width) *
                numberOfItems,
            ),
          );
        }}
        decelerationRate="fast"
        disableIntervalMomentum
        pagingEnabled
      >
        {instagramPostData?.content?.map((element, index) => {
          if (element?.type === 'video' && !!element?.videoUrl) {
            return (
              <InstagramVideo
                // @ts-ignore
                source={element?.videoUrl}
                key={element?.pictureUrl}
                poster={element?.pictureUrl}
                aspectRatio={
                  instagramPostData?.width / instagramPostData?.height
                }
                hasFocus={index === carouselCurrentIndex}
              />
            );
          } else {
            return (
              <Image
                source={{uri: element?.pictureUrl}}
                key={element?.pictureUrl}
                style={styles.postImage(
                  instagramPostData?.width,
                  instagramPostData?.height,
                )}
              />
            );
          }
        })}
      </ScrollView>
      {/* <TouchableOpacity
      activeOpacity={0.8}
      onPress={() => Linking.openURL(`https://www.instagram.com/p/${id}/`)}
    ></TouchableOpacity> */}
      <TouchableOpacity
        onPress={() => Linking.openURL(`https://www.instagram.com/p/${id}/`)}
        style={styles.footer}
      >
        <Text
          style={{
            color: 'rgb(0, 147, 240)',
            // marginHorizontal: 4,
            marginVertical: 14,
            fontSize: 14,
            fontWeight: 'bold',
          }}
        >
          {t('view_more_on_insta')}
        </Text>
        <View
          style={{
            width: '100%',
            height: 1,
            backgroundColor: '#d6d6d6',
          }}
        />
        <InteractionRow
          numberOfBullets={isCarousel ? numberOfItems : null}
          itemIndexCurrentlyFocused={carouselCurrentIndex}
          darkMode={darkMode}
          id={id}
        />
        <Text style={styles.like}>
          {getLikeTranslation(
            numberWithSpace(instagramPostData?.likeCount),
            // @ts-ignore
            language,
          )}
        </Text>
        <Text>
          <Text style={styles.footerOwnerName}>
            {instagramPostData?.ownerName + ' '}
          </Text>
          <Text style={styles.text}>
            {instagramPostData?.text ? instagramPostData?.text : ''}
          </Text>
        </Text>
        <Text style={styles.date}>
          {getPostTime(
            instagramPostData?.timestamp,
            // @ts-ignore
            language,
          ) || ''}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

Instagram.defaultProps = {
  language: 'en',
  darkMode: false,
  borderRadius: 0,
};

const stylings = darkMode =>
  StyleSheet.create({
    container: containerBorderRadius => ({
      backgroundColor: darkMode
        ? backgroundColorDarkMode
        : backgroundColorLightMode,
      borderRadius: containerBorderRadius,
      marginVertical: 16,
      // marginRight: 10,
      borderWidth: 1,
      borderColor: '#a9a9a9',
    }),
    header: {
      marginVertical: 16,
      marginHorizontal: 8,
      flexDirection: 'row',
      alignItems: 'center',
    },
    headerUsername: {
      marginLeft: 8,
      fontSize: 16,
      fontWeight: '700',
      color: darkMode ? textColorDarkMode : textColorLightMode,
    },
    profilePicture: {
      width: 32,
      height: 32,
      borderRadius: 16,
    },
    verified: {
      width: 16,
      height: 16,
      marginLeft: 8,
    },
    footer: {
      marginHorizontal: 16,
      marginBottom: 16,
    },
    like: {
      color: darkMode ? textColorDarkMode : textColorLightMode,
      fontSize: 16,
      fontWeight: '700',
      marginBottom: 8,
    },
    footerOwnerName: {
      color: darkMode ? textColorDarkMode : textColorLightMode,
      fontSize: 16,
      fontWeight: '700',
    },
    text: {
      color: darkMode ? textColorDarkMode : textColorLightMode,
      lineHeight: 22,
      fontSize: 16,
    },
    date: {
      color: 'rgb(142, 142, 142)',
      marginTop: 10,
      fontSize: 14,
    },
    // @ts-ignore wrong react-native typings
    postImage: (width, height) => ({
      flex: 1,
      aspectRatio: width / height,
    }),
  });
