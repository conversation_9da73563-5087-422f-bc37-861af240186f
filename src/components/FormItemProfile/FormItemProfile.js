import React from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Switch,
  TouchableWithoutFeedback,
  TextInput,
  TouchableOpacity,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome5';
import AntDesignIcon from 'react-native-vector-icons/AntDesign';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';

import Text from '../Text';

import appStyles from '../../styles/global';
import {GREY} from '../../config';
import * as Animatable from 'react-native-animatable';
import {moderateScale} from 'react-native-size-matters';
import IconTooltip from 'assets/imgs/ic_tooltip.png';
import IconArrowRight from 'assets/imgs/profile/icon-arrow-right.svg';

const isTablet = DeviceInfo.isTablet();

const FormItemProfile = ({
  label,
  value,
  valueStyle,
  valueDINbold,
  Din79Font,
  styleIcon,
  icon,
  iconGrey,
  toggle,
  onPress,
  currentStatus,
  setStatus,
  disabled,
  bottomBorder,
  topBorder,
  iconColor,
  backgroundColor,
  placeholder,
  onChangeText,
  defaultValue,
  autoCapitalize,
  warning,
  style,
  borderBottomColor = GREY + '50',
  borderBottomWidth = moderateScale(1),
  widthIcon = wp('5%'),
  labelStyle = {},
  hasIconArrowRight = false,
  onPressTooltip,
  hasTooltip,
  isFollowPlayer = false,
}) => {
  const renderFollowPlayer = value => {
    const followPlayers = value.split(',');
    if (followPlayers && followPlayers.length > 0) {
      return (
        <View style={[appStyles.row, appStyles.hCenter]}>
          <View style={appStyles.flex}>
            {followPlayers.map(val => (
              <Text
                DINbold={valueDINbold}
                Din79Font={Din79Font}
                style={[
                  appStyles.pRSm,
                  appStyles.textRight,
                  {color: GREY},
                ].concat(valueStyle)}
              >
                {val}
              </Text>
            ))}
          </View>

          <IconArrowRight />
        </View>
      );
    }
    return '';
  };
  const toggleSwitch = () => setStatus(previousState => !previousState);
  return (
    <TouchableWithoutFeedback
      disabled={disabled ? true : false}
      onPress={onPress}
      accessible={false}
    >
      <Animatable.View
        animation={'fadeIn'}
        accessible={false}
        style={[
          appStyles.row,
          backgroundColor ? backgroundColor : appStyles.whiteBg,
          {
            alignItems: 'flex-start',
          },
          bottomBorder
            ? {
                borderBottomColor: borderBottomColor,
                borderBottomWidth: borderBottomWidth,
              }
            : {},
          topBorder
            ? {
                borderTopColor: borderBottomColor,
                borderTopWidth: borderBottomWidth,
              }
            : {},
          style,
        ]}
      >
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginTop: hasTooltip ? -10 : 0,
          }}
        >
          <Text size={16} style={[appStyles.mRSm, labelStyle]}>
            {label}
          </Text>
        </View>
        <View
          style={[
            appStyles.flex,
            appStyles.row,
            {
              flexWrap: 'wrap',
              justifyContent: 'flex-end',
              alignItems: 'flex-end',
            },
          ]}
        >
          {icon && value ? (
            isFollowPlayer ? (
              renderFollowPlayer(value)
            ) : (
              <View style={[appStyles.row, appStyles.hCenter]}>
                <Text
                  DINbold={valueDINbold}
                  Din79Font={Din79Font}
                  style={[
                    appStyles.pRSm,
                    appStyles.textRight,
                    {color: GREY},
                  ].concat(valueStyle)}
                >
                  {value}
                </Text>
                <IconArrowRight />
              </View>
            )
          ) : icon ? (
            <View
              style={[
                appStyles.row,
                appStyles.hCenter,
                {marginTop: Platform.OS === 'android' ? 6 : 0},
              ]}
            >
              <IconArrowRight />
            </View>
          ) : value ? (
            hasIconArrowRight ? (
              <Text style={[{color: GREY}].concat(valueStyle)}>
                {value} {hasIconArrowRight && '􀆊'}
              </Text>
            ) : (
              <Text style={[{color: GREY}].concat(valueStyle)}>{value}</Text>
            )
          ) : toggle ? (
            <Switch
              accessible={true}
              testID="Active-club-switch"
              trackColor={{false: '#767577', true: '#34C759'}}
              thumbColor={'#ffffff'}
              disabled={disabled ? true : false}
              ios_backgroundColor={GREY}
              onValueChange={toggleSwitch}
              value={currentStatus}
            />
          ) : onChangeText ? (
            <TextInput
              style={[{padding: 0, textAlign: 'right'}]}
              placeholderTextColor={GREY}
              placeholder={placeholder}
              onChangeText={value => onChangeText(value.trimStart())}
              defaultValue={defaultValue}
              disabled={disabled}
              autoCapitalize={autoCapitalize}
              underlineColorAndroid="transparent"
              autoCorrect={false}
            />
          ) : null}
        </View>
      </Animatable.View>
    </TouchableWithoutFeedback>
  );
};

FormItemProfile.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string,
  icon: PropTypes.string,
  onPress: PropTypes.func,
  labelStyle: PropTypes.object,
};

FormItemProfile.defaultProps = {
  label: '',
  value: '',
  icon: null,
  onPress: () => {},
  labelStyle: {},
};

export default FormItemProfile;
