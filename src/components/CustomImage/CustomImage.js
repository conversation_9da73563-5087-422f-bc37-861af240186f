import React, {useState} from 'react';
import FastImage from 'react-native-fast-image/src';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import LinearGradient from 'react-native-linear-gradient';
import {View} from 'react-native';
import { widthPercentageToDP } from 'react-native-responsive-screen';

const CustomImage = props => {
  const [isImageLoading, setImageLoading] = useState(true);
  const [isError, setError] = useState(false);

  const imageStyle = {...props.style, zIndex: 2};
  let {width, height, aspectRatio, ...otherStyle} = props.style;
  if (!aspectRatio) {
    aspectRatio = 1;
  }
  if (!width) {
    if (!height) {
      width = widthPercentageToDP(100);
    } else {
      width = height * aspectRatio;
    }
  }
  return (
    <View>
      {isImageLoading && (
        <ShimmerPlaceholder
          LinearGradient={LinearGradient}
          width={width}
          height={height ? height : width / aspectRatio}
          style={{...otherStyle, zIndex: 1, position: 'absolute', top: 0}}
        />
      )}
      <FastImage
        style={imageStyle}
        source={{
          uri: isError
            ? props.source.fallbackUri
            : props.source.uri || props.source.fallbackUri,
          cache: FastImage.cacheControl.web,
        }}
        onError={e => {
          if (props.source.fallbackUri) {
            setError(true);
          }
        }}
        onLoadEnd={() => {
          setImageLoading(false);
        }}
      />
    </View>
  );
};

export default CustomImage;
