import React from 'react';
import PropTypes from 'prop-types';
import {View, TextInput, TouchableOpacity, Keyboard, Platform} from 'react-native';

import Icon from 'react-native-vector-icons/FontAwesome5';

import appStyles from '../../styles/global';
import {StyleSheet} from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import ArrowDown from 'assets/imgs/onBoarding/arrow_down.svg';

import {GREY} from '../../config';
import Text from 'components/Text/Text';

const ButtonInput = ({
  style,
  placeholder,
  value,
  onChangeText,
  defaultValue,
  leftIcon,
  secureIcon,
  buttonPress,
  mode,
  secureTextEntry,
  autoCapitalize,
  keyboardType,
  returnKeyType,
  disabled,
  borderColor = GREY,
  stylesInput,
  isDoubleText = false,
  ...props
}) => {
  return (
    <TouchableOpacity
      style={[
        appStyles.pHMd,
        appStyles.row,
        appStyles.hCenter,
        styles.textInputContainer,
        {borderColor: borderColor},
      ].concat(style)}
      onPress={buttonPress}
      activeOpacity={0.8}
    >
      {leftIcon ? (
        <Icon
          name={leftIcon}
          color={GREY}
          size={wp('4%')}
          style={appStyles.mRSm}
        />
      ) : null}
      <View
        style={{flexDirection: 'column', alignItems: 'flex-start'}}
        pointerEvents={'none'}
      >
        {value && isDoubleText && (
          <Text
            size={16}
            gray
            style={{marginTop: Platform.OS === 'ios' ? 7 : 0}}
          >
            {placeholder}
          </Text>
        )}

        <TextInput
          style={[
            appStyles.sm,
            appStyles.flex,
            {flexWrap: 'wrap'},
            mode === 'light' ? styles.textInputLight : styles.textInput,
            {...stylesInput},
          ]}
          placeholderTextColor={GREY}
          placeholder={placeholder}
          value={value}
          onChangeText={input => onChangeText(input)}
          defaultValue={defaultValue}
          secureTextEntry={secureTextEntry}
          autoCapitalize={autoCapitalize}
          keyboardType={keyboardType}
          returnKeyType={returnKeyType || 'done'}
          disabled={disabled}
          underlineColorAndroid="transparent"
          autoCorrect={false}
          blurOnSubmit={false}
          onSubmitEditing={() => Keyboard.dismiss()}
          textContentType="oneTimeCode"
          {...props}
        />
      </View>

      {/* Secure input text */}
      <TouchableOpacity
        style={[{marginLeft: 'auto', marginRight: -6.67}]}
        onPress={buttonPress}
        disabled={disabled}
      >
        <ArrowDown />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

ButtonInput.propTypes = {
  placeholder: PropTypes.string,
  onChangeText: PropTypes.func,
  defaultValue: PropTypes.string,
  buttonPress: PropTypes.func,
  mode: PropTypes.string,
  secureTextEntry: PropTypes.bool,
  autoCapitalize: PropTypes.string,
  keyboardType: PropTypes.string,
  returnKeyType: PropTypes.string,
};

ButtonInput.defaultProps = {
  placeholder: '',
  onChangeText: () => {},
  defaultValue: '',
  buttonPress: () => {},
  mode: '',
  secureTextEntry: false,
  autoCapitalize: 'none',
  keyboardType: 'default',
  returnKeyType: null,
};

export default ButtonInput;

const styles = StyleSheet.create({
  textInputContainer: {
    borderRadius: wp('20%'),
    borderWidth: 1,
    borderColor: GREY,
    height: hp('6%'),
  },
  textInput: {
    color: 'white',
  },
  textInputLight: {
    color: GREY,
  },
});
