import React, {useState, useMemo, useEffect} from 'react';
import {Image, StyleSheet, ImagePropTypes} from 'react-native';
import PropTypes from 'prop-types';

function AutoResizeImage({
  style,
  source,
  maxHeight = 128,
  maxWidth = 260,
  ...restProps
}) {
  const flattenedStyles = useMemo(() => StyleSheet.flatten(style), [style]);
  if (
    typeof flattenedStyles.width !== 'number' &&
    typeof flattenedStyles.height !== 'number'
  ) {
    throw new Error('AutoResizeImage requires either width or height');
  }

  const [size, setSize] = useState({
    width: flattenedStyles.width,
    height: flattenedStyles.height,
  });

  useEffect(() => {
    if ((!flattenedStyles.width || !flattenedStyles.height) && source.uri) {
      Image.getSize(source?.uri, (w, h) => {
        const ratio = w / h;
        let width = w;
        let height = h;
        if (maxWidth > 0 && w > maxWidth && ratio > 1) {
          //if ratio > 1, that means it's horizontal image, so should set the image's width to the maxWidth
          width = maxWidth;
          height = maxWidth / ratio;
        } else if (ratio <= 1 && maxHeight > 0 && h > maxHeight) {
          //if ratio <= 1, that means it's vertical image, so should set the image's height to the maxHeight
          width = ratio * maxHeight;
          height = maxHeight;
        }
        setSize({
          width,
          height,
        });
      });
    }
  }, [source.uri, flattenedStyles.width, flattenedStyles.height]);
  if (source.uri) {
    return (
      <Image source={{uri: source.uri}} style={[style, size]} {...restProps} />
    );
  } else {
    return <Image style={[style, size]} {...restProps} />;
  }
}

export default AutoResizeImage;
