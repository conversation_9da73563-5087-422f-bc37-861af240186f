import {AcquireNativeObjectAsync, Tools} from '@babylonjs/core';
import {Main} from './main';
import {events} from './util/events';
import {scene} from './util/scene';

export function IViewer(t, callBack) {
  this.events = new events();
  t.engine && (this.scene = new scene(t.engine, true));

  Object.setPrototypeOf(this, {
    ...Object.getPrototypeOf(this.events),
    init: () => {
      const loadingTemplate = `<img class="loading" src="http://localhost:4200/assets/viewer-library/assets/loader.svg">`;
      const loadingSrc = `/assets/loader.svg`;
      this.options = Object.assign(
        {},
        {
          element: 'viewer_canvas',
          loadingTemplate: loadingTemplate,
          loadingSrc: loadingSrc,
          size: {
            width: window.innerWidth,
            height: window.innerHeight,
          },
          apikey: '',
          secretkey: null,
          singleHole: true,
          holeNumber: 5,
          courseID: '',
          navigationMode: true,
          mode: t.mode ? t.mode : '2D',
          style: 'a',
          substyle: 'v2',
          hostOrigin: 'https://api-connect.igolf.com/',
          nativePlatform: t.platform,
        },
        t,
      );
      (this.options.platform === 'ios' ||
        this.options.platform === 'android') &&
        (this.options.platform = 'native');
      return this.options;
    },
    loadFont: async e => {
      if (e === 'ios' || e === 'android') {
        AcquireNativeObjectAsync().then(native => {
          Tools.LoadFileAsync(
            'https://viewer-library-ui.dedicateddevelopers.us' +
              '/assets/fonts/BebasNeue-Regular.ttf',
            true,
          ).then(async data => {
            if (data instanceof ArrayBuffer) {
              native && (await native.Canvas.loadTTFAsync('Bebas Neue', data));
            }
          });
        });
      }
    },
    nativeInitialized: e => {
      this.scene.executeWhenReady(e => {
        e._materialsRenderTargets.data = [];
        e._meshesForIntersections.data = [];
        e._processedMaterials.data = [];
        e._registeredForLateAnimationBindings.data = [];
        e._renderTargets.data = [];
        e._softwareSkinnedMeshes.data = [];
        e._toBeDisposed = [];
        e._activeMeshes.data = [];
        e._activeParticleSystems.data = [];
        e._activeSkeletons.data = [];
        this.scene = e;
        this.engine = this.scene.getEngine();
        this.scene.freezeActiveMeshes(true);
        this.main = new Main(this);
      });
    },
  });
  this.loadFont(t.platform).then(() => {
    (t.platform === 'ios' || t.platform === 'android') &&
      this.nativeInitialized(this.init());
  });

  return callBack && typeof callBack === 'function' ? callBack(this) : this;
}
