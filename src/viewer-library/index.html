<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="description" content="Free Web tutorials">
    <meta name="keywords" content="HTML, CSS, JavaScript">
    <meta name="author" content="<PERSON>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>viewer-library</title>
    <style>
        body{
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
    </style>
</head>
<script src="viewer.js"></script>

<body>
    <div id="viewer"></div>
    <script>
        IGolf.IViewer.init({
            element: 'viewer', apikey: 'PRK9ptMjb9mHMsI', //'fWBvf_X4zrBpcdc',
            secretkey: 'cU6vi5sWrVng4wxOJmu18ZcclI2oHK', //'-LX1JsjX-LbiLPu0E1yKjUKGiUgXlO',
            courseID: 'fl7NtrEMFEK8'
        })
        console.log()
    </script>
</body>

</html>
