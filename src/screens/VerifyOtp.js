import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Keyboard,
  TouchableOpacity,
  Platform,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useDispatch, useSelector} from 'react-redux';
import moment from 'moment';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {CommonActions} from '@react-navigation/native';

import Text from '../components/Text';
import TextInput from '../components/TextInput';
import LinearGradient from 'react-native-linear-gradient';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {addCurrentUser} from '../reducers/user';
import {
  login,
  sendOTP,
  verifyOTP,
  getUser,
  updateUser,
} from '../requests/accounts';
import {updatePermissions} from '../reducers/app';

import appStyles from '../styles/global';
import {showToast} from '../utils/toast';
import {CustomToast} from '../utils/CustomToast';
import {getAuth0Client} from '../utils/auth0';
import {GA_logEvent, GA_logScreenViewV2} from '../utils/googleAnalytics';
import {t} from 'i18next';
import {refreshConfigureFeatures} from '../utils/configureFeatures';
import {
  API_TOKEN_EXPIRATION_TIME,
  AUTH0_TOKEN_EXPIRATION_TIME,
  checkMainCountry,
  GA_EVENT_NAME,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_TYPES,
} from 'utils/constant';
import LogoTMBlack from 'assets/imgs/onBoarding/LogoTMBlack.svg';
import TaylorMadeBlack from 'assets/imgs/onBoarding/TaylorMadeBlack.svg';
import BackIcon from '../../assets/imgs/onBoarding/back.svg';
import {
  getDeviceCountry,
  isCanadaMarket,
  setCountry,
  setLanguage,
  setNewUser,
} from 'utils/commonVariable';
import {getWHSGolferProfile} from 'requests/whs';
import {updateWHSHandicapIndex} from 'reducers/whs';
import {getGolferProfile} from 'requests/ghin';
import {updateGHINHandicapIndex} from 'reducers/ghin';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';

let isNewAccount = false;
const smallScreenIphone = hp(100) < 780 ? true : false;
const VerifyOtp = ({navigation, addCurrentUser, updatePermissions, route}) => {
  const [loading, setLoading] = useState(false);
  const [otp, setOtp] = useState('');
  const [loadingFull, setLoadingFull] = useState(false);
  const [loadingResend, setLoadingResend] = useState(false);
  const [isValidOtp, setIsValidOtp] = useState(false);
  const [showToastCustom, setShowToastCustom] = useState(false);
  const dispatch = useDispatch();
  const email = route?.params?.email;
  const isSignUp = route?.params?.isSignUp || false;
  const isFromInitial = route?.params?.isFromInitial || false;
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);

  const pressReSendOTP = async () => {
    setLoadingResend(true);
    try {
      await sendOTP(email);
      setLoadingResend(false);
    } catch (error) {
      setLoadingResend(false);
      setShowToastCustom(true);
    }
  };

  const pressVerifyOtp = async () => {
    setLoading(true);
    // Close Keyboard
    Keyboard.dismiss();

    if (!otp) {
      setLoading(false);
      return showToast({
        type: 'error',
        message: t('Invalid_otp'),
      });
    }

    try {
      const user = await verifyOTP(email, otp);
      if (user && user.success) {
        setLoading(false);
        isNewAccount = true;
        openAuth0();
      }
      return;
    } catch (error) {
      setIsValidOtp(true);
      setLoading(false);
    }
  };

  const GA_signInFromOnBoarding = async (status, errorMessage) => {
    try {
      await GA_logScreenViewV2(
        PAGE_NAME.LOGIN,
        PAGE_CATEGORY.LOGIN,
        SCREEN_TYPES.LOGIN,
        SCREEN_TYPES.LOGIN,
      );
      if (status === 'success') {
        await GA_logEvent(GA_EVENT_NAME.LOGIN, {
          authentication_status: status,
          signup_method: 'email',
          page_type: SCREEN_TYPES.LOGIN,
          page_category: PAGE_CATEGORY.LOGIN,
          page_name: PAGE_NAME.LOGIN,
          screen_type: SCREEN_TYPES.LOGIN,
        });
      } else {
        await GA_logEvent(GA_EVENT_NAME.LOGIN, {
          authentication_status: status,
          signup_method: 'email',
          error_message: errorMessage,
          page_type: SCREEN_TYPES.LOGIN,
          page_category: PAGE_CATEGORY.LOGIN,
          page_name: PAGE_NAME.LOGIN,
          screen_type: SCREEN_TYPES.LOGIN,
        });
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const openAuth0 = async () => {
    const auth0 = await getAuth0Client();
    setLoading(true);
    // Open browser and navigate to Auth0 Universal Login page
    auth0.webAuth
      .authorize({scope: 'openid profile email offline_access'})
      .then(credentials => userLogin(credentials))
      .catch(error => {
        setLoading(false);
      });
  };

  const clearWebAuthSession = async () => {
    const auth0 = await getAuth0Client();
    setTimeout(() => {
      auth0.webAuth.clearSession();
    }, 1000);
  };

  const userLogin = async credentials => {
    try {
      setLoadingFull(true);
      // Make request to login endpoint to get user data
      const user = await login(credentials.accessToken);
      if (user && !user.emailVerified && checkMainCountry(user?.userCountry)) {
        setLoadingFull(false);
        sendOTP(user?.email).catch(() => {});
        navigation.navigate('VerifyOtp', {
          screen: 'VerifyOtp',
          params: {email: user.email},
        });
        return;
      }

      // Save current user's email for jwt renewal
      await AsyncStorage.setItem('userEmail', user.email);
      // Update permissions in redux
      updatePermissions({
        myTMSubscriptionLevel: user?.myTMSubscriptionLevel,
        myTMPermission: user?.myTMPermission,
        subscriptionService: user?.subscriptionService,
        isTrialSubscription: user?.isTrialSubscription,
      });
      setLanguage(user?.language || 'en');
      // Set auth0 tokens to AsyncStorage
      await AsyncStorage.setItem('auth0AccessToken', credentials.accessToken);
      await AsyncStorage.setItem('auth0RefreshToken', credentials.refreshToken);
      await AsyncStorage.setItem(
        'auth0ExpiresIn',
        moment().add(AUTH0_TOKEN_EXPIRATION_TIME, 'm').toISOString(),
      );
      await AsyncStorage.setItem(
        'refreshApp',
        moment().add(29, 'days').toISOString(),
      );
      // Set TM tokens to AsyncStorage
      await AsyncStorage.setItem('idToken', user.idToken);
      await AsyncStorage.setItem('refreshToken', user.refreshToken);
      await AsyncStorage.setItem(
        'expiresIn',
        moment().add(API_TOKEN_EXPIRATION_TIME, 'm').toISOString(),
      );

      setCountry(user?.userCountry);
      addCurrentUser({...user});
      getUser();
      await refreshConfigureFeatures(dispatch, user, showHideFeatures);
      if (isCanadaMarket()) {
        try {
          // Add user to redux
          const golferProfile = await getWHSGolferProfile();

          if (golferProfile) {
            //Show handicap Entered
            updateWHSHandicapIndex(golferProfile);
            addCurrentUser({
              golfCanadaCardId: golferProfile?.golfCanadaCardId,
            });
          }
        } catch (error) {
          if (error?.response?.data?.golfCanadaCardId) {
            updateWHSHandicapIndex({
              golfCanadaCardId: error?.response?.data.golfCanadaCardId,
            });
          }
        }
      } else {
        try {
          // Add user to redux
          const golferProfile = await getGolferProfile();

          if (golferProfile) {
            //Show handicap Entered
            updateGHINHandicapIndex(golferProfile);
            addCurrentUser({ghinId: golferProfile.ghin});
          }
        } catch (error) {
          if (error?.response?.data?.ghin_id) {
            updateGHINHandicapIndex({
              ghin: error?.response?.data.ghin_id,
              email: error?.response?.data?.ghinEmail,
            });
          }
          //Show handicap Pendding
        }
      }
      GA_signInFromOnBoarding('success', 'N/A');
      if (isNewAccount) {
        setNewUser(true);
        navigation.replace('Introduce');
      } else {
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{name: 'App', screen: 'Home'}],
          }),
        );
      }
      // Stop loading state and navigate to next screen
      setLoading(false);
    } catch (error) {
      setLoading(false);
      if (error?.response?.data?.internalErrorCode === 'UNAUTHORIZED') {
        showToast({
          type: 'error',
          message: error?.response?.data?.errorMessage,
        });
        GA_signInFromOnBoarding('failure', error?.response?.data?.errorMessage);
        clearWebAuthSession();
      } else {
        showToast({
          type: 'error',
          message: t('login.confirmation.backup_on_the_first_tee'),
          subText: t('login.confirmation.your_group_will_be_teeing_off_soon'),
        });
        GA_signInFromOnBoarding(
          'failure',
          t('login.confirmation.backup_on_the_first_tee'),
        );
      }
    }
  };

  const renderLoadingProcess = () => {
    return loading ? (
      <ActivityIndicator
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 1000,
        }}
        size={'large'}
        color={'black'}
      />
    ) : null;
  };

  useEffect(() => {
    if (otp && otp.length >= 6) {
      pressVerifyOtp();
    }
  }, [otp]);

  return loadingFull ? (
    <SafeAreaView
      style={[
        appStyles.flex,
        appStyles.hCenter,
        {paddingTop: hp(33), backgroundColor: '#fff'},
      ]}
    >
      <LinearGradient
        start={{x: 1, y: 0.1}}
        end={{x: 1, y: 1}}
        colors={['#fff', 'rgba(0, 0, 0, 1)']}
        style={[
          {
            width: wp('100%'),
            top: hp(33),
            bottom: 0,
            left: 0,
            opacity: 0.25,
            position: 'absolute',
          },
        ]}
      />
      <View style={[{marginBottom: 40}]}>
        <TaylorMadeBlack />
      </View>
      <View>
        <ActivityIndicator
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
          size={'large'}
          color={'black'}
        />
      </View>
    </SafeAreaView>
  ) : (
    <>
      <SafeAreaView
        style={[
          appStyles.flex,
          appStyles.hCenter,
          {
            marginTop:
              Platform.OS === 'android' ? '3%' : smallScreenIphone ? 10 : 0,
          },
        ]}
      >
        {showToastCustom && (
          <CustomToast
            message={t('otp.resend.error')}
            onHide={() => setShowToastCustom(false)}
          />
        )}
        <FocusAwareStatusBar barStyle={'light-content'} />
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
          <View
            style={{
              width: wp(91.8),
              minHeight:
                Platform.OS === 'android' || smallScreenIphone
                  ? hp(96)
                  : hp(90),
            }}
          >
            {isFromInitial ? (
              <View
                style={[styles.viewBackIcon, {backgroundColor: 'transparent'}]}
              />
            ) : (
              <View
                style={[
                  appStyles.row,
                  appStyles.hCenter,
                  appStyles.vCenter,
                  styles.viewBackIcon,
                ]}
              >
                <TouchableOpacity
                  style={{padding: 8}}
                  onPress={() => {
                    if (!isSignUp) {
                      clearWebAuthSession();
                    }
                    navigation.goBack();
                  }}
                >
                  <BackIcon />
                </TouchableOpacity>
              </View>
            )}
            <View style={{alignItems: 'center', marginTop: -10}}>
              <LogoTMBlack />
            </View>

            <View>
              <View
                style={[appStyles.hCenter, {marginBottom: 27, marginTop: 64}]}
              >
                <Text size={16} style={{lineHeight: 16.5}}>
                  {`${t('otp.please_enter_the_code')} `}
                </Text>
                <Text size={16} weight={'700'} style={{color: '#333333'}}>
                  {email}
                </Text>
              </View>
              <TextInput
                style={{
                  ...styles.viewTextInput,
                  borderColor: isValidOtp ? '#FF0000' : '#fff',
                }}
                stylesInput={{
                  ...styles.stylesInput,
                  paddingBottom: otp ? 7 : 0,
                  marginLeft: otp && Platform.OS === 'ios' ? 0 : -3,
                }}
                placeholder={t('otp.input')}
                onChangeText={setOtp}
                autoCapitalize="none"
                keyboardType="numeric"
                autoCorrect={false}
                value={otp}
                isDoubleText={true}
              />
              {isValidOtp && (
                <View style={[appStyles.hCenter]}>
                  <Text size={12} style={{lineHeight: 16.5, color: '#FF0000'}}>
                    {`${t('otp.error.invalid')} `}
                  </Text>
                </View>
              )}

              <TouchableOpacity
                style={[appStyles.hCenter, {marginTop: 24}]}
                onPress={pressReSendOTP}
              >
                <Text
                  style={[
                    appStyles.textCenter,
                    appStyles.hCenter,
                    appStyles.uppercase,
                    {letterSpacing: 1.62},
                  ]}
                  Din79Font
                  size={12}
                  weight={'700'}
                  black
                >
                  {loadingResend ? (
                    <ActivityIndicator color={'black'} />
                  ) : (
                    `${t('otp.resend')} `
                  )}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {renderLoadingProcess()}
        </KeyboardAwareScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  viewBackIcon: {
    backgroundColor: '#FFFFFF80',
    width: 24,
    height: 24,
    borderRadius: 24,
  },
  viewTextInput: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginBottom: 4,
    backgroundColor: '#fff',
    height: 56,
    borderRadius: 24,
    paddingHorizontal: 16,
  },
  stylesInput: {
    fontSize: 16,
    paddingTop: Platform.OS === 'ios' ? 0 : 2,
    fontWeight: '400',
    color: '#000',
    width: wp('85%'),
  },
});

const mapDispatchToProps = {
  addCurrentUser,
  updatePermissions,
  updateGHINHandicapIndex,
};

export default connect(null, mapDispatchToProps)(VerifyOtp);
