/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect} from 'react';
import {
  ScrollView,
  View,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {moderateScale} from 'react-native-size-matters';
import {connect, useSelector, useDispatch} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';

import PlanCoachMessage from 'components/PlanCoachMessage';
import Text from 'components/Text';
import ProgressCircle from 'components/ProgressCircle';

import {setLoadingCoachingPlan} from 'reducers/plans';

import appStyles from 'styles/global';
import {LIGHT_GREY, GREEN} from 'config';
import {showToast} from 'utils/toast';
import {getSwingProfile, getSwingProfileAndRoadMap} from 'utils/plans';
import {swingDefinitions} from '../../json/swing-index';
import {t} from 'i18next';
import BackButton from 'components/BackButton';

const SwingBarData = ({data, swingIndex}) => {
  // Make clone of array
  const swingSubElements = [...data.swingSubElements];
  const contentStrings = useSelector(state => state.plans.planContentStrings);

  const showDefinition = () => {
    const definition = swingDefinitions.find(
      swingDefinition => swingDefinition.name === data.name,
    );
    return Alert.alert(definition.name, definition.description);
  };

  const showFault = stat => {
    // Set title for alert
    const faultTitle = `Fault in ${data.name} > ${stat.name}`;
    // Find the swing index failed copy from the content strings state
    let faultDescription = contentStrings.swing_potential_index_failed;
    // Replace names with swing element and swing sub element
    faultDescription = faultDescription.replace(
      '{{swingElement.name}}',
      data.name,
    );
    faultDescription = faultDescription.replace(
      '{{swingSubElement.name}}',
      stat.name,
    );
    return Alert.alert(faultTitle, faultDescription);
  };

  return (
    <View style={appStyles.mBSm}>
      <View
        style={[
          appStyles.row,
          appStyles.hCenter,
          appStyles.mLSm,
          appStyles.mTXs,
          appStyles.mBXxs,
        ]}
      >
        <Text
          DINbold
          style={[appStyles.mRXs, appStyles.mdms, {marginBottom: -5}]}
        >
          {data?.name?.toUpperCase()}
        </Text>
        <TouchableOpacity onPress={showDefinition}>
          <Icon
            name="information-circle-outline"
            size={moderateScale(24)}
            color={LIGHT_GREY}
          />
        </TouchableOpacity>
      </View>
      {swingIndex ? (
        <View
          style={[appStyles.whiteBg, appStyles.borderRadius, appStyles.pSm]}
        >
          {swingSubElements
            .sort((a, b) => a.id - b.id)
            .map((stat, i) => {
              const swingIndexData = swingIndex?.swingSubElements?.find(
                element => element?.swingSubElementId === stat?.id,
              );
              const barColor = !swingIndexData?.failed ? '#34C759' : 'red';
              return (
                <View
                  key={stat?.name}
                  style={
                    i !== swingSubElements.length - 1 ? appStyles.mBSm : {}
                  }
                >
                  <View style={[appStyles.row, appStyles.hCenter]}>
                    <Text style={[appStyles.grey, appStyles.mBXxs]}>
                      {stat?.name}
                    </Text>
                    {swingIndexData.boosted ? (
                      <TouchableOpacity onPress={() => showFault(stat)}>
                        <Icon
                          style={appStyles.mLXs}
                          name="md-chevron-up"
                          size={moderateScale(24)}
                          color={GREEN}
                        />
                      </TouchableOpacity>
                    ) : null}
                  </View>
                  <View
                    style={[
                      appStyles.vCenter,
                      swingIndexData?.current !== 1
                        ? appStyles.pRSm
                        : appStyles.pRXs,
                      {
                        backgroundColor: barColor,
                        borderRadius: 5,
                        width: `${
                          swingIndexData?.current === 1
                            ? 1.5 * 10
                            : swingIndexData?.current * 10
                        }%`,
                        height: moderateScale(28),
                      },
                    ]}
                  >
                    <Text style={[appStyles.white, appStyles.textRight]}>
                      {swingIndexData?.current} / 10
                    </Text>
                  </View>
                </View>
              );
            })}
        </View>
      ) : null}
    </View>
  );
};

const PlanSwingProfile = ({
  planContentBatch,
  planCoach,
  swingIndex,
  navigation,
  navigation: {navigate},
}) => {
  const hasSwingIndex = Object.keys(swingIndex).length ? true : false;
  const [filter, setFilter] = useState('Your Index');
  const [refreshing, setRefreshing] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <BackButton
          color="white"
          onPress={() => {
            navigation.goBack();
          }}
        />
      ),
    });
  }, []);

  useEffect(() => {
    (async () => {
      try {
        dispatch(setLoadingCoachingPlan(true));
        await getSwingProfile(dispatch);
        dispatch(setLoadingCoachingPlan(false));
      } catch (error) {
        dispatch(setLoadingCoachingPlan(false));
        showToast({
          type: 'error',
          message: t('plan.relevant_lessons.hit_a_few_warm_up_shots'),
          subText: t('plan.swing_profile.have_your_swing_profile_momentarily'),
        });
      }
    })();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    setRefreshing(false);
    getSwingProfileAndRoadMap(dispatch);
  };

  return (
    <SafeAreaView style={[appStyles.flex]} edges={['left', 'right']}>
      <ScrollView
        style={[appStyles.flex]}
        contentContainerStyle={{
          paddingBottom: 0,
        }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <PlanCoachMessage type="swingIndex" swingIndex={swingIndex} />

        {hasSwingIndex ? (
          <>
            <View
              style={[
                appStyles.whiteBg,
                appStyles.pTSm,
                appStyles.pHSm,
                appStyles.pBMd,
                appStyles.mBSm,
                appStyles.hCenter,
              ]}
            >
              {/* <PillTabs
              options={['Your Index', 'Advanced Index']}
              filter={filter}
              setFilter={setFilter}
            />
            <Text
              style={[
                appStyles.xs,
                appStyles.grey,
                appStyles.textCenter,
                appStyles.mBMd,
              ]}
            >
              Your Swing Index to achieve your current goal (Break 100)
            </Text> */}

              <ProgressCircle
                progress={
                  (swingIndex?.currentAvg / swingIndex?.potentialAvg) * 100
                }
                value={swingIndex?.currentAvg}
                potential={swingIndex?.potentialAvg}
              />
            </View>

            <View style={[appStyles.pHSm]}>
              {planContentBatch?.swingElement?.map(data => {
                return (
                  <SwingBarData
                    key={data.id}
                    data={data}
                    swingIndex={swingIndex}
                  />
                );
              })}
            </View>
          </>
        ) : null}
      </ScrollView>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  planContentBatch: state.plans.planContentBatch,
  planCoach: state.plans.planCoach,
  swingIndex: state.plans.planSwingProfile,
});

export default connect(mapStateToProps, null)(PlanSwingProfile);
