import React, {useState, useRef, useEffect} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {moderateScale} from 'react-native-size-matters';
import Carousel from 'react-native-snap-carousel';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import moment from 'moment';
import Video from 'react-native-video';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';

import appStyles from 'styles/global';
import {GREY} from 'config';
import FastImage from 'react-native-fast-image/src';
import {t} from 'i18next';
import BackButton from 'components/BackButton';
import {getCountryDateWithFormat} from 'utils/countries';

const PlanAnnotations = ({route, navigation}) => {
  const {navigate} = navigation;
  const annotations = route.params?.annotations;
  const annotationId = route.params?.annotationId;
  const [currentAnnotation, setCurrentAnnotation] = useState(
    annotations.find(annotation => annotation.annotationId === annotationId),
  );
  const [paused, setPaused] = useState(true);
  const [ended, setEnded] = useState(true);
  const carouselRef = useRef(null);
  const audioRef = useRef(null);
  const [hasListenedToVideo, setHasListenedToVideo] = useState(false);

  useEffect(() => {
    if (carouselRef) {
      // Set carousel to the selected annotation
      setTimeout(() => {
        carouselRef.current.snapToItem(annotations.indexOf(currentAnnotation));
      }, 500);
    }
  }, [carouselRef]);

  useEffect(() => {
    if (paused === false) {
      setHasListenedToVideo(true);
    }
  }, [paused]);

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <BackButton
          color="white"
          onPress={() => {
            navigation.goBack();
          }}
        />
      ),
      headerTitle: route.params?.lessonTitle,
    });
  }, [hasListenedToVideo]);

  const renderItem = ({item}) => {
    return (
      <View key={item.annotationId}>
        <FastImage
          source={{uri: item.image, cache: FastImage.cacheControl.web}}
          style={[appStyles.fullWidth, {height: hp('65%')}]}
          resizeMode="cover"
        />
        <Text
          style={[
            appStyles.xs,
            appStyles.grey,
            appStyles.textCenter,
            appStyles.mTSm,
          ]}
        >
          {`${t('plan.annotations.from_your_swingShot')} ${moment(
            item?.createdDate,
          ).format(getCountryDateWithFormat('M/D/YY'))}`}
        </Text>
        <Text style={[appStyles.xs, appStyles.grey, appStyles.textCenter]}>
          {`${t('plan.annotations.annotated_by')} ${item.annotatedByUser}`}
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView
      style={[appStyles.whiteBg]}
      edges={['bottom', 'right', 'left']}
    >
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View
        style={[
          appStyles.row,
          appStyles.hCenter,
          appStyles.vCenter,
          appStyles.spaceEvenly,
          {marginVertical: '1%'},
        ]}
      >
        <TouchableOpacity
          onPress={() => {
            if (ended) {
              setEnded(false);
              audioRef.current?.seek(0);
            }
            setPaused(!paused);
          }}
        >
          <Icon
            name={paused ? 'play-circle' : 'pause-circle'}
            size={moderateScale(30)}
          />
        </TouchableOpacity>
        <TouchableOpacity disabled={true}>
          <Icon
            name="text-box-multiple"
            size={moderateScale(26)}
            color={GREY}
          />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            navigate('Video', {
              video: {
                id: currentAnnotation?.videoHash,
                host: 'wistia',
                annotation: true,
                origin: route?.params?.origin,
                lessonVideoTitle: route?.params?.lessonVideoTitle,
                lessonType: route?.params?.dataInfotrust?.lessonType,
                roadmapStep: route?.params?.dataInfotrust?.roadmapStep,
                lessonName: route?.params?.dataInfotrust?.lessonName,
                swingAnalysisName: route?.params?.swingAnalysisName,
              },
            });
            setPaused(true);
          }}
        >
          <Icon name="filmstrip" size={moderateScale(30)} />
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={[
          {position: 'absolute', top: hp('35%'), zIndex: 1},
          appStyles.pVMd,
        ]}
        onPress={() => {
          carouselRef.current.snapToItem(
            annotations.indexOf(currentAnnotation) === 0
              ? annotations.length - 1
              : annotations.indexOf(currentAnnotation) - 1,
          );
          setPaused(true);
        }}
      >
        <Icon name="chevron-left" size={moderateScale(30)} />
      </TouchableOpacity>
      <Carousel
        ref={carouselRef}
        data={annotations}
        renderItem={renderItem}
        sliderWidth={wp('100%')}
        itemWidth={wp('100%')}
        inactiveSlideScale={1}
        inactiveSlideOpacity={1}
        enableMomentum={true}
        decelerationRate={0.9}
        loop={true}
        onSnapToItem={index => {
          setCurrentAnnotation(annotations[index]);
          setPaused(true);
        }}
      />
      <TouchableOpacity
        style={[
          {position: 'absolute', right: 0, top: hp('35%'), zIndex: 1},
          appStyles.pVMd,
        ]}
        onPress={() => {
          carouselRef?.current?.snapToItem(
            annotations.indexOf(currentAnnotation) === annotations?.length - 1
              ? 0
              : annotations?.indexOf(currentAnnotation) + 1,
          );
          setPaused(true);
        }}
      >
        <Icon name="chevron-right" size={moderateScale(30)} />
      </TouchableOpacity>

      <View
        style={[
          appStyles.row,
          appStyles.pBMd,
          appStyles.vCenter,
          {marginTop: '5%'},
        ]}
      >
        {annotations?.map((x, i) => {
          return (
            <View
              key={i}
              style={[
                i !== annotations?.length - 1 ? appStyles.mRXs : {},
                {
                  position: 'relative',
                  overflow: 'hidden',
                },
              ]}
            >
              <View
                style={[
                  appStyles.indicator,
                  appStyles.blackBg,
                  {
                    opacity:
                      annotations.indexOf(currentAnnotation) === i ? 1 : 0.3,
                  },
                ]}
              />
            </View>
          );
        })}
      </View>
      <Video
        ref={audioRef}
        source={{uri: currentAnnotation?.voice}}
        paused={paused}
        onEnd={() => {
          setEnded(true);
          setPaused(true);
        }}
        audioOnly
        ignoreSilentSwitch="ignore"
      />
    </SafeAreaView>
  );
};

export default PlanAnnotations;
