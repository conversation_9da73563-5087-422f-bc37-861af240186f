import React, {useState} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';
import {convertYardsMeters} from 'utils/convert';

import appStyles from 'styles/global';
import {t} from 'i18next';

const ClubIron = ({navigation, updateClubRecommender}) => {
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const user = useSelector(state => state?.user);
  const userDistanceUnit = user?.measurementUnits && user?.measurementUnits.toLowerCase() === 'meters' ? 'meters'  : 'yards';
  const [ironCarry, setIronCarry] = useState(
    clubRecommender?.carryYour7Iron
      ? parseInt(clubRecommender?.carryYour7Iron)
      : 145,
  );

  const goNext = value => {
    updateClubRecommender({carryYour7Iron: value ? value?.toString() : null});
    navigation.navigate('ClubDriverBallFlight');
  };

  // Helper function to get the display value based on user's distance unit
  const getDisplayValue = (yardsValue) => {
    if (userDistanceUnit === 'meters') {
      return convertYardsMeters('yards', yardsValue);
    }
    return yardsValue;
  };

  // Helper function to get the unit label
  const getUnitLabel = () => {
    if (userDistanceUnit === 'meters') {
      return t('club.iron.meters');
    }
    return t('club.iron.yards');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              club.how_far_do_you_carry_your_7_iron
            </Text>
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={100}>
            <Text
              style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
              DINbold
            >
              {getDisplayValue(ironCarry)} {getUnitLabel()}
            </Text>
            <Slider
              style={[{width: '100%'}]}
              minimumValue={45}
              maximumValue={220}
              minimumTrackTintColor="#fff"
              maximumTrackTintColor="#fff"
              onValueChange={value => setIronCarry(parseInt(value.toFixed(0)))}
              value={ironCarry}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={200}>
          <Button
            text="common.next"
            backgroundColor="white"
            onPress={() => goNext(ironCarry)}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {updateClubRecommender};

export default connect(null, mapDispatchToProps)(ClubIron);
