import React, {useState, useEffect} from 'react';
import {View, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';

const drawImg = require('assets/imgs/Draw.png');
const fadeImg = require('assets/imgs/Fade.png');
const hookImg = require('assets/imgs/Hook.png');
const sliceImg = require('assets/imgs/Slice.png');
const straightImg = require('assets/imgs/Straight.png');

const activeImage = {drawImg, fadeImg, hookImg, sliceImg, straightImg};

const ClubDriverShotShape = ({navigation, updateClubRecommender}) => {
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [shotShape, setShotShape] = useState(
    clubRecommender?.typicalDriverShotShape || 'straight',
  );
  const [shotShapeImg, setShotShapeImg] = useState(
    clubRecommender?.typicalDriverShotShape
      ? activeImage[`${clubRecommender?.typicalDriverShotShape}Img`]
      : straightImg,
  );
  const validated = !isEmpty(shotShape || '');

  useEffect(() => {
    updateClubRecommender({typicalDriverShotShape: shotShape});
  }, [shotShape]);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return shotShape?.includes(type) ? 'white' : GREY;
      case 'border':
        return shotShape?.includes(type) ? GREEN : GREY;
      case 'background':
        return shotShape?.includes(type) ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const setShotShapeInfo = value => {
    setShotShape(value);
    setShotShapeImg(activeImage[`${value}Img`]);
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View
        style={[
          appStyles.flex,
          appStyles.mTSm,
          appStyles.pHMd,
          appStyles.spaceBetween,
        ]}
      >
        <Animatable.View animation="fadeInUp">
          <Text
            style={[
              appStyles.sm,
              appStyles.white,
              appStyles.textCenter,
              appStyles.mBMd,
            ]}
          >
            club.driver.shot.shape.what_is_your_typical_shot_shape
          </Text>
        </Animatable.View>
        <Animatable.View animation="fadeIn" delay={100}>
          <Image
            style={[
              appStyles.alignCenter,
              appStyles.mBXxs,
              appStyles.responsiveImageBallShape,
            ]}
            source={shotShapeImg}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={200}>
          <View style={(appStyles.flex, appStyles.wrap)}>
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.avoid.supporting_copy.draw"
              textColor={getButtonActiveColor('draw', 'text')}
              borderColor={getButtonActiveColor('draw', 'border')}
              backgroundColor={getButtonActiveColor('draw', 'background')}
              onPress={() => setShotShapeInfo('draw')}
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.shot_shape.supporting_copy.straight"
              textColor={getButtonActiveColor('straight', 'text')}
              borderColor={getButtonActiveColor('straight', 'border')}
              backgroundColor={getButtonActiveColor('straight', 'background')}
              onPress={() => setShotShapeInfo('straight')}
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.avoid.supporting_copy.fade"
              textColor={getButtonActiveColor('fade', 'text')}
              borderColor={getButtonActiveColor('fade', 'border')}
              backgroundColor={getButtonActiveColor('fade', 'background')}
              onPress={() => setShotShapeInfo('fade')}
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.avoid.supporting_copy.hook"
              textColor={getButtonActiveColor('hook', 'text')}
              borderColor={getButtonActiveColor('hook', 'border')}
              backgroundColor={getButtonActiveColor('hook', 'background')}
              onPress={() => setShotShapeInfo('hook')}
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.avoid.supporting_copy.slice"
              textColor={getButtonActiveColor('slice', 'text')}
              borderColor={getButtonActiveColor('slice', 'border')}
              backgroundColor={getButtonActiveColor('slice', 'background')}
              onPress={() => setShotShapeInfo('slice')}
            />
          </View>
        </Animatable.View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="common.next"
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated}
            onPress={() => navigation.navigate('ClubDesiredShotShape')}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {updateClubRecommender};

export default connect(null, mapDispatchToProps)(ClubDriverShotShape);
