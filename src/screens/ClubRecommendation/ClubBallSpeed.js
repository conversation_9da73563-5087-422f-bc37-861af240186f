import React, {useState} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {convertToPointFiveIncrement} from 'utils/convert';

const ClubBallSpeed = ({
  navigation,
  updateClubRecommender,
  clubLaunchMonitor,
}) => {
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [ballSpeed, setBallSpeed] = useState(
    clubRecommender?.ballSpeed
      ? parseFloat(clubRecommender?.ballSpeed)
      : clubLaunchMonitor?.ballMphModified
      ? convertToPointFiveIncrement(
          parseFloat(clubLaunchMonitor?.ballMphModified),
          60,
          200,
        )
      : 130,
  );

  const goNext = value => {
    updateClubRecommender({
      ballSpeed: value ? value?.toString() : null,
    });
    navigation.navigate('ClubLaunchAngle');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              club.ball.speed.what_is_your_ballspeed
            </Text>
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={100}>
            <View style={{flexDirection: 'row', justifyContent: 'center'}}>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                {`${ballSpeed} `}
              </Text>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                club.ball.speed.mph
              </Text>
            </View>
            <Slider
              style={[{width: '100%'}]}
              minimumValue={60}
              maximumValue={200}
              step={0.5}
              minimumTrackTintColor="#fff"
              maximumTrackTintColor="#fff"
              onValueChange={value => setBallSpeed(value)}
              value={ballSpeed}
            />
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <TouchableOpacity onPress={() => goNext(null)}>
              <Text
                style={[
                  appStyles.xs,
                  appStyles.underlined,
                  appStyles.grey,
                  appStyles.textCenter,
                  appStyles.mTMd,
                ]}
              >
                common.skip_i_dont_konw
              </Text>
            </TouchableOpacity>
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="common.next"
            backgroundColor="white"
            onPress={() => goNext(ballSpeed)}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubBallSpeed);
