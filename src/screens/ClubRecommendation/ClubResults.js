import React, {useRef, useState, useEffect} from 'react';
import {View, TouchableOpacity, FlatList, ScrollView} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import BottomSheet from 'reanimated-bottom-sheet';
import {connect, useSelector, useDispatch} from 'react-redux';
import {moderateScale} from 'react-native-size-matters';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import ShaftItem from 'components/ShaftItem';
import Button from 'components/Button';

import appStyles from 'styles/global';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image/src';
import {refreshConfigureFeatures} from 'utils/configureFeatures';
import {showToast} from 'utils/toast';
import {openEcomWebview, prepareLink} from 'utils/shop';
import {getAuth0AccessToken} from 'utils/user';
import {WEBVIEW_PAGE_TYPE} from 'utils/constant';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const ClubResults = ({navigation, route, clubRecommender}) => {
  const user = useSelector(state => state.user);
  const launchFeaturesSelector = useSelector(
    state => state.app?.launchFeatures,
  );
  const clubResults = route?.params?.clubResults;
  const bottomSheetRef = useRef(null);
  const [selectedShaft, setSelectedShaft] = useState(
    clubResults?.shafts?.mwdata[0],
  );
  const [shaft, setShaft] = useState(clubResults?.shafts?.mwdata[0]);
  const dispatch = useDispatch();
  const [showToastError, setShowToastError] = useState(false);
  const {navigate} = navigation;
  const [isLoading, setLoading] = useState(false);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
  const {getEcomProductDetail} = useEcomProductDetail();

  useEffect(() => {
    if (showToastError) {
      showToast({
        type: 'error',
        message: t('club_config_session_invalid'),
      });
      setTimeout(() => {
        navigation.navigate('ClubDetails', {
          clubConfig: getClubConfig(),
          setShowToastError: setShowToastError,
        });
      }, 1000);
      setShowToastError(false);
    }
  }, [showToastError]);

  useEffect(() => {
    refreshConfigureFeatures(dispatch, user, showHideFeatures);
  }, []);

  const getClubModelType = () => {
    switch (clubRecommender.clubTypeId) {
      case 1:
        return 'TM_DRIVER_MODEL';
      case 'Fairway':
        return 'TM_FAIRWAY_MODEL';
      case 'Irons':
        return 'TM_IRONS_MODEL';
      case 'Rescue':
        return 'TM_RESCUE_MODEL';
      case 'Putter':
        return 'TM_PUTTER_MODEL';
      default:
        break;
    }
  };

  const getClubConfig = () => {
    return {
      name: clubResults?.headModelName,
      model: getClubModelType(),
      headModelImageUrl: clubResults?.headModelImageUrl,
      genderCode: clubRecommender?.gender === 'male' ? 'M' : 'W',
      handCode: clubRecommender?.handed === 'right' ? 'RH' : 'LH',
      headLoft: clubResults?.headLoft,
      shaftVendorName: shaft?.vendorName,
      shaftModelName: shaft?.modelName,
      shaftFlex: shaft?.flex,
      gripVendor: clubResults?.gripVendorName,
      gripModel: clubResults?.gripModelName,
    };
  };

  const goToShop = async () => {
    setLoading(true);
    const shopNowURL =
      'https://www.taylormadegolf.com/drivers/?utm_source=teamtaylormade&utm_medium=app&utm_campaign=mtw+dri+gen+app+tmapp+conv+gen+txt';
    const accessToken = await getAuth0AccessToken(dispatch);
    const linkUrl = await prepareLink(
      shopNowURL,
      accessToken,
      WEBVIEW_PAGE_TYPE.GRID_PAGE,
    );
    openEcomWebview(
      navigate,
      {
        uri: linkUrl,
        canGoBack: true,
        originUri: shopNowURL,
        imageUrl: clubResults?.headModelImageUrl,
        clickLocation: 'club-recommender-result',
        origin: 'ClubResults',
      },
      getEcomProductDetail,
    );
    setLoading(false);
  };

  return (
    <>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <SafeAreaView style={appStyles.flex} edges={['left', 'right', 'bottom']}>
        <ScrollView style={[appStyles.flex, appStyles.pHSm]}>
          <FastImage
            style={[
              appStyles.borderRadius,
              appStyles.mVSm,
              appStyles.fullWidth,
              {height: moderateScale(300)},
            ]}
            source={{
              uri: clubResults?.headModelImageUrl,
              cache: FastImage.cacheControl.web,
            }}
            resizeMode="cover"
          />
          <Text style={[appStyles.white, appStyles.bold, appStyles.mBXs]}>
            {clubResults?.headModelName}
            {`${clubResults?.headLoft ? ` ${clubResults?.headLoft}°` : ''}`}
          </Text>
          <Text style={[appStyles.white, appStyles.mBXs]}>
            {t('club.results.because_of_your_answer_we_recommend')}{' '}
            {clubResults?.headModelName}.
          </Text>
          <Text
            style={[appStyles.white, appStyles.mBMd]}
            params={{value: shaft?.modelName}}
          >
            club.result.recommend_the_shaft
          </Text>
          <ShaftItem
            item={shaft}
            shaft={shaft}
            selectedShaft={selectedShaft}
            setSelectedShaft={setSelectedShaft}
            disabled
            isSelected
          />
          <TouchableOpacity onPress={() => bottomSheetRef.current?.snapTo(0)}>
            <Text
              style={[
                appStyles.mTSm,
                appStyles.mBMd,
                appStyles.grey,
                appStyles.underlined,
                appStyles.textCenter,
              ]}
            >
              club.see_other_recommended_shafts
            </Text>
          </TouchableOpacity>
        </ScrollView>
        {user?.features?.CLUBPURCHASE ? (
          <View style={[appStyles.pHSm]}>
            <Button
              text="club.results.shop_now"
              backgroundColor="white"
              centered
              style={[{opacity: !launchFeaturesSelector?.EBSCONFIG ? 0.5 : 1}]}
              disabled={!launchFeaturesSelector?.EBSCONFIG || isLoading}
              DINbold
              onPress={goToShop}
              loading={isLoading}
            />
          </View>
        ) : null}
      </SafeAreaView>
      <BottomSheet
        ref={bottomSheetRef}
        style={appStyles.flex}
        snapPoints={['85%', 0]}
        borderRadius={20}
        initialSnap={1}
        renderContent={() => (
          <View
            style={[appStyles.pSm, appStyles.darkGreyBg, appStyles.fullHeight]}
          >
            <Text style={[appStyles.textCenter, appStyles.white]}>
              club.select_your_shaft
            </Text>
            <View style={[appStyles.row, appStyles.mTSm, appStyles.mBLg]}>
              <TouchableOpacity
                onPress={() => {
                  bottomSheetRef.current?.snapTo(1);
                  setSelectedShaft(shaft);
                }}
              >
                <Text style={[appStyles.blue]}>mybag.club_details.cancel</Text>
              </TouchableOpacity>
              <Text
                style={[
                  appStyles.mLAuto,
                  appStyles.mRAuto,
                  appStyles.white,
                  appStyles.bold,
                ]}
              >
                club.recommended_shafts
              </Text>
              <TouchableOpacity
                onPress={() => {
                  bottomSheetRef.current?.snapTo(1);
                  setShaft(selectedShaft);
                }}
              >
                <Text style={[appStyles.blue]}>common.done</Text>
              </TouchableOpacity>
            </View>

            <FlatList
              style={appStyles.flex}
              data={clubResults?.shafts?.mwdata}
              renderItem={({item}) => (
                <ShaftItem
                  item={item}
                  shaft={shaft}
                  selectedShaft={selectedShaft}
                  setSelectedShaft={setSelectedShaft}
                />
              )}
              keyExtractor={(item, index) => `${item.modelName}-${index}`}
            />
          </View>
        )}
      />
    </>
  );
};

const mapStateToProps = state => ({
  clubRecommender: state.clubRecommender.clubRecommender,
});

export default connect(mapStateToProps, null)(ClubResults);
