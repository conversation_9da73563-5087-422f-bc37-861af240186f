import React, {useState} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';
import DeviceInfo from 'react-native-device-info';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';
import {convertYardsMeters} from 'utils/convert';

import appStyles from 'styles/global';
import {t} from 'i18next';

import HigherPeakHeight from 'assets/imgs/club-higher-peak-height.svg';
import HighPeakHeight from 'assets/imgs/club-high-peak-height.svg';
import MidPeakHeight from 'assets/imgs/club-mid-peak-height.svg';
import LowPeakHeight from 'assets/imgs/club-low-peak-height.svg';
import LowerPeakHeight from 'assets/imgs/club-lower-peak-height.svg';

const midPeakImg = require('assets/imgs/club-mid-peak.png');
const highPeakImg = require('assets/imgs/club-high-peak.png');
const higherPeakImg = require('assets/imgs/club-higher-peak.png');
const lowPeakImg = require('assets/imgs/club-low-peak.png');
const lowerPeakImg = require('assets/imgs/club-lower-peak.png');

const ClubPeakHeight = ({
  navigation,
  updateClubRecommender,
  clubLaunchMonitor,
}) => {
  const isTablet = DeviceInfo.isTablet();
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const user = useSelector(state => state?.user);
  const userDistanceUnit = user?.measurementUnits && user?.measurementUnits.toLowerCase() === 'meters' ? 'meters'  : 'yards';
  const [peakHeight, setPeakHeight] = useState(
    clubRecommender?.peakHeight
      ? parseInt(clubRecommender?.peakHeight)
      : clubLaunchMonitor?.rdPeakHeightYards
      ? parseInt(clubLaunchMonitor?.rdPeakHeightYards?.toFixed(0))
      : 30,
  );

  const goNext = value => {
    updateClubRecommender({peakHeight: value ? value?.toString() : null});
    navigation.navigate('ClubHeadSpeed');
  };

  // Helper function to get the display value based on user's distance unit
  const getDisplayValue = (yardsValue) => {
    if (userDistanceUnit === 'meters') {
      return convertYardsMeters('yards', yardsValue);
    }
    return yardsValue;
  };

  // Helper function to get the unit label
  const getUnitLabel = () => {
    if (userDistanceUnit === 'meters') {
      return t('club.iron.meters');
    }
    return t('club.iron.yards');
  };

  const getPeakHeightImg = () => {
    if (peakHeight <= 12) {
      return isTablet ? lowerPeakImg : <LowerPeakHeight />;
    } else if (peakHeight >= 13 && peakHeight < 25) {
      return isTablet ? lowPeakImg : <LowPeakHeight />;
    } else if (peakHeight >= 25 && peakHeight < 37) {
      return isTablet ? midPeakImg : <MidPeakHeight />;
    } else if (peakHeight >= 37 && peakHeight < 49) {
      return isTablet ? highPeakImg : <HighPeakHeight />;
    } else {
      return isTablet ? higherPeakImg : <HigherPeakHeight />;
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTMd, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View
            animation="fadeIn"
            delay={100}
            style={appStyles.hCenter}
          >
            {isTablet ? (
              <Image
                style={[
                  appStyles.alignCenter,
                  appStyles.responsivePeak,
                  {marginTop: '-5%'},
                ]}
                source={getPeakHeightImg()}
              />
            ) : (
              <>{getPeakHeightImg()}</>
            )}
          </Animatable.View>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[
                appStyles.white,
                appStyles.textCenter,
                appStyles.mTMd,
                appStyles.mBMd,
              ]}
            >
              club.what_is_your_peak_height
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Text
              style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
              DINbold
            >
              {getDisplayValue(peakHeight)} {getUnitLabel()}
            </Text>
            <Slider
              style={[{width: '100%'}]}
              minimumValue={1}
              maximumValue={60}
              minimumTrackTintColor="#fff"
              maximumTrackTintColor="#fff"
              onValueChange={value => setPeakHeight(parseInt(value.toFixed(0)))}
              value={peakHeight}
            />
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={300}>
            <TouchableOpacity onPress={() => goNext(null)}>
              <Text
                style={[
                  appStyles.xs,
                  appStyles.underlined,
                  appStyles.grey,
                  appStyles.textCenter,
                  appStyles.mTMd,
                ]}
              >
                common.skip_i_dont_konw
              </Text>
            </TouchableOpacity>
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={400}>
          <Button
            text="common.next"
            backgroundColor="white"
            onPress={() => goNext(peakHeight)}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubPeakHeight);
