import React, {useState} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';

const ClubGender = ({user, navigation, updateClubRecommender}) => {
  const [gender, setGender] = useState(user.gender || '');
  const validated = !isEmpty(gender || '');

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return gender === type ? 'white' : GREY;
      case 'border':
        return gender === type ? GREEN : GREY;
      case 'background':
        return gender === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const goNext = () => {
    updateClubRecommender({gender});
    navigation.navigate('ClubBirthday');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              club.what_type_of_clubs_do_you_play
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="club.gender.mens"
              textColor={getButtonActiveColor('male', 'text')}
              borderColor={getButtonActiveColor('male', 'border')}
              backgroundColor={getButtonActiveColor('male', 'background')}
              onPress={() => setGender('male')}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="club.gender.womens"
              textColor={getButtonActiveColor('female', 'text')}
              borderColor={getButtonActiveColor('female', 'border')}
              backgroundColor={getButtonActiveColor('female', 'background')}
              onPress={() => setGender('female')}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="common.next"
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated}
            onPress={goNext}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  user: state.user,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubGender);
