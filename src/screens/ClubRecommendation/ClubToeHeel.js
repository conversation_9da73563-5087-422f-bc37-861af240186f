import React, {useState} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';
import {moderateScale} from 'react-native-size-matters';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import LieImg from 'assets/imgs/club-lie.svg';
import GreenIndicator from 'assets/imgs/club-green-indicator.svg';

import appStyles from 'styles/global';
import {t} from 'i18next';

const clubLieImg = require('assets/imgs/club-lie.png');
const clubGreenIndicator = require('assets/imgs/club-green-indicator.png');

const ClubToeHeel = ({
  navigation,
  updateClubRecommender,
  clubLaunchMonitor,
}) => {
  const isTablet = DeviceInfo.isTablet();
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [toeHeel, setToeHeel] = useState(
    clubRecommender?.toeHeelImpactLocation
      ? parseFloat(clubRecommender?.toeHeelImpactLocation)
      : clubLaunchMonitor?.clubFaceImpactX
      ? parseFloat(clubLaunchMonitor?.clubFaceImpactX)
      : 0,
  );

  const goNext = value => {
    updateClubRecommender({
      toeHeelImpactLocation: value ? value?.toString() : null,
    });
    navigation.navigate('ClubHighLow');
  };

  const getIndicatorPosition = () => {
    const rightPosition = toeHeel
      ? `${(toeHeel > 0 ? -toeHeel : Math.abs(toeHeel)) / 2 + 40}%`
      : '40%';
    return {
      position: 'absolute',
      top: '60%',
      right: rightPosition,
    };
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex]}>
        <View style={appStyles.flex}>
          <Animatable.View
            animation="fadeIn"
            delay={100}
            style={{alignItems: 'flex-end'}}
          >
            <>
              <Image
                style={[
                  appStyles.alignCenter,
                  appStyles.responsiveClubLie,
                  {
                    marginBottom: -60,
                    marginTop: -130,
                    marginLeft: wp(32),
                  },
                ]}
                source={clubLieImg}
              />
              <Image
                style={[
                  {
                    width: 60,
                    height: 60,
                  },
                  getIndicatorPosition(),
                ]}
                source={clubGreenIndicator}
              />
            </>
          </Animatable.View>
          <View style={[appStyles.pHSm, appStyles.mTMd]}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                club.what_is_your_toe_heel_impact
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                {toeHeel} {t('club.highlow_mm')}
              </Text>
              <Slider
                style={[{width: '100%'}]}
                minimumValue={-40}
                maximumValue={40}
                minimumTrackTintColor="#fff"
                maximumTrackTintColor="#fff"
                onValueChange={value => setToeHeel(parseInt(value.toFixed(0)))}
                value={toeHeel}
              />
              <View style={[appStyles.row]}>
                <Text style={[appStyles.white]}>
                  quiz.ball_miss.supporting_copy.toe
                </Text>
                <Text style={[appStyles.white, appStyles.mLAuto]}>
                  quiz.ball_miss.supporting_copy.heel
                </Text>
              </View>
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={300}>
              <TouchableOpacity onPress={() => goNext(null)}>
                <Text
                  style={[
                    appStyles.xs,
                    appStyles.underlined,
                    appStyles.grey,
                    appStyles.textCenter,
                    appStyles.mTMd,
                  ]}
                >
                  common.skip_i_dont_konw
                </Text>
              </TouchableOpacity>
            </Animatable.View>
          </View>
        </View>

        <View style={appStyles.pHSm}>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              text="common.next"
              backgroundColor="white"
              onPress={() => goNext(toeHeel)}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubToeHeel);
