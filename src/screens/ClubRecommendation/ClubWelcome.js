import React, {useState} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {
  clearClubRecommender,
  updateClubRecommender,
  updateClubLaunchMonitor,
} from 'reducers/clubRecommender';
import {getClubLaunchMonitor} from 'requests/club-recommender';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';

const ClubWelcome = ({
  navigation: {navigate},
  clearClubRecommender,
  updateClubRecommender,
  updateClubLaunchMonitor,
}) => {
  const [club, setClub] = useState('drivers');

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return club?.includes(type) ? 'white' : GREY;
      case 'border':
        return club?.includes(type) ? GREEN : GREY;
      case 'background':
        return club?.includes(type) ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const getStarted = async () => {
    clearClubRecommender();
    updateClubRecommender({clubTypeId: 1});
    navigate('ClubGender');
    const launchMonitor = await getClubLaunchMonitor();
    if (launchMonitor?.length) {
      updateClubLaunchMonitor(launchMonitor[0][0]);
    } else {
      updateClubLaunchMonitor(null);
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.pHSm]}>
        <View style={[appStyles.flex, appStyles.mTXl]}>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Text style={[appStyles.white, appStyles.xxl]} DINbold>
              club.welcome.built-for
            </Text>
            <Text
              style={[appStyles.white, appStyles.mBSm, appStyles.xxl]}
              DINbold
            >
              club.welcome.your_gamea
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Text style={[appStyles.white, appStyles.mBMd]}>
              club.welcome.select_a_club_and_find_fit
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              style={[appStyles.mBSm]}
              text="my_bag.supporting_copy.drivers"
              textColor={getButtonActiveColor('drivers', 'text')}
              borderColor={getButtonActiveColor('drivers', 'border')}
              backgroundColor={getButtonActiveColor('drivers', 'background')}
              onPress={() => setClub('drivers')}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={400}>
          <Button
            text={club ? 'common.next' : 'quiz.welcome.cta'}
            backgroundColor={club ? 'white' : GREY}
            disabled={!club}
            onPress={getStarted}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {
  clearClubRecommender,
  updateClubRecommender,
  updateClubLaunchMonitor,
};

export default connect(null, mapDispatchToProps)(ClubWelcome);
