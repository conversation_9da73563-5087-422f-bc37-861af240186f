import React, {useState} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';
import {moderateScale} from 'react-native-size-matters';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import LieImg from 'assets/imgs/club-lie.svg';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {convertToPointFiveIncrement} from 'utils/convert';

const clubLieImg = require('assets/imgs/club-lie.png');

const ClubLie = ({navigation, updateClubRecommender, clubLaunchMonitor}) => {
  const isTablet = DeviceInfo.isTablet();
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [lie, setLie] = useState(
    clubRecommender?.clubLieAtImpact
      ? parseFloat(clubRecommender?.clubLieAtImpact)
      : clubLaunchMonitor?.clubLieAngle
      ? convertToPointFiveIncrement(
          parseFloat(clubLaunchMonitor?.clubLieAngle),
          -15,
          15,
        )
      : 0,
  );

  const goNext = value => {
    updateClubRecommender({clubLieAtImpact: value ? value?.toString() : null});
    navigation.navigate('ClubLoft');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex]}>
        <View style={appStyles.flex}>
          <Animatable.View
            animation="fadeIn"
            delay={100}
            style={{
              transform: [{rotate: `${lie}deg`}],
            }}
          >
            <Image
              style={[
                appStyles.alignCenter,
                appStyles.responsiveClubLie,
                {
                  marginBottom: -wp(16),
                  marginTop: -wp(19),
                  marginLeft: wp(32),
                },
              ]}
              source={clubLieImg}
            />
          </Animatable.View>
          <View
            style={[
              {
                borderWidth: 1,
                borderColor: GREY,
                borderRadius: 1,
                borderStyle: 'dotted',
              },
            ]}
          />
          <View style={[appStyles.pHSm, appStyles.mTMd]}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                club.what_is_your_club_lie_at_impact
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <Text
                style={[
                  appStyles.white,
                  appStyles.xxxl,
                  appStyles.textCenter,
                  {paddingLeft: '5%'},
                ]}
                DINbold
              >
                {lie}°
              </Text>
              <Slider
                style={[{width: '100%'}]}
                minimumValue={-15}
                maximumValue={15}
                step={0.5}
                minimumTrackTintColor="#fff"
                maximumTrackTintColor="#fff"
                onValueChange={value => setLie(value)}
                value={lie}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={300}>
              <TouchableOpacity onPress={() => goNext(null)}>
                <Text
                  style={[
                    appStyles.xs,
                    appStyles.underlined,
                    appStyles.grey,
                    appStyles.textCenter,
                    appStyles.mTMd,
                  ]}
                >
                  common.skip_i_dont_konw
                </Text>
              </TouchableOpacity>
            </Animatable.View>
          </View>
        </View>

        <View style={appStyles.pHSm}>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              text="common.next"
              backgroundColor="white"
              onPress={() => goNext(lie)}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubLie);
