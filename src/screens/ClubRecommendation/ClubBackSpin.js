import React, {useState} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {convertIncrements} from 'utils/convert';

const backSpinImg = require('assets/imgs/club-backspin.png');

const min = 1000;
const max = 5500;
// Create increment values for slider
const getIncrements = () => {
  const increments = [];
  let num = min;
  while (num < max) {
    num += 50;
    increments.push(num);
  }
  return increments;
};
const increments = getIncrements();

const ClubBackSpin = ({
  navigation,
  updateClubRecommender,
  clubLaunchMonitor,
}) => {
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [backSpin, setBackSpin] = useState(
    clubRecommender?.backspin
      ? parseInt(clubRecommender?.backspin)
      : clubLaunchMonitor?.verticalRpmModified
      ? convertIncrements(
          parseInt(clubLaunchMonitor?.verticalRpmModified?.toFixed(0)),
          increments,
          min,
          max,
        )
      : 3250,
  );

  const goNext = value => {
    updateClubRecommender({backspin: value ? value?.toString() : null});
    navigation.navigate('ClubSideSpin');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeIn" delay={100}>
            <Image
              style={[
                appStyles.alignCenter,
                appStyles.mBMd,
                appStyles.responsiveBackSpin,
              ]}
              source={backSpinImg}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              club.back.spin.what_is_your_backspin
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <View style={{flexDirection: 'row', justifyContent: 'center'}}>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                {`${backSpin} `}
              </Text>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                club.back.spin.rpms
              </Text>
            </View>
            <Slider
              style={[{width: '100%'}]}
              minimumValue={min}
              maximumValue={max}
              step={50}
              minimumTrackTintColor="#fff"
              maximumTrackTintColor="#fff"
              onValueChange={value => setBackSpin(value)}
              value={backSpin}
            />
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={300}>
            <TouchableOpacity onPress={() => goNext(null)}>
              <Text
                style={[
                  appStyles.xs,
                  appStyles.underlined,
                  appStyles.grey,
                  appStyles.textCenter,
                  appStyles.mTMd,
                ]}
              >
                common.skip_i_dont_konw
              </Text>
            </TouchableOpacity>
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={400}>
          <Button
            text="common.next"
            backgroundColor="white"
            onPress={() => goNext(backSpin)}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubBackSpin);
