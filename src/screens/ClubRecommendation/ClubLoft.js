import React, {useState} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';
import {moderateScale} from 'react-native-size-matters';
import DeviceInfo from 'react-native-device-info';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import LoftImg from 'assets/imgs/club-loft.svg';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {convertToPointFiveIncrement} from 'utils/convert';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

const clubLoftImg = require('assets/imgs/club-loft.png');

const ClubLoft = ({navigation, updateClubRecommender, clubLaunchMonitor}) => {
  const isTablet = DeviceInfo.isTablet();
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [loft, setLoft] = useState(
    clubRecommender?.clubLoftAtImpact
      ? parseFloat(clubRecommender?.clubLoftAtImpact)
      : clubLaunchMonitor?.clubLoftAngle
      ? convertToPointFiveIncrement(
          parseFloat(clubLaunchMonitor?.clubLoftAngle),
          1,
          25,
        )
      : 12,
  );

  const goNext = value => {
    updateClubRecommender({clubLoftAtImpact: value ? value?.toString() : null});
    navigation.navigate('ClubFaceAngleTargetLine');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex]}>
        <View style={appStyles.flex}>
          <Animatable.View
            animation="fadeIn"
            delay={100}
            style={{
              alignSelf: 'center',
              transform: [
                {
                  rotate: `${loft === 25 ? -12 : 13 - loft}deg`,
                },
              ],
            }}
          >
            <Image
              style={[
                appStyles.alignCenter,
                appStyles.responsiveClubLoft,
                {
                  marginBottom: -wp(15),
                  marginTop: -wp(19),
                },
              ]}
              source={clubLoftImg}
            />
          </Animatable.View>
          <View
            style={[
              {
                borderWidth: 1,
                borderColor: GREY,
                borderRadius: 1,
                borderStyle: 'dotted',
              },
            ]}
          />
          <View style={[appStyles.pHSm, appStyles.mTMd]}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                club.what_is_your_club_loft_at_impact
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                {loft}°
              </Text>
              <Slider
                style={[{width: '100%'}]}
                minimumValue={1}
                maximumValue={25}
                step={0.5}
                minimumTrackTintColor="#fff"
                maximumTrackTintColor="#fff"
                onValueChange={value => setLoft(value)}
                value={loft}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={300}>
              <TouchableOpacity onPress={() => goNext(null)}>
                <Text
                  style={[
                    appStyles.xs,
                    appStyles.underlined,
                    appStyles.grey,
                    appStyles.textCenter,
                    appStyles.mTMd,
                  ]}
                >
                  common.skip_i_dont_konw
                </Text>
              </TouchableOpacity>
            </Animatable.View>
          </View>
        </View>

        <View style={appStyles.pHSm}>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              text="common.next"
              backgroundColor="white"
              onPress={() => goNext(loft)}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubLoft);
