import React, {useState, useRef} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect} from 'react-redux';
import moment from 'moment';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Selector from 'components/Selector';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {GREY, ERROR_RED} from 'config';
import {t} from 'i18next';

const ClubBirthday = ({user, navigation, updateClubRecommender}) => {
  const [birthday, setBirthday] = useState(user.dob || '');
  const validated = !isEmpty(birthday || '');
  const sheetRef = useRef(null);

  const goNext = () => {
    updateClubRecommender({
      age: moment().diff(moment(birthday).format('YYYY'), 'years').toString(),
    });
    navigation.navigate('ClubHanded');
  };

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                quiz.birthday.headline
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                text={
                  birthday?.length
                    ? moment(birthday).format('LL')
                    : t('quiz.birthday.supporting_copy')
                }
                textColor={birthday.length ? 'white' : GREY}
                borderColor={GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                rightIcon="chevron-down"
              />
            </Animatable.View>
          </View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              text="common.next"
              backgroundColor={validated ? 'white' : GREY}
              disabled={!validated}
              onPress={goNext}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="date"
        value={birthday}
        onChange={value => setBirthday(moment(value).toISOString())}
      />
    </>
  );
};

const mapStateToProps = state => ({
  user: state.user,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubBirthday);
