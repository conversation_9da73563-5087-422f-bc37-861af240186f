import React, {useState} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {convertIncrements} from 'utils/convert';

const min = -2000;
const max = 2000;
// Create increment values for slider
const getIncrements = () => {
  const increments = [];
  let num = min;
  while (num < max) {
    num += 50;
    increments.push(num);
  }
  return increments;
};
const increments = getIncrements();

const ClubSideSpin = ({
  navigation,
  updateClubRecommender,
  clubLaunchMonitor,
}) => {
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [sideSpin, setSideSpin] = useState(
    clubRecommender?.sidespin
      ? parseInt(clubRecommender?.sidespin)
      : clubLaunchMonitor?.horizontalRpmModified
      ? convertIncrements(
          parseInt(clubLaunchMonitor?.horizontalRpmModified?.toFixed(0)),
          increments,
          min,
          max,
        )
      : 0,
  );

  const goNext = value => {
    updateClubRecommender({sidespin: value ? value?.toString() : null});
    navigation.navigate('ClubDeviationAngle');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              club.what_is_your_sidespin
            </Text>
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={100}>
            <View style={{flexDirection: 'row', justifyContent: 'center'}}>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                {`${sideSpin} `}
              </Text>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                club.back.spin.rpms
              </Text>
            </View>
            <Slider
              style={[{width: '100%'}]}
              minimumValue={min}
              maximumValue={max}
              step={50}
              minimumTrackTintColor="#fff"
              maximumTrackTintColor="#fff"
              onValueChange={value => setSideSpin(value)}
              value={sideSpin}
            />
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <TouchableOpacity onPress={() => goNext(null)}>
              <Text
                style={[
                  appStyles.xs,
                  appStyles.underlined,
                  appStyles.grey,
                  appStyles.textCenter,
                  appStyles.mTMd,
                ]}
              >
                common.skip_i_dont_konw
              </Text>
            </TouchableOpacity>
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="common.next"
            backgroundColor="white"
            onPress={() => goNext(sideSpin)}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubSideSpin);
