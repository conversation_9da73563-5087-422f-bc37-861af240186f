import React, {useState} from 'react';
import {View, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import DeviceInfo from 'react-native-device-info';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';

import NoBallFlight from 'assets/imgs/club-no-ball-flight.svg';
import HigherBallFlight from 'assets/imgs/club-higher-ball-flight.svg';
import HighBallFlight from 'assets/imgs/club-high-ball-flight.svg';
import MidBallFlight from 'assets/imgs/club-mid-ball-flight.svg';
import LowBallFlight from 'assets/imgs/club-low-ball-flight.svg';
import LowerBallFlight from 'assets/imgs/club-lower-ball-flight.svg';
import {t} from 'i18next';

const noFlight = require('assets/imgs/club-no-flight.png');
const lowFlight = require('assets/imgs/club-low-flight.png');
const lowerFlight = require('assets/imgs/club-lower-flight.png');
const midFlight = require('assets/imgs/club-mid-flight.png');
const highFlight = require('assets/imgs/club-high-flight.png');
const higherFlight = require('assets/imgs/club-higher-flight.png');

const ClubDriverBallFlight = ({navigation, updateClubRecommender}) => {
  const isTablet = DeviceInfo.isTablet();
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [driverBallFlight, setDriverBallFlight] = useState(
    clubRecommender?.driverBallFlight || null,
  );

  const goNext = () => {
    updateClubRecommender({driverBallFlight});
    navigation.navigate('ClubDriverShotShape');
  };

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return driverBallFlight === type ? 'white' : GREY;
      case 'border':
        return driverBallFlight === type ? GREEN : GREY;
      case 'background':
        return driverBallFlight === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const getBallFlightImg = () => {
    switch (driverBallFlight) {
      case 'higher':
        return isTablet ? higherFlight : <HigherBallFlight />;
      case 'high':
        return isTablet ? highFlight : <HighBallFlight />;
      case 'mid':
        return isTablet ? midFlight : <MidBallFlight />;
      case 'low':
        return isTablet ? lowFlight : <LowBallFlight />;
      case 'lower':
        return isTablet ? lowerFlight : <LowerBallFlight />;
      default:
        return isTablet ? noFlight : <NoBallFlight />;
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View
        style={[
          appStyles.flex,
          appStyles.mTSm,
          appStyles.pHSm,
          appStyles.spaceBetween,
        ]}
      >
        <Animatable.View animation="fadeInUp">
          <Text style={[appStyles.white, appStyles.textCenter]}>
            club.driver.ball.flight.what_is_your_driver_ball_flight
          </Text>
        </Animatable.View>
        <Animatable.View
          animation="fadeIn"
          delay={100}
          style={appStyles.hCenter}
        >
          {isTablet ? (
            <Image
              style={[
                appStyles.alignCenter,
                appStyles.responsivePeak,
                {marginTop: '-5%'},
              ]}
              source={getBallFlightImg()}
            />
          ) : (
            <>{getBallFlightImg()}</>
          )}
          <Text style={[appStyles.grey, appStyles.xs, appStyles.textCenter]}>
            club.ball_lands_with_a_bounce
          </Text>
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={200}>
          <View style={(appStyles.flex, appStyles.wrap)}>
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="club.driver.ball.higher"
              textColor={getButtonActiveColor('higher', 'text')}
              borderColor={getButtonActiveColor('higher', 'border')}
              backgroundColor={getButtonActiveColor('higher', 'background')}
              onPress={() => setDriverBallFlight('higher')}
              centered
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text={t('home.scores.supporting_copy.high')}
              textColor={getButtonActiveColor('high', 'text')}
              borderColor={getButtonActiveColor('high', 'border')}
              backgroundColor={getButtonActiveColor('high', 'background')}
              onPress={() => setDriverBallFlight('high')}
              centered
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="club.driver.ball.mid"
              textColor={getButtonActiveColor('mid', 'text')}
              borderColor={getButtonActiveColor('mid', 'border')}
              backgroundColor={getButtonActiveColor('mid', 'background')}
              onPress={() => setDriverBallFlight('mid')}
              centered
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text={t('home.scores.supporting_copy.low')}
              textColor={getButtonActiveColor('low', 'text')}
              borderColor={getButtonActiveColor('low', 'border')}
              backgroundColor={getButtonActiveColor('low', 'background')}
              onPress={() => setDriverBallFlight('low')}
              centered
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="club.driver.ball.lower"
              textColor={getButtonActiveColor('lower', 'text')}
              borderColor={getButtonActiveColor('lower', 'border')}
              backgroundColor={getButtonActiveColor('lower', 'background')}
              onPress={() => setDriverBallFlight('lower')}
              centered
            />
          </View>
        </Animatable.View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="common.next"
            backgroundColor={driverBallFlight ? 'white' : GREY}
            disabled={!driverBallFlight}
            onPress={goNext}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {updateClubRecommender};

export default connect(null, mapDispatchToProps)(ClubDriverBallFlight);
