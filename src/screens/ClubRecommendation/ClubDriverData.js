import React, {useState} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import analytics from '@react-native-firebase/analytics';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';

const ClubDriverData = ({navigation, updateClubRecommender}) => {
  const [hasDriverData, setHasDriverData] = useState(null);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return hasDriverData === type ? 'white' : GREY;
      case 'border':
        return hasDriverData === type ? GREEN : GREY;
      case 'background':
        return hasDriverData === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const goNext = () => {
    updateClubRecommender({typicalDriverData: hasDriverData});
    if (hasDriverData === 'yes') {
      navigation.navigate('ClubBallSpeed');
    } else {
      navigation.navigate('ClubIron');
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              club.driver.data.do_you_know_your_typical_driver_data
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="club.driver.ball.yes"
              textColor={getButtonActiveColor('yes', 'text')}
              borderColor={getButtonActiveColor('yes', 'border')}
              backgroundColor={getButtonActiveColor('yes', 'background')}
              onPress={async () => {
                setHasDriverData('yes');
                await analytics().logEvent('club_recommender_cta_open', {
                  name: 'Yes: Club Recommender Flow',
                });
                await analytics().logEvent('cta_open', {
                  name: 'Yes: Club Recommender Flow',
                });
              }}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="club.driver.ball.no"
              textColor={getButtonActiveColor('no', 'text')}
              borderColor={getButtonActiveColor('no', 'border')}
              backgroundColor={getButtonActiveColor('no', 'background')}
              onPress={async () => {
                setHasDriverData('no');
                await analytics().logEvent('club_recommender_cta_open', {
                  name: 'No: Club Recommender Flow',
                });
                await analytics().logEvent('cta_open', {
                  name: 'No: Club Recommender Flow',
                });
              }}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="common.next"
            backgroundColor={hasDriverData ? 'white' : GREY}
            disabled={!hasDriverData}
            onPress={goNext}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {updateClubRecommender};

export default connect(null, mapDispatchToProps)(ClubDriverData);
