import React, {useState} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {moderateScale} from 'react-native-size-matters';
import DeviceInfo from 'react-native-device-info';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import LoftImg from 'assets/imgs/club-face-angle.svg';

import appStyles from 'styles/global';
import {convertToPointFiveIncrement} from 'utils/convert';

const clubFaceAngle = require('assets/imgs/club-face-angle.png');

const ClubFaceAngleTargetLine = ({
  navigation,
  updateClubRecommender,
  clubLaunchMonitor,
}) => {
  const isTablet = DeviceInfo.isTablet();
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [faceAngle, setFaceAngle] = useState(
    clubRecommender?.faceAngleToTargetLine
      ? parseFloat(clubRecommender?.faceAngleToTargetLine)
      : clubLaunchMonitor?.clubFaceToTargetAngle
      ? convertToPointFiveIncrement(
          parseFloat(clubLaunchMonitor?.clubFaceToTargetAngle),
          -15,
          15,
        )
      : 0,
  );

  const goNext = value => {
    updateClubRecommender({
      faceAngleToTargetLine: value ? value?.toString() : null,
    });
    navigation.navigate('ClubFaceAnglePath');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex]}>
        <View style={appStyles.flex}>
          <Image
            style={{
              position: 'absolute',
              width: '100%',
              marginTop: isTablet ? '10%' : '0%',
            }}
            source={require('assets/imgs/club-lines.png')}
          />
          <Animatable.View
            animation="fadeIn"
            delay={100}
            style={{
              alignSelf: 'center',
              transform: [{rotate: `${faceAngle}deg`}],
            }}
          >
            <Image
              style={[
                appStyles.alignCenter,
                appStyles.responsiveFaceAngle,
                {
                  marginBottom: -wp(15),
                  marginTop: -wp(4),
                },
              ]}
              source={clubFaceAngle}
            />
          </Animatable.View>
          <View style={[appStyles.pHSm, appStyles.mTSm]}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                club.what_is_your_face_angle_to_target_line
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <Text
                style={[
                  appStyles.white,
                  appStyles.xxxl,
                  appStyles.textCenter,
                  {paddingLeft: '5%'},
                ]}
                DINbold
              >
                {faceAngle}°
              </Text>
              <Slider
                style={[{width: '100%'}]}
                minimumValue={-15}
                maximumValue={15}
                step={0.5}
                minimumTrackTintColor="#fff"
                maximumTrackTintColor="#fff"
                onValueChange={value => setFaceAngle(value)}
                value={faceAngle}
              />
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={300}>
              <TouchableOpacity onPress={() => goNext(null)}>
                <Text
                  style={[
                    appStyles.xs,
                    appStyles.underlined,
                    appStyles.grey,
                    appStyles.textCenter,
                    appStyles.mTMd,
                  ]}
                >
                  common.skip_i_dont_konw
                </Text>
              </TouchableOpacity>
            </Animatable.View>
          </View>
        </View>

        <View style={appStyles.pHSm}>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              text="common.next"
              backgroundColor="white"
              onPress={() => goNext(faceAngle)}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(ClubFaceAngleTargetLine);
