import React, {useState} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import DeviceInfo from 'react-native-device-info';
import {moderateScale} from 'react-native-size-matters';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import LieImg from 'assets/imgs/club-lie.svg';
import GreenIndicator from 'assets/imgs/club-green-indicator.svg';

import appStyles from 'styles/global';
import {t} from 'i18next';

const clubLieImg = require('assets/imgs/club-lie.png');
const clubGreenIndicator = require('assets/imgs/club-green-indicator.png');

const ClubHighLow = ({
  navigation,
  updateClubRecommender,
  clubLaunchMonitor,
}) => {
  const isTablet = DeviceInfo.isTablet();
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [highLow, setHighLow] = useState(
    clubRecommender?.highLowImpactLocation
      ? parseFloat(clubRecommender?.highLowImpactLocation)
      : clubLaunchMonitor?.clubFaceImpactY
      ? parseFloat(clubLaunchMonitor?.clubFaceImpactY)
      : 0,
  );

  const goNext = value => {
    updateClubRecommender({
      highLowImpactLocation: value ? value?.toString() : null,
    });
    navigation.navigate('ClubDesiredShotShape');
  };

  const getIndicatorPosition = () => {
    const bottomPosition = highLow ? `${highLow / 2 + 14}%` : '14%';
    return {
      position: 'absolute',
      bottom: bottomPosition,
      right: '40%',
    };
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex]}>
        <View style={appStyles.flex}>
          <Animatable.View
            animation="fadeIn"
            delay={100}
            style={{alignItems: 'flex-end'}}
          >
            <>
              <Image
                style={[
                  appStyles.alignCenter,
                  appStyles.responsiveClubLie,
                  {
                    marginBottom: -60,
                    marginTop: -130,
                    marginLeft: wp(32),
                  },
                ]}
                source={clubLieImg}
              />
              <Image
                style={[
                  {
                    width: 60,
                    height: 60,
                  },
                  getIndicatorPosition(),
                ]}
                source={clubGreenIndicator}
              />
            </>
          </Animatable.View>
          <View style={[appStyles.pHSm, appStyles.mTMd]}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                club.what_is_your_hight_low_impact
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                {highLow} {t('club.highlow_mm')}
              </Text>
              <Slider
                style={[{width: '100%'}]}
                minimumValue={-40}
                maximumValue={40}
                minimumTrackTintColor="#fff"
                maximumTrackTintColor="#fff"
                onValueChange={value => setHighLow(parseInt(value.toFixed()))}
                value={highLow}
              />
              <View style={[appStyles.row]}>
                <Text style={[appStyles.white]}>
                  home.scores.supporting_copy.low
                </Text>
                <Text style={[appStyles.white, appStyles.mLAuto]}>
                  home.scores.supporting_copy.high
                </Text>
              </View>
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={300}>
              <TouchableOpacity onPress={() => goNext(null)}>
                <Text
                  style={[
                    appStyles.xs,
                    appStyles.underlined,
                    appStyles.grey,
                    appStyles.textCenter,
                    appStyles.mTMd,
                  ]}
                >
                  common.skip_i_dont_konw
                </Text>
              </TouchableOpacity>
            </Animatable.View>
          </View>
        </View>

        <View style={appStyles.pHSm}>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              text="common.next"
              backgroundColor="white"
              onPress={() => goNext(highLow)}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubHighLow);
