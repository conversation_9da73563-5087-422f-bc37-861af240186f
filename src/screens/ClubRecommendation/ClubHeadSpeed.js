import React, {useState} from 'react';
import {View, TouchableOpacity, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {moderateScale} from 'react-native-size-matters';
import DeviceInfo from 'react-native-device-info';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import HeadSpeedImg from 'assets/imgs/club-head-speed.svg';
import appStyles from 'styles/global';
import {convertToPointFiveIncrement} from 'utils/convert';
import {t} from 'i18next';

const headSpeedImg = require('assets/imgs/club-head-speed.png');

const ClubHeadSpeed = ({
  navigation,
  updateClubRecommender,
  clubLaunchMonitor,
}) => {
  const isTablet = DeviceInfo.isTablet();
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [headSpeed, setHeadSpeed] = useState(
    clubRecommender?.clubHeadSpeed
      ? parseFloat(clubRecommender?.clubHeadSpeed)
      : clubLaunchMonitor?.clubMph
      ? convertToPointFiveIncrement(
          parseFloat(clubLaunchMonitor?.clubMph),
          50,
          130,
        )
      : 90,
  );

  const goNext = value => {
    updateClubRecommender({clubHeadSpeed: value ? value?.toString() : null});
    navigation.navigate('ClubAngleOfAttack');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeIn">
            <Image
              style={[
                appStyles.alignCenter,
                appStyles.mBMd,
                appStyles.responsiveHeadSpeed,
                {
                  marginTop: wp(-20),
                  marginLeft: wp(13),
                },
              ]}
              source={headSpeedImg}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              club.what_is_your_club_head_speed
            </Text>
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={100}>
            <View style={{flexDirection: 'row', justifyContent: 'center'}}>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                {`${headSpeed} `}
              </Text>
              <Text
                style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
                DINbold
              >
                club.ball.speed.mph
              </Text>
            </View>
            <Slider
              style={[{width: '100%'}]}
              minimumValue={50}
              maximumValue={130}
              step={0.5}
              minimumTrackTintColor="#fff"
              maximumTrackTintColor="#fff"
              onValueChange={value => setHeadSpeed(value)}
              value={headSpeed}
            />
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <TouchableOpacity onPress={() => goNext(null)}>
              <Text
                style={[
                  appStyles.xs,
                  appStyles.underlined,
                  appStyles.grey,
                  appStyles.textCenter,
                  appStyles.mTMd,
                ]}
              >
                common.skip_i_dont_konw
              </Text>
            </TouchableOpacity>
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="common.next"
            backgroundColor="white"
            onPress={() => goNext(headSpeed)}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubHeadSpeed);
