import React, {useState} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';

const ClubHanded = ({user, navigation, updateClubRecommender}) => {
  const [handed, setHanded] = useState(user.golferProfile?.handed || '');
  const validated = !isEmpty(handed || '');

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return handed === type ? 'white' : GREY;
      case 'border':
        return handed === type ? GREEN : GREY;
      case 'background':
        return handed === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const goNext = () => {
    updateClubRecommender({handed});
    navigation.navigate('ClubHandicap');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              quiz.swing_orientation.headline
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.swing_orientation.supporting_copy.right"
              textColor={getButtonActiveColor('right', 'text')}
              borderColor={getButtonActiveColor('right', 'border')}
              backgroundColor={getButtonActiveColor('right', 'background')}
              onPress={() => setHanded('right')}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.swing_orientation.supporting_copy.left"
              textColor={getButtonActiveColor('left', 'text')}
              borderColor={getButtonActiveColor('left', 'border')}
              backgroundColor={getButtonActiveColor('left', 'background')}
              onPress={() => setHanded('left')}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="common.next"
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated}
            onPress={goNext}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  user: state.user,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubHanded);
