import React, {useState} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {updateClubRecommender} from 'reducers/clubRecommender';

import appStyles from 'styles/global';
import {convertToPointFiveIncrement} from 'utils/convert';

const ClubDeviationAngle = ({
  navigation,
  updateClubRecommender,
  clubLaunchMonitor,
}) => {
  const clubRecommender = useSelector(
    state => state?.clubRecommender?.clubRecommender,
  );
  const [deviationAngle, setDeviationAngle] = useState(
    clubRecommender?.deviationAngle
      ? parseInt(clubRecommender?.deviationAngle)
      : clubLaunchMonitor?.deviationAngleModified
      ? convertToPointFiveIncrement(
          parseInt(clubLaunchMonitor?.deviationAngleModified),
          -15,
          15,
        )
      : 0,
  );

  const goNext = value => {
    updateClubRecommender({deviationAngle: value ? value?.toString() : null});
    navigation.navigate('ClubPeakHeight');
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              club.deviation.angle.what_is_your_deviation_angle
            </Text>
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={100}>
            <Text
              style={[
                appStyles.white,
                appStyles.xxxl,
                appStyles.textCenter,
                {paddingLeft: '5%'},
              ]}
              DINbold
            >
              {deviationAngle}°
            </Text>
            <Slider
              style={[{width: '100%'}]}
              minimumValue={-15}
              maximumValue={15}
              step={0.5}
              minimumTrackTintColor="#fff"
              maximumTrackTintColor="#fff"
              onValueChange={value => setDeviationAngle(value)}
              value={deviationAngle}
            />
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <TouchableOpacity onPress={() => goNext(null)}>
              <Text
                style={[
                  appStyles.xs,
                  appStyles.underlined,
                  appStyles.grey,
                  appStyles.textCenter,
                  appStyles.mTMd,
                ]}
              >
                common.skip_i_dont_konw
              </Text>
            </TouchableOpacity>
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="common.next"
            backgroundColor="white"
            onPress={() => goNext(deviationAngle)}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  clubLaunchMonitor: state.clubRecommender.clubLaunchMonitor,
});

const mapDispatchToProps = {updateClubRecommender};

export default connect(mapStateToProps, mapDispatchToProps)(ClubDeviationAngle);
