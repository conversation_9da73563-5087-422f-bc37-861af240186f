import React, {useState, useEffect} from 'react';
import {
  View,
  Image,
  StyleSheet,
  Platform,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  FlatList,
} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import appStyles from 'styles/global';
import {getCoachProfile} from 'requests/club-house';
import Text from 'components/Text';
import HtmlParser from 'components/HtmlView';
import LinearGradient from 'react-native-linear-gradient';
import FastImage from 'react-native-fast-image/src';
import {
  GA_logSelectContentEvent,
  getThumbnail,
  isVideoArticle,
} from 'utils/article';
import {t} from 'i18next';

const CoachDetail = ({navigation, route}) => {
  const data = route?.params?.item;
  const [loading, setLoading] = useState(false);
  const [listVideos, setListVideos] = useState([]);
  const insets = useSafeAreaInsets();
  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    setLoading(true);
    try {
      const response = await getCoachProfile(data?.tags);
      if (response?.data) {
        const filterVideo = response?.data?.filter(item =>
          isVideoArticle(item),
        );
        setListVideos(filterVideo);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const onPressClose = () => {
    navigation.goBack();
  };

  const onPressItem = item => {
    GA_logSelectContentEvent({
      contentName: item?.title,
      contentType: 'video',
      clickLocation: 'drills-by-coach',
    });
    navigation.navigate('Video', {
      video: {
        id: item?.urlId,
        title: item?.title,
        host: item?.videoType,
        contentId: item?.id,
        videoProvider: item?.dataSource,
        clickLocation: 'drills-by-coach',
        contentName: item?.title,
      },
    });
  };
  const renderHeader = () => {
    return (
      <View
        style={{
          alignItems: 'center',
          top: insets.top + (Platform.OS === 'ios' ? -10 : 10),
          position: 'absolute',
          right: 6,
        }}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            alignItems: 'center',
            paddingVertical: 10,
            paddingHorizontal: 10,
            borderRadius: 24,
          }}
          onPress={onPressClose}
        >
          <View
            style={{
              borderRadius: 24,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              padding: 1,
            }}
          >
            <Icon name="close" size={20} color={'#fff'} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };
  const renderVideos = ({item, index}) => {
    const thumbnail = getThumbnail(item);
    return (
      <TouchableOpacity
        style={[
          appStyles.flex,
          appStyles.row,
          {
            marginHorizontal: 8,
            marginBottom: 8,
            backgroundColor: '#fff',
            borderRadius: 16,
          },
          appStyles.viewShadowLightBig,
        ]}
        onPress={() => onPressItem(item)}
      >
        <FastImage
          style={[styles.image]}
          source={{
            uri: thumbnail,
            cache: FastImage.cacheControl.web,
          }}
        />
        <View
          style={[
            appStyles.flex,
            {
              paddingVertical: 8,
              justifyContent: 'center',
              marginLeft: 16,
              paddingRight: 8,
            },
          ]}
        >
          {item?.tags?.mytmInstructor?.length > 0 && (
            <Text
              weight={700}
              size={12}
              style={[
                {
                  color: 'rgba(0, 0, 0, 0.4)',
                  textTransform: 'uppercase',
                  letterSpacing: 1,
                  marginBottom: 4,
                },
              ]}
            >
              {item?.tags.mytmInstructor[0].title}
            </Text>
          )}
          <Text numberOfLines={3} black size={16} weight={700}>
            {item?.title}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View
      style={{
        flex: 1,
      }}
    >
      <LinearGradient
        start={{x: 1, y: 0.5}}
        end={{x: 1, y: 4}}
        colors={['#ffffff', 'rgba(0, 0, 0, 1)']}
        style={{
          flex: 1,
        }}
      >
        <ScrollView>
          <Image
            source={{
              uri: data?.imageLink,
            }}
            style={{width: wp(100), height: wp(100) * 1.17}}
          />
          <View
            style={{paddingHorizontal: 16, paddingTop: 16, paddingBottom: 6}}
          >
            <Text
              Din79Font
              size={22}
              weight={800}
              black
              style={{
                textTransform: 'uppercase',
                letterSpacing: 1,
                marginBottom: 20,
              }}
            >
              {data?.title}
            </Text>
            <HtmlParser
              html={data?.description?.replace(/(\r\n|\n|\r)/gm, '')}
              textComponentProps={{
                style: [appStyles.black],
              }}
              markupColor={true}
              navigation={navigation}
            />

            <Text
              Din79Font
              size={16}
              weight={800}
              black
              style={{
                textTransform: 'uppercase',
                letterSpacing: 1,
                marginTop: 20,
              }}
            >
              {`${data?.title?.split?.(' ')?.[0]}${t(
                'clubhouse.drills.coachy_drills',
              )}`}
            </Text>
          </View>
          <FlatList
            data={listVideos}
            renderItem={renderVideos}
            keyExtractor={item => item?.id}
            style={{paddingBottom: 60, paddingTop: 20}}
          />
        </ScrollView>
      </LinearGradient>
      {renderHeader()}
      {loading && (
        <ActivityIndicator
          style={StyleSheet.absoluteFill}
          color={'#000'}
          size={'large'}
        />
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  image: {
    height: wp(46) * 0.55,
    width: wp(46),
    borderTopStartRadius: 12,
    borderBottomStartRadius: 12,
    margin: 4,
  },
});
export default CoachDetail;
