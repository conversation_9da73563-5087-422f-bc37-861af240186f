import React, {useState} from 'react';
import FastImage from 'react-native-fast-image/src';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import LinearGradient from 'react-native-linear-gradient';
import {View} from 'react-native';

const ImageDrill = props => {
  const [isError, setError] = useState(false);
  const [isImageLoading, setImageLoading] = useState(true);

  const firstImage = props.drillData?.primaryImage;
  const secondImage = props.drillData?.primaryImageSecondary;
  const imageStyle = {...props.style, zIndex: 2};
  const {width, height, aspectRatio, ...otherStyle} = props.style;

  return (
    <View>
      {isImageLoading && (
        <ShimmerPlaceholder
          LinearGradient={LinearGradient}
          width={props.style.width}
          height={
            props.style.height
              ? props.style.height
              : props.style.width / props.style.aspectRatio
          }
          style={{...otherStyle, zIndex: 1, position: 'absolute', top: 0}}
        />
      )}
      <FastImage
        style={imageStyle}
        source={{
          uri: isError ? secondImage : firstImage || secondImage,
          cache: FastImage.cacheControl.web,
        }}
        onError={e => setError(true)}
        onLoadEnd={() => {
          setImageLoading(false);
        }}
      />
    </View>
  );
};

export default ImageDrill;
