import React from 'react';
import {StyleSheet} from 'react-native';
import Animated, {
  useAnimatedStyle,
  interpolate,
  Extrapolation,
} from 'react-native-reanimated';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
const widthFull = wp(100);
const widthClose = wp(12.6);
const widthPadding = 4;
const ItemLine = ({
  item,
  index,
  offset,
  listItem,
  indexPage,
  listCurrentTime,
}) => {
  const getWidthLine =
    (widthFull - widthClose) / listItem.length - widthPadding;
  const options = item?.options ? JSON.parse(item?.options) : {duration: 0};
  const duration =
    listCurrentTime.current?.[indexPage.current]?.duration ||
    options?.duration ||
    0;
  const styleLineAnimation = useAnimatedStyle(() => {
    if (
      listItem[indexPage.current]?.type?.toUpperCase() === 'VIDEO' &&
      index === indexPage.current
    ) {
      const width = interpolate(
        offset.value,
        [0, duration],
        [0, getWidthLine - 2],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );
      return {width};
    }
    return {
      width:
        index === indexPage.current
          ? offset.value
          : index > indexPage.current
          ? 0
          : getWidthLine - 2,
    };
  });
  return (
    <Animated.View
      key={item.id + 'header'}
      style={[styles.viewLine, {width: getWidthLine}]}
    >
      <Animated.View
        key={item.id + 'child'}
        style={[
          styles.viewInSideLine,
          styleLineAnimation,
          index < indexPage.current && {width: getWidthLine - 2},
        ]}
      />
      {index < indexPage.current && (
        <Animated.View
          style={[
            styles.viewInSideLine,
            {
              width: getWidthLine - 2,
              position: 'absolute',
              alignSelf: 'center',
            },
          ]}
        />
      )}
      {index > indexPage.current && (
        <Animated.View
          style={[
            styles.viewInSideLine,
            {
              width: getWidthLine,
              backgroundColor: '#000',
              position: 'absolute',
              alignSelf: 'center',
            },
          ]}
        />
      )}
    </Animated.View>
  );
};
const styles = StyleSheet.create({
  viewLine: {
    height: 4,
    justifyContent: 'center',
    paddingHorizontal: 1,
    backgroundColor: '#000',
    borderRadius: 24,
    width: 100,
    marginLeft: wp(1),
  },
  viewInSideLine: {
    height: 2,
    backgroundColor: '#fff',
    borderRadius: 24,
  },
});
export default ItemLine;
