import React, {useEffect, useState} from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Platform,
  ScrollView,
  Image,
  TouchableOpacity,
} from 'react-native';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';

import appStyles from 'styles/global';
import {t} from 'i18next';
import BackButton from 'assets/imgs/clubhouse/btn_back.png';
import LinearGradient from 'react-native-linear-gradient';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {articlePressed} from 'utils/article';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {getGearList} from 'requests/content';
import moment from 'moment';
import CustomImage from 'components/CustomImage/CustomImage';
import {uniqBy} from 'lodash';
import debounce from 'lodash.debounce';

const GearDeepDives = ({navigation, route}) => {
  const [dataGear, setDataGear] = useState([]);
  const [loading, setLoading] = useState();
  const insets = useSafeAreaInsets();
  const [loadingMore, setLoadingMore] = useState(false);
  const [totalPageCount, setTotalPageCount] = useState(1);
  const [page, setPage] = useState(1);

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    try {
      setLoading(true);
      const response = await getGearList();
      if (response?.data?.length > 0) {
        const dataResponse = response?.data;
        setTotalPageCount(response?.meta?.pagination?.total_pages);
        setDataGear(dataResponse);
      } else {
        setDataGear([]);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const loadMoreData = async () => {
    try {
      if (loadingMore) return;
      if (page + 1 > totalPageCount) {
        return;
      }
      setLoadingMore(true);
      const response = await getGearList(page + 1);
      if (response?.data?.length > 0) {
        const dataResponse = response?.data;
        setPage(page + 1);
        setDataGear(uniqBy([...dataGear, ...dataResponse], 'title'));
      } else {
        // setDataArticles([]);
      }
    } catch (error) {
    } finally {
      setLoadingMore(false);
    }
  };

  const debounceLoadMore = debounce(() => loadMoreData(), 2000);

  const onPressGear = async item => {
    articlePressed(navigation.navigate, item, 'entertainment-gear-deep-dives');
  };
  const renderVideos = ({item, index}) => (
    <TouchableWithoutFeedback style={[]} onPress={() => onPressGear(item)}>
      <View
        style={[
          appStyles.flex,
          appStyles.viewShadow,
          appStyles.row,
          styles.tileContainer,
        ]}
      >
        <CustomImage source={{uri: item.primaryImage}} style={styles.image} />
        <View style={[appStyles.flex, {paddingHorizontal: 8}]}>
          <Text Din79Font size={12} weight={700} style={styles.dateText}>
            {moment(item.postDate).format('MM.DD.YY')}
          </Text>
          <Text
            size={16}
            weight={700}
            white
            style={{lineHeight: 18, marginVertical: 8}}
            numberOfLines={3}
          >
            {item.title}
          </Text>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );

  const renderHeader = () => {
    return (
      <>
        <View
          style={{
            flexDirection: 'row',
            marginBottom: 20,
            alignItems: 'center',
          }}
        >
          <TouchableOpacity
            style={styles.buttonBack}
            onPress={() => {
              navigation.goBack();
            }}
          >
            <Image source={BackButton} style={{width: 24, height: 24}} />
          </TouchableOpacity>
          <Text
            white
            Din79Font
            size={22}
            weight={800}
            style={styles.textHeaderTitle}
            numberOfLines={1}
          >
            {t('entertainment.gear.gear_deep_dives')}
          </Text>
        </View>
        {loading && <ActivityIndicator style={appStyles.pTLg} color="white" />}
      </>
    );
  };

  const renderFooter = () => {
    return (
      <>
        {dataGear.length > 0 && page + 1 <= totalPageCount && (
          <ActivityIndicator style={{paddingTop: 15}} color="white" />
        )}
      </>
    );
  };

  return (
    <View style={{flex: 1}}>
      <LinearGradient
        start={{x: 1, y: 0}}
        end={{x: 1, y: 1}}
        colors={['rgba(60, 60, 60, 1)', 'rgba(0, 0, 0, 1)']}
        style={{
          flex: 1,
          paddingTop: insets.top + (Platform.OS === 'ios' ? 0 : 10),
        }}
      >
        <FocusAwareStatusBar barStyle={'light-content'} />
        <FlatList
          style={[appStyles.flex, {paddingHorizontal: 8}]}
          contentContainerStyle={{paddingBottom: 63.5}}
          showsHorizontalScrollIndicator={false}
          onEndReachedThreshold={0.9}
          data={dataGear}
          renderItem={renderVideos}
          keyExtractor={item => item.id}
          ListHeaderComponent={renderHeader()}
          ListFooterComponent={renderFooter()}
          onEndReached={() => {
            debounceLoadMore();
          }}
        />
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  image: {
    width: wp(46),
    aspectRatio: 1.79,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    marginRight: 8,
  },
  dateText: {
    letterSpacing: 1.62,
    textTransform: 'uppercase',
    color: 'rgba(179, 179, 179, 1)',
  },
  tileContainer: {
    marginBottom: 8,
    padding: 4,
    borderRadius: 16,
    backgroundColor: 'rgba(51,51,51,1)',
    alignItems: 'center',
  },
  buttonBack: {
    paddingLeft: 8,
    paddingRight: 10,
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textHeaderTitle: {
    letterSpacing: 1.1,
    textTransform: 'uppercase',
    marginTop: -2,
  },
});

export default GearDeepDives;
