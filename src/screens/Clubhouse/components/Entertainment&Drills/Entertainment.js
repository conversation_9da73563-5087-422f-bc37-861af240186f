import React, {useState, useRef, useImperativeHandle, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  StyleSheet,
  Platform,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import Text from 'components/Text';
import {
  heightPercentageToDP,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {
  getEntertainmentList,
  getPlayerCmsTags,
  getTourPlayers,
  getGearList,
} from 'requests/content';
import appStyles from 'styles/global';
import moment from 'moment';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import {t} from 'i18next';
import {articlePressed, getThumbnail, isVideoArticle} from 'utils/article';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {uniqBy} from 'lodash';
import CustomImage from 'components/CustomImage/CustomImage';
import {
  BOTTOM_BAR_REAL_HEIGHT,
  CACHE_KEY,
  GA_EVENT_NAME,
  NOT_APPLICABLE,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_TYPES,
} from 'utils/constant';
import {GA_logEvent} from 'utils/googleAnalytics';
import {updateEntertainmentData, updateGearListData, updatePlayerProfileData} from 'reducers/dataCache';
import {cloneDeep} from 'lodash';

const Entertainment = ({onFocus}, ref) => {
  const [data, setData] = useState([]);
  const [page, setPage] = useState(1);
  const [totalPageCount, setTotalPageCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [loadingPlayer, setLoadingPlayer] = useState(false);
  const [loadMore, setLoadMore] = useState(false);

  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const navigation = useNavigation();
  const [isFixedHeight, setFixedHeight] = useState(true);
  const [dataPlayer, setDataPlayer] = useState([]);

  const [dataGear, setDataGear] = useState([]);
  const tabBarheight = useBottomTabBarHeight();
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const entertainmentDataCache = useSelector(state => state.dataCache?.entertainment);
  const gearListCache = useSelector(state => state.dataCache?.gearList);
  const playerProfileCache = useSelector(
    state => state.dataCache?.playerProfile,
  );
  const currentCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.CLUB_PLAYER_PROFILE,
  )?.version;
  const dispatch = useDispatch();

  useImperativeHandle(ref, () => ({
    refreshData: () => {
      loadAllData();
    },
    fixHeight: () => {
      setFixedHeight(true);
    },
    removeFixHeight: () => {
      setFixedHeight(false);
    },
  }));

  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    getData();
    getDataGear();
  };

  useFocusEffect(
    React.useCallback(() => {
      onFocus?.();
    }, []),
  );

  useEffect(() => {
    if (appCacheVersions) {
      refreshDataPlayerIfNeeded();
    }
  }, [appCacheVersions]);

  useEffect(() => {
    (async () => {
      try {
        if (playerProfileCache?.data?.length > 0) {
          const getTags = await getPlayerCmsTags(userCountry);
          const playerTags = getTags?.data;
          const playerProfileData = cloneDeep(playerProfileCache?.data);
          playerProfileData.forEach(item => {
            const matchedTag = playerTags.find(tag => tag.title === item.title);
            item.slug = matchedTag?.slug || null;
          });
          setDataPlayer(playerProfileData?.filter?.(item => item.slug));
        } else {
          setDataPlayer([]);
        }
      } catch (error) {
        console.log('error', error.message);
      }
    })();
  }, [playerProfileCache]);

  const refreshDataPlayerIfNeeded = async () => {
    try {
      if (
        currentCacheVersion === playerProfileCache?.version &&
        appCacheVersions?.country === playerProfileCache?.country &&
        currentCacheVersion != null
      ) {
        return;
      } else {
        setLoadingPlayer(true);
        const getPlayers = await getTourPlayers(userCountry);
        dispatch(
          updatePlayerProfileData({
            country: userCountry,
            version: currentCacheVersion,
            data: getPlayers?.widgets || [],
          }),
        );
      }
    } catch (error) {
    } finally {
      setLoadingPlayer(false);
    }
  };

  const getData = async () => {
    try {
      setPage(1);
      if (entertainmentDataCache?.data?.length > 0) {
        const dataResponse = entertainmentDataCache?.data;
        setData(dataResponse);
      } else {
        setLoading(true);
      }
      const response = await getEntertainmentList();
      if (response?.data?.length > 0) {
        const dataResponse = response?.data;
        dispatch(updateEntertainmentData({
          data: dataResponse,
        }));
        setData(dataResponse);
        setTotalPageCount(response?.meta?.pagination?.total_pages);
      } else {
        setData([]);
        setTotalPageCount(0);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getDataGear = async () => {
    try {
      if (gearListCache?.data?.length > 0) {
        const dataResponse = gearListCache?.data;
        setDataGear(dataResponse?.slice?.(0, 3));
      }
      const response = await getGearList();
      if (response?.data?.length > 0) {
        const dataResponse = response?.data;
        dispatch(updateGearListData({
          data: dataResponse,
        }));
        setDataGear(dataResponse?.slice?.(0, 3));
      } else {
        setDataGear([]);
      }
    } catch (error) {
    } finally {
    }
  };

  const onLoadMore = async () => {
    try {
      if (page + 1 > totalPageCount || loadMore) {
        return;
      }
      GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
        screen_type: SCREEN_TYPES.CLUBHOUSE,
        page_name: PAGE_NAME.CLUBHOUSE_ENTERTAINMENT_INFO,
        page_type: SCREEN_TYPES.CLUBHOUSE,
        page_category: PAGE_CATEGORY.CLUBHOUSE_ENTERTAINMENT,
        nav_type: 'clubhouse',
        nav_item_selected: 'load more articles',
        nav_level: 'entertainment',
      });
      setLoadMore(true);
      const response = await getEntertainmentList(page + 1);
      if (response?.data?.length > 0) {
        const dataResponse = response?.data;
        setData(uniqBy([...data, ...dataResponse], 'title'));
        setPage(page + 1);
      } else {
        //remain the data
      }
    } catch (error) {
    } finally {
      setLoadMore(false);
    }
  };

  const onPlayerPress = item => {
    GA_logEvent(GA_EVENT_NAME.SORTING_CONTENT, {
      sort_by: 'player',
      sort_value: item?.title,
      content_type: 'article',
      content_category: NOT_APPLICABLE,
      content_name: NOT_APPLICABLE,
      click_location: 'find-articles-by-player',
      screen_type: SCREEN_TYPES.CLUBHOUSE,
      page_name: PAGE_NAME.CLUBHOUSE_ENTERTAINMENT_INFO,
      page_type: SCREEN_TYPES.CLUBHOUSE,
      page_category: PAGE_CATEGORY.CLUBHOUSE_ENTERTAINMENT,
    });
    navigation.navigate('EntertainmentStack', {
      screen: 'ListArticlesByPlayer',
      params: {item},
    });
  };

  const onPressArticle = async item => {
    articlePressed(navigation.navigate, item, 'clubhouse-entertainment');
  };

  const onPressSeeAll = () => {
    navigation.navigate('GearStack', {
      screen: 'GearDeepDives',
    });
  };

  const renderEntertainmentTile = ({item, index}) => {
    const thumbnail = getThumbnail(item);
    return (
      <TouchableOpacity
        delayPressIn={100}
        delayPressOut={100}
        activeOpacity={0.8}
        key={item.id}
        style={[styles.tileContainer, appStyles.viewShadow]}
        onPress={() => onPressArticle(item)}
      >
        <View style={styles.innerView}>
          <CustomImage
            source={{uri: thumbnail, fallbackUri: item.primaryImageSecondary}}
            style={styles.image}
          />
          <View style={styles.contentView}>
            <Text Din79Font size={12} weight={700} style={styles.dateText}>
              {moment(item.postDate).format('MM.DD.YY')}
            </Text>
            <Text
              size={16}
              weight={700}
              white
              style={styles.titleText}
              numberOfLines={3}
            >
              {item.title}
            </Text>
            <View style={styles.buttonWatchNow}>
              <Text
                Din79Font
                size={12}
                weight={700}
                style={{letterSpacing: 1.62, textTransform: 'uppercase'}}
                white
              >
                {isVideoArticle(item)
                  ? t('clubhouse.entertainment.watch_now')
                  : t('clubhouse.entertainment.read_more')}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderItemPlayer = ({item, index}) => {
    return (
      <TouchableOpacity
        delayPressIn={100}
        delayPressOut={100}
        activeOpacity={0.8}
        onPress={() => onPlayerPress(item)}
        style={[styles.itemPlayer, appStyles.viewShadowLightBig]}
      >
        <CustomImage
          style={styles.itemImage}
          source={{uri: item.imageLink || item.headshot}}
        />
        <View style={{marginHorizontal: 4}}>
          <Text style={[styles.copyText]} white size={16} numberOfLines={2}>
            {item?.title}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderFindArticlesByPlayer = () => {
    if (dataPlayer.length <= 0 && !loadingPlayer) {
      return null;
    }
    if (dataPlayer.length <= 0 && loadingPlayer) {
      return (
        <View style={{flexDirection: 'row', marginTop: 24, marginBottom: 10}}>
          {Array(3)
            .fill()
            .map(item => {
              return (
                <ShimmerPlaceholder
                  LinearGradient={LinearGradient}
                  width={wp(41) - 16}
                  height={(wp(41) - 16) * 1.67}
                  style={{borderRadius: 16, marginRight: 8}}
                />
              );
            })}
        </View>
      );
    }
    return (
      <FlatList
        data={dataPlayer}
        horizontal
        keyExtractor={(item, index) => item?.id}
        renderItem={renderItemPlayer}
        style={{width: wp(100)}}
        contentContainerStyle={{paddingRight: 8}}
        showsHorizontalScrollIndicator={false}
      />
    );
  };

  const renderTilePlaceHolder = () => {
    return (
      <View style={[styles.tileContainer, appStyles.viewShadow]}>
        <View style={styles.innerView}>
          <ShimmerPlaceholder
            LinearGradient={LinearGradient}
            width={wp(100) - 26}
            height={(wp(100) - 26) / 1.77}
            style={{
              borderTopLeftRadius: 12,
              borderTopRightRadius: 12,
            }}
          />
          <View style={styles.contentView}>
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={wp(20)}
              height={10}
              style={{borderRadius: 8}}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={wp(90)}
              height={22}
              style={{borderRadius: 8, marginVertical: 8}}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={wp(30)}
              height={40}
              style={{borderRadius: 8}}
            />
          </View>
        </View>
      </View>
    );
  };

  const renderListArticles = () => {
    if (data.length <= 0 && !loading) {
      return null;
    }
    if (data.length <= 0 && loading) {
      return (
        <>
          {Array(3)
            .fill()
            .map(item => {
              return renderTilePlaceHolder();
            })}
        </>
      );
    }
    return (
      <>
        <FlatList
          style={appStyles.flex}
          data={data}
          renderItem={renderEntertainmentTile}
          keyExtractor={item => item.id}
          contentContainerStyle={{}}
        />
        {page + 1 <= totalPageCount && (
          <TouchableOpacity
            style={[styles.buttonLoadMore, appStyles.viewShadow]}
            onPress={onLoadMore}
            disabled={loadMore}
          >
            {loadMore ? (
              <ActivityIndicator color={'white'} />
            ) : (
              <Text
                style={{letterSpacing: 1.62, textTransform: 'uppercase'}}
                Din79Font
                size={12}
                weight={700}
                white
              >
                clubhouse.entertainment.load_more_articles
              </Text>
            )}
          </TouchableOpacity>
        )}
      </>
    );
  };

  const renderItemGear = ({item, index}) => {
    return (
      <TouchableOpacity
        delayPressIn={100}
        delayPressOut={100}
        activeOpacity={0.8}
        key={item.id}
        style={[
          styles.tileContainer,
          appStyles.viewShadow,
          {marginRight: 8, width: wp(86) + 8},
        ]}
        onPress={() => onPressArticle(item)}
      >
        <View style={styles.innerView}>
          <CustomImage
            source={{
              uri: item.primaryImage,
              fallbackUri: item.primaryImageSecondary,
            }}
            style={styles.imageGear}
          />
          <View style={styles.contentView}>
            <Text Din79Font size={12} weight={700} style={styles.dateText}>
              {moment(item.postDate).format('MM.DD.YY')}
            </Text>
            <Text
              size={16}
              weight={700}
              white
              style={styles.titleText}
              numberOfLines={3}
            >
              {item.title}
            </Text>
            <View style={[styles.buttonWatchNow]}>
              <Text
                Din79Font
                size={12}
                weight={700}
                style={{letterSpacing: 1.62, textTransform: 'uppercase'}}
                white
              >
                {isVideoArticle(item)
                  ? t('clubhouse.entertainment.watch_now')
                  : t('clubhouse.entertainment.read_more')}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderGearDeepDives = () => {
    return (
      <View>
        <View
          style={[
            {
              flexDirection: 'row',
              marginHorizontal: 8,
              marginBottom: 10,
              marginTop: 16,
              alignItems: 'center',
            },
            appStyles.spaceBetween,
          ]}
        >
          <Text size={16} white>
            entertainment.gear.gear_deep_dives
          </Text>
          <TouchableOpacity
            style={[
              {justifyContent: 'center', alignItems: 'center'},
              appStyles.row,
            ]}
            onPress={onPressSeeAll}
          >
            <Text size={16} white style={{fontWeight: '700', marginRight: 8}}>
              entertainment.gear.see_all
            </Text>
            <MaterialIcons name="arrow-forward" color={'white'} size={20} />
          </TouchableOpacity>
        </View>
        <FlatList
          data={dataGear}
          horizontal
          keyExtractor={(item, index) => item?.id}
          renderItem={renderItemGear}
          style={{width: wp(100)}}
          contentContainerStyle={{paddingRight: 8}}
          showsHorizontalScrollIndicator={false}
        />
      </View>
    );
  };
  return (
    <View
      style={{
        paddingHorizontal: 8,
        height: isFixedHeight ? heightPercentageToDP(50) : undefined,
        paddingBottom: BOTTOM_BAR_REAL_HEIGHT + 30,
      }}
    >
      {renderListArticles()}
      <View style={{marginTop: 24, marginBottom: 24}}>
        <Text style={{marginBottom: 9, marginHorizontal: 8}} white size={16}>
          {t('clubhouse.entertainment.find_articles_by_player')}
        </Text>
        {renderFindArticlesByPlayer()}
      </View>
      {dataGear?.length > 0 && renderGearDeepDives()}
    </View>
  );
};

const styles = StyleSheet.create({
  tileContainer: {
    marginBottom: 16,
    borderRadius: 16,
    backgroundColor: 'rgba(51,51,51,1)',
  },
  innerView: {
    width: '100%',
    paddingVertical: 4,
    overflow: 'hidden',
    alignItems: 'center',
  },
  image: {
    width: wp(100) - 26,
    aspectRatio: 1.77,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  imageGear: {
    width: wp(86),
    aspectRatio: 1.77,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  contentView: {
    width: '100%',
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 8,
  },
  dateText: {
    letterSpacing: 1.62,
    textTransform: 'uppercase',
    color: 'rgba(179, 179, 179, 1)',
  },
  buttonWatchNow: {
    alignSelf: 'flex-start',
  },
  titleText: {lineHeight: 18, paddingTop: 10, paddingBottom: 8},
  buttonLoadMore: {
    backgroundColor: 'rgba(77,77,77,1)',
    alignItems: 'center',
    borderRadius: 24,
    paddingVertical: 14,
    marginBottom: 16,
  },
  itemPlayer: {
    width: wp(43),
    marginRight: 8,
    flexDirection: 'column',
    borderRadius: 16,
    backgroundColor: 'rgba(51,51,51,1)',
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 16,
  },
  itemImage: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    aspectRatio: 0.75,
    width: wp(43) - 8,
    marginBottom: 14,
  },
  copyText: {
    fontWeight: '700',
    marginBottom: 24,
    lineHeight: 18,
  },
});

export default React.forwardRef(Entertainment);
