import {useFocusEffect} from '@react-navigation/native';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import useAppState from 'hooks/useAppState';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  ScrollView,
  Platform,
  Image,
  TouchableOpacity,
  FlatList,
  StyleSheet,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import appStyles from 'styles/global';
import {getArticlesByPlayer} from 'requests/content';
import Text from 'components/Text';
import {
  heightPercentageToDP,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import moment from 'moment';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import {t} from 'i18next';
import {lowerCase} from 'lodash';
import BackButton from 'assets/imgs/clubhouse/btn_back.png';
import {articlePressed, getThumbnail, isVideoArticle} from 'utils/article';
import debounce from 'lodash.debounce';
import {uniqBy} from 'lodash';
import CustomImage from 'components/CustomImage/CustomImage';
import {useSelector} from 'react-redux';

const ListArticlesByPlayer = ({navigation, route}) => {
  const playerItem = route.params?.item;
  const user = useSelector(state => state?.user);
  const country = user?.userCountry;
  const [page, setPage] = useState(1);
  const [totalPageCount, setTotalPageCount] = useState(1);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [dataArticles, setDataArticles] = useState([]);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    reloadData();
  }, []);

  const getPlayerSlug = player => {
    if (player?.slug) {
      return player?.slug;
    } else {
      let playerName = player.title;
      return lowerCase(playerName)?.replace?.(' ', '-');
    }
  };

  const reloadData = async () => {
    try {
      setLoading(true);
      const response = await getArticlesByPlayer(
        getPlayerSlug(playerItem),
        page,
        country,
      );
      if (response?.data?.length > 0) {
        const dataResponse = response?.data;
        setTotalPageCount(response?.meta?.pagination?.total_pages);
        setDataArticles(dataResponse);
      } else {
        setDataArticles([]);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const loadMoreData = async () => {
    try {
      if (loadingMore) return;
      if (page + 1 > totalPageCount) {
        return;
      }
      setLoadingMore(true);
      const response = await getArticlesByPlayer(
        getPlayerSlug(playerItem),
        page + 1,
         country
      );
      if (response?.data?.length > 0) {
        const dataResponse = response?.data;
        setPage(page + 1);
        setDataArticles(uniqBy([...dataArticles, ...dataResponse], 'title'));
      } else {
        // setDataArticles([]);
      }
    } catch (error) {
    } finally {
      setLoadingMore(false);
    }
  };

  const debounceLoadMore = debounce(() => loadMoreData(), 2000);

  const onPressArticle = async item => {
    articlePressed(
      navigation.navigate,
      item,
      'entertainment-articles-by-player',
    );
  };

  const renderItem = ({item, index}) => {
    const thumbnail = getThumbnail(item);
    return (
      <TouchableOpacity
        key={item.id + item.title}
        style={[styles.tileContainer, appStyles.viewShadow]}
        onPress={() => onPressArticle(item)}
      >
        <View style={styles.innerView}>
          <CustomImage source={{uri: thumbnail}} style={styles.image} />
          <View style={styles.contentView}>
            <Text Din79Font size={12} weight={700} style={styles.dateText}>
              {moment(item.postDate).format('MM.DD.YY')}
            </Text>
            <Text
              size={16}
              weight={700}
              white
              style={styles.titleText}
              numberOfLines={3}
            >
              {item.title}
            </Text>
            <View style={styles.buttonWatchNow}>
              <Text
                Din79Font
                size={12}
                weight={700}
                style={{letterSpacing: 1.62, textTransform: 'uppercase'}}
                white
              >
                {isVideoArticle(item)
                  ? t('clubhouse.entertainment.watch_now')
                  : t('clubhouse.entertainment.read_more')}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderTilePlaceHolder = () => {
    return (
      <View style={[styles.tileContainer, appStyles.viewShadow]}>
        <View style={styles.innerView}>
          <ShimmerPlaceholder
            LinearGradient={LinearGradient}
            width={wp(100) - 26}
            height={(wp(100) - 26) / 1.77}
            style={{
              borderTopLeftRadius: 12,
              borderTopRightRadius: 12,
            }}
          />
          <View style={styles.contentView}>
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={wp(20)}
              height={10}
              style={{borderRadius: 8}}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={wp(90)}
              height={22}
              style={{borderRadius: 8, marginVertical: 8}}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={wp(30)}
              height={40}
              style={{borderRadius: 8}}
            />
          </View>
        </View>
      </View>
    );
  };

  const renderFindArticlesByPlayer = () => {
    return (
      <FlatList
        data={dataArticles}
        keyExtractor={(item, index) => item?.id + item?.title}
        renderItem={renderItem}
        style={{width: wp(100)}}
        contentContainerStyle={{paddingHorizontal: 8, paddingBottom: 73.5}}
        showsHorizontalScrollIndicator={false}
        onEndReachedThreshold={0.9}
        ListHeaderComponent={renderHeader()}
        ListFooterComponent={renderFooter()}
        onEndReached={() => {
          debounceLoadMore();
        }}
      />
    );
  };

  const renderFooter = () => {
    return (
      <>
        {dataArticles.length > 0 &&
          page + 1 <= totalPageCount &&
          Array(1)
            .fill()
            .map(item => {
              return renderTilePlaceHolder();
            })}
      </>
    );
  };

  const renderHeader = () => {
    return (
      <>
        <View
          style={{
            flexDirection: 'row',
            marginBottom: 20,
            alignItems: 'center',
          }}
        >
          <TouchableOpacity
            style={styles.buttonBack}
            onPress={() => {
              navigation.goBack();
            }}
          >
            <Image source={BackButton} style={{width: 24, height: 24}} />
          </TouchableOpacity>
          <Text
            white
            Din79Font
            size={22}
            weight={800}
            style={styles.textHeaderTitle}
          >
            {playerItem?.title}
          </Text>
        </View>
        {loading &&
          Array(3)
            .fill()
            .map(item => {
              return renderTilePlaceHolder();
            })}
      </>
    );
  };

  return (
    <View style={{flex: 1}}>
      <LinearGradient
        start={{x: 1, y: 0}}
        end={{x: 1, y: 1}}
        colors={['rgba(60, 60, 60, 1)', 'rgba(0, 0, 0, 1)']}
        style={{
          flex: 1,
          paddingTop: insets.top + (Platform.OS === 'ios' ? 0 : 10),
        }}
      >
        <FocusAwareStatusBar barStyle={'light-content'} />
        {renderFindArticlesByPlayer()}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  tileContainer: {
    flex: 1,
    marginBottom: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(51,51,51,1)',
  },
  innerView: {
    width: '100%',
    paddingVertical: 4,
    overflow: 'hidden',
    alignItems: 'center',
  },
  image: {
    width: wp(100) - 26,
    aspectRatio: 1.77,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  contentView: {width: '100%', paddingHorizontal: 12, paddingTop: 12, paddingBottom: 8},
  dateText: {
    letterSpacing: 1.62,
    textTransform: 'uppercase',
    color: 'rgba(179, 179, 179, 1)',
  },
  buttonWatchNow: {
    alignSelf: 'flex-start',
  },
  titleText: {lineHeight: 18, paddingTop: 10, paddingBottom: 8},
  buttonBack: {
    paddingLeft: 8,
    paddingRight: 10,
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textHeaderTitle: {
    letterSpacing: 1.1,
    textTransform: 'uppercase',
    marginTop: -2,
  },
});

export default ListArticlesByPlayer;
