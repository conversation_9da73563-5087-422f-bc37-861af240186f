import React, {useState, useImperativeHandle, useEffect} from 'react';
import {View, StyleSheet, TouchableOpacity, Image} from 'react-native';
import Text from 'components/Text';
import {useDispatch, useSelector} from 'react-redux';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Carousel from 'react-native-snap-carousel';
import appStyles from 'styles/global';
import {useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import CustomImage from 'components/CustomImage/CustomImage';
import { getCollectionList } from 'requests/content';
import {updateCollectionListData} from 'reducers/dataCache';

const Collections = ({}, ref) => {
  const [dataCollections, setDataCollections] = useState([]);
  const [loading, setLoading] = useState(false);
  const [alignment, setAlignment] = useState('start');
  const navigation = useNavigation();
  const {navigate} = navigation;
  const dispatch = useDispatch();
  const user = useSelector(state => state.user);
  const collectionListCache = useSelector(state => state.dataCache?.collectionList);
  const userCountry = user?.userCountry;

  useImperativeHandle(ref, () => ({
    refreshData: () => {
      getData();
    },
  }));
  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    try {
      if (collectionListCache.data?.length > 0 && collectionListCache.country === userCountry) {
        const dataResponse = collectionListCache?.data;
        setDataCollections(dataResponse);
      } else {
        setLoading(true);
      }
      const response = await getCollectionList(userCountry);
      if (response?.data?.length > 0) {
        const dataResponse = response?.data;
        dispatch(updateCollectionListData({
          data: dataResponse,
          country: userCountry,
        }));
        setDataCollections(dataResponse);
      } else {
        setDataCollections([]);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const onPressCollection = item => {
    navigation.navigate('DrillsStack', {
      screen: 'CollectionDetail',
      params: {
        title: item?.title?.toUpperCase?.(),
        collectionData: item,
      },
    });
  };

  const renderItem = ({item, index}) => {
    return (
      <View
        key={item.id}
        style={[
          styles.itemWrapper,
          appStyles.viewShadowLightBig,
          alignment === 'start'
            ? {marginLeft: 8, marginRight: 0}
            : {marginRight: 8, marginLeft: 0},
        ]}
      >
        <CustomImage style={styles.itemImage} source={{uri: item.image}} />
        <View style={styles.viewLinear}>
          <View
            style={[
              StyleSheet.absoluteFill,
              {
                overflow: 'hidden',
              },
            ]}
          >
            <Image
              source={{uri: item.image}}
              blurRadius={5}
              style={styles.viewBlur}
              resizeMode="cover"
            />
            <LinearGradient
              colors={[
                'rgba(0, 0, 0, 0)',
                'rgba(0, 0, 0, 0.015)',
                'rgba(0, 0, 0, 0.275)',
              ]}
              style={[StyleSheet.absoluteFill]}
            />
          </View>
          <View
            style={{
              marginHorizontal: 16,
              alignItems: 'flex-start',
              marginTop: 16,
            }}
          >
            <Text
              Din79Font
              style={styles.copyText}
              white
              size={34}
              numberOfLines={2}
            >
              {item?.title}
            </Text>
            <Text
              Din79Font
              style={styles.textDes}
              white
              size={12}
              numberOfLines={2}
            >
              {item?.description}
            </Text>
            <TouchableOpacity
              style={[
                {
                  backgroundColor: '#fff',
                },
                styles.touchWatchNow,
              ]}
              onPress={() => onPressCollection(item)}
            >
              <Text
                Din79Font
                weight={700}
                size={12}
                black
                style={{
                  letterSpacing: 1.2,
                }}
              >
                clubhouse.start_watching
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };
  if (dataCollections.length === 0 && !loading) {
    return null;
  }

  if (dataCollections.length === 0 && loading) {
    return (
      <View style={{marginTop: 8}}>
        <View style={[appStyles.row, {marginBottom: 10}]}>
          <ShimmerPlaceholder
            LinearGradient={LinearGradient}
            width={wp(88)}
            height={wp(90) * 1.28}
            shimmerStyle={{marginHorizontal: 8, borderRadius: 16}}
          />
          <ShimmerPlaceholder
            LinearGradient={LinearGradient}
            width={wp(88)}
            height={wp(90) * 1.28}
            shimmerStyle={{borderRadius: 16}}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={{marginTop: 8}}>
      <View style={{marginHorizontal: 16}}>
        <Text size={16} white>
          clubhouse.collection
        </Text>
      </View>
      <Carousel
        data={dataCollections}
        renderItem={renderItem}
        sliderWidth={wp(100)}
        itemWidth={wp(90)}
        inactiveSlideScale={1}
        inactiveSlideOpacity={1}
        enableMomentum={true}
        decelerationRate={0.9}
        swipeThreshold={40}
        activeSlideAlignment={alignment}
        onBeforeSnapToItem={index => {
          if (index === 0) {
            setAlignment('start');
          } else if (index === dataCollections.length - 1) {
            setAlignment('end');
          }
        }}
        removeClippedSubviews={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  itemWrapper: {
    marginLeft: 8,
    borderRadius: 24,
    marginBottom: 10,
    paddingTop: 10,
  },
  itemImage: {
    borderRadius: 24,
    height: wp(90) * 1.33 - 8,
    width: wp(90) - 8,
  },
  copyText: {
    fontWeight: '800',
    textTransform: 'uppercase',
    letterSpacing: 1.2,
    lineHeight: 33.25,
    marginBottom: 8,
  },
  textDes: {
    fontWeight: '800',
    textTransform: 'uppercase',
    letterSpacing: 1.2,
  },
  viewBlur: {
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    width: wp(90) - 8,
    height: wp(90) * 1.33 - 8,
    position: 'absolute',
    bottom: 0,
  },
  viewLinear: {
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    overflow: 'hidden',
    zIndex: 99,
  },
  touchWatchNow: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 40,
    marginVertical: 16,
  },
});

export default React.forwardRef(Collections);
