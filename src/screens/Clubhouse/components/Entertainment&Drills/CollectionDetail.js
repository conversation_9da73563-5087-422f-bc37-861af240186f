import React, {useEffect, useState} from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Platform,
  ScrollView,
  Image,
  TouchableOpacity,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {connect, useSelector} from 'react-redux';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import LinearGradient from 'react-native-linear-gradient';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {drillPressed} from 'utils/article';
import ImageDrill from '../ImageDrill';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {getCollectionDetail} from 'requests/content';
import CustomImage from 'components/CustomImage/CustomImage';

const CollectionDetail = ({navigation, route}) => {
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;

  const collectionData = route.params?.collectionData;
  const isTablet = DeviceInfo.isTablet();
  const [loading, setLoading] = useState();
  const insets = useSafeAreaInsets();
  const [videoList, setVideoList] = useState([]);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const contentVideos = await getCollectionDetail(
          collectionData?.id,
          userCountry,
        );
        if (contentVideos.data?.videos?.length) {
          setVideoList(contentVideos.data?.videos);
        } else {
          setVideoList([]);
        }
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('drills.browse.working_on_our_swing'),
          subText: t('drills.collections.videos_ready_soon'),
        });
      }
    })();
  }, []);
  const onPressDrill = async item => {
    drillPressed(
      navigation.navigate,
      item,
      'DrillsCollections',
      'drill-collections',
    );
  };
  const renderVideos = ({item, index}) => (
    <TouchableWithoutFeedback style={[]} onPress={() => onPressDrill(item)}>
      <View
        style={[
          appStyles.flex,
          appStyles.viewShadow,
          appStyles.row,
          styles.tileContainer,
        ]}
      >
        <ImageDrill drillData={item} style={styles.image} />
        <View style={[appStyles.flex, {paddingHorizontal: 8}]}>
          {item?.tags?.mytmInstructor?.[0]?.title && (
            <Text Din79Font size={12} weight={700} style={styles.coachText}>
              {item?.tags?.mytmInstructor?.[0]?.title}
            </Text>
          )}
          <Text
            size={16}
            weight={700}
            white
            style={{lineHeight: 18, marginVertical: 8}}
            numberOfLines={3}
          >
            {item.title}
          </Text>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );

  const renderHeader = () => {
    return (
      <View
        style={{
          overflow: 'hidden',
        }}
      >
        <CustomImage
          style={styles.headerImage}
          source={{uri: collectionData?.image}}
        />
        <View style={{marginTop: 12, marginBottom: 14, paddingHorizontal: 8}}>
          <Text
            Din79Font
            style={styles.copyText}
            white
            size={22}
            numberOfLines={2}
          >
            {collectionData?.title}
          </Text>
          <Text
            Din79Font
            style={styles.textDes}
            white
            size={12}
            numberOfLines={2}
          >
            {collectionData?.description}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={{flex: 1}}>
      <LinearGradient
        start={{x: 1, y: 0}}
        end={{x: 1, y: 1}}
        colors={['rgba(60, 60, 60, 1)', 'rgba(0, 0, 0, 1)']}
        style={{
          flex: 1,
        }}
      >
        <FocusAwareStatusBar barStyle={'light-content'} />
        <TouchableOpacity
          style={[
            styles.closeButton,
            {
              top: insets.top + (Platform.OS === 'ios' ? -10 : 10),
            },
          ]}
          onPress={() => navigation.goBack()}
        >
          <View
            style={{
              borderRadius: 24,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              padding: 1,
            }}
          >
            <Icon name="close" size={23} color={'#fff'} />
          </View>
        </TouchableOpacity>
        <ScrollView showsVerticalScrollIndicator={false}>
          {renderHeader()}
          {loading ? (
            <ActivityIndicator style={appStyles.pTLg} color="white" />
          ) : (
            <FlatList
              style={[
                appStyles.flex,
                {paddingHorizontal: 8, paddingBottom: 63.5, marginTop: 8},
              ]}
              data={videoList}
              renderItem={renderVideos}
              keyExtractor={item => item.id}
            />
          )}
        </ScrollView>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  image: {
    width: wp(46),
    aspectRatio: 1.79,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    marginRight: 8,
  },
  coachText: {
    letterSpacing: 1.62,
    textTransform: 'uppercase',
    color: 'rgba(179, 179, 179, 1)',
  },
  tileContainer: {
    marginBottom: 8,
    padding: 4,
    borderRadius: 16,
    backgroundColor: 'rgba(51,51,51,1)',
    alignItems: 'center',
  },
  headerImage: {
    height: wp(100) * 1.17,
    width: wp(100),
    marginBottom: 8,
  },
  copyText: {
    fontWeight: '800',
    textTransform: 'uppercase',
    letterSpacing: 22 * 0.05,
    marginBottom: 18,
    paddingHorizontal: 8,
  },
  textDes: {
    fontWeight: '700',
    textTransform: 'uppercase',
    letterSpacing: 0.135 * 12,
    paddingHorizontal: 8,
    maxWidth: wp(62.3),
  },
  closeButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderRadius: 24,
    position: 'absolute',
    right: 6,
    zIndex: 9999,
  },
});

export default CollectionDetail;
