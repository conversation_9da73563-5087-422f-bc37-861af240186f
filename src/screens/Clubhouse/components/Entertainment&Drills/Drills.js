import React, {useState, useRef, useImperativeHandle, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  StyleSheet,
  Platform,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import Text from 'components/Text';
import {
  heightPercentageToDP,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import appStyles from 'styles/global';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import {
  getBrowseCategories,
  getCoachProfiles,
  getForYouContent,
} from 'requests/drills';
import CoachProfile from './CoachProfile';
import analytics from '@react-native-firebase/analytics';
import {drillPressed} from 'utils/article';
import ImageDrill from '../ImageDrill';
import Collections from './Collections';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {t} from 'i18next';
import {
  BOTTOM_BAR_REAL_HEIGHT,
  CACHE_KEY,
  NOT_APPLICABLE,
} from 'utils/constant';
import {
  SCREEN_TYPES,
  GA_EVENT_NAME,
  PAGE_CATEGORY,
  PAGE_NAME,
} from 'utils/constant';
import {updateLoyaltyData} from 'reducers/loyalty';
import {GA_logEvent} from 'utils/googleAnalytics';
import {updateCoachProfileData, updateDrillVideosData} from 'reducers/dataCache';

const Drills = ({onFocus}, ref) => {
  const [data, setData] = useState([]);
  const [coachData, setCoachData] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingCategory, setLoadingCategory] = useState(false);
  const [loadingCoachProfile, setLoadingCoachProfile] = useState(false);
  const [foryouContent, setForyouContent] = useState({});
  const tabBarheight = useBottomTabBarHeight();

  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const navigation = useNavigation();
  const [isFixedHeight, setFixedHeight] = useState(true);
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const coachProfileCache = useSelector(state => state.dataCache?.coachProfile);
  const drillsForYouCache = useSelector(state => state.dataCache?.drillVideos);
  const currentCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.CLUB_COACH_PROFILE,
  )?.version;
  const dispatch = useDispatch();

  useEffect(() => {
    if (appCacheVersions) {
      refreshCoachProfileIfNeeded();
    }
  }, [appCacheVersions]);

  useImperativeHandle(ref, () => ({
    refreshData: () => {
      loadAllData();
    },
    fixHeight: () => {
      setFixedHeight(true);
    },
    removeFixHeight: () => {
      setFixedHeight(false);
    },
  }));

  useEffect(() => {
    loadAllData();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      onFocus?.();
    }, []),
  );

  const loadAllData = () => {
    getDataForYou();
    getCategory();
  };

  useEffect(() => {
    if (coachProfileCache?.data?.length > 0) {
      setCoachData(coachProfileCache?.data);
    } else {
      setCoachData([]);
    }
  }, [coachProfileCache]);

  const refreshCoachProfileIfNeeded = async () => {
    try {
      if (
        currentCacheVersion === coachProfileCache?.version &&
        appCacheVersions?.country === coachProfileCache?.country &&
        currentCacheVersion != null
      ) {
        return;
      } else {
        setLoadingCoachProfile(true);
        const coachProfiles = await getCoachProfiles(30);
        dispatch(
          updateCoachProfileData({
            country: userCountry,
            version: currentCacheVersion,
            data: coachProfiles,
          }),
        );
      }
    } catch (error) {
    } finally {
      setLoadingCoachProfile(false);
    }
  };

  const getDataForYou = async () => {
    try {
      if (drillsForYouCache?.data?.length > 0) {
        const drillsArr = drillsForYouCache?.data?.reduce(
          (total, item) => total.concat(item?.data),
          [],
        );
        setForyouContent(drillsForYouCache?.data);
        if (drillsArr.length > 0) {
          setData(drillsArr);
        }
      } else {
        setLoading(true);
      }

      const forYouContent = await getForYouContent(playService);
      const drillsArray = forYouContent?.reduce(
        (total, item) => total.concat(item?.data),
        [],
      );
      setForyouContent(forYouContent);
      if (drillsArray.length > 0) {
        const dataResponse = drillsArray;
        setData(dataResponse);
        dispatch(updateDrillVideosData({
          data: forYouContent,
        }));
      } else {
        setData([]);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getCategory = async () => {
    try {
      setLoadingCategory(true);
      let browseCategories = await getBrowseCategories();
      if (browseCategories.length > 0) {
        browseCategories = browseCategories.map(val => {
          if (val.slug && val.slug === 'sftw') {
            val.title = 's.f.t.w.';
          }
          return val;
        });
        const validCategories = browseCategories?.filter?.(
          item => item.title !== '',
        );
        if (
          validCategories.length === 1 &&
          validCategories[0]?.slug === 'sftw'
        ) {
          setCategories([]);
        } else {
          setCategories(validCategories);
        }
      } else {
        setCategories([]);
      }
    } catch (error) {
    } finally {
      setLoadingCategory(false);
    }
  };

  const onPressDrill = async item => {
    drillPressed(
      navigation.navigate,
      item,
      'DrillsForYou',
      'clubhouse-drills-for-you',
    );
  };

  const renderDrillsTile = ({item, index}) => {
    return (
      <TouchableOpacity
        delayPressIn={100}
        delayPressOut={100}
        activeOpacity={0.8}
        onPress={() => onPressDrill(item)}
        key={item.id}
        style={[styles.tileContainer, appStyles.viewShadow]}
      >
        <View style={styles.innerView}>
          <ImageDrill drillData={item} style={styles.image} />
          <View style={styles.contentView}>
            {item?.tags?.mytmInstructor?.[0]?.title && (
              <Text Din79Font size={12} weight={700} style={styles.coachText}>
                {item?.tags?.mytmInstructor?.[0]?.title}
              </Text>
            )}
            <Text
              size={16}
              weight={700}
              white
              style={styles.titleText}
              numberOfLines={3}
            >
              {item.title}
            </Text>
            <View style={styles.buttonWatchNow}>
              <Text
                Din79Font
                size={12}
                weight={700}
                style={{letterSpacing: 1.62, textTransform: 'uppercase'}}
                white
              >
                clubhouse.drills.watch_now
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const pressCategory = async item => {
    let title = item?.title;
    if (!title && item.slug === 'sftw') {
      title = 'S.F.T.W.';
    }
    GA_logEvent(GA_EVENT_NAME.SORTING_CONTENT, {
      sort_by: 'category',
      sort_value: title,
      content_type: 'video',
      content_category: title,
      content_name: NOT_APPLICABLE,
      click_location: 'sort-by-category',
      page_type: SCREEN_TYPES.CLUBHOUSE,
      page_category: PAGE_CATEGORY.CLUBHOUSE_DRILL,
      page_name: PAGE_NAME.CLUBHOUSE_DRILL_INFO,
      screen_type: SCREEN_TYPES.CLUBHOUSE,
    });
    navigation.navigate('DrillsStack', {
      screen: 'DrillsByCategory',
      params: {
        title: title,
        origin: 'collection',
        tags: item.slug,
      },
    });
  };

  const renderCategory = () => {
    return (
      <View style={{marginBottom: 20}}>
        <View style={styles.textCategory}>
          <Text size={16} white style={{marginBottom: 10, fontWeight: '400'}}>
            clubhouse.drills.category
          </Text>
        </View>
        <View style={styles.hashtagContainer}>
          {categories?.length > 0 &&
            categories.map((val, index) => (
              <TouchableOpacity
                key={index}
                style={styles.hashtagView}
                onPress={() => pressCategory(val)}
              >
                <Text Din79Font style={[styles.hashtag]}>
                  {val?.title}
                </Text>
              </TouchableOpacity>
            ))}
        </View>
      </View>
    );
  };
  const renderTilePlaceHolder = () => {
    return (
      <View style={[styles.tileContainer, appStyles.viewShadow]}>
        <View style={styles.innerView}>
          <ShimmerPlaceholder
            LinearGradient={LinearGradient}
            width={wp(100) - 26}
            height={(wp(100) - 26) / 1.77}
            style={{
              borderTopLeftRadius: 12,
              borderTopRightRadius: 12,
            }}
          />
          <View style={styles.contentView}>
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={wp(20)}
              height={10}
              style={{borderRadius: 8}}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={wp(90)}
              height={22}
              style={{borderRadius: 8, marginVertical: 8}}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={wp(30)}
              height={40}
              style={{borderRadius: 8}}
            />
          </View>
        </View>
      </View>
    );
  };
  const renderDrillsForYou = () => {
    if (data?.length <= 0 && !loading) {
      return null;
    }
    if (data?.length <= 0 && loading) {
      return (
        <>
          {Array(3)
            .fill()
            .map(item => {
              return renderTilePlaceHolder();
            })}
        </>
      );
    }
    return (
      <>
        {foryouContent.map(content => {
          return (
            <FlatList
              style={appStyles.flex}
              data={content.data}
              renderItem={renderDrillsTile}
              keyExtractor={item => item.id}
              contentContainerStyle={{
                marginTop: 14,
              }}
              ListHeaderComponent={
                <Text size={16} white style={{marginBottom: 16}}>{`${t(
                  'clubhouse.drills.yourWeeklyClinic',
                )} ${content.category}`}</Text>
              }
            />
          );
        })}
      </>
    );
  };

  return (
    <View
      style={{
        paddingBottom: BOTTOM_BAR_REAL_HEIGHT + 30,
      }}
    >
      <View
        style={{
          height: isFixedHeight ? heightPercentageToDP(50) : undefined,
        }}
      >
        <Collections />
        {categories?.length > 0 && renderCategory()}
        <View style={{paddingHorizontal: 8}}>{renderDrillsForYou()}</View>
      </View>
      <CoachProfile coachData={coachData} navigation={navigation} />
    </View>
  );
};

const styles = StyleSheet.create({
  tileContainer: {
    flex: 1,
    marginBottom: 16,
    borderRadius: 16,
    backgroundColor: 'rgba(51,51,51,1)',
  },
  innerView: {
    width: '100%',
    paddingVertical: 4,
    overflow: 'hidden',
    alignItems: 'center',
  },
  image: {
    width: wp(100) - 26,
    aspectRatio: 1.77,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  contentView: {
    width: '100%',
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 8,
  },
  coachText: {
    letterSpacing: 1.62,
    textTransform: 'uppercase',
    color: 'rgba(179, 179, 179, 1)',
  },
  buttonWatchNow: {
    alignSelf: 'flex-start',
  },
  titleText: {lineHeight: 18, paddingTop: 10, paddingBottom: 8},
  hashtagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 4,
  },
  hashtagView: {
    backgroundColor: '#4d4d4d',
    margin: 4,
    borderRadius: 24,
  },
  hashtag: {
    alignSelf: 'flex-start',
    padding: 16,
    fontSize: 12,
    fontWeight: '700',
    color: 'white',
    textTransform: 'uppercase',
    letterSpacing: 1.62,
    lineHeight: 11.7,
  },
  textCategory: {
    marginHorizontal: 16,
    marginTop: 24,
  },
});

export default React.forwardRef(Drills);
