import React, {useState, useRef, useImperativeHandle} from 'react';
import {View, StyleSheet, Platform} from 'react-native';
import Text from 'components/Text';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {useNavigation} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {createStackNavigator} from '@react-navigation/stack';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import Entertainment from './Entertainment';
import Drills from './Drills';
import {GA_logEvent} from 'utils/googleAnalytics';
import {
  GA_EVENT_NAME,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_TYPES,
} from 'utils/constant';

const EntertainDrillsTabContent = createStackNavigator();
const EntertainDrillsTab = createMaterialTopTabNavigator();

const EntertainmentDrillsSection = ({onLayout}, ref) => {
  const [mode, setMode] = useState('Drills');

  const entertainmentRef = useRef();
  const drillsRef = useRef();

  useImperativeHandle(ref, () => ({
    refreshData: async () => {
      entertainmentRef?.current?.refreshData?.();
      drillsRef?.current?.refreshData?.();
    },
    setFocusTab: tabName => {
      onPressMode(tabName);
    },
  }));

  const onFocusEntertainTab = () => {
    drillsRef?.current?.fixHeight?.();
    entertainmentRef?.current?.removeFixHeight?.();
  };

  const onFocusDrillsTab = () => {
    entertainmentRef?.current?.fixHeight?.();
    drillsRef?.current?.removeFixHeight?.();
  };

  const onPressMode = title => {
    setMode(title);
    if (title === 'Entertainment') {
      GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
        screen_type: SCREEN_TYPES.CLUBHOUSE,
        page_name: PAGE_NAME.CLUBHOUSE_ENTERTAINMENT_INFO,
        page_type: SCREEN_TYPES.CLUBHOUSE,
        page_category: PAGE_CATEGORY.CLUBHOUSE_ENTERTAINMENT,
        nav_type: 'clubhouse',
        nav_item_selected: 'entertainment',
        nav_level: 'entertainment',
      });
    } else {
      GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
        screen_type: SCREEN_TYPES.CLUBHOUSE,
        page_name: PAGE_NAME.CLUBHOUSE_DRILL_INFO,
        page_type: SCREEN_TYPES.CLUBHOUSE,
        page_category: PAGE_CATEGORY.CLUBHOUSE_DRILL,
        nav_type: 'clubhouse',
        nav_item_selected: 'drills',
        nav_level: 'drills',
      });
    }
  };

  const renderMode = (title, disable) => {
    return (
      <EntertainDrillsTabContent.Screen
        name={title}
        key={title}
        children={() => {
          return title === 'Entertainment' ? (
            <Entertainment
              key={'entertainment'}
              ref={entertainmentRef}
              onFocus={onFocusEntertainTab}
            />
          ) : (
            <Drills key={'drills'} ref={drillsRef} onFocus={onFocusDrillsTab} />
          );
        }}
        options={{
          headerShown: false,
          tabBarLabel: ({focused, color, size}) => (
            <Text
              style={{
                ...styles.labelStyle,
                color: disable ? '#00000060' : color,
                textTransform: 'uppercase',
              }}
            >
              {title}
            </Text>
          ),
        }}
        listeners={{
          tabPress: e => {
            if (disable) {
              // Prevent default action
              e.preventDefault();
            } else if (mode !== title) {
              onPressMode(title);
            }
          },
        }}
      />
    );
  };

  return (
    <View
      style={{flex: 1}}
      onLayout={e => {
        onLayout?.(e.nativeEvent.layout.y);
      }}
    >
      <EntertainDrillsTab.Navigator
        tabBarOptions={{
          tabStyle: {
            justifyContent: 'center',
            alignItems: 'center',
          },
          labelStyle: styles.labelStyle,
          indicatorStyle: {
            ...styles.indicatorStyle,
            marginLeft: mode === 'Entertainment' ? 0 : 8,
          },
          style: styles.tabBarStyle,
          inactiveBackgroundColor: '#111111',
          allowFontScaling: false,
          activeTintColor: 'black',
          inactiveTintColor: 'white',
        }}
        initialRouteName={mode}
        sceneContainerStyle={{backgroundColor: 'transparent'}}
        swipeEnabled={false}
      >
        {renderMode('Drills')}
        {renderMode('Entertainment')}
      </EntertainDrillsTab.Navigator>
    </View>
  );
};

const styles = StyleSheet.create({
  labelStyle: {
    marginTop: 7,
    alignItems: 'center',
    color: 'rgba(17, 17, 17, 1)',
    textAlign: 'center',
    fontWeight: '700',
    fontSize: 12,
    letterSpacing: 1.62,
    fontFamily: Platform.select({
      android: 'DINNext79',
      ios: 'DIN Next 79',
    }),
  },
  tabBarStyle: {
    borderRadius: 24,
    height: 56,
    backgroundColor: '#6B6B6B',
    marginHorizontal: 8,
    marginVertical: 15,
    elevation: 0,
  },
  indicatorStyle: {
    height: 40,
    width: (wp(100) - 32) / 2,
    marginBottom: 8,
    backgroundColor: 'white',
    borderRadius: 20,
    alignSelf: 'center',
  },
});

export default React.forwardRef(EntertainmentDrillsSection);
