import React, {useState, useRef, useImperativeHandle, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import Text from 'components/Text';
import {getJustIn} from 'requests/home';
import LinearGradient from 'react-native-linear-gradient';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {getTourorStories} from 'requests/club-house';
import CustomImage from 'components/CustomImage/CustomImage';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image/src';
import {
  clearHistoryTourorStories,
  getHistoryTourorStories,
  saveHistoryTourorStories,
} from 'utils/asyncStorage';
import {preloadImages} from 'utils/image';
import {CACHE_KEY} from 'utils/constant';
import {updateJustInTileData, updateTourStoryData} from 'reducers/dataCache';
import {cloneDeep} from 'lodash';

const JustIn = ({}, ref) => {
  const [data, setData] = useState(null);
  const [dataStories, setDataStories] = useState(null);
  const [loading, setLoading] = useState(false);

  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const navigation = useNavigation();
  const indexPageRef = useRef(0);
  const justInDataCache = useSelector(state => state.dataCache?.justInTile);
  const tourStoryDataCache = useSelector(state => state.dataCache?.tourStory);
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const currentJustInCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.CLUB_JUST_IN,
  )?.version;
  const currentTourStoryCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.CLUB_TOUR_STORIES,
  )?.version;
  const dispatch = useDispatch();

  useEffect(() => {
    if (appCacheVersions) {
      refreshJustInDataIfNeeded();
      refreshTourStoryDataIfNeeded();
    }
  }, [appCacheVersions]);

  const refreshJustInDataIfNeeded = async () => {
    try {
      if (
        currentJustInCacheVersion === justInDataCache?.version &&
        appCacheVersions?.country === justInDataCache?.country &&
        currentJustInCacheVersion != null
      ) {
        return;
      } else {
        const params = {
          page: 1,
          take: 30,
          country: userCountry,
        };
        try {
          const justInResponse = await getJustIn(params);
          dispatch(
            updateJustInTileData({
              country: userCountry,
              version: currentJustInCacheVersion,
              data: justInResponse?.widgets || [],
            }),
          );
        } catch (error) {}
      }
    } catch (error) {}
  };

  const refreshTourStoryDataIfNeeded = async (options = {}) => {
    try {
      const shouldRefresh =
        options.refreshAfterWatch ||
        currentTourStoryCacheVersion !== tourStoryDataCache?.version ||
        appCacheVersions?.country !== tourStoryDataCache?.country ||
        currentTourStoryCacheVersion == null;

      if (!shouldRefresh) {
        return;
      }
      setLoading(true);
      // Fetch and update tour stories
      const stories = await getTourorStories(userCountry);

      dispatch(
        updateTourStoryData({
          country: userCountry,
          version: currentTourStoryCacheVersion,
          data: stories || {},
        }),
      );
    } catch (error) {
      console.error('Failed to refresh tour story data:', error);
    }
  };

  useEffect(() => {
    getDataJustIn();
  }, [justInDataCache]);

  useEffect(() => {
    getDataTourStory();
  }, [tourStoryDataCache]);

  const getDataJustIn = async () => {
    if (justInDataCache?.data?.length > 0) {
      const dataResponse = justInDataCache?.data?.[0];
      setData({
        ...dataResponse,
        options: JSON.parse(dataResponse?.options),
      });
    } else {
      setData(null);
    }
  };

  useImperativeHandle(ref, () => ({
    refreshData: async () => {
      return await getDataTourStory();
    },
  }));

  const getDataTourStory = async () => {
    let listDownloadImage = [];
    try {
      const stories = cloneDeep(tourStoryDataCache?.data);
      if (stories?.tourStories?.length > 0) {
        setDataStories(stories);
      } else {
        setDataStories(null);
      }
      if (data?.length > 0 && stories?.tourStories?.length > 0) {
        let sortData = stories?.tourStories;
        if (stories?.newStories?.length > 0) {
          const newStories = stories?.newStories.reduce((pre, cur) => {
            return {...pre, [cur]: cur};
          }, {});
          //sort data by newStories. id is new in tourStories so we need move to end
          sortData = stories?.tourStories.sort((a, b) => {
            if (!newStories?.[a?.id] && newStories?.[b?.id]) {
              return -1;
            }
            if (newStories?.[a?.id] && !newStories?.[b?.id]) {
              return 1;
            }
            return 0;
          });
        }
        if (sortData) {
          sortData?.forEach?.(_element => {
            if (_element?.type?.toUpperCase() === 'IMAGE') {
              listDownloadImage = [
                ...listDownloadImage,
                {
                  uri: _element?.url,
                  priority: FastImage.priority.high,
                  id: _element.id,
                },
              ];
            }
          });
        }

        let indexOfNewStory = null;
        if (stories?.newStories?.length > 0) {
          const newStories = stories?.newStories.reduce((pre, cur) => {
            return {...pre, [cur]: cur};
          }, {});
          for (let index = 0; index < listDownloadImage.length; index++) {
            const element = listDownloadImage[index];
            if (newStories?.[element?.id]) {
              indexOfNewStory = index;
              break;
            }
          }
        }
        let callbackCompleteOnce = () => {
          setLoading(false);
        };
        let callbackCompleteAll = () => {
          setLoading(false);
        };
        if (listDownloadImage.length > 0) {
          let imagesToPreload = [...listDownloadImage];
          //If there are new stories, preload the images from the new ones so user can see this these new images immediately
          if (indexOfNewStory >= 0) {
            imagesToPreload = [
              ...listDownloadImage.slice(
                indexOfNewStory,
                listDownloadImage.length,
              ),
              ...listDownloadImage.slice(0, indexOfNewStory),
            ];
          }
          //preload image  when user open the page first time
          preloadImages(
            imagesToPreload,
            0,
            callbackCompleteOnce,
            callbackCompleteAll,
          );
        }
        return true;
      } else {
        return false;
      }
    } catch (error) {
      setDataStories(null);
      setData(null);
      setLoading(false);
      return false;
    } finally {
      if (listDownloadImage.length === 0) {
        setLoading(false);
      }
    }
  };

  const onPressCTA = async () => {
    //navigate to Tourer Stories
    if (dataStories) {
      //This is get current index when user reopen.
      //if has new storie then open from this storie
      //if haven't id in history list then get index at that
      //if have all ids in history list then get index = 0
      const dataTourStories = dataStories?.tourStories;
      const dataNewStories = dataStories?.newStories;
      let sortedStories = [...dataTourStories];
      if (dataNewStories?.length > 0) {
        const newStories = dataNewStories.reduce((pre, cur) => {
          return {...pre, [cur]: cur};
        }, {});
        await clearHistoryTourorStories();
        for (let i = 0; i < dataTourStories.length; i++) {
          if (newStories?.[dataTourStories[i]?.id]) {
            indexPageRef.current = i;
            break;
          } else {
            const idStory = dataTourStories[i]?.id;
            await saveHistoryTourorStories(idStory);
          }
        }
      } else {
        let viewedStories = [];
        let newStories = [];
        const history = await getHistoryTourorStories();
        for (let i = 0; i < dataTourStories.length; i++) {
          if (history?.[dataTourStories[i]?.id]) {
            viewedStories.push(dataTourStories[i]);
          } else {
            newStories.push(dataTourStories[i]);
          }
        }
        sortedStories = [...viewedStories, ...newStories];
        for (let i = 0; i < sortedStories.length; i++) {
          if (!history?.[sortedStories[i]?.id]) {
            indexPageRef.current = i;
            break;
          }
          if (i === sortedStories.length - 1) {
            indexPageRef.current = 0;
            clearHistoryTourorStories();
          }
        }
      }
      const listInfo = sortedStories.reduce((pre, cur, curIndex) => {
        return {...pre, [curIndex]: {hideLoading: false}};
      }, []);
      navigation.navigate('TourorStories', {
        dataStories: sortedStories,
        indexPage: indexPageRef.current,
        listInfo,
        getData: () => {
          refreshTourStoryDataIfNeeded({refreshAfterWatch: true});
        },
      });
    }
  };

  const renderButtonDefault = () => {
    return (
      <TouchableOpacity
        activeOpacity={0.9}
        style={[
          {
            alignSelf: 'center',
            marginTop: 24,
            marginBottom: 24,
            backgroundColor: data?.options?.ctaBackgroundColor || '#fff',
          },
          styles.touchCTA,
        ]}
        onPress={onPressCTA}
        disabled={loading}
      >
        {loading && (
          <ActivityIndicator
            size={'small'}
            color={data?.options?.ctaTextColor || 'white'}
            style={{position: 'absolute', bottom: 0, top: 0, left: 16}}
          />
        )}
        <Text
          Din79Font
          weight={700}
          size={12}
          style={{
            color: data?.options?.ctaTextColor || '#000',
            letterSpacing: 1.62,
            textAlign: 'center',
            textTransform: 'uppercase',
            marginLeft: loading ? 24 : 0,
          }}
        >
          {data?.ctaText}
        </Text>
      </TouchableOpacity>
    );
  };
  const renderButtonHasNewItem = () => {
    return (
      <LinearGradient
        start={{x: 0.25, y: 0.25}}
        end={{x: 0.0, y: 1.0}}
        locations={[0.1, 0.7, 1]}
        useAngle={true}
        angle={185}
        colors={[
          'rgba(255, 0, 0, 1)',
          'rgba(255, 138, 0, 1)',
          'rgba(242, 189, 0, 1)',
        ]}
        style={styles.viewLinearButton}
      >
        <TouchableOpacity
          activeOpacity={0.9}
          style={[
            {
              backgroundColor: data?.options?.ctaBackgroundColor || '#fff',
            },
            styles.touchCTA,
          ]}
          onPress={onPressCTA}
          disabled={loading}
        >
          {loading && (
            <ActivityIndicator
              size={'small'}
              color={data?.options?.ctaTextColor || 'white'}
              style={{position: 'absolute', bottom: 0, top: 0, left: 16}}
            />
          )}
          <Text
            Din79Font
            weight={700}
            size={12}
            style={{
              color: data?.options?.ctaTextColor || '#000',
              letterSpacing: 1.62,
              textAlign: 'center',
              textTransform: 'uppercase',
              marginLeft: loading ? 24 : 0,
            }}
          >
            {data?.ctaText}{' '}
            {dataStories?.totalActive > 0
              ? `(${dataStories?.totalActive} ${t('clubhouse.justin.new')})`
              : ''}
          </Text>
        </TouchableOpacity>
      </LinearGradient>
    );
  };
  if (!data || !dataStories) {
    return null;
  }

  return (
    <View style={styles.container}>
      <CustomImage
        source={{
          uri: data?.imageLink,
        }}
        style={{width: wp(100), height: wp(100) * 1.33}}
      />
      <View style={styles.viewLinear}>
        <View
          style={[
            StyleSheet.absoluteFill,
            {
              overflow: 'hidden',
            },
          ]}
        >
          <LinearGradient
            colors={[
              'rgba(0, 0, 0, 0)',
              'rgba(0, 0, 0, 0.015)',
              'rgba(0, 0, 0, 0.275)',
            ]}
            style={[StyleSheet.absoluteFill]}
          />
        </View>
        <Text
          size={22}
          Din79Font
          weight={800}
          style={{
            color: data?.options?.titleColor || '#fff',
            paddingHorizontal: 16,
            marginTop: 25,
            textAlign: 'center',
            letterSpacing: 1.1,
          }}
        >
          {data?.options?.title}
        </Text>
        {data?.ctaText &&
          (dataStories?.totalActive > 0
            ? renderButtonHasNewItem()
            : renderButtonDefault())}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    width: wp(100),
    height: wp(100) * 1.33,
  },
  touchCTA: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 40,
    flexDirection: 'row',
  },
  viewLinearButton: {
    padding: 4,
    borderRadius: 40,
    alignSelf: 'center',
    marginTop: 24,
    marginBottom: 24,
  },
  viewLinear: {
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    overflow: 'hidden',
    zIndex: 99,
  },
});

export default React.forwardRef(JustIn);
