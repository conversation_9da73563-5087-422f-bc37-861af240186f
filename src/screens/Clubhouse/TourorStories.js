import React, {useRef, useState, useEffect} from 'react';
import {View, StyleSheet, Platform, TouchableOpacity, ActivityIndicator} from 'react-native';
import PagerView from 'react-native-pager-view';
import Animated, {
  useSharedValue,
  withTiming,
  runOnJS,
  cancelAnimation,
  Easing,
} from 'react-native-reanimated';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Video from 'react-native-video';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import IconFontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import appStyles from 'styles/global';
import ItemLine from './components/TourorStories/ItemLine';
import useAppState from 'hooks/useAppState';
import {updateUserTourorStories} from 'requests/club-house';
import FastImage from 'react-native-fast-image/src';
import {saveHistoryTourorStories} from 'utils/asyncStorage';
import {VolumeManager} from 'react-native-volume-manager';
import {
  verticalScreenAnimation,
  horizontalScreenAnimation,
} from 'utils/animations';
import YouTubePlayer from 'components/YouTubePlayer';
import * as Animatable from 'react-native-animatable';
import {screenHeight, screenWidth} from 'config';

const LONG_PRESS_DETECT = 500;
const THUMBNAIL_TIMEOUT = 700;
const ASPECT_RATIO = screenHeight / screenWidth;

function getYouTubeVideoId(url) {
  const regex =
    /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:shorts\/|[^/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i;
  const match = url.match(regex);
  return match ? match[1] : null;
}

let touchMoveX = 0;
let touchMoveY = 0;
const TourorStories = ({navigation, route}) => {
  const dataStories = route?.params?.dataStories;
  const insets = useSafeAreaInsets();
  const [updateView, setUpdateView] = useState(new Date());
  const [mute, setMute] = useState(true);
  const pagerRef = useRef(null);
  const indexPageRef = useRef(route?.params?.indexPage);
  const tempIndex = useRef(route?.params?.indexPage);
  const listCurrentTime = useRef(route?.params?.listInfo);
  const listIdSended = useRef({});
  const refClosing = useRef(false);
  const getWidthLine = (wp(100) - wp(12.6)) / dataStories.length - wp(2);
  const offset = useSharedValue(0);
  const [isLongPress, setIsLongPress] = useState(false);
  const timerDetectLongPressRef = useRef(null);
  const offsetValue = useRef();
  const [thumbnailTimeout, setThumbnailTimeout] = useState(true);
  useAppState({
    onForeground: () => {
      indexPageRef.current = tempIndex.current;
      setUpdateView(new Date().getTime());
      resumeAnimation(true);
    },
    onBackground: () => {
      offsetValue.current = offset.value;
      if (dataStories[indexPageRef.current]?.type?.toUpperCase() !== 'VIDEO') {
        cancelAnimation(offset);
      }
      clearTimeout(timerDetectLongPressRef.current);
      tempIndex.current = indexPageRef.current;
      indexPageRef.current = -1;
      setUpdateView(new Date().getTime());
    },
  });

  useEffect(() => {
    const volumeListener = VolumeManager.addVolumeListener(result => {
      if (result.volume === 0) {
        setMute(true);
      } else {
        setMute(false);
      }
    });
    onRunAnimation();
    return () => {
      volumeListener.remove();
      navigation.setOptions({
        ...horizontalScreenAnimation,
      });
    };
  }, []);

  const onLoad = (data, index) => {
    listCurrentTime.current = {
      ...listCurrentTime.current,
      [index]: {
        ...listCurrentTime.current?.[index],
        duration: data?.duration,
      },
    };
    setUpdateView(new Date().getTime());
    setTimeout(() => {
      setThumbnailTimeout(false);
    }, THUMBNAIL_TIMEOUT);
  };

  const onProgress = (data, index) => {
    if (index === indexPageRef.current) {
      offset.value = data.currentTime;
    }
  };

  const onEnd = index => {
    if (index === indexPageRef.current) {
      if (index === dataStories.length - 1) {
        onPressClose();
      } else {
        runOnJS(onGoForward)();
      }
    }
  };

  const handeSendData = async () => {
    try {
      const idStory = dataStories[indexPageRef.current]?.id;
      if (!listIdSended?.current[idStory]) {
        listIdSended.current = {...listIdSended.current, [idStory]: idStory};
        await updateUserTourorStories(dataStories[indexPageRef.current]?.id);
        saveHistoryTourorStories(idStory);
      }
    } catch (error) {}
  };

  const onPressClose = async () => {
    if (!refClosing.current) {
      refClosing.current = true;
      navigation.goBack();
      await handeSendData();
      route?.params?.getData();
    }
  };

  const onRunAnimation = () => {
    if (dataStories[indexPageRef.current]?.type?.toUpperCase() !== 'VIDEO') {
      cancelAnimation(offset);
      offset.value = 0;
      offset.value = withTiming(
        getWidthLine - 2,
        //show the image in 5 seconds
        {duration: 5000, easing: Easing.linear},
        ended => {
          if (ended) {
            if (indexPageRef.current === dataStories.length - 1) {
              runOnJS(onPressClose)();
            } else {
              runOnJS(onGoForward)();
            }
          }
        },
      );
    }
  };

  const onGoBack = () => {
    if (indexPageRef.current > -1) {
      handeSendData();
      setThumbnailTimeout(true);
      if (indexPageRef.current > 0) {
        indexPageRef.current = indexPageRef.current - 1;
      }
      offset.value = 0;
      if (listCurrentTime.current?.[indexPageRef.current]?.duration) {
        listCurrentTime.current?.[
          indexPageRef.current
        ]?.refVideo?.replayVideo();
        setTimeout(() => {
          setThumbnailTimeout(false);
        }, THUMBNAIL_TIMEOUT);
      }
      onRunAnimation();
      pagerRef.current?.setPageWithoutAnimation(indexPageRef.current);
      setUpdateView(new Date().getTime());
    }
  };

  const onGoForward = () => {
    if (
      indexPageRef.current < dataStories.length - 1 &&
      indexPageRef.current > -1
    ) {
      handeSendData();
      offset.value = 0;
      setThumbnailTimeout(true);
      indexPageRef.current = indexPageRef.current + 1;
      if (listCurrentTime.current?.[indexPageRef.current]?.duration) {
        listCurrentTime.current?.[
          indexPageRef.current
        ]?.refVideo?.replayVideo();
        setTimeout(() => {
          setThumbnailTimeout(false);
        }, THUMBNAIL_TIMEOUT);
      }
      onRunAnimation();
      pagerRef.current?.setPageWithoutAnimation(indexPageRef.current);
      setUpdateView(new Date().getTime());
    } else if (indexPageRef.current === dataStories.length - 1) {
      onPressClose();
    }
  };

  const onMute = () => {
    setMute(!mute);
  };

  const onProgressPage = (data, index) => {
    onProgress(data, index);
  };

  const onTouchMove = e => {
    //close screen when user touch and move to right
    if (
      e.nativeEvent.locationX - touchMoveX > 20 &&
      touchMoveX !== 0 &&
      indexPageRef.current === 0
    ) {
      touchMoveX = 0;
      navigation.setOptions({
        ...horizontalScreenAnimation,
      });
      onPressClose();
    } else if (e.nativeEvent.locationY - touchMoveY > 40 && touchMoveY !== 0) {
      //close screen when user touch and move to bottom
      navigation.setOptions({
        ...verticalScreenAnimation,
      });
      touchMoveY = 0;
      //when set option for navigation need to wait to apply this option
      setTimeout(() => {
        onPressClose();
      }, 0);
    }
  };
  const onTouchStart = e => {
    touchMoveX = e.nativeEvent.locationX;
    touchMoveY = e.nativeEvent.locationY;
    // Start a timer when the touch starts
    timerDetectLongPressRef.current = setTimeout(() => {
      setIsLongPress(true);
      //If user hold press, stop the animation and pause the video if the video is playing
      cancelAnimation(offset);
      if (listCurrentTime.current?.[indexPageRef.current]?.duration) {
        listCurrentTime.current?.[indexPageRef.current]?.refVideo?.pauseVideo();
      }
    }, LONG_PRESS_DETECT); // 500 milliseconds for long press detection
  };

  const renderHeader = () => {
    return (
      <Animated.View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          top: insets.top + (Platform.OS === 'ios' ? 0 : 10),
          position: 'absolute',
          marginLeft: 4,
        }}
      >
        {dataStories.map((item, index) => {
          return (
            <ItemLine
              key={item.id + 'ItemLine'}
              item={item}
              index={index}
              listItem={dataStories}
              indexPage={indexPageRef}
              listCurrentTime={listCurrentTime}
              offset={offset}
              updateView={updateView}
            />
          );
        })}
        <TouchableOpacity
          style={{
            flex: 1,
            alignItems: 'center',
            paddingVertical: 10,
            borderRadius: 24,
          }}
          onPress={onPressClose}
        >
          <View style={styles.viewClose}>
            <Icon name="close" size={20} color={'#fff'} />
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };
  const resumeAnimation = (fromBackground = false) => {
    setIsLongPress(false);
    let barWidth = getWidthLine - 2;
    if (dataStories[indexPageRef.current]?.type?.toUpperCase() !== 'VIDEO') {
      if (fromBackground) {
        offset.value = offsetValue.current;
      }
      let pausedTime = offset.value;
      offset.value = withTiming(
        getWidthLine - 2,
        //show the image in 5 seconds
        {
          duration: (5000 * (barWidth - pausedTime)) / barWidth,
          easing: Easing.linear,
        },
        ended => {
          if (ended) {
            if (indexPageRef.current === dataStories.length - 1) {
              runOnJS(onPressClose)();
            } else {
              runOnJS(onGoForward)();
            }
          }
        },
      );
    } else {
      if (listCurrentTime.current?.[indexPageRef.current]?.duration) {
        listCurrentTime.current?.[
          indexPageRef.current
        ]?.refVideo?.resumeVideo();
      }
    }
  };

  const onTouchEnd = touchPosition => {
    touchMoveX = 0;
    touchMoveY = 0;
    // Clear the timer when the touch ends
    clearTimeout(timerDetectLongPressRef.current);
    setIsLongPress(false);
    if (isLongPress) {
      resumeAnimation();
    } else {
      if (touchPosition === 'right') {
        onGoForward();
      } else {
        onGoBack();
      }
    }
  };

  return (
    <View
      style={{
        flex: 1,
      }}
    >
      <PagerView
        ref={pagerRef}
        style={{flex: 1, backgroundColor: 'black'}}
        initialPage={indexPageRef.current}
        scrollEnabled={false}
        offscreenPageLimit={10}
      >
        {dataStories.map((item, index) => {
          if (item?.type?.toUpperCase() === 'IMAGE') {
            return (
              <FastImage
                key={item.id}
                source={{
                  uri: item.url,
                  cache: FastImage.cacheControl.immutable,
                  priority: FastImage.priority.high,
                }}
                style={styles.viewFullScreen}
                resizeMode="contain"
              />
            );
          }
          const videoId = getYouTubeVideoId(item?.url);
          const thumbnailURL = `https://img.youtube.com/vi/${videoId}/sddefault.jpg`;
          return (
            <View style={appStyles.flex} key={item.id}>
              {thumbnailTimeout && (
                <ActivityIndicator
                  style={[
                    StyleSheet.absoluteFill,
                    {backgroundColor: 'transparent', zIndex: 1000},
                  ]}
                  size={24}
                  color={'white'}
                  pointerEvents="none"
                />
              )}
              {thumbnailTimeout && (
                <Animatable.View style={styles.thumbnail}>
                  <FastImage
                    style={{
                      height: ASPECT_RATIO <= 1.78 ? '100%' : '82%',
                      width: '100%',
                    }}
                    source={{
                      uri: thumbnailURL,
                    }}
                  />
                </Animatable.View>
              )}
              <YouTubePlayer
                toggle={false}
                slidingControl={false}
                videoId={videoId}
                videoUrl={item?.url}
                ref={ref => {
                  listCurrentTime.current = {
                    ...listCurrentTime.current,
                    [index]: {
                      ...listCurrentTime.current?.[index],
                      refVideo: ref,
                    },
                  };
                }} // Store reference
                paused={indexPageRef.current !== index}
                onLoad={data => onLoad(data, index)}
                onProgress={data => onProgressPage(data, index)}
                onEnd={() => onEnd(index)}
                muted={mute}
                videoControlHidden
                progressTimeInterval={500}
              />
            </View>
          );
        })}
      </PagerView>
      <View style={[StyleSheet.absoluteFill, appStyles.row]}>
        <View
          style={[appStyles.flex]}
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={() => onTouchEnd('left')}
        />
        <View
          style={[appStyles.flex]}
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={() => onTouchEnd('right')}
        />
      </View>
      {dataStories[indexPageRef.current]?.type?.toUpperCase() === 'VIDEO' && (
        <TouchableOpacity style={styles.viewTouch} onPress={onMute}>
          <View style={styles.viewVolume} onPress={onMute}>
            <IconFontAwesome5
              name={mute ? 'volume-mute' : 'volume-down'}
              size={16}
              color={'#fff'}
            />
          </View>
        </TouchableOpacity>
      )}
      {!isLongPress && renderHeader()}
    </View>
  );
};
const styles = StyleSheet.create({
  viewFullScreen: {
    flex: 1,
  },
  viewClose: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 24,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewVolume: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 24,
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewTouch: {
    padding: 10,
    position: 'absolute',
    bottom: 30,
    right: 6,
    borderRadius: 24,
  },
  thumbnail: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100,
  },
});
export default TourorStories;
