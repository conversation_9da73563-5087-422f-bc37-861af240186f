import React, {
  forwardRef,
  useEffect,
  useMemo,
  useRef,
  useState,
  useImperativeHandle,
} from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  Platform,
  ActivityIndicator,
  FlatList,
  TouchableWithoutFeedback,
  StyleSheet,
  Animated as ReactAnimated,
} from 'react-native';
import Text from 'components/Text';
import appStyles from 'styles/global';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {BlurView} from '@react-native-community/blur';
import USGAHorizontalLogo from 'assets/imgs/usga-logo-horizontal.png';
import {useDispatch, useSelector} from 'react-redux';
import {t} from 'i18next';
import {checkCanPostRoundToUSGA, checkConnectedWHS} from 'utils/user';
import {
  getFiveRecentRounds,
  getOverallStats,
  getFiveRecentUSGARounds,
} from 'requests/play-stats';
import {uniqBy} from 'lodash';
import {showErrorToast, showToast} from 'utils/toast';
import {Swipeable} from 'react-native-gesture-handler';
import moment from 'moment';
import {deleteRound} from 'requests/add-round';
import Ionicons from 'react-native-vector-icons/Ionicons';
import GHIN_Double_Check_Icon from 'assets/imgs/ic_ghin_double_check.svg';
import WHS_Double_Check_Icon from 'assets/imgs/ic_ghin_double_check_red.svg';
import {createStackNavigator} from '@react-navigation/stack';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import GolfCanadaLogo from 'assets/imgs/logo-canada-golf-small.svg';
import {deleteCoursePlaying, getCoursePlaying} from 'utils/realmHelper';
import GHIN_USGA_Icon from 'assets/imgs/usga_logo_horizontal.svg';
import {MODE_ROUND} from 'screens/PlayCourseMap/DataSubmitDefault';
import ParAverage from './ParAverage';
import DrivingClassicStats from './DrivingClassicStats';
import ApproachClassicStats from './ApproachClassicStats';
import ShortGameClassicStats from './ShortGameClassicStats';
import PuttsClassicStats from './PuttsClassicStats';
import ButtonCancelGrey from './ButtonCancelGrey';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Extrapolation,
  interpolate,
  Easing,
  runOnJS,
} from 'react-native-reanimated';
import {getActionTrackRound} from 'requests/loyalty';
import {ScrollView} from 'react-native-gesture-handler';
import {BOTTOM_BAR_REAL_HEIGHT, checkMainCountry} from 'utils/constant';
import {useIsFocused} from '@react-navigation/native';
import {
  isConnectedNetwork,
  showToastErrorInternet,
} from 'utils/queueAndNetwork';

const VIEW_ABOVE_STATS_HEIGHT = 565;
const VIEW_STATS_TOP_HEIGHT = 237;
const isSmallScreen = hp(100) - VIEW_STATS_TOP_HEIGHT < VIEW_ABOVE_STATS_HEIGHT;
const CARD_HEIGHT = 104;
const USGA_CARD_HEIGHT = 115;
const ScoreTabContent = createStackNavigator();
const ScoreTab = createMaterialTopTabNavigator();
const HEIGHT_SCORE = isSmallScreen
  ? VIEW_ABOVE_STATS_HEIGHT
  : hp(100) - VIEW_STATS_TOP_HEIGHT;
const PER_PAGE = 5;
const ScoreStats = forwardRef(
  (
    {
      navigation,
      setIsDeleteRound,
      shouldReload,
      onOpenScore,
      onCloseScore,
      offsetAnimation,
      setAvgScoreForMainPlay,
      setNumberOfRoundsForMainPlay,
    },
    ref,
  ) => {
    const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
    const playService = useSelector(
      state => state?.user?.tmUserIds?.playServicePreference,
    );
    const user = useSelector(state => state.user);
    const canPostToUSGA = checkCanPostRoundToUSGA(showHideFeatures, user);
    const ghin = useSelector(state => state.ghin);
    const whs = useSelector(state => state.whs);
    const isGolfCanada = checkConnectedWHS(whs);
    const canPostScoreToHandicapIndex = canPostToUSGA || isGolfCanada;
    const [scoreList, setScoreList] = useState([]);
    const [usgaScoreList, setUsgaScoreList] = useState([]);
    const [pausedRound, setPausedRound] = useState();
    const [page, setPage] = useState(1);
    const pageRef = useRef(1);
    const pageRefUsga = useRef(1);
    const [pageUsga, setPageUsga] = useState(1);
    const [mode, setMode] = useState(
      canPostScoreToHandicapIndex ? 'HANDICAP' : 'MYTM',
    );
    const [loadMore, setLoadMore] = useState(false);
    const [statsShowing, setStatsShowing] = useState(false);
    const [overallStatsData, setOverallStatsData] = useState(null);
    const [pageTotalCount, setPageTotalCount] = useState(0);
    const [pageTotalUSGACount, setPageTotalUSGACount] = useState(0);
    const [loadingStats, setLoadingStats] = useState();
    const [trackRoundPoints, setTrackRoundPoints] = useState(0);
    const isFocused = useIsFocused();
    let swipedCardRef = null;
    let rowRefs = new Map();

    const offset = useSharedValue(HEIGHT_SCORE);

    const animatedStyles = useAnimatedStyle(() => {
      return {
        transform: [
          {
            translateY: offset.value,
          },
        ],
      };
    });

    const animatedHeaderStyles = useAnimatedStyle(() => {
      const translateX = interpolate(
        offsetAnimation.value,
        [0, 100],
        [-50, 0],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );
      const opacity = interpolate(offsetAnimation.value, [0, 100], [0, 1], {
        extrapolateRight: Extrapolation.CLAMP,
      });
      return {
        transform: [
          {
            translateX,
          },
        ],
        opacity,
      };
    });
    const refreshData = () => {
      //only refresh data when the Score panel is hidden in the bottom
      if (!statsShowing) {
        getTrackRoundLoyaltyPoints();
        if (canPostToUSGA || isGolfCanada) {
          onLoadUsgaScoreList();
        }
        if (!canPostToUSGA || isGolfCanada) {
          onLoadScoreList();
        }
      }
      getStatsData(statsShowing ? false : true);
    };

    const reloadDataMyTM = itemUpdate => {
      if (itemUpdate) {
        const updateItem = scoreList.map(_item => {
          if (_item.id === itemUpdate.id) {
            if (pausedRound?.id === itemUpdate.id) {
              setPausedRound(null);
            }
            return itemUpdate;
          }
          return _item;
        });
        setScoreList(updateItem);
      } else {
        if (canPostToUSGA) {
          onLoadUsgaScoreList(true);
        } else if (isGolfCanada) {
          onLoadScoreList(true);
          onLoadUsgaScoreList(true);
        } else {
          onLoadScoreList(true);
        }
      }
      getStatsData();
    };

    const cancelRoundResume = id => {
      const removeResumeRound = scoreList.filter(_item => _item.id !== id);
      setScoreList(removeResumeRound);
      const removeResumeRoundUsga = usgaScoreList.filter(
        _item => _item.id !== id,
      );
      setUsgaScoreList(removeResumeRoundUsga);
      setPausedRound(null);
    };

    useEffect(() => {
      if (!canPostScoreToHandicapIndex && mode === 'HANDICAP') {
        setMode('MYTM');
      }
    }, [canPostScoreToHandicapIndex]);

    useImperativeHandle(
      ref,
      () => {
        return {
          reloadScoreStatsData: () => {
            refreshData();
          },
          onChangePan: event => {
            if (!statsShowing && !loadingStats) {
              if (event.translationY <= -100) {
                offset.value = withTiming(0, {
                  duration: 700,
                  easing: Easing.elastic(1),
                });
                hideBottomTab();
                setStatsShowing(true);
                onOpenScore();
              } else {
                offset.value = event.translationY + HEIGHT_SCORE;
              }
            }
          },
          onFinalizePan: event => {
            if (
              event.translationY + HEIGHT_SCORE > -100 + HEIGHT_SCORE &&
              !statsShowing
            ) {
              offset.value = withTiming(HEIGHT_SCORE, {
                duration: 700,
                easing: Easing.elastic(1),
              });
              runOnJS(showBottomTab)();
              runOnJS(setStatsShowing)(false);
            }
          },
        };
      },
      [statsShowing, loadingStats, isGolfCanada, canPostToUSGA],
    );

    const formatPausedRound = data => {
      const {roundData, selectHole, idRound} = data;
      const result = {
        completed: roundData.completed,
        courseId: 0,
        courseName: roundData.course_name,
        coursePar: 72,
        duration: 0,
        generatedBy: roundData.generated_by,
        holes: [],
        id: idRound,
        igolfCourseId: roundData.igolf_course_id,
        inprogress: roundData.inprogress,
        mapId: roundData.map_id,
        numberOfHolesPlayed: selectHole,
        playedOn: roundData.played_on,
        roundMode: roundData.round_mode,
        statsCompleted: true,
        teeName: roundData?.teeName,
        totalScore: roundData.total_score,
        userTimezone: roundData.user_timezone,
        data,
        userToCourse: roundData.user_to_course,
      };
      return result;
    };

    const onLoadUsgaScoreList = async (shouldRemainAllPage = false) => {
      try {
        // setLoading(true);
        const dataCourse = await getCoursePlaying();
        const mrpId = user?.tmUserIds?.mrp;
        const userRoundId = dataCourse?.roundData?.user_id;
        // Make request to get recent rounds
        let recentRounds = null;
        if (canPostToUSGA) {
          recentRounds = await getFiveRecentUSGARounds(
            1,
            playService,
            true,
            shouldRemainAllPage ? pageRefUsga.current * PER_PAGE : undefined,
          );
        } else {
          recentRounds = await getFiveRecentRounds(
            1,
            playService,
            true,
            'golfnet',
            shouldRemainAllPage ? pageRefUsga.current * PER_PAGE : undefined,
          );
        }
        const completedRound = [...recentRounds.data];
        //add the paused round if exists, only apply for USGA, because the USGA list contains TM Round List and USGA Round List
        if (dataCourse && mrpId === userRoundId && canPostToUSGA) {
          const formatedCourse = formatPausedRound(dataCourse);
          setPausedRound(formatedCourse);
          completedRound.unshift(formatedCourse);
        } else {
          setPausedRound(null);
          await deleteCoursePlaying();
        }
        setPageTotalUSGACount(recentRounds?.pageCount);
        setUsgaScoreList(completedRound);
        // Set current page number of request
        if (shouldRemainAllPage) {
          if (recentRounds?.perPage > 0) {
            // As the current max per page (recentRounds?.perPage) is 100, so set the current page to 20
            if (pageRefUsga.current > recentRounds?.perPage / PER_PAGE) {
              setPageUsga(recentRounds?.perPage / PER_PAGE);
              pageRefUsga.current = recentRounds?.perPage / PER_PAGE;
            }
          }
        } else {
          // If it is initial data loading
          setPageUsga(1);
          pageRefUsga.current = 1;
        }
        // setLoading(false);
      } catch (error) {
        // setLoading(false);
        showErrorToast({
          error,
          title: t('Scores_Error'),
          description: t('An_error_occurred_retrieving_scores'),
        });
      }
    };

    const onReloadResumeRound = async () => {
      try {
        const dataCourse = await getCoursePlaying();
        if (dataCourse) {
          const formatedCourse = formatPausedRound(dataCourse);
          setPausedRound(formatedCourse);
          let completedRound = [...scoreList].filter(item => item.completed);
          completedRound.unshift(formatedCourse);
          setScoreList(completedRound);
        }
      } catch (error) {}
    };

    const onLoadScoreList = async (shouldRemainAllPage = false) => {
      try {
        // setLoading(true);
        const dataCourse = await getCoursePlaying();
        const mrpId = user?.tmUserIds?.mrp;
        const userRoundId = dataCourse?.roundData?.user_id;
        // Make request to get recent rounds
        const recentRounds = await getFiveRecentRounds(
          1,
          playService,
          undefined,
          undefined,
          shouldRemainAllPage ? pageRef.current * PER_PAGE : PER_PAGE,
        );
        let completedRound = [...recentRounds.data];
        //add the paused round if exists,
        if (dataCourse && mrpId === userRoundId) {
          const formatedCourse = formatPausedRound(dataCourse);
          setPausedRound(formatedCourse);
          completedRound.unshift(formatedCourse);
        } else {
          setPausedRound(null);
          await deleteCoursePlaying();
        }
        setPageTotalCount(recentRounds?.pageCount);
        setScoreList(completedRound);
        // Set current page number of request
        if (shouldRemainAllPage) {
          if (recentRounds?.perPage > 0) {
            // As the current max per page (recentRounds?.perPage) is 100, so set the current page to 20
            if (pageRef.current > recentRounds?.perPage / PER_PAGE) {
              setPage(recentRounds?.perPage / PER_PAGE);
              pageRef.current = recentRounds?.perPage / PER_PAGE;
            }
          }
        } else {
          // If it is initial data loading
          setPage(1);
          pageRef.current = 1;
        }
        // setLoading(false);
      } catch (error) {
        // setLoading(false);
        showErrorToast({
          error,
          title: t('Scores_Error'),
          description: t('An_error_occurred_retrieving_scores'),
        });
      }
    };

    const getMoreRecentRounds = async (roundType, pageCount) => {
      if (
        pageCount === 2 &&
        roundType === 'MYTM' &&
        scoreList?.length < PER_PAGE
      ) {
        return;
      }
      if (
        pageCount === 2 &&
        roundType === 'HANDICAP' &&
        usgaScoreList?.length < PER_PAGE
      ) {
        return;
      }
      let recentRounds = [];
      try {
        // Make request to get next page of recent rounds
        setLoadMore(true);
        if (roundType === 'HANDICAP' && canPostToUSGA) {
          recentRounds = await getFiveRecentUSGARounds(
            pageCount,
            playService,
            true,
          );
        } else if (roundType === 'HANDICAP' && isGolfCanada) {
          recentRounds = await getFiveRecentRounds(
            pageCount,
            playService,
            true,
            'golfnet',
          );
        } else {
          recentRounds = await getFiveRecentRounds(pageCount, playService);
        }
        if (recentRounds.data?.length) {
          const roundScores =
            roundType === 'HANDICAP' ? [...usgaScoreList] : [...scoreList];
          let newScores = roundScores.concat(recentRounds.data);
          newScores = uniqBy(newScores, 'id');
          if (roundType === 'HANDICAP') {
            setPageTotalUSGACount(recentRounds?.pageCount);
            setUsgaScoreList(newScores);
          } else {
            setPageTotalCount(recentRounds?.pageCount);
            setScoreList(newScores);
          }
        }
        // Set current page number of request
        if (pageCount) {
          if (roundType === 'HANDICAP') {
            setPageUsga(pageCount);
            pageRefUsga.current = pageCount;
          } else {
            setPage(pageCount);
            pageRef.current = pageCount;
          }
        }
        setLoadMore(false);
      } catch (error) {
        setLoadMore(false);
        showErrorToast({
          error,
          title: t('Scores_Error'),
          description: t('An_error_occurred_retrieving_scores'),
        });
      }
    };

    const getStatsData = async (shouldShowLoading = true) => {
      try {
        shouldShowLoading && setLoadingStats(true);
        const statsResult = await getOverallStats(playService);
        setOverallStatsData(statsResult);
        setAvgScoreForMainPlay?.((+statsResult?.averageScore)?.toFixed?.(1));
        setNumberOfRoundsForMainPlay?.(statsResult?.roundIds?.length || 0);
        setLoadingStats(false);
      } catch (error) {
        console.log(error.message);
        setLoadingStats(false);
      }
    };

    const getTrackRoundLoyaltyPoints = async () => {
      try {
        const isMainCountry = checkMainCountry(user?.userCountry);
        if (isMainCountry) {
          const roundPointResponse = await getActionTrackRound();
          if (roundPointResponse && roundPointResponse?.status === 'ACTIVE') {
            setTrackRoundPoints(roundPointResponse?.actionPoints);
          }
        }
      } catch (error) {}
    };

    const flatListRef = useRef(null);
    const renderRightActions = (progress, _dragAnimatedValue, itemSelected) => {
      const canDeleteRound =
        (itemSelected.ghinRoundId == null && canPostToUSGA) ||
        (itemSelected.golfnetRoundId == null && isGolfCanada) ||
        (!canPostToUSGA && !isGolfCanada);
      if (!canDeleteRound) {
        return null;
      }
      return (
        <View style={[styles.rightActionContainer]}>
          {renderRightAction(
            'Delete',
            'rgba(rgba(255, 0, 0, 1)',
            80,
            progress,
            itemSelected,
          )}
        </View>
      );
    };

    const renderRightAction = (text, color, x, progress, itemSelected) => {
      const trans = progress.interpolate({
        inputRange: [0, 1],
        outputRange: [-28, -28],
      });

      const pressHandler = async () => {
        try {
          setIsDeleteRound(true);
          let deleteId = itemSelected?.id;
          await deleteRound(deleteId);
          if (canPostToUSGA) {
            await onLoadUsgaScoreList(true);
          } else {
            await onLoadScoreList(true);
          }

          //reload stats data
          getStatsData();
          setIsDeleteRound(false);
          showToast({
            type: 'success',
            message: 'Round deleted',
          });
        } catch (error) {
          setIsDeleteRound(false);
          throw error;
        }
      };

      return (
        <ReactAnimated.View
          style={[
            styles.itemSwipe,
            {
              transform: [{translateX: trans}],
            },
          ]}
        >
          <View style={{width: 20, backgroundColor: 'white'}} />
          <TouchableOpacity
            style={[
              styles.rightAction,
              {
                backgroundColor: 'red',
              },
            ]}
            onPress={pressHandler}
          >
            <Ionicons name={'trash-outline'} size={30} color={'white'} />
            <Text size={13} style={styles.actionText}>
              {text}
            </Text>
          </TouchableOpacity>
        </ReactAnimated.View>
      );
    };

    const onOpen = ref => {
      if (swipedCardRef) {
        swipedCardRef.close();
      }
    };
    const onClose = ref => {
      if (ref === swipedCardRef) {
        swipedCardRef = null;
      }
    };

    const animateScoreStatsPosition = start => {
      if (start) {
        offset.value = withTiming(0, {
          duration: 700,
          easing: Easing.elastic(1),
        });
      } else {
        offset.value = withTiming(HEIGHT_SCORE, {
          duration: 700,
          easing: Easing.elastic(1),
        });
        flatListRef.current?.scrollTo({
          y: 0,
          animated: false,
        });
        setStatsShowing(false);
        showBottomTab();
      }
    };

    const onPressRoundScore = item => {
      const checkNetwork = isConnectedNetwork();
      if (!checkNetwork) {
        showToastErrorInternet();
        return;
      }
      if (item.completed) {
        if (
          item.roundMode === MODE_ROUND.CLASSIC ||
          item.roundMode === MODE_ROUND.BASIC
        ) {
          navigation?.navigate('ScoresStats', {
            screen: 'RoundOverview',
            params: {
              roundInfo: item,
              pausedRound: pausedRound,
              reloadListData: _item => reloadDataMyTM(_item),
            },
          });
        } else {
          if (item?.ghinRoundId != null) {
            navigation?.navigate('ScoresStats', {
              screen: 'RoundOverview',
              params: {
                roundInfo: item,
                isFrom3rdPartyRound: canPostToUSGA || !!item?.ghinRoundId,
                reloadListData: _item => reloadDataMyTM(_item),
              },
            });
          } else {
            navigation?.navigate('ScoresStats', {
              screen: 'ScoreDetail',
              params: {
                roundInfo: item,
                // origin: route?.params?.origin || '',
                pausedRound: pausedRound,
                reloadListData: _item => reloadDataMyTM(_item),
              },
            });
          }
        }
      } else {
        const {data} = item;
        const {
          roundData,
          teeSelected,
          scoreCardCourse,
          idRound,
          selectHole,
          clubs,
          isFinder,
          isPracticeMode,
        } = data;
        if (!scoreCardCourse) {
          return;
        }
        // setModePlayed(roundData.round_mode);
        navigation.navigate('ScoresStats', {
          screen: 'MenuPlayRound',
          params: {
            roundData,
            isFinder,
            idRound,
            teeSelected,
            scoreCardDefault: scoreCardCourse,
            selectHole,
            clubs,
            isPausedRound: true,
            isPracticeMode,
            cancelRoundResume,
            reloadListData: _item => reloadDataMyTM(_item),
            onReloadListUSGA: onLoadUsgaScoreList,
            onReloadResumeRound,
          },
        });
      }
    };

    const onPressUsgaRoundScore = item => {
      if (
        isGolfCanada &&
        !item?.golfnetRoundId &&
        item.roundMode === MODE_ROUND.SIMPLE
      ) {
        navigation?.navigate('ScoresStats', {
          screen: 'ScoreDetail',
          params: {
            roundInfo: item,
            // origin: route?.params?.origin || '',
            pausedRound: pausedRound,
            reloadListData: _item => reloadDataMyTM(_item),
          },
        });
      } else {
        navigation?.navigate('ScoresStats', {
          screen: 'RoundOverview',
          params: {
            roundInfo: item,
            // origin: route?.params?.origin || '',
            isFrom3rdPartyRound: canPostToUSGA || isGolfCanada,
            reloadListData: _item => reloadDataMyTM(_item),
          },
        });
      }
    };

    const onTrackRound = () => {
      try {
        navigation?.navigate('ScoresStats', {
          screen: 'FindACourse',
        });
      } catch (error) {
        console.log('error', error);
      }
    };

    const hideBottomTab = () => {
      navigation
        .getParent()
        ?.setOptions({tabBarStyle: {display: 'none'}, tabBarVisible: false});
    };
    const showBottomTab = () => {
      navigation
        .getParent()
        ?.setOptions({tabBarStyle: undefined, tabBarVisible: undefined});
    };

    const SIDE_PADDING = 46;
    const RIGHT_VIEW_WIDTH_COMPLETED = 118;
    const RIGHT_VIEW_WIDTH_INCOMPLETE = 132;

    const getLeftSideMaxWidth = item => {
      return (
        wp(100) -
        SIDE_PADDING -
        (item.completed
          ? RIGHT_VIEW_WIDTH_COMPLETED
          : RIGHT_VIEW_WIDTH_INCOMPLETE)
      );
    };
    const getRightSideMaxWidth = item => {
      return item.completed
        ? RIGHT_VIEW_WIDTH_COMPLETED
        : RIGHT_VIEW_WIDTH_INCOMPLETE;
    };
    const singleScoreView = ({item}) => {
      if (item) {
        const date = item?.playedOn
          ? moment(item.playedOn).format('MM.DD.YYYY')
          : '';
        return (
          <Swipeable
            renderRightActions={(progress, drag) =>
              renderRightActions(progress, drag, item)
            }
            // renderLeftActions={(progress, drag) =>
            //   renderLeftActions(progress, drag, item)
            // }
            overshootLeft={false}
            overshootRight={false}
            onSwipeableOpen={onOpen}
            onSwipeableClose={onClose}
            onSwipeableWillOpen={() => {
              [...rowRefs.entries()].forEach(([key, ref]) => {
                if (key !== item.id && ref) {
                  ref.close();
                }
              });
            }}
            ref={ref => {
              if (ref && !rowRefs.get(item.id)) {
                rowRefs.set(item.id, ref);
              }
            }}
            key={item.id}
            containerStyle={{paddingHorizontal: 8}}
          >
            <TouchableOpacity
              onPress={() => onPressRoundScore(item)}
              activeOpacity={1}
            >
              <View
                style={[
                  appStyles.whiteBg,
                  appStyles.row,
                  styles.shadow,
                  styles.container,
                ]}
              >
                <View style={[{maxWidth: getLeftSideMaxWidth(item)}]}>
                  <View style={[appStyles.row]}>
                    <Text
                      style={[appStyles.mRSm, styles.dateText]}
                      Din79Font
                    >{`${date}`}</Text>
                  </View>
                  <Text black style={styles.courseNameTitle} numberOfLines={2}>
                    {item.courseName}
                  </Text>
                  {canPostToUSGA && item?.ghinRoundId != null && (
                    <View
                      style={[
                        appStyles.row,
                        {
                          alignItems: 'center',
                          paddingTop: Platform.OS === 'ios' ? 8 : 4,
                        },
                      ]}
                    >
                      <GHIN_Double_Check_Icon />
                      <GHIN_USGA_Icon />
                    </View>
                  )}
                </View>
                <View
                  style={[
                    appStyles.row,
                    appStyles.spaceEnd,
                    appStyles.hCenter,
                    {maxWidth: getRightSideMaxWidth(item)},
                  ]}
                >
                  {item.completed ? (
                    <>
                      <Text Din79Font style={[styles.totalScore]} black>
                        {item.totalScore}
                      </Text>
                      {item?.scoreToPar !== undefined ? (
                        <View
                          style={[
                            appStyles.vCenter,
                            appStyles.hCenter,
                            appStyles.mLSm,
                            styles.scoreToParContainer,
                          ]}
                        >
                          <Text Din79Font black style={[styles.scoreToParText]}>
                            {item.scoreToPar > 0 ? '+' : ''}
                            {item.scoreToPar === 0 ? 'E' : item.scoreToPar}
                          </Text>
                        </View>
                      ) : (
                        <View style={[styles.parContainer, appStyles.mLSm]} />
                      )}
                    </>
                  ) : (
                    <View style={styles.viewResumeRound}>
                      <Text style={styles.textResumeRound}>
                        play.resume_round
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </TouchableOpacity>
          </Swipeable>
        );
      }
    };

    const onPressMode = title => {
      setMode(title);
    };

    const singleGolfnetScoreView = ({item}) => {
      if (item) {
        const date = item?.playedOnUtc
          ? moment(item.playedOnUtc).format('MM.DD.YYYY')
          : item?.playedOn
          ? moment(item.playedOn).format('MM.DD.YYYY')
          : '';
        const courseId = item.golfnetRoundId
          ? item?.golfnetCourseId
          : item?.ghinCourseId;
        const courseName = item.golfnetRoundId
          ? item?.golfnetCourseName
          : item?.ghinCourseName;
        return (
          <Swipeable
            key={item.id || courseId}
            containerStyle={{paddingHorizontal: 8}}
          >
            <TouchableOpacity
              onPress={() => onPressUsgaRoundScore(item)}
              activeOpacity={1}
            >
              <View
                style={[
                  appStyles.whiteBg,
                  appStyles.row,
                  styles.shadow,
                  styles.container,
                  {minHeight: USGA_CARD_HEIGHT},
                ]}
              >
                <View style={[{maxWidth: getLeftSideMaxWidth(item)}]}>
                  <View style={[appStyles.row]}>
                    <Text
                      style={[appStyles.mRSm, styles.dateText]}
                      Din79Font
                    >{`${date}`}</Text>
                  </View>
                  <Text black style={styles.courseNameTitle} numberOfLines={2}>
                    {courseName}
                  </Text>
                  {item.ghinRoundId != null && (
                    <View
                      style={[
                        appStyles.row,
                        {
                          alignItems: 'center',
                          paddingTop: Platform.OS === 'ios' ? 10 : 4,
                        },
                      ]}
                    >
                      <GHIN_Double_Check_Icon />
                      <GHIN_USGA_Icon />
                    </View>
                  )}
                  {item.golfnetRoundId != null && (
                    <View
                      style={[
                        appStyles.row,
                        {
                          alignItems: 'center',
                          paddingTop: Platform.OS === 'ios' ? 7 : 1,
                        },
                      ]}
                    >
                      <WHS_Double_Check_Icon />
                      <Text
                        Din79Font
                        style={{color: '#B5121B', letterSpacing: 0.135 * 12}}
                        size={12}
                        weight={700}
                      >
                        {t('score.golf_canada')}
                      </Text>
                    </View>
                  )}
                </View>
                <View
                  style={[
                    appStyles.row,
                    appStyles.spaceEnd,
                    appStyles.hCenter,
                    {maxWidth: getRightSideMaxWidth(item)},
                  ]}
                >
                  <Text Din79Font style={[styles.totalScore]} black>
                    {item.golfNetScore || item.ghinScore || item.totalScore}
                  </Text>
                  {item?.scoreToPar !== undefined ? (
                    <View
                      style={[
                        appStyles.vCenter,
                        appStyles.hCenter,
                        appStyles.mLSm,
                        styles.scoreToParContainer,
                      ]}
                    >
                      <Text Din79Font black style={[styles.scoreToParText]}>
                        {item.scoreToPar > 0 ? '+' : ''}
                        {item.scoreToPar === 0 ? 'E' : item.scoreToPar}
                      </Text>
                    </View>
                  ) : (
                    <View style={[styles.parContainer, appStyles.mLSm]} />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          </Swipeable>
        );
      }
    };

    const calculateRoundNum = useMemo(() => {
      return overallStatsData?.roundIds?.length || 0;
    }, [overallStatsData]);

    const renderMode = (value, title, disable) => {
      return (
        <ScoreTabContent.Screen
          name={value}
          children={() => {
            return null;
          }}
          options={{
            headerShown: false,
            tabBarLabel: ({focused, color, size}) => (
              <Text
                Din79Font
                style={{
                  ...styles.labelStyle,
                  paddingLeft: value === 'HANDICAP' ? 0 : 6,
                  paddingRight: value === 'HANDICAP' ? 6 : 0,
                  color: disable ? '#00000060' : color,
                }}
              >
                {title}
              </Text>
            ),
          }}
          listeners={{
            tabPress: e => {
              if (disable) {
                // Prevent default action
                e.preventDefault();
              } else if (mode !== value) {
                onPressMode(value);
              }
            },
          }}
        />
      );
    };

    const canEnableLoadMore =
      (mode === 'MYTM' &&
        page !== pageTotalCount &&
        (pageTotalCount || 0) !== 0) ||
      (mode === 'HANDICAP' &&
        pageUsga !== pageTotalUSGACount &&
        (pageTotalUSGACount || 0) !== 0);
    return (
      <Animated.View
        style={[
          {
            position: 'absolute',
            zIndex: 5,
            width: wp(100) - (statsShowing ? 0 : 16),
            overflow: Platform.OS === 'android' ? 'hidden' : 'visible',
            backgroundColor: statsShowing
              ? 'rgba(235, 235, 235, 1)'
              : 'rgba(235, 235, 235, 0.3)',
            borderRadius: statsShowing ? 0 : 24,
            marginHorizontal: statsShowing ? 0 : 8,
          },
          animatedStyles,
        ]}
      >
        {!statsShowing &&
          (Platform.OS === 'ios' ? (
            <BlurView
              style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                borderRadius: 24,
              }}
              blurType="light"
              blurAmount={10}
              reducedTransparencyFallbackColor="white"
            />
          ) : (
            <BlurView
              style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                borderRadius: 24,
              }}
              blurType="light"
              blurAmount={32}
              reducedTransparencyFallbackColor="white"
            />
          ))}
        {statsShowing && (
          <ButtonCancelGrey
            onPress={() => {
              animateScoreStatsPosition(false);
              onCloseScore();
            }}
            top={Platform.OS === 'ios' ? 25 : 0}
          />
        )}
        <ScrollView
          style={{
            marginBottom: 50,
            flex: 1,
            height: hp(100) - (Platform.OS === 'android' ? 20 : 0),
          }}
          scrollEnabled={statsShowing}
          ref={flatListRef}
          contentContainerStyle={{
            paddingTop: statsShowing ? '8%' : 0,
            paddingBottom: BOTTOM_BAR_REAL_HEIGHT + 10,
          }}
          scrollsToTop={isFocused}
        >
          <TouchableWithoutFeedback
            onPress={() => {
              if (!loadingStats) {
                hideBottomTab();
                animateScoreStatsPosition(true);
                setStatsShowing(true);
                onOpenScore();
              }
            }}
          >
            <View
              style={{
                paddingVertical: isSmallScreen ? 0 : 18,
                alignItems: 'center',
                paddingBottom: 20,
                overflow: 'hidden',
              }}
            >
              {statsShowing && (
                <Animated.View style={[animatedHeaderStyles]}>
                  {canPostToUSGA && (
                    <Image
                      source={USGAHorizontalLogo}
                      style={{width: 168, height: 33.79}}
                    />
                  )}
                  {canPostScoreToHandicapIndex && (
                    <Text black size={16} style={{fontWeight: '400'}}>
                      <Text Din79Font style={{fontWeight: '800'}} size={22}>
                        {ghin?.hiDisplay || whs?.displayHandicp}
                      </Text>{' '}
                      H.I.™ Low H.I.™{' '}
                      {ghin?.lowHiDisplay || whs?.hiDetail?.displayLowValue
                        ? ghin?.lowHiDisplay || whs?.hiDetail?.displayLowValue
                        : '-'}
                    </Text>
                  )}
                </Animated.View>
              )}
              <Text
                Din79Font
                size={22}
                style={{fontWeight: '800', paddingVertical: 14}}
                black
              >
                scoreStats.title.score_and_stats
              </Text>
              <Text
                size={12}
                style={{
                  color: statsShowing ? 'rgba(0, 0, 0, 0.5)' : 'black',
                }}
              >
                {overallStatsData
                  ? t(
                      calculateRoundNum === 1
                        ? 'stats.showing_stats_for_one'
                        : 'stats.showing_stats_for_multiple',
                      {value: calculateRoundNum},
                    )
                  : t('play.stats.do_not_have_stats')}
              </Text>
              {loadingStats ? (
                <ActivityIndicator
                  color={'black'}
                  style={{
                    width: 70,
                    height: 70,
                  }}
                />
              ) : overallStatsData ? (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'flex-end',
                    minHeight: 70,
                    marginTop: -5,
                  }}
                >
                  <Text
                    Din79Font
                    size={54}
                    style={{fontWeight: '700', paddingHorizontal: 4}}
                    black
                  >
                    {parseFloat(overallStatsData?.averageScore || 0.0).toFixed(
                      1,
                    )}
                  </Text>
                  <View
                    style={{
                      borderRadius: 5,
                      marginBottom: Platform.OS === 'ios' ? 10 : 5,
                      paddingHorizontal: 5,
                    }}
                  >
                    <Text
                      size={22}
                      black
                      style={{fontWeight: '800', letterSpacing: 1.1}}
                      Din79Font
                    >
                      AVG
                    </Text>
                  </View>
                </View>
              ) : (
                <TouchableOpacity onPress={onTrackRound}>
                  <View style={[styles.buttonTrackRound, appStyles.viewShadow]}>
                    <Text
                      size={12}
                      Din79Font
                      style={styles.trackRoundText}
                      black
                    >
                      {t('play.stats.track_a_round')}{' '}
                      {trackRoundPoints > 0
                        ? `(${trackRoundPoints || 0}PTS)`
                        : null}
                    </Text>
                  </View>
                </TouchableOpacity>
              )}
              <ParAverage overallStatsData={overallStatsData} />
              <View style={styles.separator} />
              <DrivingClassicStats overallStatsData={overallStatsData} />
              <View style={styles.separator} />
              {/* Approach Stat */}
              <ApproachClassicStats overallStatsData={overallStatsData} />
              {/* Approach Stats */}
              {/* Short game */}
              <View style={styles.separator} />
              <ShortGameClassicStats
                overallStatsData={overallStatsData}
                isHorizontal
              />
              <View style={styles.separator} />
              <PuttsClassicStats
                overallStatsData={overallStatsData}
                isHorizontal
              />
              <View style={styles.separator} />
              {canPostScoreToHandicapIndex && isGolfCanada && (
                <View style={{flex: 1, flexDirection: 'row'}}>
                  <ScoreTab.Navigator
                    tabBarOptions={{
                      tabStyle: {
                        justifyContent: 'center',
                        alignItems: 'center',
                      },
                      labelStyle: styles.labelStyle,
                      indicatorStyle: {
                        ...styles.indicatorStyle,
                        marginLeft: mode === 'HANDICAP' ? 0 : 8,
                      },
                      style: styles.tabBarStyle,
                      inactiveBackgroundColor: '#111111',
                      allowFontScaling: false,
                      activeTintColor: 'white',
                      inactiveTintColor: 'black',
                    }}
                    initialRouteName={mode}
                  >
                    {renderMode('MYTM', t('scoreStats.tab.tracked_rounds'))}
                    {renderMode(
                      'HANDICAP',
                      isGolfCanada
                        ? t('scoreStats.tab.golf_canada_rounds')
                        : t('scoreStats.tab.usga_submitted'),
                    )}
                  </ScoreTab.Navigator>
                </View>
              )}
              <View style={{width: '100%'}}>
                <View style={{flex: 1}}>
                  {mode === 'MYTM' ? (
                    <FlatList
                      contentContainerStyle={{paddingBottom: '2%'}}
                      style={[appStyles.flex, appStyles.pTSm, {width: '100%'}]}
                      data={scoreList}
                      renderItem={singleScoreView}
                      keyExtractor={item =>
                        item?.id + item?.playedOn + item?.totalScore
                      }
                    />
                  ) : canPostToUSGA ? (
                    <FlatList
                      contentContainerStyle={{paddingBottom: '2%'}}
                      style={[appStyles.flex, appStyles.pTSm, {width: '100%'}]}
                      data={usgaScoreList}
                      renderItem={singleScoreView}
                      keyExtractor={item =>
                        item?.id + item?.playedOn + item?.totalScore
                      }
                    />
                  ) : (
                    <FlatList
                      contentContainerStyle={{paddingBottom: '2%'}}
                      style={[appStyles.flex, appStyles.pTSm]}
                      data={usgaScoreList}
                      renderItem={singleGolfnetScoreView}
                      keyExtractor={item =>
                        item?.id + item?.playedOn + item?.totalScore
                      }
                    />
                  )}
                </View>
              </View>
              {
                <TouchableOpacity
                  style={[
                    styles.buttonLoadMoreRounds,
                    {
                      backgroundColor: !canEnableLoadMore
                        ? 'rgba(0, 0, 0, 0.6)'
                        : 'black',
                    },
                  ]}
                  onPress={() =>
                    getMoreRecentRounds(
                      mode,
                      (mode === 'HANDICAP' ? pageUsga : page) + 1,
                    )
                  }
                  disabled={loadMore || !canEnableLoadMore}
                >
                  {loadMore ? (
                    <ActivityIndicator color={'white'} />
                  ) : (
                    <Text white Din79Font style={{fontWeight: '700'}} size={12}>
                      {t('scoreStats.title.load_more_rounds')}
                    </Text>
                  )}
                </TouchableOpacity>
              }
            </View>
          </TouchableWithoutFeedback>
        </ScrollView>
      </Animated.View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    minHeight: CARD_HEIGHT,
    marginTop: 4,
    paddingHorizontal: 15,
    marginBottom: 4,
    overflow: 'hidden',
    alignItems: 'center',
    borderRadius: 24,
    justifyContent: 'space-between',
  },
  itemSwipe: {
    marginTop: 5,
    flexDirection: 'row',
    marginBottom: 4,
  },
  shadow: {
    width: '100%',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 4,
    shadowColor: '#d8d7d7',
  },
  courseNameTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginTop: Platform.OS === 'ios' ? 6 : 0,
  },
  totalScore: {
    fontSize: 54,
    fontWeight: '700',
  },
  rightActionContainer: {
    width: 80,
    flexDirection: 'row',
    paddingRight: 8,
  },
  rightAction: {
    alignItems: 'center',
    width: 80,
    justifyContent: 'center',
    borderRadius: 24,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
  },
  actionText: {
    color: 'white',
    marginTop: 2,
    fontSize: 13,
    fontWeight: '600',
  },
  textResumeRound: {
    fontWeight: '600',
    fontSize: 13,
    lineHeight: 15.5,
    color: '#3ABA56',
  },
  viewResumeRound: {
    borderColor: '#3ABA56',
    borderRadius: 22,
    borderWidth: 1,
    height: 36,
    paddingHorizontal: 15,
    ...appStyles.hCenter,
    ...appStyles.vCenter,
  },
  labelStyle: {
    marginTop: 7,
    alignItems: 'center',
    textTransform: 'uppercase',
    color: 'rgba(17, 17, 17, 1)',
    textAlign: 'center',
    fontWeight: '700',
    fontSize: 12,
    letterSpacing: 1.62,
  },
  tabBarStyle: {
    borderRadius: 37,
    height: 56,
    backgroundColor: 'rgb(205,205,205)',
    marginHorizontal: 8,
    marginTop: 25,
    shadowColor: 'black',
  },
  indicatorStyle: {
    height: 40,
    width: (wp(100) - 32) / 2,
    marginBottom: 8,
    backgroundColor: 'black',
    borderRadius: 22,
    alignSelf: 'center',
  },
  separator: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    width: '90%',
  },
  dateText: {
    fontSize: 12,
    fontWeight: '700',
    color: 'rgba(0, 0, 0, 0.4)',
  },
  scoreToParContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    minWidth: 36,
    marginTop: 20,
    borderRadius: 5,
  },
  scoreToParText: {
    fontSize: 22,
    fontWeight: '800',
    lineHeight: 21.52,
    marginTop: 4,
  },
  buttonLoadMoreRounds: {
    backgroundColor: 'black',
    borderRadius: 24,
    paddingVertical: 12,
    paddingHorizontal: 16,
    minWidth: 167,
    alignItems: 'center',
  },
  buttonTrackRound: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'white',
    borderRadius: 25,
    marginTop: 14,
  },
  trackRoundText: {
    fontWeight: '700',
    letterSpacing: 1.62,
    textTransform: 'uppercase',
  },
});

export default ScoreStats;
