import React, {useState, useRef} from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  Platform,
  StyleSheet,
} from 'react-native';
import Text from 'components/Text';
import appStyles from 'styles/global';
import {BlurView} from '@react-native-community/blur';
import ArrowIcon from 'assets/imgs/arrow-down.png';
import {nameOfMeasureMiWithKM, valueOfMeasureMiWithKM} from 'utils/countries';
import {useNavigation} from '@react-navigation/native';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import LinearGradient from 'react-native-linear-gradient';
import {GA_playNavClick} from 'utils/googleAnalytics';
import {NOT_APPLICABLE} from 'utils/constant';
import {useSelector} from 'react-redux';
import {convertDistanceFromYards} from 'utils/convert';

const CoursePlaySection = ({
  onPressPlay,
  nearbyCourses,
  teeSelected,
  pickerTeeRef,
  loadingNearby,
  requestPermission,
  isLocationPermissionAllowed,
}) => {
  const user = useSelector(state => state.user);
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';
  const navigation = useNavigation();
  const onFindDifferentCourse = () => {
    GA_playNavClick('find different course');
    navigation?.navigate('ScoresStats', {
      screen: 'FindACourse',
    });
  };
  const distanceMeterUnit = !!(
    user?.measurementUnits && user?.measurementUnits.toLowerCase() === 'meters'
  );

  const HEIGHT_ITEM = 253;
  const WITDH_ITEM = wp(100) - 20;
  // Set default text distance width to 120 pixels
  const [textDistanceWidth, setTextDistanceWidth] = useState(120);
  const renderLoading = () => {
    return (
      <View style={[{flexDirection: 'row', marginTop: 30}]}>
        <ShimmerPlaceholder
          LinearGradient={LinearGradient}
          width={WITDH_ITEM}
          height={HEIGHT_ITEM}
          shimmerStyle={{
            borderRadius: 24,
          }}
        />
      </View>
    );
  };
  let nearestCourse = nearbyCourses?.[0];
  let stateData = '';
  if (nearestCourse?.stateShort && nearestCourse?.stateShort !== '') {
    stateData = nearestCourse?.stateShort;
  } else if (nearestCourse?.address2 && nearestCourse?.address2 !== '') {
    stateData = nearestCourse?.address2;
  } else if (nearestCourse?.otherState && nearestCourse?.otherState !== '') {
    stateData = nearestCourse?.otherState;
  } else {
    stateData = nearestCourse?.address1?.split(',')?.[1];
  }
  const teeDistanceObj = convertDistanceFromYards({
    distanceInYards: teeSelected?.ydsTotal,
    userUnit: userDistanceUnit,
  });
  return loadingNearby && isLocationPermissionAllowed ? (
    renderLoading()
  ) : (
    <View style={[styles.container]}>
      <View
        style={{
          marginTop: 12,
        }}
      />
      {Platform.OS === 'ios' ? (
        <BlurView
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            borderRadius: 24,
          }}
          blurType="light"
          blurAmount={10}
          reducedTransparencyFallbackColor="white"
        />
      ) : (
        <BlurView
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            borderRadius: 24,
          }}
          blurType="light"
          blurAmount={32}
          reducedTransparencyFallbackColor="white"
        />
      )}
      {isLocationPermissionAllowed ? (
        <>
          <Text
            Din79Font
            size={22}
            style={styles.courseNameText}
            numberOfLines={2}
            black
          >
            {nearestCourse?.courseName}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              marginHorizontal: 16,
            }}
          >
            <View>
              <Text
                Din79Font
                style={[
                  styles.courseAddressText,
                  {
                    //52: two side padding
                    maxWidth: wp(100) - textDistanceWidth - 52,
                  },
                ]}
                numberOfLines={1}
                size={12}
                black
              >
                {nearestCourse?.city}
                {`${stateData ? ', ' + stateData : ''}`}
              </Text>
            </View>
            <Text
              Din79Font
              style={styles.distanceText}
              numberOfLines={1}
              size={12}
              black
              onLayout={event => {
                const {width} = event.nativeEvent.layout;
                setTextDistanceWidth(width);
              }}
            >
              {'  '}|{'  '}
              {parseFloat(
                valueOfMeasureMiWithKM(
                  nearestCourse?.distance,
                  distanceMeterUnit,
                ),
              ).toFixed(2)}{' '}
              {' ' + nameOfMeasureMiWithKM(distanceMeterUnit) + ' '} away
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.buttonSelectTee, appStyles.viewShadow]}
            activeOpacity={0.7}
            onPress={() => {
              pickerTeeRef.current?.snapTo(0);
            }}
            disabled={!teeSelected}
          >
            <View style={{flex: 1}}>
              <Text Din79Font size={12} style={styles.teeSelectText} black>
                play.tee_select
              </Text>
              <Text style={{fontWeight: '400', marginTop: 8}} size={16} black>
                {teeSelected
                  ? `${teeSelected.teeName}${
                      teeDistanceObj?.value
                        ? ` - ${teeDistanceObj?.value} ${teeDistanceObj?.unit}`
                        : ''
                    }`
                  : NOT_APPLICABLE}
              </Text>
            </View>
            {teeSelected ? (
              <TouchableOpacity
                onPress={() => {
                  pickerTeeRef.current?.snapTo(0);
                }}
              >
                <Image source={ArrowIcon} style={{width: 40, height: 40}} />
              </TouchableOpacity>
            ) : null}
          </TouchableOpacity>
          <TouchableOpacity onPress={onPressPlay}>
            <View style={[styles.buttonStartRound, appStyles.viewShadow]}>
              <Text size={12} Din79Font style={styles.startMyRoundText} black>
                play.start_my_round
              </Text>
            </View>
          </TouchableOpacity>
        </>
      ) : (
        <>
          <Text
            Din79Font
            size={16}
            style={{fontWeight: '800', letterSpacing: 2.08}}
            black
          >
            play.location_services
          </Text>
          <Text
            style={{
              marginVertical: 24,
              textAlign: 'center',
              marginHorizontal: 20,
            }}
            size={12}
          >
            play.location_services.description
          </Text>
          <TouchableOpacity onPress={requestPermission}>
            <View style={styles.enableLocationButton}>
              <Text size={12} Din79Font style={styles.enableLocationText} black>
                play.enable_location
              </Text>
            </View>
          </TouchableOpacity>
        </>
      )}
      <TouchableOpacity onPress={onFindDifferentCourse}>
        <Text Din79Font size={12} style={styles.findCourseText}>
          {isLocationPermissionAllowed
            ? 'play.find_different_course'
            : 'play.find_course_manually'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 30,
    borderRadius: 24,
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.3)',
    overflow: Platform.OS === 'android' ? 'hidden' : 'visible',
  },
  courseNameText: {
    fontWeight: '800',
    textTransform: 'uppercase',
    textAlign: 'center',
    width: '100%',
    paddingHorizontal: 16,
  },
  courseAddressText: {
    fontWeight: '700',
    paddingVertical: 20,
    textTransform: 'uppercase',
    letterSpacing: 1.62,
    flex: 1,
  },
  distanceText: {
    fontWeight: '700',
    paddingVertical: 20,
    textTransform: 'uppercase',
    letterSpacing: 1.62,
  },
  buttonSelectTee: {
    marginHorizontal: 16,
    paddingHorizontal: 16,
    flexDirection: 'row',
    borderRadius: 24,
    paddingVertical: 8,
    backgroundColor: 'white',
  },
  teeSelectText: {
    fontWeight: '700',
    letterSpacing: 1.62,
    textTransform: 'uppercase',
  },
  buttonStartRound: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'white',
    borderRadius: 25,
    marginTop: 24,
  },
  startMyRoundText: {
    fontWeight: '700',
    letterSpacing: 1.62,
    textTransform: 'uppercase',
  },
  enableLocationButton: {
    paddingVertical: 15,
    paddingHorizontal: 16,
    backgroundColor: 'white',
    borderRadius: 25,
  },
  enableLocationText: {
    fontWeight: '700',
    letterSpacing: 1.62,
    textTransform: 'uppercase',
  },
  findCourseText: {
    fontWeight: '700',
    textTransform: 'uppercase',
    textDecorationLine: 'underline',
    letterSpacing: 1.62,
    color: 'rgba(0, 0, 0, 0.6)',
    marginTop: 8,
    marginBottom: 16,
  },
});

export default CoursePlaySection;
