import React from 'react';
import {View, StyleSheet} from 'react-native';
import Text from 'components/Text';
import CircleChartSmall from 'components/CircleChart/CircleChartSmall';
import ProgressCircleCustom from 'components/ProgressCircleCustom';
import {ERROR_RED, GRAY_BACKGROUND} from 'config';
const PuttsClassicStats = ({
  overallStatsData,
  courseHolesLength,
  isHorizontal = false,
}) => {
  const renderCircleChartWithDot = () => {
    const totalPutts = Math.round(overallStatsData?.putts?.totalPutts || 0);
    const MAX_PUTT_VALUE = courseHolesLength === 9 ? 24 : 48;
    const STANDARD_VALUE = courseHolesLength === 9 ? 18 : 36;
    return (
      <ProgressCircleCustom
        value={totalPutts}
        maxValue={MAX_PUTT_VALUE}
        size={108}
        valueDot={STANDARD_VALUE}
        colorProgress={
          totalPutts > STANDARD_VALUE ? ERROR_RED : 'rgba(3, 168, 0, 1)'
        }
        endText={''}
        duration={400}
        border={7}
        Din79Font
        textStyle={{
          fontWeight: '700',
          fontSize: 34,
        }}
        dotSFPro
        backgroundColor={'rgba(235, 235, 235, 1)'}
      />
    );
  };
  const renderPuttSmallChart = (title, value, color) => {
    return (
      <View style={styles.numOfPuttsContainerView}>
        <Text MonoFont style={{fontWeight: '700', marginBottom: 6}} black>
          {title}
        </Text>
        <CircleChartSmall
          value={value}
          color={color}
          backgroundColor={'rgba(235, 235, 235, 1)'}
        />
      </View>
    );
  };
  return (
    <View
      style={{
        alignItems: 'center',
        flex: 1,
        marginVertical: 24,
      }}
    >
      <Text
        Din79Font
        style={{fontWeight: '800', marginBottom: 13, letterSpacing: 2.08}}
        size={16}
        black
      >
        scoreStats.title.putts
      </Text>
      <View
        style={{
          flexDirection: isHorizontal ? 'row' : 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {renderCircleChartWithDot()}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginTop: isHorizontal ? 0 : 16,
            marginLeft: isHorizontal ? 10 : 0,
          }}
        >
          {renderPuttSmallChart(
            '1',
            Math.round(overallStatsData?.putts?.onePuttPercent || 0),
            'rgba(58, 186, 86, 1)',
          )}
          {renderPuttSmallChart(
            '2',
            Math.round(overallStatsData?.putts?.twoPuttsPercent || 0),
            'rgba(0,65,105,255)',
          )}
          {renderPuttSmallChart(
            '3+',
            Math.round(overallStatsData?.putts?.moreThreePuttsPercent || 0),
            'rgba(255, 0, 0, 1)',
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  numOfPuttsContainerView: {
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
});
export default PuttsClassicStats;
