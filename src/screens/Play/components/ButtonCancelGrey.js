import React from 'react';
import {TouchableOpacity, Image, Platform} from 'react-native';
import GreyCloseIcon from 'assets/imgs/cancel-icon-grey.png';

const ButtonCancelGrey = ({onPress, top}) => {
  return (
    <TouchableOpacity
      style={{
        position: 'absolute',
        top: top !== undefined ? top : Platform.OS === 'ios' ? 25 : 0,
        right: 0,
        zIndex: 6,
        padding: 20,
      }}
      onPress={onPress}
    >
      <Image source={GreyCloseIcon} style={{width: 24, height: 24}} />
    </TouchableOpacity>
  );
};

export default ButtonCancelGrey;
