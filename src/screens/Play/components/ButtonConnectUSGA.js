import React from 'react';
import {
  View,
  Image,
  Platform,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Text from 'components/Text';
import appStyles from 'styles/global';
import ConnectUSGAButton from 'assets/imgs/connect-usga-image.png';
import {BlurView} from '@react-native-community/blur';
import USGA_Icon from 'assets/imgs/playScore/USGA_logo.svg';
import Handicap_data_affiliate_Icon from 'assets/imgs/playScore/Handicap_data_affiliate.svg';
import {canShowGHIN, checkConnectedGHIN, checkConnectedWHS} from 'utils/user';
import {useSelector} from 'react-redux';
import {isCanadaMarket} from 'utils/commonVariable';
import {GA_playNavClick} from 'utils/googleAnalytics';

const ButtonConnectUSGA = ({navigation}) => {
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
  const user = useSelector(state => state.user);
  const ghin = useSelector(state => state.ghin);
  const whs = useSelector(state => state.whs);
  const canShowConnectUSGA =
    !isCanadaMarket() &&
    !checkConnectedGHIN(user) &&
    canShowGHIN(showHideFeatures, user);
  const canShowConnectWHS = isCanadaMarket() && !checkConnectedWHS(whs);
  const openHandicapScreen = () => {
    GA_playNavClick('sync handicap index');
    navigation?.navigate('Profile', {
      screen: 'HandicapUSGA',
      params: {
        clickLocation: 'play',
      },
    });
  };
  return canShowConnectWHS || canShowConnectUSGA ? (
    <TouchableOpacity onPress={openHandicapScreen}>
      <View style={[styles.container, appStyles.viewShadow]}>
        {Platform.OS === 'ios' ? (
          <BlurView
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              borderRadius: 16,
            }}
            blurType="light"
            blurAmount={10}
            reducedTransparencyFallbackColor="white"
          />
        ) : (
          <BlurView
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              borderRadius: 16,
            }}
            blurType="light"
            blurAmount={20}
            reducedTransparencyFallbackColor="white"
          />
        )}
        <View style={styles.innerView}>
          <View style={styles.rightPanel}>
            {canShowConnectUSGA ? (
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <USGA_Icon style={{marginRight: 4}} />
                <Handicap_data_affiliate_Icon />
              </View>
            ) : (
              <Text size={16} style={{fontWeight: '700'}} black>
                play.whs.sync_your_handicap
              </Text>
            )}
            <Text size={12} black style={{marginTop: 12, lineHeight: 16.5}}>
              play.ghin.do_you_have_a_handicap
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.3)',
    overflow: Platform.OS === 'android' ? 'hidden' : 'visible',
  },
  innerView: {
    flexDirection: 'row',
    overflow: 'hidden',
    alignItems: 'center',
    marginVertical: 24,
  },
  image: {width: 110, height: 110, borderRadius: 20, margin: 4},
  rightPanel: {flex: 1, paddingHorizontal: 12},
});

export default ButtonConnectUSGA;
