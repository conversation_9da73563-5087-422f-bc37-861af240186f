import React from 'react';
import {View, StyleSheet} from 'react-native';
import Text from 'components/Text';
import GreenHillImg from 'assets/imgs/green-hill.svg';
import FlagIcon from 'assets/imgs/green-flag.svg';
import CircleChartSmall from 'components/CircleChart/CircleChartSmall';

const ApproachClassicStats = ({overallStatsData}) => {
  return (
    <View
      style={{
        marginVertical: 24,
        paddingBottom: 39,
        alignItems: 'center',
      }}
    >
      <Text Din79Font size={16} style={styles.statsTitle} black>
        scoreStats.title.approach
      </Text>
      <View
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: 39,
        }}
      >
        <View style={styles.approachChartView}>
          <CircleChartSmall
            value={Math.round(overallStatsData?.grMissedLongPercent || 0)}
            color={'rgba(255, 0, 0, 1)'}
            style={{position: 'absolute', top: -23}}
          />
          <CircleChartSmall
            value={Math.round(overallStatsData?.grMissedLeftPercent || 0)}
            color={'rgba(255, 0, 0, 1)'}
            style={{position: 'absolute', left: -23, top: 75}}
          />
          <CircleChartSmall
            value={Math.round(overallStatsData?.grMissedRightPercent || 0)}
            color={'rgba(255, 0, 0, 1)'}
            style={{position: 'absolute', right: -23, top: 75}}
          />
          <CircleChartSmall
            value={Math.round(overallStatsData?.grMissedShortPercent || 0)}
            color={'rgba(255, 0, 0, 1)'}
            style={{position: 'absolute', bottom: -23}}
          />
          <GreenHillImg />
          <FlagIcon style={{position: 'absolute', top: 45}} />
          <View style={{position: 'absolute', alignItems: 'center'}}>
            <View style={{flexDirection: 'row'}}>
              <Text
                size={34}
                Din79Font
                style={{fontWeight: '700', marginTop: 8}}
                white
              >
                {Math.round(
                  overallStatsData?.classicStatsGreensInRegulation || 0,
                )}
              </Text>
              <Text MonoFont style={{fontWeight: '700', marginTop: 16}} white>
                %
              </Text>
            </View>
            <Text size={16} style={{fontWeight: '400', marginRight: 10}} white>
              scoreStats.title.gir
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  statsTitle: {fontWeight: '800', letterSpacing: 2.08},
  approachChartView: {
    width: 196,
    height: 196,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 98,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default ApproachClassicStats;
