import React, {useEffect, useImperativeHandle, useState} from 'react';
import {
  View,
  SafeAreaView,
  StyleSheet,
  Image,
  TouchableOpacity,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {t} from 'i18next';
import moment from 'moment/moment';
import Text from 'components/Text';
import appStyles from 'styles/global';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {FlatList} from 'react-native-gesture-handler';
import {getAuth0AccessToken} from 'utils/user';
import {openEcomWebview, convertProductIdToImageURL} from 'utils/shop';
import {getConfig} from 'config/env';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import {updateRewardProducts} from 'utils/loyalty';
import {getEcomSite} from 'utils/countries';
import {
  GA_logEvent,
  GA_logViewItemList,
  GA_selectPromotionWithEcomData,
} from 'utils/googleAnalytics';
import {
  GA_EVENT_NAME,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_TYPES,
  WEBVIEW_PAGE_TYPE,
} from 'utils/constant';
import {isEmpty, map} from 'lodash';
import {isCanadaMarket} from 'utils/commonVariable';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const TIERCOLORS = [
  {
    name: 'PAR',
    color: '#03A800',
  },
  {
    name: 'BIRDIE',
    color: '#00A3FF',
  },
  {
    name: 'EAGLE',
    color: '#FC0',
  },
];

const ShopWithPoint = ({}, ref) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const {navigate} = navigation;
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const {points, tier, rewardProducts} = useSelector(state => state?.loyalty);
  const {currentTier} = tier;
  const {availablePoints, pointsToExpire} = points || 0;
  const pointsApplied = availablePoints;
  const {pointsToExpireDate} = points || null;
  const isFirstRender = React.useRef(true);
  const user = useSelector(state => state.user);
  const {getEcomProductDetail, getMultipleEcomProductDetails} =
    useEcomProductDetail();
  const isFocused = useIsFocused();

  let dayExpire = 0;
  if (pointsToExpireDate) {
    dayExpire = moment(points?.pointsToExpireDate).diff(moment(), 'days');
  }
  const findColorTier = currentTier => {
    if (currentTier) {
      const item = TIERCOLORS.find(
        val => val.name === currentTier.toUpperCase(),
      );
      if (item) {
        return item.color;
      }
    }

    return '#03A800';
  };
  const tierColor = findColorTier(currentTier);

  useImperativeHandle(ref, () => ({
    refreshData: () => {
      loadData();
      isFirstRender.current = false;
    },
  }));

  useEffect(() => {
    if (isFocused) {
      if (!isFirstRender.current) {
        updateRewardProducts(dispatch, user?.userCountry);
        loadData();
      }
    }
  }, [isFocused]);

  const onItemPress = async (item, index) => {
    GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
      screen_type: SCREEN_TYPES.REWARDS,
      page_name: PAGE_NAME.REWARDS_MAIN_INFO,
      page_type: SCREEN_TYPES.REWARDS,
      page_category: PAGE_CATEGORY.REWARDS_MAIN,
      nav_type: 'rewards',
      nav_item_selected: item?.productName,
      nav_level: tier?.currentTier + ' benefits',
    });
    const accessToken = await getAuth0AccessToken(dispatch);
    const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
    const ecomSite = getEcomSite();
    const urlProduct = `https://${ECOM_HOST_URL}/${item?.productId}.html?lang=en_US`;
    let lang = 'en_US';
    if (isCanadaMarket()) {
      lang = 'en_CA';
    }
    const pdpUrl = `https://${ECOM_HOST_URL}/on/demandware.store/Sites-${ecomSite}-Site/${lang}/MyTM-OpenSession?provider=auth0&page=pdp&token=${accessToken}&iframe=true&pid=${item?.productId}`;
    logGASelectPromotion(item, index);
    openEcomWebview(
      navigate,
      {
        title: item?.productName,
        uri: pdpUrl,
        canGoBack: true,
        originUri: urlProduct,
        imageUrl: item?.image,
        clickLocation: 'rewards-shop-with-points',
        origin: 'RewardProductWeb',
        isLogSelectItem: false,
      },
      getEcomProductDetail,
    );
  };

  const logGASelectPromotion = async (productItem, index) => {
    if (productItem) {
      const listProductId = [];
      if (productItem.productId) {
        try {
          let productId = productItem.productId;
          let productDetails = await getEcomProductDetail(productId);
          if (productDetails) {
            listProductId.push(productId);
          }
        } catch (error) {}
      }

      const tileTitle = productItem?.productName;
      if (!isEmpty(listProductId)) {
        const paramGA = {
          promotion_id: productItem?.productId,
          promotion_name: productItem?.productName,
          creative_name: 'Circle Script T-Shirt',
          creative_slot: 'Reward > Shop With Points',
          location_id: 'Reward > Shop With Points',
        };
        const rsLogGA = await GA_selectPromotionWithEcomData({
          listProductId,
          paramGA,
          clickIndex: index,
          tileTitle,
          getMultipleEcomProductDetails,
        });
        if (!rsLogGA) {
          logEventPromotion({
            products: [productItem],
            eventName: GA_EVENT_NAME.SELECT_PROMOTION,
            clickIndex: index,
            tileTitle,
          });
        }
      } else {
        logEventPromotion({
          products: [productItem],
          eventName: GA_EVENT_NAME.SELECT_PROMOTION,
          clickIndex: index,
          tileTitle,
        });
      }
    }
  };

  const logEventPromotion = ({
    products,
    eventName,
    clickIndex = 0,
    tileTitle,
  }) => {
    if (products && products.length) {
      const items = [];
      const listProductId = map(products, 'productId').toString();
      const listProductName = map(products, 'productName').toString();
      products.map((val, key) => {
        const item = {
          promotion_id: val?.productId,
          promotion_name: val?.productName,
          creative_name: 'Circle Script T-Shirt',
          creative_slot: 'Reward > Shop With Points',
          location_id: 'Reward > Shop With Points',
          item_id: val?.productId,
          item_name: val?.productName,
          index: key + 1 + '',
          price: val?.price ? Number(val.price) : 0,
          item_list_id: listProductId,
          item_list_name: listProductName,
        };
        if (eventName === GA_EVENT_NAME.SELECT_PROMOTION) {
          item.index = clickIndex + 1 + '';
        }
        items.push(item);
      });
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  const handleDataProduct = (object, key) => {
    const {creditsToCurrencyRatio} = points;
    const image =
      object?.image?.link ||
      convertProductIdToImageURL(object?.productId) ||
      '';
    let price = object?.price || '0';
    let priceDiscount = '0';
    if (pointsApplied && price) {
      priceDiscount =
        Number(price) - Number(pointsApplied) * Number(creditsToCurrencyRatio);
      let realPointsApplied = pointsApplied;
      if (priceDiscount && priceDiscount > 0) {
        priceDiscount = priceDiscount.toFixed(2);
      } else {
        realPointsApplied = Number(price) / Number(creditsToCurrencyRatio);
        realPointsApplied = realPointsApplied?.toFixed?.(0);
        priceDiscount = '0.00';
      }
      return {
        ...object,
        priceDiscount: priceDiscount,
        pointsApplied: realPointsApplied.toString(),
        image,
      };
    }

    return {
      ...object,
      priceDiscount: price,
      price: '0.00',
      pointsApplied: pointsApplied.toString() || '0',
      image,
    };
  };

  const loadData = async () => {
    try {
      setLoading(true);
      if (rewardProducts && rewardProducts.length) {
        const handleData = rewardProducts.map((val, key) => {
          return handleDataProduct(val, key);
        });
        setData(handleData);
        GA_logViewItemList(
          handleData.map(item => item.productId),
          'rewards-shop-with-points',
          t('rewards.shopWithPoint'),
          undefined,
          getMultipleEcomProductDetails,
        );
        logEventPromotion({
          products: handleData,
          eventName: GA_EVENT_NAME.VIEW_PROMOTION,
        });
      } else {
        setData([]);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const formatTime = () => {
    if (points?.pointsToExpireDate) {
      return moment(points?.pointsToExpireDate).fromNow(true);
    }
    return '';
  };

  const renderItem = ({item, index}) => {
    return (
      <TouchableOpacity
        index={index}
        activeOpacity={0.8}
        delayPressIn={100}
        delayPressOut={100}
        onPress={() => onItemPress(item, index)}
        style={[styles.itemWrapper, appStyles.viewShadowLightBig]}
      >
        <Image
          style={[styles.itemImage, {marginBottom: 12}]}
          source={{uri: item?.image || ''}}
        />
        <View style={{marginLeft: 4}}>
          {item?.pointsApplied && item?.pointsApplied !== '0' && (
            <Text
              style={[styles.viewPointApply, {color: tierColor}]}
              weight={700}
              size={12}
            >
              {`${item?.pointsApplied} Points Applied`}
            </Text>
          )}

          <Text
            numberOfLines={2}
            Din79Font
            style={styles.title}
            weight={800}
            size={16}
          >
            {item?.productName}
          </Text>
          <View style={styles.viewPriceAll}>
            {item?.priceDiscount && (
              <Text style={styles.viewPriceDiscount} weight={400} size={12}>
                {`$${item?.priceDiscount}`}
              </Text>
            )}

            {item?.price && item?.price !== '0' && (
              <Text style={styles.viewPrice} weight={400} size={12}>
                {`$${item?.price}`}
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <View>
      <View style={styles.viewText}>
        {pointsToExpire && formatTime() ? (
          <View style={styles.viewPoint}>
            <Text size={16} weight={700} style={styles.pointsToExpire}>
              {pointsToExpire}
            </Text>
            <Text
              size={16}
              weight={400}
              style={styles.dayExpire}
            >{` Points Expiring in ${formatTime()}`}</Text>
          </View>
        ) : null}

        {data && data.length > 0 ? (
          <Text size={16} weight={700} style={styles.shopWithPoint}>
            {t('rewards.shopWithPoint')}
          </Text>
        ) : null}
      </View>
      {loading && !data.length ? (
        <View style={[appStyles.row]}>
          <View style={styles.viewLoading}>
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={160}
              height={160}
              style={styles.viewShimmer}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={140}
              height={18}
              style={[styles.viewShimmer2, {marginTop: '12%'}]}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={100}
              height={13}
              style={styles.viewShimmer2}
            />
          </View>
          <View style={styles.viewLoading}>
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={160}
              height={160}
              style={styles.viewShimmer}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={140}
              height={18}
              style={[styles.viewShimmer2, {marginTop: '12%'}]}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={100}
              height={13}
              style={styles.viewShimmer2}
            />
          </View>
          <View style={styles.viewLoading}>
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={160}
              height={160}
              style={styles.viewShimmer}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={140}
              height={18}
              style={[styles.viewShimmer2, {marginTop: '12%'}]}
            />
            <ShimmerPlaceholder
              LinearGradient={LinearGradient}
              width={100}
              height={13}
              style={styles.viewShimmer2}
            />
          </View>
        </View>
      ) : data && data.length > 0 ? (
        <FlatList
          data={data}
          horizontal
          keyExtractor={(item, index) => item?.id}
          renderItem={renderItem}
          style={{width: wp(100), marginBottom: 22}}
          contentContainerStyle={{paddingRight: 8}}
          showsHorizontalScrollIndicator={false}
        />
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  viewText: {
    marginHorizontal: 16,
    marginBottom: 14,
  },
  viewPoint: {
    flexDirection: 'row',
    marginBottom: 2,
  },
  pointsToExpire: {
    color: '#fff',
    lineHeight: 18,
  },
  dayExpire: {
    color: '#fff',
    lineHeight: 18,
    textTransform: 'capitalize',
  },
  shopWithPoint: {
    color: '#fff',
    lineHeight: 18,
  },
  itemWrapper: {
    width: wp(43),
    height: wp(70),
    marginLeft: 8,
    flexDirection: 'column',
    borderRadius: 16,
    backgroundColor: '#000',
    minHeight: 186,
    padding: 4,
  },
  itemImage: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    height: wp(41),
    width: wp(41),
  },
  viewPointApply: {
    lineHeight: 16.5,
  },
  title: {
    lineHeight: 20.668,
    letterSpacing: 1.28,
    textTransform: 'uppercase',
    marginBottom: 8,
    color: '#fff',
    minHeight: 40,
  },
  viewPriceAll: {
    flexDirection: 'row',
  },
  viewPriceDiscount: {
    lineHeight: 16.5,
    color: '#fff',
    marginRight: 8,
  },
  viewPrice: {
    lineHeight: 16.5,
    color: '#b2b2b2',
    textDecorationLine: 'line-through',
  },
  viewLoading: {
    width: 168,
    height: 272,
    borderRadius: 16,
    backgroundColor: '#000',
    margin: 4,
  },
  viewShimmer: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    margin: 4,
  },
  viewShimmer2: {
    margin: 4,
  },
});

export default React.forwardRef(ShopWithPoint);
