import React, {useImperativeHandle, useState} from 'react';
import {View, StyleSheet, Image} from 'react-native';
import {getTierBenefits} from 'requests/loyalty';
import Text from 'components/Text';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {useSelector} from 'react-redux';
import {TYPE_LEVEL} from 'utils/constant';
import LockedIcon from 'assets/imgs/rewards/circle_locked.png';
import ParCheckIcon from 'assets/imgs/rewards/circle_check_white.png';
import BirdieCheckIcon from 'assets/imgs/rewards/circle_check_blue.png';
import EagleCheckIcon from 'assets/imgs/rewards/circle_check_yellow.png';
import InactiveCheckIcon from 'assets/imgs/rewards/circle_check_gray.png';

const PAR_COLOR = 'white';
const BIRDIE_COLOR = 'rgba(2, 129, 204, 1)';
const EAGLE_COLOR = 'rgba(230, 184, 0, 1)';

const TierLevels = ({}, ref) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const loyalty = useSelector(state => state.loyalty);
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  useImperativeHandle(ref, () => ({
    refreshData: () => {
      loadData();
    },
  }));

  const loadData = async () => {
    try {
      setLoading(true);
      const dataBenefits = await getTierBenefits(
        userCountry,
        loyalty?.tier?.currentTier?.toLowerCase?.(),
      );
      if (dataBenefits.length > 0) {
        setData(dataBenefits);
      } else {
        setData([]);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  const isParLevel =
    loyalty?.tier?.currentTier?.toUpperCase() === TYPE_LEVEL.PAR;
  const isBirdieLevel =
    loyalty?.tier?.currentTier?.toUpperCase() === TYPE_LEVEL.BIRDIE;
  const isEagleLevel =
    loyalty?.tier?.currentTier?.toUpperCase() === TYPE_LEVEL.EAGLE;
  const renderHeader = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          height: 60,
          backgroundColor: 'black',
        }}
      >
        <View style={styles.tierDescriptionView}>
          <Text Din79Font white weight={700} size={12} style={styles.textTitle}>
            Tier
          </Text>
        </View>
        <View
          style={[
            styles.tierParView,
            {
              backgroundColor: isParLevel ? PAR_COLOR : 'transparent',
            },
          ]}
        >
          <Text
            Din79Font
            weight={800}
            size={16}
            style={[
              styles.tierTextTitle,
              {
                color: isParLevel ? 'black' : 'rgba(77, 77, 77, 1)',
              },
            ]}
          >
            PAR
          </Text>
        </View>
        <View
          style={[
            styles.tierBirdieView,
            {backgroundColor: isBirdieLevel ? BIRDIE_COLOR : 'transparent'},
          ]}
        >
          <Text
            Din79Font
            white
            weight={800}
            size={16}
            style={[
              styles.tierTextTitle,
              {
                color: isBirdieLevel ? 'black' : 'rgba(77, 77, 77, 1)',
              },
            ]}
          >
            BIRDIE
          </Text>
        </View>
        <View
          style={[
            styles.tierEagleView,
            {backgroundColor: isEagleLevel ? EAGLE_COLOR : 'transparent'},
          ]}
        >
          <Text
            Din79Font
            white
            weight={800}
            size={16}
            style={[
              styles.tierTextTitle,
              {
                color: isEagleLevel ? 'black' : 'rgba(77, 77, 77, 1)',
              },
            ]}
          >
            EAGLE
          </Text>
        </View>
      </View>
    );
  };
  const renderContent = () => {
    return (
      <View style={{flex: 1}}>
        {data.map((item, index) => {
          return (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor:
                  index % 2 !== 0 ? 'black' : 'rgba(26, 26, 26, 1)',
                flex: 1,
              }}
            >
              <View style={styles.tierDescriptionView}>
                <Text size={12} style={styles.textBenefit}>
                  {item.title}
                </Text>
              </View>
              <View
                style={[
                  styles.tierParView,
                  {paddingVertical: 10},
                  isParLevel
                    ? {
                        borderRightColor: PAR_COLOR,
                        borderLeftColor: PAR_COLOR,
                        borderRightWidth: 2,
                        borderLeftWidth: 2,
                      }
                    : {},
                  isParLevel && index === data?.length - 1
                    ? {
                        borderBottomColor: PAR_COLOR,
                        borderBottomWidth: 2,
                      }
                    : {},
                ]}
              >
                {item.par?.lock ? (
                  <Image source={LockedIcon} style={styles.logoImage} />
                ) : item.par?.value === true ? (
                  isParLevel ? (
                    <Image source={ParCheckIcon} style={styles.logoImage} />
                  ) : (
                    <Image
                      source={InactiveCheckIcon}
                      style={styles.logoImage}
                    />
                  )
                ) : (
                  <Text
                    Din79Font
                    style={[
                      item?.title.toLowerCase() === 'points accelerator'
                        ? styles.tierTextContentLarge
                        : styles.tierTextContent,
                      {color: isParLevel ? 'white' : 'rgba(77, 77, 77, 1)'},
                    ]}
                  >
                    {item.par?.value}
                  </Text>
                )}
              </View>
              <View
                style={[
                  styles.tierBirdieView,
                  {paddingVertical: 10},
                  isBirdieLevel
                    ? {
                        borderRightColor: BIRDIE_COLOR,
                        borderLeftColor: BIRDIE_COLOR,
                        borderRightWidth: 2,
                        borderLeftWidth: 2,
                      }
                    : {},
                  isBirdieLevel && index === data?.length - 1
                    ? {
                        borderBottomColor: BIRDIE_COLOR,
                        borderBottomWidth: 2,
                      }
                    : {},
                ]}
              >
                {item.birdie?.lock ? (
                  <Image source={LockedIcon} style={styles.logoImage} />
                ) : item.birdie?.value === true ? (
                  isBirdieLevel ? (
                    <Image source={BirdieCheckIcon} style={styles.logoImage} />
                  ) : (
                    <Image
                      source={InactiveCheckIcon}
                      style={styles.logoImage}
                    />
                  )
                ) : (
                  <Text
                    Din79Font
                    weight={800}
                    size={16}
                    style={[
                      item?.title.toLowerCase() === 'points accelerator'
                        ? styles.tierTextContentLarge
                        : styles.tierTextContent,
                      {color: isBirdieLevel ? 'white' : 'rgba(77, 77, 77, 1)'},
                    ]}
                  >
                    {item.birdie?.value}
                  </Text>
                )}
              </View>
              <View
                style={[
                  styles.tierEagleView,
                  {paddingVertical: 10},
                  isEagleLevel
                    ? {
                        borderRightColor: EAGLE_COLOR,
                        borderLeftColor: EAGLE_COLOR,
                        borderRightWidth: 2,
                        borderLeftWidth: 2,
                      }
                    : {},
                  isEagleLevel && index === data?.length - 1
                    ? {
                        borderBottomColor: EAGLE_COLOR,
                        borderBottomWidth: 2,
                      }
                    : {},
                ]}
              >
                {item.eagle?.lock ? (
                  <Image source={LockedIcon} style={styles.logoImage} />
                ) : item.eagle?.value === true ? (
                  isEagleLevel ? (
                    <Image source={EagleCheckIcon} style={styles.logoImage} />
                  ) : (
                    <Image
                      source={InactiveCheckIcon}
                      style={styles.logoImage}
                    />
                  )
                ) : (
                  <Text
                    Din79Font
                    weight={800}
                    size={16}
                    style={[
                      item?.title.toLowerCase() === 'points accelerator'
                        ? styles.tierTextContentLarge
                        : styles.tierTextContent,
                      {color: isEagleLevel ? 'white' : 'rgba(77, 77, 77, 1)'},
                    ]}
                  >
                    {item.eagle?.value}
                  </Text>
                )}
              </View>
            </View>
          );
        })}
      </View>
    );
  };
  return (
    <View style={{marginBottom: 24, marginTop: 16}}>
      {renderHeader()}
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  tierParView: {
    paddingHorizontal: 8,
    width: wp(16.7),
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tierDescriptionView: {
    paddingHorizontal: 8,
    width: wp(39.5),
    paddingVertical: 14,
    borderRightWidth: 1,
    borderRightColor: 'rgba(51, 51, 51, 1)',
    height: '100%',
    justifyContent: 'center',
  },
  tierBirdieView: {
    paddingHorizontal: 8,
    width: wp(21.9),
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tierEagleView: {
    paddingHorizontal: 8,
    width: wp(21.9),
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textTitle: {
    textTransform: 'uppercase',
    letterSpacing: 1.62,
    color: 'rgba(179, 179, 179, 1)',
  },
  textBenefit: {
    color: 'rgba(179, 179, 179, 1)',
  },
  tierTextTitle: {
    textTransform: 'uppercase',
    letterSpacing: 1.28,
    textAlign: 'center',
  },
  tierTextContentLarge: {
    textTransform: 'uppercase',
    letterSpacing: 1.28,
    textAlign: 'center',
    fontWeight: '800',
    fontSize: 16,
  },
  tierTextContent: {
    textTransform: 'uppercase',
    letterSpacing: 1.62,
    textAlign: 'center',
    fontWeight: '700',
    fontSize: 12,
  },
  logoImage: {
    width: 24,
    height: 24,
  },
});

export default React.forwardRef(TierLevels);
