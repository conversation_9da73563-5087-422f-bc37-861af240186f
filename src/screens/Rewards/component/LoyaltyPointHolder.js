import Text from 'components/Text/Text';
import React, {useImperativeHandle, useState, useEffect} from 'react';
import {View, StyleSheet} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import appStyles from 'styles/global';
import BGLine from 'assets/imgs/clubhouse/bg_line.jpeg';
import {updateUserLoyaltyData} from 'utils/loyalty';
import {useDispatch, useSelector} from 'react-redux';
import Animated, {
  useAnimatedStyle,
  Extrapolation,
  interpolate,
  useSharedValue,
  interpolateColor,
  withTiming,
} from 'react-native-reanimated';
import fontFamily from 'components/Text/styles';
import {TYPE_LEVEL} from 'utils/constant';
import DeviceInfo from 'react-native-device-info';

const widthLineMax = wp(86);
const widthLineMin = wp(69);
const heightHeaderMax = wp(33);
const heightHeaderMin = wp(26);
const heightPaddingTop = wp(21);
const heightHeaderMaxContainTop = wp(40);
const LoyaltyPointHolder = ({offset, paddingTop}, ref) => {
  const [wHDefault, setwHDefault] = useState({
    pts: undefined,
    money: undefined,
  });
  const {tierAll, points, tier} = useSelector(state => state?.loyalty);
  const loyalty = useSelector(state => state?.loyalty);
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const user = useSelector(state => state.user);
  const pointPTX = useSharedValue(0);
  const pointMoney = useSharedValue(0);
  const dispatch = useDispatch();
  const toValue = heightHeaderMaxContainTop - heightHeaderMin + paddingTop / 2;
  useImperativeHandle(ref, () => ({
    refreshData: async () => {
      await loadData();
    },
  }));
  const widthEachPointMax =
    tierAll?.length > 0
      ? widthLineMax /
        Number.parseInt(tierAll[0]?.tierMilestone?.tierThreshold, 10)
      : 0;
  const widthEachPointMin =
    tierAll?.length > 0
      ? widthLineMin /
        Number.parseInt(tierAll[0]?.tierMilestone?.tierThreshold, 10)
      : 0;
  const pointProgress =
    points?.totalSpent > tierAll[0]?.tierMilestone?.tierThreshold
      ? tierAll[0]?.tierMilestone?.tierThreshold
      : points?.totalSpent || 0;
  const progressMax = useSharedValue(pointProgress * widthEachPointMax);
  const progressMin = useSharedValue(pointProgress * widthEachPointMin);
  const pointDefaultBirdie =
    tierAll?.length > 1
      ? Number.parseInt(tierAll[1]?.tierMilestone?.tierThreshold, 10)
      : 0;
  const pointDefaultEagle =
    tierAll?.length > 1
      ? Number.parseInt(tierAll[0]?.tierMilestone?.tierThreshold, 10)
      : 0;
  useEffect(() => {
    progressMax.value = withTiming(pointProgress * widthEachPointMax, {
      duration: 100,
    });
    progressMin.value = withTiming(pointProgress * widthEachPointMin, {
      duration: 100,
    });
  }, [tierAll, points]);

  const loadData = async () => {
    try {
      await updateUserLoyaltyData(
        dispatch,
        user?.userCountry,
        loyalty,
        appCacheVersions,
      );
    } catch (error) {}
  };

  const formatNumber = () => {
    if (points?.creditsToCurrencyValue > 0) {
      const arr = (points?.creditsToCurrencyValue + '').split('.');
      return arr?.length > 1 ? arr : [arr, '00'];
    }
    return [0, '00'];
  };

  const styleHeader = useAnimatedStyle(() => {
    const translateY = interpolate(
      offset.value,
      [0, toValue],
      [heightPaddingTop, 0],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    const height = interpolate(
      offset.value,
      [0, toValue],
      [heightHeaderMax, heightHeaderMin],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    const backgroundColor = interpolateColor(
      offset.value,
      [0, toValue],
      ['#00000000', '#000'],
    );
    return {
      transform: [{translateY}],
      height,
      backgroundColor,
    };
  });

  const styleHide = useAnimatedStyle(() => {
    const opacity = interpolate(offset.value, [0, toValue], [1, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const marginTop = interpolate(offset.value, [0, toValue], [0, -16], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      opacity,
      marginTop,
    };
  });

  const styleTextBig = useAnimatedStyle(() => {
    const fontSize = interpolate(offset.value, [0, toValue], [22, 16], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      fontSize,
    };
  });
  const styleTextSmallTranslate = useAnimatedStyle(() => {
    const paddingTopAnimation = interpolate(
      offset.value,
      [0, toValue],
      [0, 3],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    return {
      paddingTop: paddingTopAnimation,
    };
  });

  const styleTextSmall = useAnimatedStyle(() => {
    const fontSize = interpolate(offset.value, [0, toValue], [16, 12], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      fontSize,
    };
  });

  const stylePoint = useAnimatedStyle(() => {
    const translateY = interpolate(offset.value, [0, toValue], [0, 5], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const translateX = interpolate(
      offset.value,
      [0, toValue],
      [0, -pointPTX.value + 16],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );

    return {
      transform: [{translateY}, {translateX}],
    };
  });

  const styleMoney = useAnimatedStyle(() => {
    const translateY = interpolate(offset.value, [0, toValue], [0, 21], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const translateX = interpolate(
      offset.value,
      [0, toValue],
      [0, -pointMoney.value + 16],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    return {
      transform: [{translateY}, {translateX}],
    };
  });

  const styleLine = useAnimatedStyle(() => {
    const width = interpolate(
      offset.value,
      [0, toValue],
      [widthLineMax, widthLineMin],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    const translateX = interpolate(offset.value, [0, toValue], [0, 40], {
      extrapolateRight: Extrapolation.CLAMP,
    });

    const translateY = interpolate(offset.value, [0, toValue], [0, -15], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      width,
      transform: [{translateX}, {translateY}],
    };
  });

  const styleLineActive = useAnimatedStyle(() => {
    const width = interpolate(
      offset.value,
      [0, toValue],
      [progressMax.value, progressMin.value],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    return {
      width,
    };
  });

  const styleDotActive = useAnimatedStyle(() => {
    const opacity = interpolate(offset.value, [0, toValue], [1, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      opacity,
    };
  });
  const styleDotInActive = useAnimatedStyle(() => {
    const opacity = interpolate(offset.value, [0, toValue], [0, 1], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      opacity,
    };
  });

  const styleProgressPoint = useAnimatedStyle(() => {
    const left = interpolate(
      offset.value,
      [0, toValue],
      [
        progressMax.value -
          (progressMax.value >= pointDefaultEagle * widthEachPointMax - 16
            ? 16
            : 6),
        progressMin.value -
          (progressMin.value >= pointDefaultEagle * widthEachPointMin - 16
            ? 16
            : 6),
      ],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    return {
      left,
    };
  });

  const styleDotBirdie = useAnimatedStyle(() => {
    const left = interpolate(
      offset.value,
      [0, toValue],
      [
        pointDefaultBirdie * widthEachPointMax,
        pointDefaultBirdie * widthEachPointMin,
      ],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    return {
      left: left - 2,
    };
  });

  const styleDotEagle = useAnimatedStyle(() => {
    const left = interpolate(
      offset.value,
      [0, toValue],
      [
        pointDefaultEagle * widthEachPointMax,
        pointDefaultEagle * widthEachPointMin,
      ],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    return {
      left: left - 14,
    };
  });

  const renderPoint = (textBig, textSmall, color, style) => {
    return (
      <Animated.View
        key={textSmall}
        style={[
          appStyles.row,
          {
            marginHorizontal: 8,
            minWidth: textSmall === 'PTS' ? wHDefault.pts : wHDefault.money,
          },
          style,
        ]}
      >
        <Animated.View>
          <Animated.Text style={[{color}, styles.textBig, styleTextBig]}>
            {textBig}
          </Animated.Text>
        </Animated.View>
        <Animated.View style={[textSmall !== 'PTS' && styleTextSmallTranslate]}>
          <Animated.Text style={[{color}, styles.textSmall, styleTextSmall]}>
            {textSmall}
          </Animated.Text>
        </Animated.View>
      </Animated.View>
    );
  };

  const renderPointHide = (textBig, textSmall, color) => {
    return (
      <Animated.View
        key={textSmall + 'hide'}
        style={[
          appStyles.row,
          {
            marginHorizontal: 8,
          },
        ]}
        onLayout={e => {
          if (textSmall === 'PTS') {
            setwHDefault({...wHDefault, pts: e.nativeEvent.layout.width});
            pointPTX.value = e.nativeEvent.layout.x;
          }
          if (textSmall !== 'PTS') {
            setwHDefault({...wHDefault, money: e.nativeEvent.layout.width});
            pointMoney.value = e.nativeEvent.layout.x;
          }
        }}
      >
        <Animated.Text style={[{color}, styles.textBig]}>
          {textBig}
        </Animated.Text>
        <Animated.Text style={[{color}, styles.textSmall]}>
          {textSmall}
        </Animated.Text>
      </Animated.View>
    );
  };

  const renderDot = style => {
    return (
      <Animated.View
        style={[styles.viewDot, {backgroundColor: '#fff'}, style]}
      />
    );
  };

  const renderDotOpacity = style => {
    return (
      <Animated.View
        style={[
          styles.viewDot,
          {backgroundColor: 'rgba(255, 255, 255, 0.7)'},
          style,
        ]}
      />
    );
  };

  const renderDotActive = (style, text) => {
    return (
      <Animated.View style={[styles.viewDot, {height: 12, opacity: 0}, style]}>
        <Text
          size={12}
          Din79Font
          weight={700}
          style={{
            textAlign: 'center',
            lineHeight: 13,
            paddingHorizontal: 2.5,
          }}
          gray
        >
          {text}
        </Text>
      </Animated.View>
    );
  };

  const renderProgressPoint = text => {
    return (
      <Animated.View
        style={[
          {
            padding: 2,
            borderRadius: 20,
            position: 'absolute',
            backgroundColor:
              tier?.currentTier?.toUpperCase() === TYPE_LEVEL.BIRDIE
                ? 'rgba(0, 163, 255, 1)'
                : tier?.currentTier?.toUpperCase() === TYPE_LEVEL.EAGLE
                ? 'rgba(255, 204, 0, 0.61)'
                : 'rgba(255, 255, 255, 1)',
          },
          styleProgressPoint,
        ]}
      >
        <View
          style={[
            styles.viewBorderPoint,
            {
              borderWidth:
                tier?.currentTier?.toUpperCase() === TYPE_LEVEL.PAR ? 1 : 0,
            },
          ]}
        >
          <Text
            size={14}
            Din79Font
            black
            weight={800}
            style={{
              lineHeight: 16,
              fontStyle: 'italic',
            }}
          >
            {text}
          </Text>
        </View>
      </Animated.View>
    );
  };

  const renderTextLevel = (style, text, point) => {
    return (
      <View style={[appStyles.row, {alignItems: 'center'}, style]}>
        {point && text === TYPE_LEVEL.EAGLE && (
          <Text size={12} white weight={400}>
            {point}x points
          </Text>
        )}
        <Text
          size={12}
          white
          weight={700}
          Din79Font
          style={{marginHorizontal: 4, lineHeight: 14}}
        >
          {text}
        </Text>
        {point && text !== TYPE_LEVEL.EAGLE && (
          <Text size={12} white weight={400}>
            {point}x points
          </Text>
        )}
      </View>
    );
  };

  const renderDots = () => {
    return (
      <View style={[{position: 'absolute', justifyContent: 'center'}]}>
        {renderDot([
          {
            left: 0,
            backgroundColor:
              pointProgress === 0
                ? '#fff'
                : tier?.currentTier?.toUpperCase() === TYPE_LEVEL.PAR
                ? 'rgba(0, 0, 0, 0.5)'
                : 'rgba(255, 255, 255, 0.7)',
          },
          styleDotActive,
        ])}
        {renderDot([
          {
            backgroundColor:
              pointProgress > pointDefaultBirdie
                ? 'rgba(255, 255, 255, 0.7)'
                : '#fff',
          },
          styleDotActive,
          styleDotBirdie,
        ])}
        {renderDot([
          {
            marginLeft: 2.5,
            backgroundColor:
              pointProgress > pointDefaultEagle
                ? 'rgba(255, 255, 255, 0.7)'
                : '#fff',
          },
          styleDotActive,
          styleDotEagle,
        ])}
        {pointProgress === 0
          ? renderDotActive([{left: 0}, styleDotInActive], 'P')
          : renderDotOpacity([{left: 0}, styleDotInActive])}
        {pointProgress < pointDefaultBirdie
          ? renderDotActive([styleDotInActive, styleDotBirdie], 'B')
          : renderDotOpacity([styleDotInActive, styleDotBirdie])}
        {pointProgress < pointDefaultEagle
          ? renderDotActive(
              [
                {
                  marginLeft: 2.5,
                },
                styleDotInActive,
                styleDotEagle,
              ],
              'E',
            )
          : renderDotOpacity([
              {
                marginLeft: 2.5,
              },
              styleDotInActive,
              styleDotEagle,
            ])}
      </View>
    );
  };

  const renderProgress = () => {
    return (
      <Animated.View style={[styles.viewProgressContainer, styleLine]}>
        <Animated.View
          style={[
            {
              width: progressMax.value,
              backgroundColor:
                tier?.currentTier?.toUpperCase() === TYPE_LEVEL.BIRDIE
                  ? 'rgba(0, 163, 255, 1)'
                  : 'rgba(255, 255, 255, 1)',
            },
            styles.viewLineActive,
            styleLineActive,
          ]}
        >
          {tier?.currentTier?.toUpperCase() === TYPE_LEVEL.EAGLE && (
            <Animated.View>
              <Animated.Image
                source={BGLine}
                style={[
                  {
                    width: progressMax.value,
                    height: 12,
                  },
                  styleLineActive,
                ]}
              />
              <Animated.View
                style={[
                  {
                    width: progressMax.value,
                  },
                  styles.viewLineActiveFull,
                  styleLineActive,
                ]}
              />
            </Animated.View>
          )}
        </Animated.View>
        {renderDots()}
        {renderProgressPoint(tier?.currentTier?.toUpperCase().charAt(0))}
      </Animated.View>
    );
  };

  return (
    <Animated.View
      style={[
        styles.viewContainer,
        styleHeader,
        {
          top:
            DeviceInfo.hasNotch() || DeviceInfo.hasDynamicIsland
              ? 0
              : -paddingTop,
        },
      ]}
    >
      <Animated.View style={styleHide}>
        <Text
          white
          weight={800}
          size={34}
          Din79Font
          style={{letterSpacing: 1.2}}
        >
          {tier?.currentTier?.toUpperCase()}
        </Text>
      </Animated.View>
      <Animated.View
        style={[appStyles.row, {width: wp(100), justifyContent: 'center'}]}
      >
        <View
          style={[
            appStyles.row,
            {width: wp(100), justifyContent: 'center', position: 'absolute'},
          ]}
        >
          {renderPointHide(points?.availablePoints, 'PTS', 'transparent')}
          {renderPointHide(
            `$${formatNumber()[0]}`,
            `.${formatNumber()[1]}`,
            'transparent',
          )}
        </View>
        {renderPoint(points?.availablePoints, 'PTS', '#bbb', stylePoint)}
        {renderPoint(
          `$${formatNumber()[0]}`,
          `.${formatNumber()[1]}`,
          '#fff',
          styleMoney,
        )}
      </Animated.View>
      {renderProgress()}

      <Animated.View
        style={[
          {
            flexDirection: 'row',
            alignItems: 'center',
            width: widthLineMax,
          },
          styleHide,
        ]}
      >
        {renderTextLevel(
          {marginLeft: -4},
          TYPE_LEVEL.PAR,
          tier?.currentTier?.toUpperCase() === TYPE_LEVEL.PAR
            ? tier?.currentTierPurchaseRatio
            : null,
        )}
        {renderTextLevel(
          {
            left: pointDefaultBirdie * widthEachPointMax - 14,
            position: 'absolute',
          },
          TYPE_LEVEL.BIRDIE,
          tier?.currentTier?.toUpperCase() === TYPE_LEVEL.BIRDIE
            ? tier?.currentTierPurchaseRatio
            : null,
        )}
        <View
          style={[appStyles.flex, {alignItems: 'flex-end', marginRight: -4}]}
        >
          {renderTextLevel(
            {},
            TYPE_LEVEL.EAGLE,
            tier?.currentTier?.toUpperCase() === TYPE_LEVEL.EAGLE
              ? tier?.currentTierPurchaseRatio
              : null,
          )}
        </View>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  viewContainer: {
    width: wp(100),
    alignItems: 'center',
    justifyContent: 'flex-end',
    overflow: 'hidden',
    position: 'absolute',
    top: 0,
    zIndex: 999,
    borderRadius: 24,
    borderTopRightRadius: DeviceInfo.hasNotch() ? 24 : 0,
    borderTopLeftRadius: DeviceInfo.hasNotch() ? 24 : 0,
    paddingBottom: 5,
  },
  viewDot: {
    minWidth: 8,
    minHeight: 8,
    borderRadius: 16,
    backgroundColor: '#fff',
    marginHorizontal: 2,
    position: 'absolute',
  },
  textBig: {
    letterSpacing: 1.2,
    fontSize: 22,
    fontWeight: '800',
    ...fontFamily.dinNext79,
  },
  textSmall: {
    letterSpacing: 1.5,
    fontSize: 16,
    fontWeight: '800',
    marginTop: 1,
    ...fontFamily.dinNext79,
  },
  viewProgressContainer: {
    width: widthLineMax,
    marginVertical: 10,
    height: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 40,
    justifyContent: 'center',
  },
  viewLineActive: {
    height: 12,
    borderRadius: 40,
    overflow: 'hidden',
  },
  viewLineActiveFull: {
    height: 12,
    position: 'absolute',
    backgroundColor: 'rgba(255, 204, 0, 0.61)',
  },
  viewBorderPoint: {
    paddingHorizontal: 4,
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 20,
    backgroundColor: '#fff',
  },
});

export default React.forwardRef(LoyaltyPointHolder);
