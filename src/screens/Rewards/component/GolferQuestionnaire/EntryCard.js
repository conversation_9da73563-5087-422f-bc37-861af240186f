import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import Text from 'components/Text';
import appStyles from 'styles/global';
import {t} from 'i18next';
import LinearGradient from 'react-native-linear-gradient';
import FastImage from 'react-native-fast-image/src';
import GradientText from 'components/GradientText';
import {getEntryCard, getGolferQuestionnaire, getQuestionList} from 'requests/gameProfile';

const CARD_HEIGHT = 192;
const BLUR_HEIGHT = 98;

const EntryCard = ({navigation}) => {
  const [entryData, setEntryData] = useState();
  const [dataQuestionnaire, setDataQuestionnaire] = useState();
  const [dataQuestionList, setDataQuestionList] = useState([]);
  const goToQuestionnaire = () => {
    //navigate to questionnaire
    navigation.navigate('GolferQuestionnaire', {
      screen: 'GolferQuestionnaire',
      params: {
        dataQuestionnaire,
        dataQuestionList,
      },
    });
  };
  const getEntryCardData = async () => {
    try {
      const dataResponse = await getEntryCard();
      setEntryData(dataResponse);
    } catch (error) {
      setEntryData([]);
    }
  };

  const getGolferQuestionnaireData = async () => {
    try {
      const dataResponse = await getGolferQuestionnaire();
      setDataQuestionnaire(dataResponse);
    } catch {}
  };

  const getQuestionListData = async () => {
    try {
      const dataResponse = await getQuestionList();
      setDataQuestionList(dataResponse);
    } catch {}
  };

  useEffect(() => {
    getEntryCardData();
    getGolferQuestionnaireData();
    getQuestionListData();
  }, []);

  let textColor = '#fff';
  let buttonBackgroundColor = '#fff';

  if (!entryData) {
    return null;
  }

  return (
    <View style={styles.containerStyle}>
      <GradientText style={styles.sectionText}>
        {t('questionnaire.title.entry_section')}
      </GradientText>
      <TouchableOpacity
        style={[
          styles.cardStyle,
          {backgroundColor: entryData?.backgroundColor},
        ]}
        onPress={() => goToQuestionnaire()}
        activeOpacity={0.8}
      >
        {entryData?.imageLink && (
          <FastImage
            style={styles.mainImg}
            source={{
              uri: entryData?.imageLink,
              cache: FastImage.cacheControl.web,
            }}
          />
        )}
        <View style={styles.contentView}>
          <Text
            style={[
              styles.title,
              {
                color: textColor,
                marginBottom: Platform.OS === 'ios' ? 0 : 5,
              },
            ]}
            numberOfLines={1}
            weight={800}
            Din79Font
          >
            {entryData?.header}
          </Text>
          <View style={styles.blurContainerStyle}>
            <View
              style={[
                StyleSheet.absoluteFill,
                {
                  overflow: 'hidden',
                },
              ]}
            >
              {entryData?.imageLink && (
                <Image
                  source={{uri: entryData?.imageLink}}
                  blurRadius={3}
                  style={styles.viewBlur}
                  resizeMode="cover"
                />
              )}
              <LinearGradient
                colors={[
                  'rgba(0, 0, 0, 0)',
                  'rgba(0, 0, 0, 0.015)',
                  'rgba(0, 0, 0, 0.275)',
                ]}
                style={[StyleSheet.absoluteFill]}
              />
            </View>
            {entryData?.subHeader && (
              <Text
                style={[
                  styles.subTitle,
                  {
                    color: textColor,
                    width: '100%',
                  },
                ]}
                numberOfLines={1}
                weight={800}
                Din79Font
              >
                {entryData?.subHeader}
              </Text>
            )}
            <View
              style={{
                flexWrap: 'wrap',
                position: 'absolute',
                bottom: 16,
              }}
            >
              <TouchableOpacity
                activeOpacity={0.8}
                style={[
                  appStyles.hCenter,
                  appStyles.vCenter,
                  styles.btnGetStarted,
                  {
                    backgroundColor: buttonBackgroundColor,
                  },
                ]}
                onPress={() => goToQuestionnaire()}
              >
                <Text
                  Din79Font
                  weight={700}
                  size={12}
                  style={styles.buttonText}
                >
                  {entryData?.ctaButton
                    ? entryData.ctaButton
                    : t('questionnaire.button.get_started')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  containerStyle: {
    marginHorizontal: 8,
    marginBottom: 24,
  },
  cardStyle: {
    height: CARD_HEIGHT,
    borderRadius: 24,
  },
  sectionText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFD836',
    marginBottom: 16,
    marginLeft: 8,
  },
  blurContainerStyle: {
    paddingHorizontal: 16,
    paddingBottom: 17,
    alignItems: 'center',
    height: BLUR_HEIGHT,
  },
  mainImg: {
    height: CARD_HEIGHT,
    borderRadius: 24,
  },
  title: {
    fontSize: 34,
    textTransform: 'uppercase',
    letterSpacing: 3.4,
    textAlign: 'center',
    paddingHorizontal: Platform.OS === 'ios' ? 16 : 14,
  },
  subTitle: {
    fontSize: 16,
    marginTop: 8,
    marginBottom: 16,
    textTransform: 'uppercase',
    letterSpacing: 1.28,
    textAlign: 'center',
    alignSelf: 'center',
  },
  btnGetStarted: {
    minWidth: 224,
    borderRadius: 30,
    height: 40,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 2,
  },
  buttonText: {
    marginHorizontal: 16,
    color: '#000',
    letterSpacing: 1.62,
    textTransform: 'uppercase',
  },
  viewBlur: {
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    width: '100%',
    height: CARD_HEIGHT,
    position: 'absolute',
    bottom: 0,
  },
  contentView: {
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    overflow: 'hidden',
  },
});

export default EntryCard;
