import React, {useEffect, useRef, useState} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  Platform,
  FlatList,
  Dimensions,
} from 'react-native';
import Footer from './common/Footer';
import Images from 'assets/imgs/Images';
import {t} from 'i18next';
import AnswerButton from './common/AnswerButton';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {useDispatch, useSelector} from 'react-redux';
import {navigateToHome} from './common/commonQuiz';
import PagerView from 'react-native-pager-view';
import {IMAGES, LOYALTY_ACTION_KEY, QUIZ_TYPE} from 'utils/constant';
import {updateUser} from 'requests/accounts';
import {showToast} from 'utils/toast';
import {saveCompletedLoyaltyAction} from 'utils/asyncStorage';
import {updateUserLoyaltyData} from 'utils/loyalty';
import {addCurrentUser} from 'reducers/user';
import {
  initialWindowMetrics,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import CloseGray from 'assets/imgs/profile/close_gray.svg';
import Text from 'components/Text';
import CustomImageBackground from 'components/CustomImageBackground/CustomImageBackground';

const WIDTH = Dimensions.get('window').width;

const QuestionItem = () => {};

const GolferQuestionnaire = ({navigation, route}) => {
  const user = useSelector(state => state.user);
  const loyalty = useSelector(state => state.loyalty);
  const quizType = route.params?.quizType;
  const fromGameProfile = route.params?.origin === 'Setting';
  const dataQuestionnaire = route.params?.dataQuestionnaire;
  const [currentStep, setCurrentStep] = useState(1);
  const dispatch = useDispatch();
  const [loadingSave, setLoadingSave] = useState(false);
  const [finishOnboarding, setFinishOnboarding] = useState(false);
  const [selectedData, setSelectedData] = useState({});
  const refPagerView = useRef();
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const insets = useSafeAreaInsets();
  const topInset =
    Platform.OS === 'ios' ? insets?.top : initialWindowMetrics?.insets?.top;
  const appImagesCache = useSelector(state => state?.images);
  const customImages = appImagesCache?.data;
  const imageBackground = customImages?.[IMAGES.DRILL_OF_THE_WEEK] || null;

  useEffect(() => {}, []);

  const nextPress = () => {
    const updatedSelectedData = {
      ...selectedData,
      //   roundsPerMonth: rounds,
    };
    setSelectedData(updatedSelectedData);
    if (currentStep < 6) {
      refPagerView.current?.setPage(currentStep);
      setCurrentStep(currentStep + 1);
    } else {
      completeOnboarding(updatedSelectedData);
    }
  };

  const updatePress = () => {
    switch (quizType) {
      case QUIZ_TYPE.ROUNDS:
        // route.params?.setRoundsPerMonth?.(rounds);
        break;
      default:
        break;
    }

    navigation.goBack();
  };

  const completeOnboarding = async dataSave => {
    setLoadingSave(true);
    try {
      // Make request to update user
      const updatedUser = await updateUser({
        ...dataSave,
        onboardingCompleteSteps: {
          firstName: true,
          lastName: true,
          gender: true,
          rpmComplete: true,
          timePlayingGolf: true,
          weakestArea: true,
          misHit: true,
          homeCourse: true,
          favoriteTeamMembers: true,
        },
        onboardingComplete: true,
        signUpByDevice: Platform.OS,
      });
      if (updatedUser && updatedUser.id) {
        saveCompletedLoyaltyAction(
          LOYALTY_ACTION_KEY.COMPLETE_QUIZ,
          loyalty?.completedActions,
          loyalty?.loyaltyActions?.data,
          'user-quiz',
        );
        updateUserLoyaltyData(
          dispatch,
          user?.userCountry,
          loyalty,
          appCacheVersions,
        );
      }
      // Update user in redux
      setFinishOnboarding(true);
      dispatch(addCurrentUser(updatedUser));
      // Stop loading state and navigate to next screen
      setLoadingSave(false);
      setTimeout(() => {
        navigateToHome({navigation, isEdit: fromGameProfile});
      }, 2000);
    } catch (error) {
      setLoadingSave(false);
      showToast({
        type: 'error',
        message: t('An_error_occurred_with_your_onboarding'),
      });
      setTimeout(() => {
        navigateToHome({navigation, isEdit: fromGameProfile});
      }, 2000);
    }
  };

  const checkDisableUpdateButton = () => {
    if ((loadingSave && fromGameProfile) || true) {
      return true;
    }
  };

  const getInitialPage = () => {
    return 0;
  };

  const closeAction = () => {
    navigateToHome({navigation, isEdit: fromGameProfile});
  };

  return (
    <CustomImageBackground
      style={[
        styles.container,
        {
          paddingBottom: insets.bottom,
          paddingTop: topInset,
        },
      ]}
      source={imageBackground ? {uri: imageBackground} : null}
    >
      <LinearGradient
        colors={[
          'rgba(0,0,0,0)',
          'rgba(37,37,37,0.1)',
          'rgba(50,50,50,0.2)',
          'rgba(0,0,0,0.7)',
        ]}
        locations={[0, 0.7, 0.78, 1]}
        style={StyleSheet.absoluteFill} // full background
      />
      {imageBackground && (
        <LinearGradient
          colors={['#0C150F', 'rgba(93, 102, 73, 0.00)']}
          locations={[0, 0.119]}
          style={StyleSheet.absoluteFill}
        />
      )}
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        style={[
          styles.closeButtonContainer,
          {
            top: topInset + (Platform.OS === 'android' ? -2 : 3) - 8,
          },
        ]}
      >
        <CloseGray />
      </TouchableOpacity>
      <View style={styles.headerContainer}>
        <Text
          Din79Font
          style={[
            styles.headerText,
            {
              color: imageBackground
                ? 'rgba(255, 255, 255, 0.85)'
                : 'rgba(0, 0, 0, 0.5)',
              fontWeight: imageBackground ? '800' : '400',
            },
          ]}
        >
          {dataQuestionnaire?.header}
        </Text>
        {dataQuestionnaire?.subHeader && (
          <Text
            style={[
              styles.subHeaderText,
              {
                color: imageBackground ? '#EAC318' : '#107605',
              },
            ]}
            Din79Font
          >
            {dataQuestionnaire?.subHeader}
          </Text>
        )}
      </View>
      <PagerView
        ref={refPagerView}
        style={[
          styles.pagerView,
          {
            marginTop: -(62 + insets.top),
          },
        ]}
        initialPage={getInitialPage()}
        scrollEnabled={false}
      >
        <QuestionItem />
      </PagerView>
      <Footer
        // total={6}
        // currentStep={currentStep}
        // disableStep={fromGameProfile}
        disabled={checkDisableUpdateButton()}
        nextPress={fromGameProfile ? updatePress : nextPress}
        buttonText={
          fromGameProfile
            ? t('common.update')
            : currentStep === 6
            ? t('quiz.homeCourse.done').toUpperCase()
            : null
        }
        btnLoading={loadingSave}
      />
    </CustomImageBackground>
  );
};
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: '#F7F7F7',
    flex: 1,
  },
  headerContainer: {width: '100%', paddingHorizontal: 16},
  headerText: {
    textTransform: 'uppercase',
    fontSize: 22,
    letterSpacing: 1.1,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2,
  },
  subHeaderText: {
    fontWeight: '800',
    letterSpacing: 1.28,
    textTransform: 'uppercase',
    fontSize: 16,
    paddingTop: Platform.OS === 'ios' ? 4 : 6,
  },
  closeButtonContainer: {position: 'absolute', right: 8, zIndex: 3, padding: 8},
  pagerView: {
    width: '100%',
    flex: 1,
    zIndex: 2,
  },
});
export default GolferQuestionnaire;
