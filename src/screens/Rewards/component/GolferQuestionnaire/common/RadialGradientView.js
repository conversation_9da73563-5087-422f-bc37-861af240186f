import React from 'react';
import {View, StyleSheet} from 'react-native';
import Svg, {Defs, RadialGradient, Stop, Rect} from 'react-native-svg';

const RadialGradientView = ({children}) => {
  return (
    <View style={styles.container}>
      <RadialGradient
        id="radial"
        cx="50%"
        cy="50%"
        rx="50%"
        ry="50%"
        fx="50%"
        fy="50%"
      >
        <Stop offset="0%" stopColor="rgba(4, 37, 0, 0)" />
        <Stop offset="77.83%" stopColor="rgba(4, 37, 0, 0)" />
        <Stop offset="92%" stopColor="rgba(4, 37, 0, 0.2)" />
        <Stop offset="100%" stopColor="rgba(4, 37, 0, 0.2)" />
      </RadialGradient>

      <View style={styles.content}>{children}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 40,
    height: 40,
    borderRadius: 28,
    overflow: 'hidden',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default RadialGradientView;
