import React, {useState, useEffect, useRef, useImperativeHandle} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  FlatList,
  Animated,
} from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';

import appStyles from 'styles/global';
import Text from 'components/Text';
import {t} from 'i18next';
import {
  useSafeAreaInsets,
  initialWindowMetrics,
} from 'react-native-safe-area-context';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {BlurView} from '@react-native-community/blur';
import LinearGradient from 'react-native-linear-gradient';
import RadialGradientView from './RadialGradientView';

const opacity = '70';
const listPaddingHorizontal = wp(50);
const circleSize = 40;
const selectedSize = 46;
const circleMarginRight = 4;
const answeredColor = 'rgba(16, 118, 5, 1)';
const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

const StepTracker = ({
  navigation,
  questionListData,
  selectedQuestionNumber,
  setSelectedQuestionNumber,
}) => {
  const [selectedWidth, setSelectedWidth] = useState(0);
  const [questionList, setQuestionList] = useState(questionListData);
  const refList = useRef();

  useEffect(() => {
    if (refList) {
      const questionIndex = selectedQuestionNumber - 1;
      setTimeout(
        () =>
          refList?.current?.scrollToOffset?.({
            offset:
              circleSize * questionIndex +
              circleMarginRight * questionIndex +
              selectedWidth / 2,
          }),
        100,
      );
    }
  }, [selectedQuestionNumber, selectedWidth]);

  const renderHeaderSelectedQuestion = ({item, index}) => {
    return (
      <View
        style={{
          alignItems: 'center',
          backgroundColor: '#F7F7F7',
          overflow: 'hidden',
          flex: 1,
        }}
      >
        <LinearGradient
          colors={[
            'rgba(255, 255, 255, 0.25)',
            'rgba(153, 153, 153, 0.30)',
            'rgba(97, 97, 97, 0.40)',
          ]}
          locations={[0.85, 0.99, 1.0]}
          start={{x: 0.5, y: 0}}
          end={{x: 0.5, y: 1}}
          style={{
            width: '100%',
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Text
            size={16}
            black
            style={{
              fontWeight: '700',
              marginTop: Platform.OS === 'android' ? -1 : 0,
            }}
          >
            {index + 1}{' '}
            <Text
              size={16}
              style={{
                fontWeight: '400',
                color: 'gray',
              }}
            >
              {' of ' + questionList?.length}
            </Text>
          </Text>
        </LinearGradient>
      </View>
    );
  };
  const renderItemNumber = ({item, index}) => {
    const isSelectedItem = index === selectedQuestionNumber - 1;
    return (
      <TouchableOpacity
        activeOpacity={1}
        style={[
          isSelectedItem
            ? {
                ...styles.touchButtonActive,
                backgroundColor: '#ffffff',
                borderWidth: 1,
                borderColor: '#E2E2E2',
              }
            : {
                ...styles.touchButton,
                width: circleSize,
                backgroundColor:
                  (item.isAnswered ? answeredColor : '#ffffff') + opacity,
              },
        ]}
        onPress={() => {
          if (!isSelectedItem) {
            setSelectedQuestionNumber(index + 1);
          }
        }}
        onLayout={e => {
          if (isSelectedItem) {
            setSelectedWidth(e.nativeEvent.layout.width);
          }
        }}
        // disabled={isEditing}
      >
        {!isSelectedItem && (
          <>
            {Platform.OS === 'android' ? (
              <View
                style={[
                  styles.viewBlur,
                  {
                    width: circleSize,
                    backgroundColor: '#f2f2f2',
                    opacity: 0.2,
                  },
                ]}
              />
            ) : (
              <BlurView
                style={[
                  styles.viewBlur,
                  {
                    width: circleSize,
                  },
                ]}
                blurType="light"
                blurAmount={10}
                reducedTransparencyFallbackColor="white"
              />
            )}
          </>
        )}
        {isSelectedItem ? (
          renderHeaderSelectedQuestion({item, index})
        ) : (
          <RadialGradientView>
            <Text
              size={12}
              style={{
                color: item.isAnswered ? 'white' : 'black',
                fontWeight: Platform.OS === 'ios' ? '700' : 'bold',
              }}
            >
              {index + 1}
            </Text>
          </RadialGradientView>
        )}
      </TouchableOpacity>
    );
  };
  return (
    <View
      style={[
        appStyles.row,
        appStyles.vCenter,
        appStyles.hCenter,
        {
          marginTop: Platform.OS === 'ios' ? 8 : 10,
          marginBottom: 8,
          zIndex: 1000,
        },
      ]}
    >
      <FlatList
        data={questionList}
        ref={refList}
        keyExtractor={item => item.id}
        extraData={questionList}
        renderItem={renderItemNumber}
        horizontal
        style={{flexGrow: 0}}
        contentContainerStyle={{
          alignItems: 'center',
          paddingHorizontal: listPaddingHorizontal,
        }}
        showsHorizontalScrollIndicator={false}
        initialNumToRender={20}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  touchButton: {
    backgroundColor: '#ffffff' + opacity,
    width: circleSize,
    height: circleSize,
    borderRadius: 20,
    marginRight: circleMarginRight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  touchButtonActive: {
    borderRadius: 22,
    height: selectedSize,
    marginRight: circleMarginRight,
    overflow: 'hidden',
    width: 102,
  },
});

export default StepTracker;
