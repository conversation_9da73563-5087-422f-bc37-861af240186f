import React, {useEffect, useRef, useState} from 'react';
import {View, FlatList, StyleSheet} from 'react-native';

import AnswerButton from './AnswerButton';
import Text from 'components/Text';
import {t} from 'i18next';

const QuestionItem = ({question, selectedAnswers, setSelectedAnswers}) => {
  const updateAnswers = (answer, isMultipleChoice = false) => {
    try {
      if (isMultipleChoice) {
        // In multi-select mode, toggle the answer
        const hasSelected = selectedAnswers?.includes(answer);
        if (hasSelected) {
          // Deselect the answer
          setSelectedAnswers(values =>
            values.filter(value => value !== answer),
          );
        } else {
          // Select the answer
          setSelectedAnswers(values => [...values, answer]);
        }
      } else {
        // In single choice mode (radio), replace any previous selection with the new one
        setSelectedAnswers([answer]);
      }
    } catch (error) {}
  };
  switch (question?.answerType) {
    case 'SINGLE_ONE_COLUMN':
      return (
        <SingleOneColumn
          question={question}
          selectedAnswers={selectedAnswers}
          updateAnswers={updateAnswers}
        />
      );
    case 'SINGLE_TWO_COLUMNS':
      return (
        <SingleTwoColumns
          question={question}
          selectedAnswers={selectedAnswers}
          updateAnswers={updateAnswers}
        />
      );
    case 'SEARCH':
      return <Search question={question} />;
    case 'DROPDOWN_WITH_SEARCH':
      return <DropdownWithSearch question={question} />;
    case 'IMAGE_TILE':
      return <ImageTile question={question} />;
    default:
      return null;
  }
};

const QuestionHeader = ({question, options}) => {
  return (
    <View>
      <Text
        style={[styles.questionText, {color: options?.nameColor || 'black'}]}
        size={22}
        Din79Font
      >
        {question?.name}
      </Text>
      {question?.questionType === 'MULTIPLE' && (
        <Text style={styles.questionMultipleText} size={16} Din79Font>
          {t('questionnaire.title.select_all_that_apply')}
        </Text>
      )}
    </View>
  );
};

const SingleOneColumn = ({question, selectedAnswers, updateAnswers}) => {
  const options =
    typeof question?.options === 'object'
      ? question?.options
      : JSON.parse(question?.options);
  return (
    <View style={styles.questionContainer}>
      <QuestionHeader question={question} options={options} />
      <FlatList
        data={question?.answers}
        keyExtractor={item => item.id}
        style={styles.answerList}
        renderItem={({item, index}) => (
          <AnswerButton
            text={item?.title}
            isSelected={selectedAnswers?.includes(question?.answers[index])}
            setSelectedIndex={i =>
              updateAnswers(item, question?.questionType === 'MULTIPLE')
            }
          />
        )}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const SingleTwoColumns = ({question, selectedAnswers, updateAnswers}) => {
  const options =
    typeof question?.options === 'object'
      ? question?.options
      : JSON.parse(question?.options);
  return (
    <View style={styles.questionContainer}>
      <QuestionHeader question={question} options={options} />
      <FlatList
        data={question?.answers}
        keyExtractor={item => item.id}
        numColumns={2}
        style={styles.answerList}
        renderItem={({item, index}) => {
          const isLeftItem = index % 2 === 0;
          return (
            <View
              style={[styles.answerWrapper, isLeftItem && {marginRight: 8}]}
            >
              <AnswerButton
                text={item?.title}
                isSelected={selectedAnswers?.includes(question?.answers[index])}
                setSelectedIndex={i =>
                  updateAnswers(item, question?.questionType === 'MULTIPLE')
                }
              />
            </View>
          );
        }}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const Search = ({question}) => {
  return (
    <View style={styles.questionContainer}>
      <Text style={styles.questionText} size={22} black Din79Font>
        {question?.name}
      </Text>
    </View>
  );
};

const DropdownWithSearch = ({question}) => {
  return (
    <View style={styles.questionContainer}>
      <Text style={styles.questionText} size={22} black Din79Font>
        {question?.name}
      </Text>
    </View>
  );
};

const ImageTile = ({question}) => {
  return (
    <View style={styles.questionContainer}>
      <Text style={styles.questionText} size={22} black Din79Font>
        {question?.name}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  questionContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  questionText: {
    lineHeight: 21.52,
    letterSpacing: 1.1,
    fontWeight: '800',
  },
  answerWrapper: {
    flex: 1,
  },
  questionMultipleText: {
    letterSpacing: 1.28,
    lineHeight: 20.68,
    color: 'rgba(0, 0, 0, 0.50)',
  },
  answerList: {marginTop: 16},
});
export default QuestionItem;
