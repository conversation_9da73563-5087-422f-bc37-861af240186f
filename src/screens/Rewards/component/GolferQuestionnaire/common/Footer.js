import Button from 'components/Button';
import React from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

const Footer = ({
  total,
  currentStep,
  disableStep,
  nextPress,
  disabled,
  buttonText,
  btnLoading,
}) => {
  return (
    <View style={styles.container}>
      <Button
        text={buttonText || 'quiz.cta.next'}
        textColor={disabled ? 'rgba(0, 0, 0, 0.50)' : 'white'}
        textStyle={{fontWeight: '700', fontSize: 12}}
        backgroundColor={disabled ? 'rgb(225, 225, 225)' : 'black'}
        borderColor={'transparent'}
        onPress={nextPress}
        loading={btnLoading}
        style={[styles.nextButton]}
        disabled={disabled}
        centered
        Din79Font
      />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  nextButton: {
    width: wp(100) - 32,
    height: 40,
    marginTop: 24,
    marginBottom: 8,
  },
});
export default Footer;
