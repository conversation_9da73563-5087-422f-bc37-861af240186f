import Button from 'components/Button';
import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';

const Footer = ({
  total,
  currentStep,
  disableStep,
  nextPress,
  disabled,
  buttonText,
  btnLoading,
  isFirstQuestion,
  backPress,
}) => {
  return (
    <View style={styles.container}>
      {!isFirstQuestion && (
        <Button
          text={'questionnaire.button.back'}
          textColor={'white'}
          textStyle={styles.buttonText}
          backgroundColor={'black'}
          borderColor={'transparent'}
          onPress={backPress}
          loading={btnLoading}
          style={[
            styles.button,
            {marginRight: 8, borderWidth: 1.5, borderColor: 'white'},
          ]}
          disabled={false}
          centered
          Din79Font
        />
      )}
      <Button
        text={'questionnaire.button.next'}
        textColor={disabled ? 'rgba(0, 0, 0, 0.50)' : 'white'}
        textStyle={styles.buttonText}
        backgroundColor={disabled ? 'rgb(225, 225, 225)' : 'black'}
        borderColor={'transparent'}
        onPress={nextPress}
        loading={btnLoading}
        style={[styles.button]}
        disabled={disabled}
        centered
        Din79Font
      />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  button: {
    flex: 1,
    height: 40,
    marginTop: 24,
    marginBottom: 8,
  },
  buttonText: {
    fontWeight: '700',
    fontSize: 12,
    textTransform: 'uppercase',
    letterSpacing: 1.62,
  }
});
export default Footer;
