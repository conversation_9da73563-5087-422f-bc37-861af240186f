import Button from 'components/Button';
import React from 'react';
import {StyleSheet, View, Image} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

const AnswerButton = ({text, index, isSelected, setSelectedIndex, style}) => {
  return (
    <View key={`${index}`}>
      <Button
        text={text}
        textColor={isSelected ? '#fff' : 'rgba(0, 0, 0, 0.75)'}
        textStyle={{fontWeight: '700', fontSize: 12, letterSpacing: 12 * 0.135}}
        backgroundColor={isSelected ? 'rgba(0, 0, 0, 0.75)' : '#fff'}
        borderColor={isSelected ? 'rgba(0, 0, 0, 0.75)' : '#fff'}
        onPress={() => setSelectedIndex(index)}
        style={[styles.answerOption, style]}
        centered
        Din79Font
      />
    </View>
  );
};
const styles = StyleSheet.create({
  answerOption: {
    marginBottom: 8,
    height: 40,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 4,
    shadowRadius: 1,
    elevation: 4,
    shadowColor: '#d8d7d7',
  },
});
export default AnswerButton;
