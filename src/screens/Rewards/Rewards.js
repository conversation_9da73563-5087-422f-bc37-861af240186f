import React, {useRef, useEffect, useState} from 'react';
import {View, Platform} from 'react-native';
import LoyaltyPointHolder from './component/LoyaltyPointHolder';
import ShopWithPoint from './component/ShopWithPoint';
import EarnPointNows from './component/EarnPointNows';
import TierLevels from './component/TierLevels';
import FAQs from './component/FAQs';
import appStyles from 'styles/global';
import LinearGradient from 'react-native-linear-gradient';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Animated, {useSharedValue} from 'react-native-reanimated';
import LoyaltyPointHeader from 'screens/Shop/components/ShopHeader';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import PopupPermission from './component/PopupPermission';
import ShopTourTrash from 'screens/Shop/ShopTourTrash';
import MemberBenefit from 'screens/MemberBenefit/MemberBenefit';
import {useDispatch, useSelector} from 'react-redux';
import {setDeepLink} from 'reducers/app';
import useAppState from 'hooks/useAppState';
import { updateCacheVersionData } from 'reducers/cacheVersion';
import { getModuleCacheData } from 'requests/moduleCache';
import EntryCard from './component/GolferQuestionnaire/EntryCard';
const heightHeaderMax = wp(40);
const heightHeaderMin = wp(26);
let timeOut = null;
const Rewards = () => {
  const [openNotify, setOpenNotify] = useState(false);
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const refEarnPoint = useRef(null);
  const refLoyalty = useRef(null);
  const refShopWithPoint = useRef(null);
  const refTierLevels = useRef(null);
  const refFAQs = useRef(null);
  const refScrollDrag = useRef(0);
  const refScroll = useRef(null);
  const refTourTrash = useRef(null);
  const refPerk = useRef(null);

  const offset = useSharedValue(0);
  const deepLink = useSelector(state => state?.app?.deepLink);
  const products = useSelector(state => state?.rewards?.tourTrash);
  const paddingTop = insets.top + (Platform.OS === 'ios' ? 0 : 15);
  const [hasOpenedNotiSetting, setHasOpenedNotiSetting] = useState(false);
  const user = useSelector(state => state.user);

  const dispatch = useDispatch();

  useEffect(() => {
    callData();
  }, []);

  const callData = async () => {
    refLoyalty?.current?.refreshData();
    refShopWithPoint?.current?.refreshData();
    refEarnPoint?.current?.refreshData();
    refTierLevels?.current?.refreshData();
    refFAQs?.current?.refreshData();
  };

  useAppState({
    onForeground: () => {
      loadPerkTourTrashData();
    },
  });

  const loadPerkTourTrashData = async () => {
    try {
      const rs = await getModuleCacheData(user.userCountry);
      dispatch(updateCacheVersionData(rs));
    } catch (error) {
    } finally {
    }
  };

  useEffect(() => {
    // Handle deep link routing
    if (deepLink?.length) {
      if (deepLink === 'tour-trash') {
        refScroll?.current?.scrollTo({
          y: 0,
        });
        dispatch(setDeepLink(''));
      } else if (deepLink === 'perks') {
        if (products?.length > 0) {
          setTimeout(() => {
            refScroll?.current?.scrollTo({
              y: products.length * (wp('96%') / 0.73) + heightHeaderMin,
            });
          }, 300);
        }
        dispatch(setDeepLink(''));
      }
    }
  }, [deepLink, products]);

  const onScroll = e => {
    const pointY =
      e.nativeEvent.contentOffset.y < 0 ? 0 : e.nativeEvent.contentOffset.y;
    if (pointY <= heightHeaderMax - heightHeaderMin + paddingTop) {
      offset.value = pointY;
    } else if (
      e.nativeEvent.contentOffset.y >
      heightHeaderMax - heightHeaderMin + paddingTop
    ) {
      offset.value = heightHeaderMax - heightHeaderMin + paddingTop;
    }
    refScrollDrag.current = pointY;
  };
  const scrollToTop = () => {
    cleanTimeOutScroll();
    timeOut = setTimeout(() => {
      refScroll.current.scrollTo({y: 0});
    }, 300);
  };
  const scrollToPoint = () => {
    cleanTimeOutScroll();
    timeOut = setTimeout(() => {
      refScroll.current.scrollTo({
        y: heightHeaderMax - heightHeaderMin + paddingTop,
      });
    }, 300);
  };
  const changeOffsetValue = () => {
    cleanTimeOutScroll();
    timeOut = setTimeout(() => {
      offset.value = heightHeaderMax - heightHeaderMin + paddingTop;
    }, 300);
  };
  const cleanTimeOutScroll = () => {
    if (timeOut) {
      clearTimeout(timeOut);
    }
  };
  const onScrollBeginDrag = e => {
    cleanTimeOutScroll();
  };

  const onScrollEndDrag = e => {
    if (
      e.nativeEvent.contentOffset.y <
      (heightHeaderMax - heightHeaderMin + paddingTop) / 2
    ) {
      scrollToTop();
    } else if (
      e.nativeEvent.contentOffset.y <
      heightHeaderMax - heightHeaderMin + paddingTop
    ) {
      scrollToPoint();
    } else {
      changeOffsetValue();
    }
  };
  const onMomentumScrollEnd = e => {
    if (
      e.nativeEvent.contentOffset.y <
      (heightHeaderMax - heightHeaderMin + paddingTop) / 2
    ) {
      scrollToTop();
    } else if (
      e.nativeEvent.contentOffset.y <
      heightHeaderMax - heightHeaderMin + paddingTop
    ) {
      scrollToPoint();
    } else {
      changeOffsetValue();
    }
  };
  const onMomentumScrollBegin = e => {
    cleanTimeOutScroll();
  };

  const scrollToEnd = () => {
    refScroll.current.scrollToEnd();
  };

  return (
    <View style={appStyles.flex}>
      <LinearGradient
        start={{x: 1, y: 0.5}}
        end={{x: 1, y: 1}}
        colors={['rgba(61, 61, 61, 1)', 'rgba(0, 0, 0, 1)']}
        style={{
          flex: 1,
          paddingTop,
        }}
      >
        <FocusAwareStatusBar barStyle={'light-content'} />
        <LoyaltyPointHeader
          colorPTS={'#ccc'}
          isDarkBackground={true}
          pointHide
          headerStyle={{marginBottom: 0, marginTop: 5}}
        />
        <LoyaltyPointHolder
          ref={refLoyalty}
          offset={offset}
          paddingTop={paddingTop}
        />
        <Animated.ScrollView
          ref={refScroll}
          style={{flex: 1}}
          onScroll={onScroll}
          onScrollBeginDrag={onScrollBeginDrag}
          onMomentumScrollEnd={onMomentumScrollEnd}
          onMomentumScrollBegin={onMomentumScrollBegin}
          onScrollEndDrag={onScrollEndDrag}
          scrollEventThrottle={16}
          contentContainerStyle={{paddingTop: heightHeaderMax}}
        >
          <EntryCard navigation={navigation} ref={refTourTrash} />
          <ShopTourTrash navigation={navigation} ref={refTourTrash} />
          <ShopWithPoint ref={refShopWithPoint} />
          <MemberBenefit navigation={navigation} ref={refPerk} />
          <EarnPointNows
            ref={refEarnPoint}
            setOpenNotify={setOpenNotify}
            openNotify={openNotify}
            hasOpenedNotiSetting={hasOpenedNotiSetting}
          />
          <TierLevels ref={refTierLevels} />
          <FAQs ref={refFAQs} scrollToEnd={scrollToEnd} />
        </Animated.ScrollView>
      </LinearGradient>
      <PopupPermission
        setOpenNotify={setOpenNotify}
        openNotify={openNotify}
        setHasOpenedNotiSetting={setHasOpenedNotiSetting}
      />
    </View>
  );
};

export default Rewards;
