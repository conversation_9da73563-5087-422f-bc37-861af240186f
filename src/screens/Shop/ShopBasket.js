import React, {useCallback, useEffect, useState} from 'react';
import {View, ScrollView} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useDispatch, useSelector} from 'react-redux';
import Text from 'components/Text';
import Button from 'components/Button';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import LoadingOverlay from 'components/LoadingOverlay';
import ProductItem from 'components/ProductItem';

import {
  createSFCCSession,
  getProductDetails,
  getProductOverwrite,
  updateBasketProduct,
} from 'requests/ecom';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {
  getAuth0AccessToken,
  isOtherPayment,
  openOtherPayment,
} from 'utils/user';
import {parseOrStringifyJSON} from 'utils/validate';
import {t} from 'i18next';
import {getConfig} from 'config/env';
import {NOT_APPLICABLE, SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';
import analytics from '@react-native-firebase/analytics';
import {sendUserInfoTTB} from 'requests/ttb';
import debounce from 'lodash.debounce';
import {updateCurrentBasket} from 'reducers/basket';
import {getCurrencySymbol} from 'utils/commonVariable';
import {getEcomSite} from 'utils/countries';

const ShopBasket = ({navigation: {navigate}}) => {
  const basket = useSelector(state => state.basket);
  const dispatch = useDispatch();
  const permissions = useSelector(state => state?.app?.permissions);
  const [loadingWebView, setLoadingWebView] = useState(false);
  const isMember = permissions?.myTMPermission?.canExclProductDrops || false;
  const hasTTBItem = basket?.product_items?.some(product => {
    const hasTTB = product?.c_tm_customconfigurator_nodes
      ? JSON.parse(product?.c_tm_customconfigurator_nodes)?.selectedNodes?.some(
          node => {
            return node?.name === 'TTB';
          },
        )
      : false;
    return hasTTB;
  });
  //listBasketProduct will contains basket?.product_items with theirs maxQty data
  const [listBasketProduct, setListBasketProduct] = useState(
    basket?.product_items || [],
  );

  useEffect(() => {
    (async () => {
      try {
        let listVariant = [];
        for (let index = 0; index < basket?.product_items?.length; index++) {
          const element = basket?.product_items[index];
          let productDetails = await getProductDetails(element.product_id);
          listVariant.push({
            item_variant: productDetails?.c_tm_comfil_size || NOT_APPLICABLE,
            item_category: NOT_APPLICABLE,
          });
        }
        GA_viewCart(listVariant);
      } catch (error) {
        console.log('error', error);
      }
    })();
  }, []);

  const updateQuantityForBasket = (itemId, newQuantity) => {
    try {
      const cloneListProduct = [...listBasketProduct];
      for (let index = 0; index < cloneListProduct.length; index++) {
        if (cloneListProduct[index].item_id === itemId) {
          cloneListProduct[index] = {
            ...cloneListProduct[index],
            quantity: newQuantity,
          };
        }
        break;
      }
      setListBasketProduct(cloneListProduct);
    } catch (error) {
      console.log('err', error);
    }
  };

  const revertProductQuantity = (itemId, basketInfo) => {
    try {
      const cloneListProduct = [...listBasketProduct];
      for (let index = 0; index < cloneListProduct.length; index++) {
        if (cloneListProduct[index].item_id === itemId) {
          cloneListProduct[index] = {
            ...cloneListProduct[index],
            quantity:
              basketInfo?.product_items?.find(item => item.item_id === itemId)
                ?.quantity || 1,
          };
          break;
        }
      }
      setListBasketProduct(cloneListProduct);
    } catch (error) {
      console.log('err', error);
    }
  };

  //this Effect will set ListBasketProduct with the max Quantity for all products in the basket
  useEffect(() => {
    (async () => {
      try {
        const productOverwrite = await getProductOverwrite();
        const cloneListProduct = [...basket?.product_items];
        if (productOverwrite && cloneListProduct.length > 0) {
          for (let index = 0; index < cloneListProduct.length; index++) {
            let result;
            result = productOverwrite.find(
              _item => _item.product_id === cloneListProduct[index].product_id,
            );
            if (result && result?.maxQty) {
              cloneListProduct[index] = {
                ...cloneListProduct[index],
                maxQty: result?.maxQty,
              };
            } else {
              cloneListProduct[index] = {
                ...cloneListProduct[index],
                maxQty: 1,
              };
            }
          }
          setListBasketProduct(cloneListProduct);
        }
      } catch (error) {
        console.log('error', error);
      }
    })();
  }, [basket]);

  const GA_viewCart = async listVariant => {
    const listProduct = [];
    basket?.product_items?.map((item, index) => {
      listProduct.push({
        item_id: item?.product_id,
        item_name: item?.c_tm_customconfigurator_family || item.product_name,
        item_category: NOT_APPLICABLE,
        item_brand: 'TaylorMade Golf',
        item_list_name: 'Cart',
        item_location_id: 'cart', //optional
        item_variant: listVariant[index]?.item_variant || NOT_APPLICABLE,
        index: index, //e.g 4
        price: item.price, //e.g 123.22
        quantity: item?.quantity || 1, //e.g 2
      });
    });
    analytics().logEvent('view_cart', {
      app_screen_name: 'cart', //e.g home, my orders
      screen_type: SCREEN_CLASS.SHOP, //e.g checkout, pdp, plp, account
      page_name: 'cart', //e.g home, my orders
      page_type: SCREEN_TYPES.TTB, //e.g basket, home, order
      page_category: SCREEN_TYPES.TTB,
      currency: 'USD',
      items: listProduct,
    });
  };

  const GA_beginCheckout = listVariantCheckout => {
    const listProduct = [];
    basket?.product_items?.map((item, index) => {
      listProduct.push({
        item_id: item.product_id,
        item_name: item.item_text,
        item_category: NOT_APPLICABLE,
        item_brand: 'TaylorMade Golf',
        item_list_name: 'Drops',
        item_location_id: 'cart', //optional
        item_variant:
          listVariantCheckout[index]?.item_variant || NOT_APPLICABLE,
        index: index, //e.g 4
        price: item.price, //e.g 123.22
        quantity: item?.quantity || 1, //e.g 2
      });
    });
    analytics().logEvent('begin_checkout', {
      app_screen_name: 'cart', //e.g home, my orders
      screen_type: SCREEN_CLASS.SHOP, //e.g checkout, pdp, plp, account
      page_name: 'cart', //e.g home, my orders
      page_type: SCREEN_TYPES.TTB, //e.g basket, home, order
      page_category: SCREEN_TYPES.TTB,
      checkout_type: 'normal',
      currency: 'USD',
      items: listProduct,
    });
  };

  const debounceValue = useCallback(
    debounce(
      (itemId, qty, basketInfo) =>
        callUpdateProductToEcom(itemId, qty, basketInfo),
      1000,
    ),
    [],
  );

  const updateProductQuantity = (itemId, newQuantity) => {
    updateQuantityForBasket(itemId, newQuantity);
    debounceValue(itemId, newQuantity, basket);
  };

  const callUpdateProductToEcom = async (itemId, newQuantity, basketInfo) => {
    if (!basketInfo) {
      return;
    }
    setLoadingWebView(true);
    try {
      // Make request to add product to sfcc basket
      const currentBasket = await updateBasketProduct({
        basketId: basketInfo.basket_id,
        resourceState: basketInfo._resource_state,
        itemId: itemId,
        updateObject: {
          quantity: newQuantity,
        },
      });
      // Update basket in redux
      dispatch(updateCurrentBasket(currentBasket));
      setLoadingWebView(false);
    } catch (error) {
      const errorMessage =
        error.response?.data?.errorMessage ||
        error.response?.data?.message ||
        error.response?.data?.fault?.message
          ? error.response?.data?.errorMessage ||
            error.response?.data?.message ||
            error.response?.data?.fault?.message
          : t('variant.configurator_modal.default_message');
      setLoadingWebView(false);
      revertProductQuantity(itemId, basketInfo);
      showToast({
        type: 'error',
        message: t('variant.configurator_modal.fore'),
        subText: errorMessage,
      });
    }
  };

  const renderBasketItems = () => {
    return listBasketProduct?.map(item => {
      return (
        <ProductItem
          key={item.item_id}
          name={item?.product_name}
          price={item?.price}
          productId={item?.product_id}
          macroCode={
            item?.c_tm_customconfigurator_ato_model_name &&
            item?.c_tm_customconfigurator_dwmacro
              ? item?.c_tm_customconfigurator_dwmacro
              : null
          }
          itemId={item?.item_id}
          customNodes={
            item?.c_tm_customconfigurator_nodes
              ? parseOrStringifyJSON(
                  item?.c_tm_customconfigurator_nodes,
                  'parse',
                )
              : null
          }
          atp={
            item?.c_tm_customconfigurator_atp
              ? parseOrStringifyJSON(item?.c_tm_customconfigurator_atp, 'parse')
              : null
          }
          isPDP
          isBasket
          isTTBItem={hasTTBItem}
          itemData={item}
          quantity={item.quantity}
          maxQty={item.maxQty}
          setQuantity={updateProductQuantity}
          styleColorTitle={{color: 'black'}}
        />
      );
    });
  };

  const navigateToWebView = async () => {
    // check is legal TTB
    try {
      setLoadingWebView(true);
      if (hasTTBItem && !__DEV__) {
        try {
          const isCanTTB = await sendUserInfoTTB();
          if (!isCanTTB) {
            showToast({
              type: 'error',
              message: t('error'),
              subText: t('ttb_not_legal'),
            });
            setLoadingWebView(false);
            return;
          }
        } catch (error) {
          showToast({
            type: 'error',
            message: t('error'),
            subText: t('ttb_verify_order_error'),
          });
          setLoadingWebView(false);
          return;
        }
      }
      const listProductCheckout = [];
      let listVariantCheckout = [];
      for (let index = 0; index < basket?.product_items?.length; index++) {
        const element = basket?.product_items[index];
        let productDetails = await getProductDetails(element.product_id);
        listVariantCheckout.push({
          item_variant: productDetails?.c_tm_comfil_size || NOT_APPLICABLE,
          item_category: NOT_APPLICABLE,
        });
      }
      basket?.product_items?.map((item, index) => {
        listProductCheckout.push({
          item_id: item.product_id,
          item_name: item.item_text,
          item_category: NOT_APPLICABLE,
          item_brand: 'TaylorMade Golf',
          price: item.price,
          currency: 'USD',
          quantity: item?.quantity || 1,
          index: index,
          item_variant:
            listVariantCheckout[index]?.item_variant || NOT_APPLICABLE,
        });
      });
      const accessToken = await getAuth0AccessToken(dispatch);
      let ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
      const ecomSite = getEcomSite();

      // Make request to create session and get dwsid session identifier cookie
      const cookie = await createSFCCSession();
      // Navigate to check out iframe
      navigate('WebView', {
        screen: 'WebView',
        params: {
          title: t('how.it_works.check_out'),
          uri: `https://${ECOM_HOST_URL}/on/demandware.store/Sites-${ecomSite}-Site/en_US/MyTM-OpenSession?provider=auth0&page=checkout&token=${accessToken}&iframe=true&ordertype=${
            hasTTBItem ? 'ttb' : 'ecom'
          }`,
          cookie,
          origin: 'ShopBasket',
          listProductCheckout: listProductCheckout,
        },
      });
      GA_beginCheckout(listVariantCheckout);
      setLoadingWebView(false);
    } catch (error) {
      setLoadingWebView(false);
      showToast({
        type: 'error',
        message: t('profile.putting_my_shoes_on'),
        subText: t('profile.be_on_the_tee_in_a_second'),
      });
    }
  };

  return (
    <SafeAreaView
      style={[appStyles.flex, appStyles.whiteBg]}
      edges={['bottom', 'left', 'right']}
    >
      {loadingWebView ? <LoadingOverlay transparent={loadingWebView} /> : null}
      <FocusAwareStatusBar barStyle={'dark-content'} />
      {basket?.product_items?.length ? (
        <>
          <ScrollView
            style={[
              appStyles.flex,
              appStyles.pTSm,
              appStyles.lightGreyBg,
              {paddingHorizontal: '2.4%'},
            ]}
          >
            {renderBasketItems()}
            {!hasTTBItem ? (
              <View style={[appStyles.mTMd, appStyles.mHSm]}>
                <View style={[appStyles.row, appStyles.pBSm, appStyles.hr]}>
                  <Text black style={{fontSize: 13, fontWeight: '400'}}>
                    shop.cart.supporting_copy.subtotal
                  </Text>
                  <Text
                    black
                    style={[
                      appStyles.mLAuto,
                      {fontSize: 13, fontWeight: '400'},
                    ]}
                  >
                    {`${getCurrencySymbol(basket?.currency)}${
                      basket?.product_sub_total
                    }`}
                  </Text>
                </View>
                <View style={[appStyles.row, appStyles.pVSm]}>
                  <Text black>shop.cart.supporting_copy.total</Text>
                  <Text black style={appStyles.mLAuto}>
                    {`${getCurrencySymbol(basket?.currency)}${
                      basket?.product_total
                    }`}
                  </Text>
                </View>
              </View>
            ) : (
              <View style={{paddingHorizontal: 15, paddingBottom: 25}}>
                <View style={appStyles.hr} />
                <View style={[appStyles.row, appStyles.pTSm]}>
                  <Text>shopbasket.due_today</Text>
                  <Text style={appStyles.mLAuto}>
                    {getCurrencySymbol(basket?.currency)}0
                  </Text>
                </View>
                <Text
                  style={[
                    appStyles.xs,
                    appStyles.grey,
                    appStyles.pTSm,
                    appStyles.pBXs,
                  ]}
                >
                  shop.cart.supporting_copy.a_hold_or_pending_charge
                </Text>
                <Text style={[appStyles.xs, appStyles.grey, appStyles.pBXs]}>
                  shop.cart.supporting_copy.a_signature_will_be_required
                </Text>
                <Text style={[appStyles.xs, appStyles.grey, appStyles.pBXs]}>
                  shop.cart.supporting_copy.shipping_and_taxes_are_calculated_at_checkout
                </Text>
              </View>
            )}
          </ScrollView>
          <View style={[appStyles.pSm]}>
            <Button
              text={
                hasTTBItem
                  ? t('shop.cart.cta.order_trial_club')
                  : t('shop.cart.cta')
              }
              textColor="white"
              backgroundColor="black"
              disabled={loadingWebView}
              loadingMode="dark"
              onPress={navigateToWebView}
              centered
              DINbold
            />
          </View>
        </>
      ) : (
        <View style={[appStyles.center, appStyles.lightGreyBg, appStyles.pHMd]}>
          <Text
            style={[
              appStyles.md,
              appStyles.grey,
              appStyles.bold,
              appStyles.mBSm,
            ]}
          >
            shop.shopping_cart
          </Text>
          <Button
            style={{width: '100%'}}
            text={
              isMember
                ? t('shop.backet.continue_shopping')
                : t('plan.upgrade_for_access')
            }
            textColor="white"
            backgroundColor="black"
            onPress={
              isMember
                ? () => navigate('App', {screen: 'ShopTabScreen'})
                : () => {
                    if (isOtherPayment(permissions)) {
                      openOtherPayment();
                      return;
                    }
                  }
            }
            centered
            DINbold
          />
        </View>
      )}
    </SafeAreaView>
  );
};

export default ShopBasket;
