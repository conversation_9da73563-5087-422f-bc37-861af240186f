import React, {useState, useEffect} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  ScrollView,
  View,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
} from 'react-native';
import moment from 'moment';
import analytics from '@react-native-firebase/analytics';
import {moderateScale} from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/AntDesign';
import Text from 'components/Text';
import Button from 'components/Button';
import HtmlParser from 'components/HtmlView';

import {joinTourTrash} from 'requests/tour-trash';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import DeviceInfo from 'react-native-device-info';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import PlayButton from 'assets/imgs/icon-play-button.svg';
import {GA_logSelectContentEvent} from 'utils/article';
import {useInAppReview} from 'hooks/useInAppReview';

const hasNotch = DeviceInfo.hasNotch();

const TourTrashPDP = ({navigation, route}) => {
  const tourTrash = route.params?.tourTrash;
  const refreshTourTrashData = route.params?.refreshTourTrashData;
  const [loading, setLoading] = useState(false);
  const isClosed = moment().isAfter(moment(tourTrash.endDate));
  const [dataProduct, setDataProduct] = useState([]);
  const {triggerInAppReview} = useInAppReview();
  useEffect(() => {
    getData();
  }, [tourTrash]);
  const getData = () => {
    const data = [];
    tourTrash.product?.images?.map(image => {
      data.push({link: image?.imageUrl});
    });
    if (tourTrash.product?.videoId && tourTrash.product?.videoId?.length > 0) {
      const video = {
        thumbnailUrl: `https://img.youtube.com/vi/${tourTrash.product?.videoId}/maxresdefault.jpg`,
        id: tourTrash.product?.videoId,
        title: tourTrash.product?.name,
        host: tourTrash.product?.videoType,
      };
      data.push(video);
    }
    setDataProduct(data);
  };

  const goToVideo = item => {
    GA_logSelectContentEvent({
      contentName: item?.title,
      contentType: 'video',
      clickLocation: 'rewards-tour-trash-pdp',
    });
    navigation.navigate('Video', {
      video: {
        id: item.id,
        title: item.title,
        host: item.host,
        clickLocation: 'rewards-tour-trash-pdp',
        contentName: item?.title,
      },
    });
  };

  const enterTourTrash = async () => {
    setLoading(true);
    try {
      // Make request to enter tour trash
      await joinTourTrash(tourTrash.id);
      setLoading(false);
      showToast({
        type: 'success',
        message: t('tourtrash.pdp.you_are_in'),
        subText: t('tourtrash.pdp.entry_to_the_giveaway'),
      });
      refreshTourTrashData?.();
      // Indicate tour trash list to refetch with params
      navigation.goBack();
      setTimeout(() => {
        triggerInAppReview();
      }, 1000);
      await analytics().logEvent('tour_trash_joined', {
        id: tourTrash?.id,
        name: tourTrash?.product?.name,
      });
    } catch (error) {
      setLoading(false);
      refreshTourTrashData?.();
      showToast({
        type: 'error',
        message: t('tourtrash.pdp.this_is_on_us'),
        subText:
          error?.data?.errorMessage ||
          error.response?.data?.errorMessage ||
          t('tourtrash.pdp.an_error_occured_entering_tour_trash'),
      });
    }
  };

  const getButtonText = () => {
    if (tourTrash.joined) {
      return t('tour_trash.successfully_entered');
    } else if (isClosed) {
      return t('tour_trash.tour_trash_closed');
    } else if (tourTrash.status === 'COMING_SOON') {
      return t('tour_trash.coming_soon');
    } else {
      return t('tour_trash.enter_for_a_chance_to_win');
    }
  };

  const getButtonColor = () => {
    if (tourTrash.status !== 'ACTIVE' || isClosed) {
      return GREY;
    } else if (tourTrash.joined) {
      return '#3ABA56';
    } else {
      return 'black';
    }
  };
  //9:00 a.m. PT on July 12th, 2022 and 9:59 a.m. PT on July 28th, 2022.
  //h:mm a. PT on MMMM Do, YYYY and h:mm a. PT on MMMM Do, YYYY
  return (
    <SafeAreaView
      style={[appStyles.flex, appStyles.whiteBg]}
      edges={['right', 'left']}
    >
      <ScrollView style={[appStyles.flex, appStyles.pBSm]}>
        <Image
          style={[
            {
              height: wp('100%') / 0.75,
              width: wp('100%'),
            },
          ]}
          source={{
            uri:
              tourTrash?.product?.images?.length > 1
                ? tourTrash?.product?.images[1]?.imageUrl
                : tourTrash?.product?.images[0]?.imageUrl,
          }}
        />
        <View style={{paddingHorizontal: 25, marginBottom: 10, marginTop: 20}}>
          <Text size={26} black DINbold style={{textTransform: 'uppercase'}}>
            {tourTrash.product?.name}
          </Text>
          <Text style={[appStyles.grey, appStyles.mBMd, {fontSize: 13}]}>
            {moment(tourTrash.startDate).format('MMM DD')} -{' '}
            {moment(tourTrash.endDate).format('MMM DD')}
          </Text>
          <View style={[appStyles.mBMd]}>
            <HtmlParser
              html={tourTrash.product?.detail?.replace(/(\r\n|\n|\r)/gm, '')}
              markupColor={true}
              removeLine={true}
            />
          </View>
        </View>
        {dataProduct?.length > 2 &&
          dataProduct?.map((_item, i) => {
            if (i === 0 || i === 1) {
              return null;
            }
            if (_item?.thumbnailUrl) {
              return (
                <TouchableOpacity
                  activeOpacity={0.9}
                  style={{
                    marginBottom: 10,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={() => goToVideo(_item)}
                >
                  <Image
                    key={_item?.id + i}
                    style={[
                      {
                        height: wp('100%') / 0.75,
                        width: wp('100%'),
                      },
                    ]}
                    source={{
                      uri: _item?.thumbnailUrl,
                    }}
                  />
                  <View
                    style={{
                      position: 'absolute',
                    }}
                  >
                    <PlayButton width={60} height={60} />
                  </View>
                </TouchableOpacity>
              );
            }
            return (
              <Image
                key={_item?.link}
                style={[
                  {
                    height: wp('100%') / 0.75,
                    width: wp('100%'),
                    marginBottom: 10,
                  },
                ]}
                source={{
                  uri: _item?.link,
                }}
              />
            );
          })}
        <View style={{paddingHorizontal: 25}}>
          <HtmlParser
            html={tourTrash.product?.legal?.replace(/(\r\n|\n|\r)/gm, '')}
            markupColor={true}
            removeLine={true}
          />
        </View>
      </ScrollView>
      <View style={[appStyles.pSm, {paddingHorizontal: 25}]}>
        <Button
          text={getButtonText()}
          textColor="white"
          backgroundColor={getButtonColor()}
          borderColor={getButtonColor()}
          disabled={
            loading ||
            tourTrash.joined ||
            tourTrash.status !== 'ACTIVE' ||
            isClosed
          }
          leftTextIcon={tourTrash.joined && '􀆅'}
          leftTextIconStyle={{marginRight: 10}}
          loading={loading}
          loadingMode="dark"
          onPress={enterTourTrash}
          centered
          DINbold
          textStyle={{fontWeight: '500'}}
        />
        <View style={[appStyles.pSm]}>
          <TouchableOpacity
            onPress={() =>
              navigation.navigate('WebView', {
                screen: 'WebView',
                params: {
                  title: t('tour_trash.terms_conditions'),
                  uri: 'https://www.taylormadegolf.com/tourtrash-terms-and-conditions.html?lang=en_US',
                  canGoBack: true,
                  origin: 'TourTrashTermWeb'
                },
              })
            }
            style={[styles.txtTerm]}
          >
            <Text
              style={[
                appStyles.black,
                appStyles.xs,
                {fontWeight: Platform.OS === 'android' ? 'bold' : '600'},
              ]}
            >
              tour_trash.terms_conditions
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      <TouchableOpacity
        style={styles.iconContainer}
        onPress={() => navigation.goBack()}
      >
        <View style={[styles.closeIcon, appStyles.vCenter, appStyles.hCenter]}>
          <Icon name="close" size={moderateScale(20)} />
        </View>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  closeIcon: {
    backgroundColor: '#ffffff',
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
  },
  iconContainer: {
    top: hasNotch ? 45 : 25,
    right: 15,
    position: 'absolute',
  },
  txtTerm: {
    borderBottomWidth: 1,
    borderBottomColor: '#000000',
    paddingBottom: 1,
    alignSelf: 'center',
  },
});

export default TourTrashPDP;
