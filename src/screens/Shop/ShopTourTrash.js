import React, {useEffect, useImperativeHandle, useMemo, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import TourTrashList from 'components/TourTrashList';
import {getTourTrashProducts} from 'utils/home';
import {result} from 'lodash';
import {CACHE_KEY} from 'utils/constant';
import {useIsFocused} from '@react-navigation/native';
import {updateDataTourTrash} from 'reducers/rewards';

const ShopTourTrash = ({navigation: {navigate}, route}, ref) => {
  const tourTrashCache = useSelector(state => state?.rewards?.tourTrash);
  const dispatch = useDispatch();
  const loyalty = useSelector(state => state?.loyalty);
  const currentTier = result(loyalty, 'tier.currentTier', null);
  const user = useSelector(state => state?.user);
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const currentTourTrashCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.REWARD_TOUR_TRASH,
  )?.version;
  const isScreenFocused = useIsFocused();

  useEffect(() => {
    if (appCacheVersions) {
      refreshTourTrashDataIfNeeded();
    }
  }, [appCacheVersions]);

  const refreshTourTrashDataIfNeeded = async () => {
    try {
      if (
        currentTourTrashCacheVersion === tourTrashCache?.version &&
        appCacheVersions?.country === tourTrashCache?.country &&
        currentTourTrashCacheVersion != null
      ) {
        if (isScreenFocused) {
          //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
          dispatch(
            updateDataTourTrash({
              country: user?.userCountry,
              version: currentTourTrashCacheVersion,
              data: tourTrashCache?.data,
            }),
          );
        }
      } else {
        console.log('vao day', tourTrashCache?.version);
        refreshTourTrashData();
      }
    } catch (error) {}
  };

  const refreshTourTrashData = () => {
    getTourTrashProducts(
      dispatch,
      user?.userCountry,
      currentTourTrashCacheVersion,
    );
  };

  const tourTrashProductByLoyaltyTier = useMemo(() => {
    if (tourTrashCache?.data?.length > 0) {
      let listTourTrash = [];
      for (let index = 0; index < tourTrashCache?.data.length; index++) {
        const product = tourTrashCache?.data?.[index];
        const options = product?.options ? JSON.parse(product.options) : null;
        const loyaltyTier = options?.loyalty_tier;
        if (loyaltyTier?.length && currentTier) {
          const hasLevel = loyaltyTier.find(
            tier => tier.toLowerCase() === currentTier.toLowerCase(),
          );
          if (hasLevel) {
            listTourTrash.push(product);
          }
        }
      }
      return listTourTrash;
    } else {
      return [];
    }
  }, [tourTrashCache, currentTier]);

  if (tourTrashProductByLoyaltyTier.length === 0) {
    return null;
  }
  return (
    <>
      {tourTrashProductByLoyaltyTier.length > 0 ? (
        <TourTrashList
          navigate={navigate}
          products={tourTrashProductByLoyaltyTier}
          refreshTourTrashData={refreshTourTrashData}
        />
      ) : null}
    </>
  );
};

export default React.forwardRef(ShopTourTrash);
