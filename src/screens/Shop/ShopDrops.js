import React, {useEffect, useState, useRef} from 'react';
import {ActivityIndicator, AppState} from 'react-native';
import {t} from 'i18next';
import {useSelector} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import ShopList from 'components/ShopList';

import {getProductsInCategory, getProductOverwrite} from 'requests/ecom';
import {getPromotion} from 'requests/ecom';
import {connect} from 'react-redux';
import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import analytics from '@react-native-firebase/analytics';
import {
  arrayEquals,
  isShowHideProduct,
  NOT_APPLICABLE,
  SCREEN_CLASS,
  SCREEN_TYPES,
} from 'utils/constant';

const ShopDrops = ({navigation, getPromotion}) => {
  const {navigate} = navigation;
  const promotions = useSelector(state => state?.home?.promotions);
  const appState = useRef(AppState.currentState);
  const [loading, setLoading] = useState(false);
  const [isRefresh, setIsRefresh] = useState(false);
  const [products, setProducts] = useState([]);
  const [productsMyTM, setProductsMyTM] = useState([]);

  const getData = async () => {
    try {
      let newProducts = [];
      const productsInCategory = await getProductsInCategory();
      const productOverwrite = await getProductOverwrite();
      setProductsMyTM(productOverwrite);
      let equalData = arrayEquals(productsInCategory, productOverwrite);
      let listProduct = [];
      if (equalData === true) {
        productOverwrite.forEach(product => {
          const c = productsInCategory.find(
            x => x.product_id === product.product_id,
          );
          if (c) {
            newProducts.push({
              ...c,
              sold_out: product.sold_out,
              isOverwrite: true,
            });
          }
        });
        // show products in Drops with soldOut = false and Orderable = true
        setProducts(newProducts);
        listProduct = [...newProducts];
      } else {
        setProducts(productsInCategory);
        listProduct = [...productsInCategory];
      }
      return listProduct;
    } catch (error) {
      throw error;
    }
  };

  const mergedProduct = (adminProducts, ecomProducts) => {
    let newProducts = [];

    adminProducts.forEach(product => {
      const c = ecomProducts.find(x => x.product_id === product.product_id);
      if (c) {
        newProducts.push({
          ...c,
          sold_out: product.sold_out,
          isOverwrite: true,
        });
      }
    });
    return newProducts;
  };

  const getDataShowHide = async () => {
    try {
      const productsInCategory = await getProductsInCategory();
      const productOverwrite = await getProductOverwrite();

      setProducts(mergedProduct(productOverwrite, productsInCategory));
    } catch (error) {
      throw error;
    }
  };

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        // Make request to get products by category from sfcc
        if (isShowHideProduct) {
          await getDataShowHide();
        } else {
          await getData();
        }
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('variant.configurator_modal.fore'),
          subText: t('shopDrops.after_we_hit_our_provisional'),
        });
      }
    })();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      setIsRefresh(true);
      onRefreshProduct();
      setTimeout(() => {
        setIsRefresh(false);
      }, 1000);
    }, []),
  );

  useEffect(() => {
    const appStateHandler = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      appStateHandler?.remove();
    };
  }, []);

  const handleAppStateChange = nextAppState => {
    // Get Promotion if coming from app background
    if (appState.current.match(/background/) && nextAppState === 'active') {
      getPromotion();
    }
    appState.current = nextAppState;
  };

  const onRefreshProduct = React.useCallback(async () => {
    setIsRefresh(true);
    try {
      getPromotion();
      let listProduct = await getData();
      GA_measureProductImpression(listProduct);
      setIsRefresh(false);
    } catch (error) {
      setIsRefresh(false);
    }
  }, []);

  const GA_measureProductImpression = listProduct => {
    let itemListForGA = [];
    listProduct?.map((item, index) => {
      itemListForGA.push({
        item_id: item.product_id,
        item_name: item.product_name,
        // eslint-disable-next-line no-underscore-dangle
        item_category: NOT_APPLICABLE,
        item_brand: 'TaylorMade Golf',
        item_list_name: 'Drops',
        item_location_id: 'shop - drops - product', //optional
        item_variant: NOT_APPLICABLE,
        index: index, //e.g 4
        price: item.price, //e.g 123.22
        quantity: 1, //e.g 2
      });
    });
    analytics().logEvent('view_item_list', {
      app_screen_name: 'shop - drops', //e.g home, my orders
      screen_type: SCREEN_CLASS.PLP, //e.g checkout, pdp, plp, account
      page_name: 'shop - drops', //e.g home, my orders
      page_type: SCREEN_TYPES.MEMBERSHOP, //e.g basket, home, order
      page_category: SCREEN_TYPES.MEMBERSHOP,
      items: itemListForGA,
      currency: 'USD',
    });
  };

  return (
    <>
      <FocusAwareStatusBar barStyle={'light-content'} />
      {loading ? (
        <ActivityIndicator style={appStyles.pTLg} />
      ) : (
        <>
          <ShopList
            title={t('shop.exclusive_product_drops')}
            description={t('shop.subcriber_access')}
            navigate={navigate}
            products={products}
            productsMyTM={productsMyTM}
            promotions={promotions}
            refreshing={isRefresh}
            onRefreshProduct={onRefreshProduct}
            refreshPromotion={getPromotion}
          />
          {/* <View
            style={{
              position: 'absolute',
              bottom: tabBarheight + 7,
              right: 0,
            }}
          >
            <TouchableOpacity
              // onPress={() =>
              //   navigation.navigate('Video', {
              //     video: {
              //       id: 'AQi5AuqrFoY',
              //       host: 'youtube',
              //       annotation: true,
              //       videoUrl: 'https://youtu.be/AQi5AuqrFoY',
              //       title: 'Shop Drops',
              //       // contentId: '430079',
              //     },
              //   })
              // }
              activeOpacity={0.7}
            >
              <Image
                source={PlayExplainerIconBlack}
                style={{width: 59, height: 57}}
              />
            </TouchableOpacity>
          </View> */}
        </>
      )}
    </>
  );
};

const mapDispatchToProps = {
  getPromotion,
};

export default connect(null, mapDispatchToProps)(ShopDrops);
