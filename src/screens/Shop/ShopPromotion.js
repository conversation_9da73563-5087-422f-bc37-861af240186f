import React from 'react';
import {
  ScrollView,
  View,
  SafeAreaView,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {moderateScale} from 'react-native-size-matters';
import DeviceInfo from 'react-native-device-info';
import Icon from 'react-native-vector-icons/Feather';
import ProductImageCarousel from 'components/ProductImageCarousel';
import Text from 'components/Text';
import HtmlParser from 'components/HtmlView';

import appStyles from 'styles/global';

const hasNotch = DeviceInfo.hasNotch();

const ShopPromotion = ({navigation, route}) => {
  const item = route.params?.item;
  const images = item?.images?.map(val => ({link: val?.imageUrl}));
  const goBack = () => {
    navigation.goBack();
  };
  return (
    <SafeAreaView style={appStyles.flex} edges={['right', 'bottom', 'left']}>
      <ScrollView style={[appStyles.flex, appStyles.pBSm]}>
        <ProductImageCarousel images={images} />
        <View style={appStyles.pHSm}>
          <Text style={appStyles.mVSm}>{item?.title}</Text>
          <View style={[appStyles.mBMd]}>
            <HtmlParser
              html={item?.description?.replace(/(\r\n|\n|\r)/gm, '')}
              markupColor={true}
              removeLine={true}
            />
          </View>
        </View>
      </ScrollView>
      <TouchableOpacity style={styles.iconContainer} onPress={() => goBack}>
        <View style={[styles.closeIcon, appStyles.vCenter, appStyles.hCenter]}>
          <Icon name="x" size={moderateScale(18)} />
        </View>
      </TouchableOpacity>
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  closeIcon: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    width: moderateScale(26),
    height: moderateScale(26),
    borderRadius: moderateScale(12),
  },
  iconContainer: {
    width: moderateScale(26),
    height: moderateScale(26),
    top: hasNotch ? 45 : 25,
    right: 15,
    position: 'absolute',
  },
});

export default ShopPromotion;
