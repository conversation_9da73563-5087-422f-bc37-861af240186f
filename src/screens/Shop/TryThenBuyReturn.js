import React, {useState, useEffect} from 'react';
import {
  ScrollView,
  View,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {moderateScale} from 'react-native-size-matters';

import Text from 'components/Text';
import Button from 'components/Button';

import {getTTBSurvey, tryThenBuy, submitTTBSurvey} from 'requests/ttb';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';

const QuestionForm = ({question, surveyAnswers, setSurveyAnswers}) => {
  const toggleAnswer = answer => {
    const foundQuestion = surveyAnswers.find(
      surveyAnswer => surveyAnswer.questionId === question.id,
    );

    // Check to see if question has been answered
    if (foundQuestion) {
      // Check if answer already exists
      if (foundQuestion.answerId === answer.id) {
        // Remove answer
        setSurveyAnswers(
          surveyAnswers.filter(
            surveyAnswer => surveyAnswer.answerId !== answer.id,
          ),
        );
      } else {
        // Update question with new answer
        setSurveyAnswers(
          surveyAnswers.map(surveyAnswer => {
            if (surveyAnswer.questionId === question.id) {
              return {
                questionId: question.id,
                answerId: answer.id,
              };
            }
            return surveyAnswer;
          }),
        );
      }
    } else {
      // Add new question and answer
      setSurveyAnswers(
        surveyAnswers.concat({
          questionId: question.id,
          answerId: answer.id,
        }),
      );
    }
  };

  return (
    <View style={appStyles.mBSm}>
      <Text style={[appStyles.textCenter, appStyles.mBSm]}>
        {question.question}
      </Text>
      <View style={[appStyles.row, appStyles.spaceBetween]}>
        {question?.answers?.map((answer, i) => {
          const hasAnswer = surveyAnswers.some(
            surveyAnswer => surveyAnswer.answerId === answer.id,
          );
          return (
            <View key={answer.id} style={[appStyles.hCenter]}>
              <TouchableOpacity onPress={() => toggleAnswer(answer)}>
                <View
                  style={{
                    width: moderateScale(20),
                    height: moderateScale(20),
                    borderRadius: moderateScale(10),
                    backgroundColor: hasAnswer ? 'black' : 'white',
                  }}
                />
              </TouchableOpacity>
              <Text style={[appStyles.mTSm, appStyles.xs]}>
                {answer.answer}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const TryThenBuyReturn = ({navigation: {navigate}, route}) => {
  const orderId = route?.params?.orderId;
  const [survey, setSurvey] = useState(null);
  const [loadingSurvey, setLoadingSurvey] = useState(false);
  const [loading, setLoading] = useState(false);
  const [surveyAnswers, setSurveyAnswers] = useState([]);

  useEffect(() => {
    (async () => {
      setLoadingSurvey(true);
      try {
        const ttbSurvey = await getTTBSurvey();
        setSurvey(ttbSurvey);
        setLoadingSurvey(false);
      } catch (error) {
        setLoadingSurvey(false);
        showToast({
          type: 'error',
          message: t('An_error_occurred_retrieving_survey'),
        });
      }
    })();
  }, []);

  const returnTTB = async () => {
    if (survey?.questions?.length !== surveyAnswers?.length) {
      return showToast({
        type: 'error',
        message: t('trythenbuy.return.answer_all_survey_questions'),
      });
    }

    try {
      setLoading(true);
      await tryThenBuy(orderId, 'return');
      setLoading(false);
      navigate('ShopTTB');
      showToast({
        type: 'success',
        message: t('trial.status.time_to_try_again'),
        subText: t('trial.status.you_return_label'),
      });
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: t('An_error_occurred_returning'),
      });
    }

    try {
      await submitTTBSurvey(survey.id, surveyAnswers);
    } catch (error) {
      showToast({
        type: 'error',
        message: t('An_error_occurred_with_your_survey'),
      });
    }
  };

  return (
    <SafeAreaView
      style={[appStyles.flex, appStyles.whiteBg]}
      edges={['bottom', 'left', 'right']}
    >
      <ScrollView
        style={[
          appStyles.flex,
          appStyles.pTMd,
          appStyles.pHSm,
          appStyles.lightGreyBg,
        ]}
        contentContainerStyle={appStyles.pBLg}
      >
        {loadingSurvey ? (
          <ActivityIndicator color="black" />
        ) : (
          <>
            <Text style={[appStyles.lg]} DINbold>
              screen.try_then_buy.mind_if_we_ask_why
            </Text>
            <Text style={[appStyles.mTSm, appStyles.mBLg]}>
              screen.try_then_buy.please_fill_our
            </Text>
            {survey?.questions?.map(question => {
              return (
                <QuestionForm
                  key={question.id}
                  question={question}
                  surveyAnswers={surveyAnswers}
                  setSurveyAnswers={setSurveyAnswers}
                />
              );
            })}
            <Text style={[appStyles.lightGrey, appStyles.xs, appStyles.mTSm]}>
              try.then.buy.not_return_trial
            </Text>
          </>
        )}
      </ScrollView>
      <View style={[appStyles.pSm]}>
        <Button
          text="GET RETURN LABEL"
          textColor="black"
          backgroundColor="white"
          borderColor="black"
          loading={loading}
          onPress={returnTTB}
          centered
          DINbold
        />
      </View>
    </SafeAreaView>
  );
};

export default TryThenBuyReturn;
