import {useNavigation} from '@react-navigation/native';
import Button from 'components/Button';
import Text from 'components/Text';
import CustomImage from 'components/CustomImage/CustomImage';
import React, {useRef, useState, useImperativeHandle} from 'react';
import {View, StyleSheet, Image} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import appStyles from 'styles/global';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import Video from 'react-native-video';
import {getProductIdFromUrl, openEcomWebview, prepareLink} from 'utils/shop';
import {getAuth0AccessToken} from 'utils/user';
import {useDispatch} from 'react-redux';
import {GA_EVENT_NAME, WEBVIEW_PAGE_TYPE} from 'utils/constant';
import {isEmpty} from 'lodash';
import {GA_selectPromotionWithEcomData} from 'utils/googleAnalytics';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const MainKeyTile = ({data, loading, logEventPromotion}, ref) => {
  const dataVideo = useRef(null);
  const [pause, setPause] = useState(false);
  const dispatch = useDispatch();
  const {getEcomProductDetail, getMultipleEcomProductDetails} =
    useEcomProductDetail();

  const navigation = useNavigation();

  useImperativeHandle(ref, () => ({
    playVideo: () => {
      if (pause && !loading && data?.videoLink) {
        setPause(false);
      }
    },
    pauseVideo: () => {
      if (!pause) {
        setPause(true);
      }
    },
  }));

  const onPressCTA = async () => {
    const accessToken = await getAuth0AccessToken(dispatch);
    let options = null;
    try {
      options = JSON.parse(data?.options);
    } catch (error) {}
    const linkUrl = await prepareLink(
      data?.ctaLink,
      accessToken,
      options?.ctaLinkType,
    );
    logGASelectPromotion(data);
    openEcomWebview(
      navigation.navigate,
      {
        title: data?.title,
        uri: linkUrl,
        canGoBack: true,
        originUri: data?.ctaLink,
        imageUrl: data?.videoLink ? data?.videoLink : data?.imageLink,
        clickLocation: 'shop-main-marquee-tile',
        origin: 'ShopMainKeyWeb',
        isLogSelectItem: false,
      },
      getEcomProductDetail,
    );
  };

  const logGASelectPromotion = async productItem => {
    if (productItem) {
      const listProductId = [];
      const options =
        typeof productItem?.options === 'object'
          ? productItem?.options
          : JSON.parse(productItem?.options);
      if (options?.ctaLinkType === WEBVIEW_PAGE_TYPE.PDP) {
        try {
          let productId = getProductIdFromUrl(productItem?.ctaLink);
          let productDetails = await getEcomProductDetail(productId);
          if (productDetails) {
            listProductId.push(productId);
          }
        } catch (error) {}
      }
      const tileTitle = productItem?.title;

      if (!isEmpty(listProductId)) {
        const paramGA = {
          promotion_id: productItem?.id,
          promotion_name: productItem?.title,
          creative_name: `${productItem?.ctaText} CTA`,
          creative_slot: 'Shop > Marquee',
          location_id: 'Shop > Marquee',
        };
        const rsLogGA = await GA_selectPromotionWithEcomData({
          listProductId,
          paramGA,
          tileTitle,
          getMultipleEcomProductDetails,
        });
        if (!rsLogGA) {
          logEventPromotion({
            products: [productItem],
            eventName: GA_EVENT_NAME.SELECT_PROMOTION,
            tileTitle,
          });
        }
      } else {
        logEventPromotion({
          products: [productItem],
          eventName: GA_EVENT_NAME.SELECT_PROMOTION,
          tileTitle,
        });
      }
    }
  };

  const styleCustom = data?.options ? JSON.parse(data?.options) : null;

  const onLoad = () => {
    dataVideo?.current?.seek(0.01);
  };

  const onEnd = () => {
    dataVideo?.current?.seek(0.01);
  };

  if (loading) {
    return (
      <ShimmerPlaceholder
        LinearGradient={LinearGradient}
        width={wp(96)}
        height={wp(96) * 1.33}
        shimmerStyle={[
          {
            borderRadius: 24,
            marginBottom: 24,
            alignSelf: 'center',
          },
          appStyles.viewShadow,
        ]}
      />
    );
  }

  return !data ? null : (
    <View
      style={[
        styles.viewContainer,
        {minHeight: wp(96) * 1.33, width: wp(96), marginBottom: 24},
      ]}
    >
      <View style={styles.viewImage}>
        {data?.videoLink ? (
          <Video
            source={{
              uri: data?.videoLink?.replace(/ /g, '%20'),
            }} // Can be a URL or a local file.
            ref={dataVideo} // Store reference
            style={[styles.viewImage, {position: 'relative'}]}
            paused={pause}
            onLoad={onLoad}
            onEnd={onEnd}
            resizeMode={'cover'}
            ignoreSilentSwitch={'ignore'}
            repeat
          />
        ) : (
          <CustomImage
            source={{
              uri: data?.imageLink,
            }}
            style={styles.viewImage}
          />
        )}
      </View>
      <View
        style={[
          styles.viewContent,
          appStyles.hCenter,
          {backgroundColor: styleCustom?.background || '#fff'},
        ]}
      >
        <View style={appStyles.flex}>
          <Text
            Din79Font
            weight={800}
            size={22}
            numberOfLines={2}
            style={[
              appStyles.textCenter,
              {
                marginTop: 10,
                marginBottom: 8,
                lineHeight: 25,
                color: styleCustom?.titleColor || '#000',
                letterSpacing: 1.1,
              },
            ]}
          >
            {data?.title}
          </Text>
          <Text
            size={16}
            style={[
              appStyles.textCenter,
              {
                color: styleCustom?.subColor || '#000',
              },
            ]}
          >
            {data?.description?.trim?.()}
          </Text>
        </View>
        <Button
          text={data?.ctaText}
          Din79Font
          textColor={styleCustom?.ctaTextColor || '#fff'}
          borderColor={styleCustom?.ctaBackgroundColor || 'rgba(0, 34, 66, 1)'}
          backgroundColor={
            styleCustom?.ctaBackgroundColor || 'rgba(0, 34, 66, 1)'
          }
          textStyle={[
            appStyles.textCenter,
            {fontWeight: '700', letterSpacing: 1.2, fontSize: 12},
          ]}
          style={{height: 40, minWidth: 180, marginBottom: 24, marginTop: 16}}
          onPress={onPressCTA}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  viewContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 24,
    marginBottom: 24,
    alignSelf: 'center',
    ...appStyles.viewShadow,
  },
  viewImage: {
    width: wp(96),
    height: wp(96) / 1.33,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  viewContent: {
    flex: 1,
    paddingHorizontal: 16,
    borderBottomEndRadius: 24,
    borderBottomStartRadius: 24,
  },
});

export default React.forwardRef(MainKeyTile);
