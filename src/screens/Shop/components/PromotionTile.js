import React, {useEffect, useImperativeHandle, useRef, useState} from 'react';
import {StyleSheet, TouchableOpacity, Linking} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {setDeepLink} from 'reducers/app';
import {isEmpty, result} from 'lodash';
import {getPromotionTile} from 'requests/shop';
import Video from 'react-native-video';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import AutoScaleImage from 'components/AutoScaleImage';
import {getAuth0AccessToken} from 'utils/user';
import {getProductIdFromUrl, openEcomWebview, prepareLink} from 'utils/shop';
import {GA_logSelectContentEvent} from 'utils/article';
import {
  GA_logEvent,
  GA_logViewItemList,
  GA_selectPromotionWithEcomData,
  getProductIds,
} from 'utils/googleAnalytics';
import {CACHE_KEY, GA_EVENT_NAME, WEBVIEW_PAGE_TYPE} from 'utils/constant';
import {updatePromotionTileData} from 'reducers/dataCache';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const PromotionTile = ({}, ref) => {
  const navigation = useNavigation();
  const {navigate} = navigation;
  const [promotion, setPromotion] = useState({});
  const [loading, setLoading] = useState(false);
  const [isShowPromotion, setIsShowPromotion] = useState(false);
  const dispatch = useDispatch();
  const loyalty = useSelector(state => state?.loyalty);
  const currentTier = result(loyalty, 'tier.currentTier', null);
  const isScreenFocused = useIsFocused();
  const dataVideo = useRef(null);
  const [pause, setPause] = useState(false);
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const promotionTileCache = useSelector(
    state => state.dataCache?.promotionTile,
  );
  const currentCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.SHOP_PROMOTION,
  )?.version;
  const {getEcomProductDetail, getMultipleEcomProductDetails} =
    useEcomProductDetail();
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (appCacheVersions) {
      refreshDataIfNeeded();
    }
  }, [appCacheVersions]);

  useEffect(() => {
    if (promotionTileCache) {
      loadPromotionData();
      isFirstRender.current = false;
    }
  }, [promotionTileCache, currentTier]);

  const refreshDataIfNeeded = async () => {
    try {
      if (
        currentCacheVersion === promotionTileCache?.version &&
        appCacheVersions?.country === promotionTileCache?.country &&
        currentCacheVersion != null
      ) {
        if (isScreenFocused && !isFirstRender.current) {
          //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
          dispatch(
            updatePromotionTileData({
              country: userCountry,
              version: currentCacheVersion,
              data: promotionTileCache?.data,
            }),
          );
        }
      } else {
        setLoading(true);
        const params = {
          page: 1,
          take: 30,
          country: userCountry,
        };
        let dataPromotion = await getPromotionTile(params);
        dispatch(
          updatePromotionTileData({
            country: userCountry,
            version: currentCacheVersion,
            data: dataPromotion?.widgets || [],
          }),
        );
      }
    } catch (error) {
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 500);
    }
  };

  useImperativeHandle(ref, () => ({
    pauseVideo: () => {
      if (!pause) {
        setPause(true);
      }
    },
    playVideo: () => {
      if (pause && !loading) {
        setPause(false);
      }
    },
  }));

  const loadPromotionData = async () => {
    try {
      const promotionDt = promotionTileCache?.data?.[0];
      const options = promotionDt?.options
        ? JSON.parse(promotionDt.options)
        : null;
      const loyaltyTier = options?.loyalty_tier;

      if (loyaltyTier?.length && currentTier) {
        const isShowPro = loyaltyTier.find(
          tier => tier.toLowerCase() === currentTier.toLowerCase(),
        );
        if (isShowPro) {
          setIsShowPromotion(true);
        } else {
          setIsShowPromotion(false);
        }
      }
      if (promotionDt) {
        let listProductId = getProductIds([promotionDt]);
        GA_logViewItemList(
          listProductId,
          'shop-promotion',
          undefined,
          undefined,
          getMultipleEcomProductDetails,
        );
        logEventPromotion({
          products: [promotionDt],
          eventName: GA_EVENT_NAME.VIEW_PROMOTION,
        });
      }

      setPromotion(promotionDt);
    } catch (error) {}
  };

  const logGASelectPromotion = async productItem => {
    if (productItem) {
      const listProductId = [];
      const options =
        typeof productItem?.options === 'object'
          ? productItem?.options
          : JSON.parse(productItem?.options);
      if (options?.ctaLinkType === WEBVIEW_PAGE_TYPE.PDP) {
        try {
          let productId = getProductIdFromUrl(productItem?.ctaLink);
          let productDetails = await getEcomProductDetail(productId);
          if (productDetails) {
            listProductId.push(productId);
          }
        } catch (error) {}
      }
      const tileTitle = productItem?.title;

      if (!isEmpty(listProductId)) {
        const paramGA = {
          promotion_id: productItem?.id,
          promotion_name: productItem?.title,
          creative_name: 'Promotion Shop',
          creative_slot: 'Shop > Promotion',
          location_id: 'Shop > Promotion',
        };
        const rsLogGA = await GA_selectPromotionWithEcomData({
          listProductId,
          paramGA,
          tileTitle,
          getMultipleEcomProductDetails,
        });
        if (!rsLogGA) {
          logEventPromotion({
            products: [productItem],
            eventName: GA_EVENT_NAME.SELECT_PROMOTION,
            tileTitle,
          });
        }
      } else {
        logEventPromotion({
          products: [productItem],
          eventName: GA_EVENT_NAME.SELECT_PROMOTION,
          tileTitle,
        });
      }
    }
  };

  const logEventPromotion = ({
    products,
    eventName,
    clickIndex = 0,
    tileTitle,
  }) => {
    if (products && products.length) {
      const items = [];
      products.map(val => {
        const item = {
          promotion_id: val?.id,
          promotion_name: val?.title,
          creative_name: 'Promotion Shop',
          creative_slot: 'Shop > Promotion',
          location_id: 'Shop > Promotion',
        };
        if (eventName === GA_EVENT_NAME.SELECT_PROMOTION) {
          item.index = clickIndex + 1 + '';
        }
        items.push(item);
      });
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  const onPressCTA = async () => {
    const {extraData, ctaLink} = promotion;
    if (
      ctaLink?.includes('/content') &&
      extraData?.contentFormat === 'clubhouseArticles'
    ) {
      GA_logSelectContentEvent({
        contentName: promotion?.extraData?.title,
        contentType: 'article',
        clickLocation: 'shop-promotion',
      });
      navigate('FeedArticle', {
        screen: 'FeedArticle',
        params: {
          item: {
            backgroundImage:
              promotion?.backgroundImage || promotion?.extraData?.primaryImage,
            title: promotion?.extraData?.title,
            htmlFields: promotion?.extraData?.fields,
            clickLocation: 'shop-promotion',
          },
        },
      });
    } else if (
      ctaLink?.includes('/content') &&
      extraData?.contentFormat === 'videos'
    ) {
      GA_logSelectContentEvent({
        contentName: extraData?.title,
        contentType: 'video',
        clickLocation: 'shop-promotion',
      });
      navigate('Video', {
        video: {
          id: extraData?.video_id,
          title: extraData?.title,
          host: extraData?.video_type,
          contentId: extraData?.id,
          clickLocation: 'shop-promotion',
          contentName: extraData?.title,
        },
      });
    } else if (
      promotion?.extraData?.id &&
      promotion?.extraData?.id?.length > 0 &&
      promotion?.ctaLink?.includes('/product')
    ) {
      navigate('Shop', {
        screen: 'ShopPDP',
        params: {
          productName: promotion?.extraData?.name,
          productId: promotion?.extraData?.id,
          origin: 'drops',
        },
      });
    } else if (
      promotion?.ctaLink &&
      promotion?.ctaLink?.includes('/tourtrash')
    ) {
      navigate('Shop', {
        screen: 'TourTrashPDP',
        params: {tourTrash: promotion?.extraData},
      });
    } else if (
      promotion?.ctaLink &&
      promotion?.ctaLink?.includes('app.link/mytaylormadeplus/')
    ) {
      const paramsDeelinks = promotion?.ctaLink?.split('/');
      if (paramsDeelinks.length > 0) {
        const lastItem = paramsDeelinks[paramsDeelinks.length - 1];
        if (lastItem && lastItem?.length > 0) {
          dispatch(setDeepLink(lastItem));
        }
      }
    } else if (
      promotion?.ctaLink &&
      (promotion?.ctaLink?.includes('https://v28z.app.link') ||
        promotion?.ctaLink?.includes('https://v28z.test-app.link'))
    ) {
      Linking.openURL(promotion?.ctaLink);
    } else if (
      promotion?.ctaLink &&
      promotion?.ctaLink?.length > 0 &&
      (promotion?.ctaLink?.includes('http') ||
        promotion?.ctaLink?.includes('www.'))
    ) {
      const accessToken = await getAuth0AccessToken(dispatch);
      let options = null;
      try {
        options = JSON.parse(promotion?.options);
      } catch (error) {}
      const linkUrl = await prepareLink(
        promotion?.ctaLink,
        accessToken,
        options?.ctaLinkType,
      );
      openEcomWebview(
        navigate,
        {
          title: promotion?.title,
          uri: linkUrl,
          canGoBack: true,
          originUri: promotion?.ctaLink,
          imageUrl: promotion?.imageLink,
          clickLocation: 'shop-promotion',
          origin: 'PromotionWeb',
          isLogSelectItem: false,
        },
        getEcomProductDetail,
      );
      logGASelectPromotion(promotion);
    }
  };

  const onLoad = () => {
    dataVideo?.current?.seek(0.01);
  };

  const onEnd = () => {
    dataVideo?.current?.seek(0.01);
  };

  return !isEmpty(promotion) && isShowPromotion ? (
    <TouchableOpacity
      style={styles.viewContainer}
      activeOpacity={0.8}
      delayPressIn={100}
      delayPressOut={100}
      onPress={onPressCTA}
    >
      {promotion?.videoLink ? (
        <Video
          source={{
            uri: promotion?.videoLink?.replace(/ /g, '%20'),
          }} // Can be a URL or a local file.
          ref={dataVideo} // Store reference
          style={[
            styles.viewImage,
            {height: wp(100) * 1.33, position: 'relative'},
          ]}
          paused={pause}
          onLoad={onLoad}
          onEnd={onEnd}
          resizeMode={'cover'}
          ignoreSilentSwitch={'ignore'}
          repeat
        />
      ) : (
        <AutoScaleImage
          source={{
            uri: promotion?.imageLink,
          }}
          style={styles.viewImage}
        />
      )}
    </TouchableOpacity>
  ) : null;
};

const styles = StyleSheet.create({
  viewContainer: {
    marginBottom: 24,
  },
  viewImage: {
    flex: 1,
    width: wp(100),
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default React.forwardRef(PromotionTile);
