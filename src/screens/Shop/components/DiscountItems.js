import React, {useEffect, useImperativeHandle, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import Text from 'components/Text';
import CustomImage from 'components/CustomImage/CustomImage';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {map} from 'lodash';
import appStyles from 'styles/global';
import {FlatList} from 'react-native-gesture-handler';
import {getDiscountItems, getFinalTile} from 'requests/shop';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {SvgUri} from 'react-native-svg';
import {isEmpty} from 'lodash';
import LogoMyTMGray from 'assets/imgs/logo-no-plus-gray.svg';
import {useDispatch, useSelector} from 'react-redux';
import {getAuth0AccessToken} from 'utils/user';
import {getProductIdFromUrl, openEcomWebview, prepareLink} from 'utils/shop';
import {getConfig} from 'config/env';
import {getEcomSite} from 'utils/countries';
import {
  CACHE_KEY,
  COUNTRY_CODE,
  GA_EVENT_NAME,
  WEBVIEW_PAGE_TYPE,
} from 'utils/constant';
import {
  GA_logEvent,
  GA_logViewItemList,
  GA_selectPromotionWithEcomData,
} from 'utils/googleAnalytics';
import {updateDiscountItemsData, updateFinalTileData} from 'reducers/dataCache';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const DiscountItems = ({isScrollFlatlist, onLayout, isVisibleToUser}, ref) => {
  const [dataTexts, setDataTexts] = useState({
    PRODUCT_CATALOG: '',
    SUB_PRODUCT_CATALOG: '',
  });
  const [discountItemsData, setDiscountItemsData] = useState([]);
  const [finalTile, setFinalTile] = useState({});
  const navigation = useNavigation();
  const {navigate} = navigation;
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const customTextsCache = useSelector(state => state.dataCache?.customTexts);
  const discountItemsDataCache = useSelector(
    state => state.dataCache?.discountItems,
  );
  const finalTileDataCache = useSelector(state => state.dataCache?.finalTile);
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const currentCacheVersion = appCacheVersions?.features.find(
    item => item.key === CACHE_KEY.SHOP_PRODUCT_CATALOG,
  )?.version;
  const currentFinalTileCacheVersion = appCacheVersions?.features.find(
    item => item.key === CACHE_KEY.SHOP_FINAL_TILE,
  )?.version;
  const dispatch = useDispatch();
  const isScreenFocused = useIsFocused();
  const {getEcomProductDetail, getMultipleEcomProductDetails} =
    useEcomProductDetail();
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (appCacheVersions) {
      refreshDataIfNeeded();
      refreshFinalDataIfNeeded();
    }
  }, [appCacheVersions]);

  useEffect(() => {
    if (discountItemsDataCache) {
      getDiscountProducts(discountItemsDataCache);
      isFirstRender.current = false;
    }
  }, [discountItemsDataCache]);

  useEffect(() => {
    if (finalTileDataCache) {
      getFinalTileData(finalTileDataCache);
    }
  }, [finalTileDataCache]);

  useEffect(() => {
    try {
      if (isVisibleToUser && isScreenFocused) {
        GA_logViewItemList(
          discountItemsData.map(item => item.product_id),
          'shop-discount-products',
          dataTexts?.PRODUCT_CATALOG,
          undefined,
          getMultipleEcomProductDetails,
        );
        logEventPromotion({
          products: discountItemsData,
          eventName: GA_EVENT_NAME.VIEW_PROMOTION,
        });
      }
    } catch (error) {}
  }, [isVisibleToUser, isScreenFocused]);

  useEffect(() => {
    if (customTextsCache) {
      getDataText(customTextsCache?.data);
    }
  }, [customTextsCache]);

  const refreshDataIfNeeded = async () => {
    try {
      if (
        currentCacheVersion === discountItemsDataCache?.version &&
        appCacheVersions?.country === discountItemsDataCache?.country &&
        currentCacheVersion != null
      ) {
        if (isScreenFocused && !isFirstRender.current) {
          //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
          dispatch(
            updateDiscountItemsData({
              country: userCountry,
              version: currentCacheVersion,
              data: discountItemsDataCache?.data,
            }),
          );
        }
      } else {
        try {
          const discountItems = await getDiscountItems(userCountry);
          dispatch(
            updateDiscountItemsData({
              country: userCountry,
              version: currentCacheVersion,
              data: discountItems?.overwrite_products || [],
            }),
          );
        } catch (error) {}
      }
    } catch (error) {
      console.log('error fresh discount item data', error.message);
    }
  };

  const refreshFinalDataIfNeeded = async () => {
    try {
      if (
        currentFinalTileCacheVersion === finalTileDataCache?.version &&
        appCacheVersions?.country === finalTileDataCache?.country &&
        currentFinalTileCacheVersion != null
      ) {
        if (isScreenFocused) {
          //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
          dispatch(
            updateFinalTileData({
              country: userCountry,
              version: currentFinalTileCacheVersion,
              data: finalTileDataCache?.data,
            }),
          );
        }
      } else {
        try {
          const params = {
            page: 1,
            take: 30,
            country: userCountry,
          };
          const finalTileData = await getFinalTile(params);
          dispatch(
            updateFinalTileData({
              country: userCountry,
              version: currentFinalTileCacheVersion,
              data: finalTileData?.widgets || [],
            }),
          );
        } catch (error) {}
      }
    } catch (error) {
      console.log('error fresh final tile data', error.message);
    }
  };

  const onItemPress = async (item, index) => {
    const isFinalTile = item?.type === 'FINAL_TILE';
    const accessToken = await getAuth0AccessToken(dispatch);
    const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
    let urlProduct = '';
    if (item?.product_id) {
      urlProduct = `https://${ECOM_HOST_URL}/${item?.product_id}.html?lang=en_US`;
    }
    if (
      item?.link &&
      item?.link?.length > 0 &&
      (item?.link?.includes('http') || item?.link?.includes('www.'))
    ) {
      const linkUrl = await prepareLink(
        urlProduct || item?.link,
        accessToken,
        urlProduct ? WEBVIEW_PAGE_TYPE.PDP : undefined,
      );
      logGASelectPromotion(item, index);
      openEcomWebview(
        navigate,
        {
          title: isFinalTile ? item?.title : item?.productName,
          uri: linkUrl,
          canGoBack: true,
          originUri: urlProduct || item?.link,
          imageUrl: isFinalTile ? item.imageLink : item.image,
          clickLocation: 'shop-discount-products',
          origin: 'DiscountWeb',
          isLogSelectItem: false,
        },
        getEcomProductDetail,
      );
      return;
    }
    if (
      item?.ctaLink &&
      item?.ctaLink?.length > 0 &&
      (item?.ctaLink?.includes('http') || item?.ctaLink?.includes('www.'))
    ) {
      let linkUrl = await prepareLink(
        urlProduct || item?.ctaLink,
        accessToken,
        urlProduct ? WEBVIEW_PAGE_TYPE.PDP : undefined,
      );
      if (isFinalTile) {
        linkUrl = await prepareLink(
          urlProduct || item?.ctaLink,
          accessToken,
          WEBVIEW_PAGE_TYPE.GRID_PAGE,
        );
      }
      logGASelectPromotion(item);
      openEcomWebview(
        navigate,
        {
          title: isFinalTile ? item?.title : item?.productName,
          uri: linkUrl,
          canGoBack: true,
          originUri: urlProduct || item?.ctaLink,
          imageUrl: isFinalTile ? item.imageLink : item.image,
          clickLocation: 'shop-discount-products',
          origin: 'DiscountWeb',
          isLogSelectItem: false,
        },
        getEcomProductDetail,
      );
    }
  };

  const logGASelectPromotion = async (productItem, index) => {
    if (productItem) {
      const listProductId = [];
      if (productItem.product_id) {
        try {
          let productId = productItem.product_id;
          let productDetails = await getEcomProductDetail(productId);
          if (productDetails) {
            listProductId.push(productId);
          }
        } catch (error) {}
      }
      const tileTitle =
        productItem?.type === 'FINAL_TILE'
          ? productItem?.title
          : productItem?.productName;
      if (!isEmpty(listProductId)) {
        const paramGA = {
          promotion_id: productItem?.id,
          creative_slot: 'Shop > Product Catalog',
          promotion_name: tileTitle,
          creative_name: 'Product Catalog Banner',
          location_id: 'Shop > Product Catalog',
        };
        const rsLogGA = await GA_selectPromotionWithEcomData({
          listProductId,
          paramGA,
          clickIndex: index,
          tileTitle,
          getMultipleEcomProductDetails,
        });
        if (!rsLogGA) {
          logEventPromotion({
            products: [productItem],
            eventName: GA_EVENT_NAME.SELECT_PROMOTION,
            clickIndex: index,
            tileTitle,
          });
        }
      } else {
        logEventPromotion({
          products: [productItem],
          eventName: GA_EVENT_NAME.SELECT_PROMOTION,
          clickIndex: index,
          tileTitle,
        });
      }
    }
  };

  const logEventPromotion = ({
    products,
    eventName,
    clickIndex = 0,
    tileTitle,
  }) => {
    if (products && products.length) {
      const items = [];
      const listProductId = map(products, 'product_id').toString();
      const listProductName = map(products, 'productName').toString();
      products.map((val, key) => {
        const item = {
          promotion_id: val?.id,
          creative_slot: 'Shop > Product Catalog',
          promotion_name: val?.productName,
          creative_name: 'Product Catalog Banner',
          location_id: 'Shop > Product Catalog',
          item_id: val?.product_id,
          item_name: val?.productName,
          index: key + 1 + '',
          price: val?.price ? Number(val.price) : 0,
          item_list_id: listProductId,
          item_list_name: listProductName,
        };
        if (eventName === GA_EVENT_NAME.SELECT_PROMOTION) {
          item.index = clickIndex + 1 + '';
        }
        items.push(item);
      });
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  const getDataText = async customTexts => {
    try {
      let title = '';
      let subTitle = '';
      if (customTexts) {
        switch (userCountry) {
          case COUNTRY_CODE.CAN:
            title = customTexts?.CA_PRODUCT_CATALOG;
            subTitle = customTexts?.CA_SUB_PRODUCT_CATALOG;
            break;
          default:
            title = customTexts?.US_PRODUCT_CATALOG;
            subTitle = customTexts?.US_SUB_PRODUCT_CATALOG;
            break;
        }
        setDataTexts({
          PRODUCT_CATALOG: title,
          SUB_PRODUCT_CATALOG: subTitle,
        });
      }
    } catch (error) {}
  };

  const getDiscountProducts = async dataCache => {
    try {
      if (dataCache?.data?.length > 0) {
        const activeProducts =
          dataCache?.data?.filter(
            item => (item.status + '').toUpperCase() === 'ACTIVE',
          ) || [];
        const getFirst5Products = activeProducts?.slice?.(0, 5);
        setDiscountItemsData(getFirst5Products);
      } else {
        setDiscountItemsData([]);
      }
    } catch (error) {}
  };

  const getFinalTileData = async finalTileCache => {
    try {
      if (finalTileCache?.data?.length > 0) {
        setFinalTile(finalTileCache?.data?.[0]);
      } else {
        setFinalTile({});
      }
    } catch (error) {}
  };

  const formatTitle = value => {
    return value?.toLowerCase()?.indexOf('teamtaylormade') > -1
      ? value?.toLowerCase()?.replace(/teamtaylormade/g, 'team-taylormade')
      : value;
  };

  const renderBrandLogo = item => {
    if (item.brandLogo) {
      if (item?.brandLogo?.includes?.('.svg')) {
        return <SvgUri uri={item.brandLogo} width={12} height={12} />;
      }
      return (
        <Image style={[styles.brandLogo]} source={{uri: item.brandLogo}} />
      );
    }
    return <LogoMyTMGray style={[styles.brandLogo]} />;
  };

  const renderItem = ({item, index}) => {
    const isFinalTile = item?.type === 'FINAL_TILE';
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        delayPressIn={100}
        delayPressOut={100}
        onPress={() => onItemPress(item, index)}
        style={[styles.itemWrapper, appStyles.viewShadowLightBig]}
      >
        <CustomImage
          style={styles.itemImage}
          source={{uri: isFinalTile ? item.imageLink : item.image}}
        />
        {isFinalTile ? (
          <View style={{marginHorizontal: 4}}>
            <Text
              Din79Font
              style={[styles.copyText, {textAlign: 'center'}]}
              black
              size={16}
              numberOfLines={2}
            >
              {item?.title}
            </Text>
          </View>
        ) : (
          <View style={{marginHorizontal: 4}}>
            {item.tag && (
              <View style={styles.brandContainer}>
                {renderBrandLogo(item)}
                <Text
                  size={12}
                  numberOfLines={1}
                  style={{color: 'rgba(0, 0, 0, 0.6)', lineHeight: 16.5}}
                >
                  | {item.tag}
                </Text>
              </View>
            )}
            <Text Din79Font style={styles.copyText} black size={16}>
              {formatTitle(item?.productName)}
            </Text>
            <View style={{flexDirection: 'row'}}>
              <Text size={12} black>
                $
                {item?.priceDiscount == '0' ||
                item?.priceDiscount == item?.price
                  ? item?.price
                  : item?.priceDiscount}
              </Text>
              {item?.priceDiscount &&
                item?.priceDiscount != '0' &&
                item?.priceDiscount != item?.price && (
                  <Text size={12} style={styles.priceText}>
                    ${item?.price}
                  </Text>
                )}
            </View>
          </View>
        )}
      </TouchableOpacity>
    );
  };
  const dataDisplay = !isEmpty(finalTile)
    ? discountItemsData.concat(finalTile)
    : discountItemsData;
  return discountItemsData.length > 0 ? (
    <View
      style={styles.viewContainer}
      onLayout={e => {
        onLayout?.(e.nativeEvent.layout.y, e.nativeEvent.layout.height);
      }}
    >
      <View style={{marginHorizontal: 16}}>
        {dataTexts?.PRODUCT_CATALOG && (
          <Text size={16} black style={{marginBottom: 4}}>
            {dataTexts?.PRODUCT_CATALOG || ''}
          </Text>
        )}
        {dataTexts?.SUB_PRODUCT_CATALOG && (
          <Text size={16} black style={{marginBottom: 8, fontWeight: '700'}}>
            {dataTexts?.SUB_PRODUCT_CATALOG || ''}
          </Text>
        )}
      </View>
      <FlatList
        data={dataDisplay}
        horizontal
        onTouchMove={() => {
          if (Platform.OS === 'android') {
            isScrollFlatlist.current = true;
          }
        }}
        onTouchCancel={() => {
          if (Platform.OS === 'android') {
            isScrollFlatlist.current = false;
          }
        }}
        keyExtractor={(item, index) => item?.id}
        renderItem={renderItem}
        style={{width: wp(100), paddingTop: 8}}
        contentContainerStyle={{paddingRight: 8}}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  viewContainer: {
    marginBottom: 14,
  },
  itemWrapper: {
    width: wp(43),
    marginLeft: 8,
    flexDirection: 'column',
    borderRadius: 16,
    backgroundColor: 'white',
    marginBottom: 10,
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 16,
  },
  itemImage: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    height: wp(41),
    width: wp(41),
    marginBottom: 14,
  },
  brandLogo: {
    height: 12,
    width: 12,
    marginRight: 4,
  },
  copyText: {
    fontWeight: '800',
    marginBottom: 14,
    letterSpacing: 1.28,
    lineHeight: 20.68,
  },
  priceText: {
    textDecorationLine: 'line-through',
    color: 'rgba(0, 0, 0, 0.3)',
    marginLeft: 8,
  },
  brandContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    alignItems: 'center',
  },
});

export default React.forwardRef(DiscountItems);
