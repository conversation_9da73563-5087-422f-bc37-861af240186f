import React, {useEffect, useImperativeHandle, useRef, useState} from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import Text from 'components/Text';
import CustomImage from 'components/CustomImage/CustomImage';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import appStyles from 'styles/global';
import {getMoreForYou} from 'requests/shop';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {FlatList} from 'react-native-gesture-handler';
import {t} from 'i18next';
import {getAuth0AccessToken} from 'utils/user';
import {openEcomWebview, prepareLink, getProductIdFromUrl} from 'utils/shop';
import {
  GA_logEvent,
  GA_logViewItemList,
  GA_selectPromotionWithEcomData,
  getProductIds,
} from 'utils/googleAnalytics';
import {CACHE_KEY, GA_EVENT_NAME, WEBVIEW_PAGE_TYPE} from 'utils/constant';
import {isEmpty} from 'lodash';
import {updateMoreForYouData} from 'reducers/dataCache';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

const MoreForYou = ({onLayout, isVisibleToUser}, ref) => {
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const [data, setData] = useState([]);
  const navigation = useNavigation();
  const {navigate} = navigation;
  const dispatch = useDispatch();
  const isScreenFocused = useIsFocused();
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const moreForYouDataCache = useSelector(state => state.dataCache?.moreForYou);
  const currentCacheVersion = appCacheVersions?.features.find(
    item => item.key === CACHE_KEY.SHOP_PRODUCT_CAROUSEL,
  )?.version;
  const {getEcomProductDetail, getMultipleEcomProductDetails} =
    useEcomProductDetail();
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (appCacheVersions) {
      refreshDataIfNeeded();
    }
  }, [appCacheVersions]);

  useEffect(() => {
    if (moreForYouDataCache) {
      getDataMoreForYou(moreForYouDataCache);
      isFirstRender.current = false;
    }
  }, [moreForYouDataCache]);

  const refreshDataIfNeeded = async () => {
    try {
      if (
        currentCacheVersion === moreForYouDataCache?.version &&
        appCacheVersions?.country === moreForYouDataCache?.country &&
        currentCacheVersion != null
      ) {
        if (isScreenFocused && !isFirstRender.current) {
          //if the data is not changed from the Admin Portal, so re-assign old value from cache to trigger the Google Analytics events
          dispatch(
            updateMoreForYouData({
              country: userCountry,
              version: currentCacheVersion,
              data: moreForYouDataCache?.data,
            }),
          );
        }
      } else {
        try {
          const params = {
            page: 1,
            take: 30,
            country: userCountry,
          };
          const results = await getMoreForYou(params);
          dispatch(
            updateMoreForYouData({
              country: userCountry,
              version: currentCacheVersion,
              data: results?.widgets || [],
            }),
          );
        } catch (error) {}
      }
    } catch (error) {
      console.log('error fresh more for you data', error.message);
    }
  };

  const onItemPress = async (item, index) => {
    if (
      item?.ctaLink &&
      item?.ctaLink?.length > 0 &&
      (item?.ctaLink?.includes('http') || item?.ctaLink?.includes('www.'))
    ) {
      const accessToken = await getAuth0AccessToken(dispatch);
      let options = null;
      try {
        options = JSON.parse(item?.options);
      } catch (error) {}

      const linkUrl = await prepareLink(
        item?.ctaLink,
        accessToken,
        options?.ctaLinkType,
      );
      logGASelectPromotion(item, index);
      openEcomWebview(
        navigate,
        {
          title: item?.title,
          uri: linkUrl,
          canGoBack: true,
          originUri: item?.ctaLink,
          imageUrl: item?.imageLink,
          clickLocation: 'shop-product-carousel',
          origin: 'ShopProductCarousel',
          isLogSelectItem: false,
        },
        getEcomProductDetail,
      );
    }
  };

  const logGASelectPromotion = async (productItem, index) => {
    if (productItem) {
      const listProductId = [];
      const options =
        typeof productItem?.options === 'object'
          ? productItem?.options
          : JSON.parse(productItem?.options);
      if (options?.ctaLinkType === WEBVIEW_PAGE_TYPE.PDP) {
        try {
          let productId = getProductIdFromUrl(productItem?.ctaLink);
          let productDetails = await getEcomProductDetail(productId);
          if (productDetails) {
            listProductId.push(productId);
          }
        } catch (error) {}
      }
      const tileTitle = productItem?.title;

      if (!isEmpty(listProductId)) {
        const paramGA = {
          promotion_id: productItem?.id,
          promotion_name: productItem?.title,
          creative_name: 'Product Carousel Shop',
          creative_slot: 'Shop > More For You',
          location_id: 'Shop > More For You',
        };
        const rsLogGA = await GA_selectPromotionWithEcomData({
          listProductId,
          paramGA,
          clickIndex: index,
          tileTitle,
          getMultipleEcomProductDetails,
        });
        if (!rsLogGA) {
          logEventPromotion({
            products: [productItem],
            eventName: GA_EVENT_NAME.SELECT_PROMOTION,
            clickIndex: index,
            tileTitle: tileTitle,
          });
        }
      } else {
        logEventPromotion({
          products: [productItem],
          eventName: GA_EVENT_NAME.SELECT_PROMOTION,
          clickIndex: index,
          tileTitle: tileTitle,
        });
      }
    }
  };

  useEffect(() => {
    try {
      if (isVisibleToUser) {
        let listProductId = getProductIds(data);
        GA_logViewItemList(
          listProductId,
          'shop-product-carousel',
          'More For You',
          undefined,
          getMultipleEcomProductDetails,
        );
        logEventPromotion(data, GA_EVENT_NAME.VIEW_PROMOTION);
      }
    } catch (error) {}
  }, [isVisibleToUser]);

  const logEventPromotion = ({
    products,
    eventName,
    clickIndex = 0,
    tileTitle,
  }) => {
    if (products && products.length) {
      const items = [];
      products.map(val => {
        const item = {
          promotion_id: val?.id,
          promotion_name: val?.title,
          creative_name: 'Product Carousel Shop',
          creative_slot: 'Shop > More For You',
          location_id: 'Shop > More For You',
        };
        if (eventName === GA_EVENT_NAME.SELECT_PROMOTION) {
          item.index = clickIndex + 1 + '';
        }
        items.push(item);
      });
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  const getDataMoreForYou = async results => {
    if (results?.data?.length > 0) {
      setData(results.data);
    } else {
      setData([]);
    }
  };

  const renderItem = ({item, index}) => {
    let configOptions = {};
    try {
      configOptions = JSON.parse(item?.options);
    } catch (error) {
      configOptions = {};
    }
    return (
      <TouchableOpacity
        index={index}
        delayPressIn={100}
        delayPressOut={100}
        activeOpacity={0.8}
        onPress={() => onItemPress(item, index)}
        style={[
          styles.itemWrapper,
          appStyles.viewShadowLightBig,
          {backgroundColor: configOptions?.background || 'white'},
        ]}
      >
        <CustomImage style={styles.itemImage} source={{uri: item?.imageLink}} />
        <Text
          style={[
            styles.copyText,
            {color: configOptions?.titleColor || 'black'},
          ]}
          size={16}
        >
          {item?.title}
        </Text>
      </TouchableOpacity>
    );
  };
  return data && data.length > 0 ? (
    <View
      style={styles.viewContainer}
      onLayout={e => {
        onLayout?.(e.nativeEvent.layout.y, e.nativeEvent.layout.height);
      }}
    >
      <Text size={16} black style={{marginHorizontal: 16, marginBottom: 10}}>
        shop.more_for_you
      </Text>
      <FlatList
        data={data}
        horizontal
        keyExtractor={(item, index) => item?.id}
        renderItem={renderItem}
        style={{width: wp(100)}}
        contentContainerStyle={{paddingRight: 8}}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  ) : null;
};
const styles = StyleSheet.create({
  viewContainer: {
    marginBottom: 10,
  },
  itemWrapper: {
    width: wp(43),
    marginLeft: 8,
    flexDirection: 'column',
    borderRadius: 16,
    backgroundColor: 'white',
    marginBottom: 10,
    marginTop: 6,
    minHeight: 186,
    padding: 4,
  },
  itemImage: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    height: wp(41) / 1.33,
    width: wp(41),
    marginBottom: 10,
  },
  copyText: {
    fontWeight: '700',
    paddingHorizontal: 4,
  },
});

export default React.forwardRef(MoreForYou);
