import Text from 'components/Text';
import React, {useEffect} from 'react';
import {StyleSheet} from 'react-native';
import Button from 'components/Button';
import appStyles from 'styles/global';
import Icon from 'react-native-vector-icons/Entypo';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Animated, {
  Extrapolation,
  useAnimatedStyle,
  interpolate,
} from 'react-native-reanimated';
import KeyTilesItem from './KeyTilesItem';
import {t} from 'i18next';
import {GA_logViewItemList, getProductIds} from 'utils/googleAnalytics';
import {getProductIdFromUrl} from 'utils/shop';
import {GA_EVENT_NAME, WEBVIEW_PAGE_TYPE} from 'utils/constant';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';
const heightItem = wp(96);
const heightContainer = heightItem + hp(34);
const maxValueContainer = 400;
const maxValue = 400;
const topButton = wp(74);
const KeyTiles = ({
  offset,
  data,
  offsetTransItem,
  selectedIndex,
  backToTop,
  logEventPromotion,
}) => {
  const {getMultipleEcomProductDetails} = useEcomProductDetail();
  useEffect(() => {
    if (selectedIndex === 0) {
      let listProductId = getProductIds(data);
      GA_logViewItemList(
        listProductId,
        'shop-key-tiles',
        t('shop.your_taylormade_offers'),
        undefined,
        getMultipleEcomProductDetails,
      );
      logEventPromotion(data, GA_EVENT_NAME.VIEW_PROMOTION);
    }
  }, [selectedIndex]);
  const animatedContainerStyles = useAnimatedStyle(() => {
    const height = interpolate(
      offset.value,
      [0, maxValueContainer],
      [heightItem / 2, heightContainer],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );
    return {
      height,
    };
  });

  const animatedButtonStyles = useAnimatedStyle(() => {
    if (
      selectedIndex > -1 &&
      selectedIndex === data?.length - 2 &&
      offsetTransItem.value <= 0
    ) {
      const top = interpolate(
        offsetTransItem.value,
        [-maxValue, 0],
        [heightItem + topButton / 2, heightItem + topButton],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );
      return {
        top,
      };
    } else if (
      selectedIndex > -1 &&
      selectedIndex === data?.length - 1 &&
      offsetTransItem.value >= 0
    ) {
      const top = interpolate(
        offsetTransItem.value,
        [0, 100],
        [heightItem + topButton / 2, heightItem + topButton],
        {
          extrapolateRight: Extrapolation.CLAMP,
        },
      );
      return {
        top,
      };
    }
    return {top: heightItem + topButton};
  });
  return (
    <Animated.View
      style={[{paddingBottom: 50, top: 0}, animatedContainerStyles]}
    >
      <Text
        size={16}
        black
        style={{marginHorizontal: 8, marginBottom: 16, zIndex: 9}}
      >
        shop.your_taylormade_offers
      </Text>
      <Animated.View>
        {data.map((item, index) => (
          <KeyTilesItem
            key={item.title + '-' + index}
            item={item}
            index={index}
            offset={offset}
            offsetTransItem={offsetTransItem}
            selectedIndex={selectedIndex}
            size={data.length - 1}
            backToTop={backToTop}
            logEventPromotion={logEventPromotion}
          />
        ))}
        <Animated.View
          style={[
            {
              top: heightItem + topButton,
            },
            animatedButtonStyles,
          ]}
        >
          <Button
            text={t('shop.back_to_top')}
            Din79Font
            textColor="#000"
            borderColor="#fff"
            backgroundColor="#fff"
            onPress={() => backToTop(true)}
            rightIconOther={() => (
              <Icon
                name="chevron-up"
                color={'#000'}
                size={20}
                style={{marginLeft: 14}}
              />
            )}
            textStyle={[
              appStyles.textCenter,
              {fontWeight: '700', letterSpacing: 1.2, fontSize: 12},
            ]}
            styleContainTextAndIcon={{
              justifyContent: 'center',
            }}
            style={styles.btnBackToTop}
          />
        </Animated.View>
      </Animated.View>
    </Animated.View>
  );
};
const styles = StyleSheet.create({
  btnBackToTop: {
    height: 40,
    minWidth: 180,
    marginBottom: 50,
    marginHorizontal: 16,
    ...appStyles.viewShadow,
  },
});
export default KeyTiles;
