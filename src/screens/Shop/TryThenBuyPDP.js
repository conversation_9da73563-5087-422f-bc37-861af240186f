import React, {useState} from 'react';
import {View, ScrollView, Alert} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import analytics from '@react-native-firebase/analytics';

import Text from 'components/Text';
import Button from 'components/Button';
import ProductItem from 'components/ProductItem';
import ClubConfiguratorModal from 'components/ClubConfiguratorModal';

import {updateCurrentBasket} from 'reducers/basket';
import {tryThenBuy} from 'requests/ttb';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import {getCurrencySymbol} from 'utils/commonVariable';

const TryThenBuyPDP = ({route, navigation: {navigate}}) => {
  const orderId = route?.params?.orderId;
  const product = route?.params?.product;
  const charge = route?.params?.charge;
  const isEnded = route?.params?.isEnded;
  const endDate = route?.params?.endDate;
  const [loading, setLoading] = useState(false);
  const [toggleSelector, setToggleSelector] = useState(false);
  const headModel = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Model';
    },
  );
  const gender = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Gender';
    },
  );
  const handed = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Hand';
    },
  );
  const loft = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Loft';
    },
  );
  const shaftModel = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Shaft Model';
    },
  );
  const shaftVendor = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Shaft Vendor';
    },
  );
  const shaftFlex = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Shaft Flex';
    },
  );
  const tippingAdjustment =
    product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(node => {
      return node.name === 'Tipping Adjustment';
    });
  const lengthAdjustment =
    product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(node => {
      return node.name === 'Length Adjustment';
    });
  const gripModel = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Grip Model';
    },
  );
  const gripVendor = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Grip Vendor';
    },
  );
  const gripWraps = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Grip Wraps';
    },
  );
  const gripLogo = product?.cTmCustomconfiguratorNodes?.selectedNodes?.find(
    node => {
      return node.name === 'Grip Logo';
    },
  );

  const buyTTB = async () => {
    setLoading(true);
    try {
      await tryThenBuy(orderId, 'buy');
      setLoading(false);
      navigate('ShopTTB');
      showToast({
        type: 'success',
        message: t('trythenbuy.pdp.confirmed_lower_scores_incoming'),
        subText: t('trythenbuy.pdp.your_purchase_was_successful'),
      });
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: t('trythenbuy.pdp.a_payment_error_occurred'),
        subText: t('trythenbuy.pdp.submitting_payment_again'),
      });
    }

    await analytics().logEvent('ttb_cta_open', {
      name: 'Confirm Purchase',
    });
    await analytics().logEvent('cta_open', {
      name: 'Confirm Purchase',
    });
  };

  const alertBuy = async () => {
    return Alert.alert(
      t('try.then_buy.pdp.confirm_purchase'),
      t('try.then_buy.pdp.trial_will_beee_charged'),
      [
        {
          text: t('mybag.club_details.cancel'),
          style: 'cancel',
        },
        {text: t('try.then_buy.pdp.confirm'), onPress: () => buyTTB()},
      ],
    );
  };

  const getClubConfig = () => {
    return {
      name: headModel?.option?.name,
      model: product?.cTmCustomconfiguratorAtoModelName,
      headModelImageUrl:
        headModel?.option?.info?.imageUrlLarge || headModel?.option?.imageURL,
      genderCode: gender?.option?.name,
      handCode: handed?.option?.name,
      headLoft: loft?.option?.name,
      shaftVendorName: shaftVendor?.option?.name,
      shaftModelName: shaftModel?.option?.name,
      shaftFlex: shaftFlex?.option?.name,
      shaftTippingAdj: tippingAdjustment?.option?.name,
      shaftLengthAdj: lengthAdjustment?.option?.name,
      gripVendor: gripVendor?.option?.name,
      gripModel: gripModel?.option?.name,
      gripWraps: gripWraps?.option?.name,
      gripLogo: gripLogo?.option?.name,
    };
  };

  const clubConfig = getClubConfig();

  return (
    <SafeAreaView
      style={[appStyles.flex, appStyles.whiteBg]}
      edges={['bottom', 'left', 'right']}
    >
      <ClubConfiguratorModal
        toggleSelector={toggleSelector}
        setToggleSelector={setToggleSelector}
        clubConfig={clubConfig}
        model={clubConfig?.model}
        navigate={navigate}
        origin="previouslyTriedTTB"
      />
      <ScrollView
        style={[
          appStyles.flex,
          appStyles.pTMd,
          appStyles.pHSm,
          appStyles.lightGreyBg,
        ]}
        contentContainerStyle={appStyles.pBLg}
      >
        {charge ? (
          <View style={appStyles.mBMd}>
            <Text style={[appStyles.lg]} DINbold>
              {t('try.then.buy.yourney_better')}
            </Text>
            <Text style={[appStyles.mTSm, appStyles.grey]}>
              {t('try.then.buy.compare_and_track')}
            </Text>
          </View>
        ) : null}

        <ProductItem
          name={product.productName}
          price={product.price}
          productId={product.productId}
          itemId={product.itemId}
          customNodes={product.cTmCustomconfiguratorNodes}
          trialStarted
          isPDP
          isEnded={isEnded}
          endDate={endDate}
        />

        <View style={[appStyles.mBLg, appStyles.mTMd]}>
          {!charge ? (
            <View style={[appStyles.row, appStyles.pBSm]}>
              <Text>shop.cart.supporting_copy.subtotal</Text>
              <Text style={appStyles.mLAuto}>
                {getCurrencySymbol(product?.currency)}
                {product?.basePrice}
              </Text>
            </View>
          ) : null}
          <View style={[appStyles.hr]} />
          {charge ? (
            <View style={[appStyles.row, appStyles.pVSm]}>
              <Text>shop.pdp_buy.headline.price</Text>
              <Text style={appStyles.mLAuto}>
                {getCurrencySymbol(product?.currency)}
                {product?.basePrice}
              </Text>
            </View>
          ) : null}
          {charge ? (
            <Text style={[appStyles.lightGrey, appStyles.xs, appStyles.mTSm]}>
              try.then.buy.not_return_trial
            </Text>
          ) : (
            <Text style={[appStyles.xs, appStyles.grey]}>
              shop.cart.supporting_copy.shipping_and_taxes_are_calculated_at_checkout
            </Text>
          )}
        </View>
      </ScrollView>
      <View style={[appStyles.pSm]}>
        <Button
          text={
            charge
              ? 'try.then_buy_pdp.charge_me'
              : 'fitting.recommendations.specs.cta.buy_now'
          }
          textColor="white"
          backgroundColor="black"
          disabled={loading}
          loading={loading}
          onPress={
            charge
              ? async () => {
                  alertBuy();
                  await analytics().logEvent('ttb_cta_open', {
                    name: 'Charge Me',
                  });
                  await analytics().logEvent('cta_open', {
                    name: 'Charge Me',
                  });
                }
              : () => {
                  setToggleSelector(true);
                }
          }
          loadingMode="dark"
          centered
          DINbold
        />
      </View>
    </SafeAreaView>
  );
};

const mapStateToProps = state => ({
  basket: state.basket,
});

const mapDispatchToProps = {updateCurrentBasket};

export default connect(mapStateToProps, mapDispatchToProps)(TryThenBuyPDP);
