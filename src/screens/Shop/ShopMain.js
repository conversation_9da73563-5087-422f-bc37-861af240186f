import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import React, {useEffect, useRef, useState} from 'react';
import {Platform} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import DiscountItems from './components/DiscountItems';
import KeyTiles from './components/KeyTiles';
import MainKeyTile from './components/MainKeyTile';
import MoreForYou from './components/MoreForYou';
import PromotionTile from './components/PromotionTile';
import LoyaltyPointHeader from './components/ShopHeader';
import SupportCards from './components/SupportCards';
import TrackingOrders from './components/TrackingOrders';
import {
  GestureHandlerRootView,
  Gesture,
  GestureDetector,
  ScrollView,
  RefreshControl,
} from 'react-native-gesture-handler';
import {heightPercentageToDP} from 'react-native-responsive-screen';
import Animated, {
  useSharedValue,
  withTiming,
  Easing,
  runOnJS,
} from 'react-native-reanimated';
import {getProductTiles} from 'requests/shop';
import LinearGradient from 'react-native-linear-gradient';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import useAppState from 'hooks/useAppState';
import {
  GA_logEvent,
  GA_logViewItemList,
  getProductIds,
} from 'utils/googleAnalytics';
import {CACHE_KEY, GA_EVENT_NAME, WEBVIEW_PAGE_TYPE} from 'utils/constant';
import ShopCategories from './components/ShopCategories';
import {getProductIdFromUrl} from 'utils/shop';
import {updateProductTilesData} from 'reducers/dataCache';
import {cloneDeep} from 'lodash';
import {updateCacheVersionData} from 'reducers/cacheVersion';
import {getModuleCacheData} from 'requests/moduleCache';
import {useEcomProductDetail} from 'hooks/useEcomProductDetail';

let runningAnimation = false;
let disableScroll = false;
let scrollToEnded = false;
let transTo = '';
let timeEndDrag = 0;
let timeBeginDrag = 0;
const maxValue = 400;
const minValueTrans = 100;
const maxValueContainer = 400;
const duration = 300;
let runningTimeout = null;
const ShopMain = () => {
  const insets = useSafeAreaInsets();
  const tabBarheight = useBottomTabBarHeight();
  const [productTitle, setProductTitle] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [loading, setLoading] = useState(false);
  const [loadingMarquee, setLoadingMarquee] = useState(false);
  const showHideFeatures = useSelector(state => state?.app.showHideFeatures);
  const SHOW_RECENT_ORDER = showHideFeatures?.data?.SHOW_RECENT_ORDER || false;
  const isScreenFocused = useIsFocused();
  const offset = useSharedValue(0);
  const offsetTransItem = useSharedValue(0);

  const panGestureRef = React.useRef(Gesture.Pan());
  const scrollRef = React.useRef();
  const pointYRef = React.useRef(0);
  const mainKeyRef = React.useRef(null);
  const discountItemRef = React.useRef(null);
  const moreForYouRef = React.useRef(null);
  const supportCardRef = React.useRef(null);
  const promotionTileRef = React.useRef(null);
  const shopCategoryRef = React.useRef(null);
  const isScrollFlatlist = React.useRef(false);
  const refIsFocus = React.useRef(false);

  const [enablePan, setEnablePan] = useState(false);
  const [focusScreen, setFocusScreen] = useState(true);

  const [trackingOrderHeight, setTrackingOrderHeight] = useState(0);
  const user = useSelector(state => state.user);
  const userCountry = user?.userCountry;
  const trackingOrderRef = React.useRef(null);
  const [moreForYouYPosition, setMoreForYouYPosition] = useState(0);
  const [moreForYouHeight, setMoreForYouHeight] = useState(0);
  const [isMoreForYouVisible, setMoreForYouVisible] = useState(false);
  const [discountItemYPosition, setDiscountItemYPosition] = useState(0);
  const [discountItemHeight, setDiscountItemHeight] = useState(0);
  const [isDiscountItemVisible, setDiscountItemVisible] = useState(false);
  const appCacheVersions = useSelector(state => state.appCacheVersions);
  const productTilesCache = useSelector(state => state.dataCache?.productTiles);
  const currentProductTilesCacheVersion = appCacheVersions?.features?.find?.(
    item => item.key === CACHE_KEY.SHOP_MARQUEE,
  )?.version;
  const dispatch = useDispatch();
  const {getMultipleEcomProductDetails} = useEcomProductDetail();
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (appCacheVersions) {
      refreshProductTilesDataIfNeeded();
    }
  }, [appCacheVersions]);

  useAppState({
    onForeground: () => {
      if (refIsFocus.current) {
        reloadData();
        mainKeyRef?.current?.playVideo();
        promotionTileRef?.current?.playVideo();
      }
    },
    onBackground: () => {
      mainKeyRef?.current?.pauseVideo();
      promotionTileRef?.current?.pauseVideo();
    },
  });
  useFocusEffect(
    React.useCallback(() => {
      reloadData();
      refIsFocus.current = true;
      mainKeyRef?.current?.playVideo();
      promotionTileRef?.current?.playVideo();
      setFocusScreen(true);
      return () => {
        setFocusScreen(false);
        refIsFocus.current = false;
        mainKeyRef?.current?.pauseVideo();
        promotionTileRef?.current?.pauseVideo();
        setDiscountItemVisible(false);
        setMoreForYouVisible(false);
      };
    }, []),
  );

  useEffect(() => {
    if (productTitle && productTitle.length) {
      logEventPromotion({
        products: productTitle,
        eventName: GA_EVENT_NAME.VIEW_PROMOTION,
      });
      let listProductId = getProductIds([productTitle[0]]);
      if (listProductId.length > 0) {
        GA_logViewItemList(
          listProductId,
          'shop-main-marquee-tile',
          undefined,
          undefined,
          getMultipleEcomProductDetails,
        );
      }
    }
  }, [productTitle]);

  const productTitleList = productTitle.slice(1, 6);
  useEffect(() => {
    if (productTilesCache) {
      if (productTilesCache?.data?.length > 0) {
        setProductTitle(cloneDeep(productTilesCache?.data));
      } else {
        setProductTitle([]);
      }
      isFirstRender.current = false;
    }
  }, [productTilesCache]);

  const refreshProductTilesDataIfNeeded = async () => {
    try {
      if (
        currentProductTilesCacheVersion === productTilesCache?.version &&
        appCacheVersions?.country === productTilesCache?.country &&
        currentProductTilesCacheVersion != null
      ) {
        if (isScreenFocused && !isFirstRender.current) {
          dispatch(
            updateProductTilesData({
              country: userCountry,
              version: currentProductTilesCacheVersion,
              data: productTilesCache?.data,
            }),
          );
        }
      } else {
        setLoadingMarquee(true);
        const params = {
          page: 1,
          take: 30,
          country: userCountry,
        };
        const response = await getProductTiles(params);
        dispatch(
          updateProductTilesData({
            country: userCountry,
            version: currentProductTilesCacheVersion,
            data: response?.widgets || [],
          }),
        );
      }
    } catch (error) {
    } finally {
      setLoadingMarquee(false);
    }
  };

  const logEventPromotion = ({
    products,
    eventName,
    clickIndex = 0,
    tileTitle,
  }) => {
    if (products && products.length) {
      const items = [];
      products.map(val => {
        const item = {
          promotion_id: val?.id,
          promotion_name: val?.title,
          creative_name: `${val?.ctaText} CTA`,
          creative_slot: 'Shop > Marquee',
          location_id: 'Shop > Marquee',
        };
        if (eventName === GA_EVENT_NAME.SELECT_PROMOTION) {
          item.index = clickIndex + 1 + '';
        }
        items.push(item);
      });
      GA_logEvent(eventName, {
        currency: 'USD',
        tile_title: tileTitle,
        items,
      });
    }
  };

  const reloadData = isRefreshing => {
    try {
      if (isRefreshing) {
        callApiCheckVersion();
      }
      trackingOrderRef?.current?.refreshData?.();
    } catch (error) {
      console.log(error.message);
    }
  };

  const callApiCheckVersion = async () => {
    try {
      setLoading(true);
      const rs = await getModuleCacheData(user.userCountry);
      dispatch(updateCacheVersionData(rs));
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const onScroll = e => {
    if (focusScreen) {
      const infoLayout = e.nativeEvent;
      let moreForYouValidPosition =
        Math.floor(infoLayout.contentOffset.y) -
        (moreForYouYPosition - heightPercentageToDP(60));
      let discountItemValidPosition =
        Math.floor(infoLayout.contentOffset.y) -
        (discountItemYPosition - heightPercentageToDP(60));
      if (
        moreForYouValidPosition > 0 &&
        moreForYouValidPosition < heightPercentageToDP(60)
      ) {
        if (!isMoreForYouVisible) {
          setMoreForYouVisible(true);
        }
      }
      if (
        discountItemValidPosition > 0 &&
        discountItemValidPosition < heightPercentageToDP(60)
      ) {
        if (!isDiscountItemVisible) {
          setDiscountItemVisible(true);
        }
      }
      if (Platform.OS === 'android') {
        if (e.nativeEvent.contentOffset.y === 0) {
          setEnablePan(false);
        } else if (!enablePan) {
          setEnablePan(true);
        }
      }
      if (
        !runningAnimation &&
        !disableScroll &&
        productTitleList?.length > 0 &&
        timeBeginDrag - timeEndDrag < 500
      ) {
        scrollToEnded = false;
        //the scroll view's scrollable height
        const scrollableHeight = Math.floor(
          infoLayout.contentSize.height - infoLayout.layoutMeasurement.height,
        );
        const currentScrollPosition = Math.floor(infoLayout.contentOffset.y);
        //In case you have reached the bottom and pulled down too far to prepare to see the all Marquees below
        if (currentScrollPosition >= scrollableHeight) {
          pointYRef.current = scrollableHeight;
        } else if (pointYRef.current > 0) {
          pointYRef.current = 0;
          setSelectedIndex(-1);
          offset.value = withTiming(0, {
            duration: duration,
          });
        }
      }
    }
  };

  const onScrollBeginDrag = () => {
    timeBeginDrag = new Date().getTime();
  };

  const onScrollEndDrag = e => {
    timeEndDrag = new Date().getTime();
  };

  const onScrollToTop = () => {
    backToTop(true);
  };

  const scrollToEndScroll = () => {
    if (scrollRef.current) {
      scrollToEnded = true;
      scrollRef.current.scrollToEnd();
    }
  };

  const onMomentumScrollEnd = e => {
    const infoLayout = e.nativeEvent;
    const heightView = Math.round(
      infoLayout.contentSize.height - infoLayout.layoutMeasurement.height,
    );
    if (
      (scrollToEnded || (offset.value === maxValue && !scrollToEnded)) &&
      heightView >= Math.round(infoLayout.contentOffset.y)
    ) {
      scrollToEnded = false;
      scrollRef.current.scrollToEnd({animated: false});
    }
  };

  const selectedItem = index => {
    setSelectedIndex(index);
    offsetTransItem.value = 0;
  };

  const backToTop = toTop => {
    setTimeout(() => {
      if (scrollRef?.current && toTop) {
        scrollRef.current.scrollTo({y: 0});
      }
    }, 100);
    setSelectedIndex(-1);
    scrollToEnded = false;
    pointYRef.current = 0;
    disableScroll = false;
    offset.value = 0;
  };

  const panGesture = Gesture.Pan()
    .runOnJS(true)
    .enabled(
      Platform.OS === 'android' ? (focusScreen ? enablePan : false) : true,
    )
    .onChange(e => {
      if (!runningTimeout && !isScrollFlatlist.current) {
        let transValue = 0;
        //Check user TransX or TransY
        if (
          (transTo === '' && e.changeY === 0 && e.changeX !== 0) ||
          (transTo === '' &&
            e.changeY === 0 &&
            e.changeX === 0 &&
            Math.abs(e.velocityX) > Math.abs(e.velocityY))
        ) {
          transTo = 'x';
        } else if (transTo === '') {
          transTo = 'y';
        }
        if (transTo === 'y') {
          transValue = e.translationY;
        } else {
          transValue = e.translationX;
        }
        if (
          offset.value === maxValueContainer &&
          pointYRef.current > 0 &&
          transValue <= 0 &&
          !runningAnimation
        ) {
          //action push
          if (selectedIndex < productTitleList.length - 1) {
            if (transValue <= -minValueTrans) {
              runningAnimation = true;
              offsetTransItem.value = withTiming(
                -maxValue,
                {
                  duration: duration,
                },
                () => {
                  runOnJS(selectedItem)(selectedIndex + 1);
                },
              );
            } else {
              offsetTransItem.value = transValue;
            }
          }
        } else if (
          offset.value === maxValueContainer &&
          pointYRef.current > 0 &&
          transValue >= 0 &&
          selectedIndex > 0 &&
          !runningAnimation
        ) {
          //action pull
          if (transValue >= minValueTrans) {
            runningAnimation = true;
            offsetTransItem.value = withTiming(
              maxValue,
              {
                duration: duration,
              },
              () => {
                runOnJS(selectedItem)(selectedIndex - 1);
              },
            );
          } else {
            offsetTransItem.value = transValue;
          }
        } else if (
          pointYRef.current > 0 &&
          !runningAnimation &&
          e.translationY < 0 &&
          e.translationY >= -maxValueContainer
        ) {
          disableScroll = true;
          //action push to show bottom swipe view
          if (e.translationY >= -maxValueContainer / 2) {
            runningAnimation = true;
            runOnJS(setSelectedIndex)(0);
            offset.value = withTiming(maxValueContainer - 1, {
              duration: duration,
            });
          }
        } else if (
          pointYRef.current > 0 &&
          offset.value <= maxValueContainer &&
          offset.value > 0 &&
          !runningAnimation &&
          e.translationY > 0
        ) {
          disableScroll = false;
          if (e.translationY >= maxValueContainer / 2) {
            //action pull to hide bottom swipe view
            runningAnimation = true;
            runOnJS(setSelectedIndex)(-1);
            offset.value = withTiming(1, {
              duration: duration,
            });
          } else {
            offset.value = maxValueContainer - e.translationY;
          }
        }
      }
    })
    .onFinalize(e => {
      if (!runningTimeout && !isScrollFlatlist.current) {
        if (
          pointYRef.current > 0 &&
          e.translationY < -20 &&
          offsetTransItem.value === 0 &&
          selectedIndex <= 0
        ) {
          // onFinalize push
          runningAnimation = true;
          //when swipe top to OPEN a card completed
          runOnJS(setSelectedIndex)(0);
          offset.value = withTiming(
            maxValueContainer,
            {
              duration: duration,
            },
            () => {
              runOnJS(scrollToEndScroll)();
            },
          );
        } else if (
          pointYRef.current > 0 &&
          e.translationY > 0 &&
          offsetTransItem.value === 0 &&
          selectedIndex === 0
        ) {
          runningAnimation = true;
          disableScroll = false;
          // onFinalize pull
          //when swipe bottom to CLOSE a card completed
          runOnJS(setSelectedIndex)(-1);
          offset.value = withTiming(0, {
            duration: duration,
          });
        } else {
          const transValueItem =
            transTo === 'y'
              ? e.translationY > -minValueTrans &&
                e.translationY < minValueTrans
              : e.translationX > -minValueTrans &&
                e.translationX < minValueTrans;
          if (
            offset.value === maxValueContainer &&
            pointYRef.current > 0 &&
            transValueItem
          ) {
            //reset view
            offsetTransItem.value = withTiming(0, {
              duration: duration,
            });
          }
        }

        runOnJS(enableRunningAnimation)();

        transTo = '';
      }
    })
    .withRef(panGestureRef);

  const enableRunningAnimation = () => {
    runningTimeout = setTimeout(() => {
      runningAnimation = false;
      runningTimeout = null;
    }, 200);
  };
  return (
    <GestureHandlerRootView style={{flex: 1, backgroundColor: '#fff'}}>
      <FocusAwareStatusBar barStyle={'dark-content'} />
      <LinearGradient
        start={{x: 1, y: 0.5}}
        end={{x: 1, y: 4}}
        colors={['#ffffff', 'rgba(0, 0, 0, 1)']}
        style={{
          flex: 1,
        }}
      >
        <ScrollView
          ref={scrollRef}
          style={{flex: 1}}
          contentContainerStyle={{
            paddingTop: insets.top + (Platform.OS === 'ios' ? 5 : 15),
            paddingBottom: tabBarheight + 30,
          }}
          simultaneousHandlers={
            selectedIndex === -1 ? panGestureRef : undefined
          }
          onScroll={onScroll}
          scrollEventThrottle={16}
          onScrollBeginDrag={onScrollBeginDrag}
          onScrollEndDrag={onScrollEndDrag}
          onMomentumScrollEnd={onMomentumScrollEnd}
          onScrollToTop={onScrollToTop}
          scrollEnabled={focusScreen}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={() => reloadData(true)}
              tintColor={'black'}
              progressViewOffset={Platform.OS === 'ios' ? 30 : undefined}
            />
          }
        >
          <GestureDetector gesture={panGesture}>
            <Animated.View>
              <LoyaltyPointHeader colorPTS={'rgba(0, 0, 0, 0.4)'} />
              {SHOW_RECENT_ORDER ? (
                <TrackingOrders
                  onLayoutFunction={event => {
                    let {width, height} = event.nativeEvent.layout;
                    setTrackingOrderHeight(height);
                  }}
                  ref={trackingOrderRef}
                />
              ) : null}

              <MainKeyTile
                data={productTitle?.[0]}
                loading={loadingMarquee}
                ref={mainKeyRef}
                logEventPromotion={logEventPromotion}
              />
              <PromotionTile ref={promotionTileRef} />
              <MoreForYou
                ref={moreForYouRef}
                onLayout={(y, height) => {
                  setMoreForYouYPosition(y);
                  setMoreForYouHeight(height);
                }}
                isVisibleToUser={isMoreForYouVisible}
              />
              <ShopCategories
                ref={shopCategoryRef}
                isScrollFlatlist={isScrollFlatlist}
              />
              <SupportCards ref={supportCardRef} />
              <DiscountItems
                ref={discountItemRef}
                isScrollFlatlist={isScrollFlatlist}
                onLayout={(y, height) => {
                  setDiscountItemYPosition(y);
                  setDiscountItemHeight(height);
                }}
                isVisibleToUser={isDiscountItemVisible}
              />
              {productTitleList?.length > 0 && (
                <KeyTiles
                  offset={offset}
                  data={productTitleList}
                  offsetTransItem={offsetTransItem}
                  selectedIndex={selectedIndex}
                  backToTop={backToTop}
                  logEventPromotion={logEventPromotion}
                />
              )}
            </Animated.View>
          </GestureDetector>
        </ScrollView>
      </LinearGradient>
    </GestureHandlerRootView>
  );
};

export default ShopMain;
