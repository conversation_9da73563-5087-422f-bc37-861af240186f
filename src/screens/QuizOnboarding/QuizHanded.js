import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import {getStartDeeperInsightScreen} from 'utils/commonVariable';
import BackButton from 'components/BackButton';

const QuizHanded = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;

  const [handed, setHanded] = useState(
    user.golferProfile?.handed || quiz.handed || '',
  );
  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete && isEdit) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() =>
              route.params?.origin === 'home'
                ? navigation.navigate('App')
                : navigation.navigate('Profile')
            }
          />
        ),
        headerLeft: () => <View />,
      });
    }

    if (getStartDeeperInsightScreen() === 'StartDeeperDrill') {
      navigation.setOptions({
        headerLeft: () => (
          <BackButton onPress={() => navigation.navigate('App')} />
        ),
      });
    }
  }, []);

  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(handed || '');

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return handed === type ? 'white' : GREY;
      case 'border':
        return handed === type ? GREEN : GREY;
      case 'background':
        return handed === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const updateUserHanded = async () => {
    // Update quiz in redux
    updateQuiz({handed});
    // Update user in backend if this is an edit
    if (isEdit) {
      setLoading(true);

      try {
        // Make request to update user's handed
        const updatedUser = await updateUser({
          handed,
        });
        // Update user in redux
        addCurrentUser(updatedUser);
        // Stop loading state and navigate to next screen
        setLoading(false);
      } catch (error) {
        setLoading(false);
        return showToast({
          type: 'error',
          message: t('An_error_occurred_updating_your_swing_orientation'),
        });
      }
    }

    if (route.params?.origin === 'settings') {
      navigation.navigate('Settings');
    } else {
      navigation.navigate('QuizDriveLength');
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              quiz.swing_orientation.headline
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.swing_orientation.supporting_copy.right"
              textColor={getButtonActiveColor('right', 'text')}
              borderColor={getButtonActiveColor('right', 'border')}
              backgroundColor={getButtonActiveColor('right', 'background')}
              onPress={() => setHanded('right')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.swing_orientation.supporting_copy.left"
              textColor={getButtonActiveColor('left', 'text')}
              borderColor={getButtonActiveColor('left', 'border')}
              backgroundColor={getButtonActiveColor('left', 'background')}
              onPress={() => setHanded('left')}
              disabled={loading}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text={
              route.params?.origin === 'settings'
                ? 'common.update'
                : 'common.next'
            }
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={updateUserHanded}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizHanded);
