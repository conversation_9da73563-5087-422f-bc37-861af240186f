import React, {useState, useEffect, useRef} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import Selector from 'components/Selector';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';

import appStyles from 'styles/global';
import {GREY} from 'config';
import BackButton from 'components/BackButton';

let ignoreOnChange = true;

const EditQuizHandicap = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const [handicap, setHandicap] = useState(
    fromGameProfile
      ? route.params?.handicap
      : user?.golferProfile?.newHandicap?.tmCalculatedHandicap ||
          user?.golferProfile?.newHandicap?.userInputHandicap ||
          quiz.handicap ||
          'None',
  );
  const [loading, setLoading] = useState(false);
  const sheetRef = useRef(null);

  const text = !isNaN(handicap)
    ? `${handicap < 0 ? `+${-handicap}` : handicap}`
    : 'Select';

  // selector will call onChange when init (library error), so we need to ignore first call by firstSecond value
  useEffect(() => {
    setTimeout(() => {
      ignoreOnChange = false;
    }, 1000);
    return () => {
      ignoreOnChange = true;
    };
  }, []);

  const onSelectorChange = value => {
    if (!ignoreOnChange) {
      setHandicap(value);
    }
  };

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
    });
  }, []);

  const updateUserHandicap = async () => {
    if (fromGameProfile) {
      route.params?.setHandicap(handicap);
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({handicap});

      navigation.navigate('QuizScoreTarget', {
        origin:
          route.params?.origin === 'home'
            ? 'home'
            : route.params?.origin
            ? 'profile'
            : null,
        isEdit,
      });
    }
  };

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                quiz.current.handicap
              </Text>
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                textColor={handicap ? 'white' : GREY}
                borderColor={GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                text={text}
                rightIcon="chevron-down"
                disabled={loading}
              />
            </Animatable.View>
          </View>

          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              text={fromGameProfile ? 'common.update' : 'common.next'}
              backgroundColor="white"
              disabled={loading}
              onPress={updateUserHandicap}
              loading={loading}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="handicap"
        value={!isNaN(handicap) ? handicap : 16}
        onChange={onSelectorChange}
        onCloseEnd={() => setHandicap(!isNaN(handicap) ? handicap : 16)}
      />
    </>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(EditQuizHandicap);
