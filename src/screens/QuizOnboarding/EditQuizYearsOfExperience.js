import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';
import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import BackButton from 'components/BackButton';

const EditQuizYearsOfExperience = ({navigation, route}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const fromGameProfile = route.params?.origin === 'Setting';
  const timePlayingGolfProfile = route.params?.timePlayingGolf;
  const [yearsOfExperience, setYearsOfExperience] = useState(
    fromGameProfile
      ? timePlayingGolfProfile || ''
      : user.golferProfile?.timePlayingGolf?.value ||
          quiz.yearsOfExperience ||
          '',
  );
  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(yearsOfExperience || '');

  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete && !fromGameProfile) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() => navigation.goBack()}
          />
        ),
      });
    }
    if (fromGameProfile) {
      navigation.setOptions({
        headerLeft: () => (
          <BackButton color="white" onPress={() => navigation.goBack()} />
        ),
      });
    }
  }, []);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return yearsOfExperience === type ? 'white' : GREY;
      case 'border':
        return yearsOfExperience === type ? GREEN : GREY;
      case 'background':
        return yearsOfExperience === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const updateUserYearsOfExperience = async () => {
    if (fromGameProfile) {
      route.params?.setTimePlayingGolf(yearsOfExperience?.replace('Years', ''));
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              quiz.freq_years.headline
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.freq_years.supporting_copy.0_1_years"
              textColor={getButtonActiveColor('0 - 1 Years', 'text')}
              borderColor={getButtonActiveColor('0 - 1 Years', 'border')}
              backgroundColor={getButtonActiveColor(
                '0 - 1 Years',
                'background',
              )}
              onPress={() => setYearsOfExperience('0 - 1 Years')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.freq_years.supporting_copy.2_3_years"
              textColor={getButtonActiveColor('2 - 3 Years', 'text')}
              borderColor={getButtonActiveColor('2 - 3 Years', 'border')}
              backgroundColor={getButtonActiveColor(
                '2 - 3 Years',
                'background',
              )}
              onPress={() => setYearsOfExperience('2 - 3 Years')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.freq_years.supporting_copy.4_5_years"
              textColor={getButtonActiveColor('4 - 5 Years', 'text')}
              borderColor={getButtonActiveColor('4 - 5 Years', 'border')}
              backgroundColor={getButtonActiveColor(
                '4 - 5 Years',
                'background',
              )}
              onPress={() => setYearsOfExperience('4 - 5 Years')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.freq_years.supporting_copy.6_7_years"
              textColor={getButtonActiveColor('6 - 7 Years', 'text')}
              borderColor={getButtonActiveColor('6 - 7 Years', 'border')}
              backgroundColor={getButtonActiveColor(
                '6 - 7 Years',
                'background',
              )}
              onPress={() => setYearsOfExperience('6 - 7 Years')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={500}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.freq_years.supporting_copy.8_9_years"
              textColor={getButtonActiveColor('8 - 9 Years', 'text')}
              borderColor={getButtonActiveColor('8 - 9 Years', 'border')}
              backgroundColor={getButtonActiveColor(
                '8 - 9 Years',
                'background',
              )}
              onPress={() => setYearsOfExperience('8 - 9 Years')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={600}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.freq_years.supporting_copy.10_years"
              textColor={getButtonActiveColor('10+ Years', 'text')}
              borderColor={getButtonActiveColor('10+ Years', 'border')}
              backgroundColor={getButtonActiveColor('10+ Years', 'background')}
              onPress={() => setYearsOfExperience('10+ Years')}
              disabled={loading}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={700}>
          <Button
            text={fromGameProfile ? 'common.update' : 'common.next'}
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={updateUserYearsOfExperience}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {};

export default connect(null, mapDispatchToProps)(EditQuizYearsOfExperience);
