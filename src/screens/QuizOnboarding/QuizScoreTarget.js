import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Slider from 'components/Slider';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {addCurrentUser} from 'reducers/user';
import {updateUser} from 'requests/accounts';
import {updateQuiz} from 'reducers/quiz';
import {getHomeStats} from 'requests/content';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import BackButton from 'components/BackButton';
import {
  transformHandicapToScore,
  transformRangeAverageToScore,
} from 'utils/user';

const QuizScoreTarget = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );

  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const [targetScore, setTargetScore] = useState(
    fromGameProfile
      ? route.params?.targetScore
      : user.golferProfile?.targetScore || quiz.targetScore || 90,
  );
  const [loading, setLoading] = useState(false);
  const [averageData, setAverageData] = useState(null);
  const [handicapData, setHandicapData] = useState(null);

  const calculateAvgValue = () => {
    const handicapStr = handicapData?.extraData?.handicapIndex;
    const averageScoreRange = user?.golferProfile?.averageScoreRange;
    const valueAverage = averageData?.title;
    let result = 0;
    if (valueAverage) {
      result = parseFloat(averageData?.title);
    } else if (averageScoreRange) {
      result = transformRangeAverageToScore(averageScoreRange);
    } else if (handicapStr) {
      result = transformHandicapToScore(parseFloat(handicapStr));
    }
    if (result !== 0) {
      return Math.round(result);
    }
    return result;
  };

  const getDataAverage = async () => {
    try {
      if (user) {
        setLoading(true);
        const dataHome = await getHomeStats();
        if (dataHome?.length > 0) {
          const dataAvg = dataHome.find(val => val.type === 'AVERAGE_SCORE');
          const dataHandicap = dataHome.find(
            val => val.type === 'HANDICAP_INDEX',
          );
          setAverageData(dataAvg);
          setHandicapData(dataHandicap);
        } else {
          setAverageData(null);
          setHandicapData(null);
        }
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
    }
  };

  useEffect(() => {
    getDataAverage();
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete && !fromGameProfile) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() =>
              route.params?.origin === 'home' ||
              route.params?.origin === 'homeTiles'
                ? navigation.navigate('Home', {
                    screen: 'Home',
                    params: {origin: 'QuizScoreTarget'},
                  })
                : navigation.navigate('Profile')
            }
          />
        ),
      });
    }

    if (
      route.params?.origin === 'homeTiles' ||
      route.params?.origin === 'Setting'
    ) {
      navigation.setOptions({
        headerLeft: () => null,
      });
    }
    if (route.params?.origin === 'Setting') {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const updateUserTargetScore = async () => {
    if (fromGameProfile) {
      route.params?.setTargetScore(targetScore);
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({targetScore});
      // Update user in backend if this is an edit
      if (isEdit) {
        try {
          setLoading(true);
          // Make request to update user's target score
          const updatedUser = await updateUser({
            targetScore,
            onboardingCompleteSteps: {
              targetScoreComplete: true,
            },
          });
          // Update user in redux
          addCurrentUser(updatedUser);
        } catch (error) {
          setLoading(false);
          return showToast({
            type: 'error',
            message: t('An_error_occurred_updating_your_target_score'),
          });
        }
      }

      if (route.params?.origin === 'homeTiles') {
        // await updateTiles();
        navigation.navigate('Home', {
          screen: 'Home',
          params: {origin: 'QuizScoreTarget'},
        });
      } else {
        navigation.navigate('QuizDriveLength', {
          origin:
            route.params?.origin === 'home'
              ? 'home'
              : route.params?.origin
              ? 'profile'
              : null,
          isEdit,
        });
      }

      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              quiz.score_target.headline
            </Text>
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={100}>
            <Slider
              min={60}
              max={100}
              minimumTrackTintColor="#ffffff"
              maximumTrackTintColor="#ffffff80"
              value={targetScore}
              onValueChange={value => setTargetScore(Math.round(value))}
              disabled={loading}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={200}>
          <Button
            text={
              route.params?.origin === 'homeTiles' ||
              route.params?.origin === 'Setting'
                ? 'common.update'
                : 'common.next'
            }
            backgroundColor={
              calculateAvgValue() > 0 && targetScore >= calculateAvgValue()
                ? 'grey'
                : 'white'
            }
            disabled={
              loading ||
              (calculateAvgValue() > 0 && targetScore >= calculateAvgValue())
            }
            onPress={updateUserTargetScore}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizScoreTarget);
