import React, {useState, useRef, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {getCountries, updateUser} from 'requests/accounts';
import appStyles from 'styles/global';
import {GREY} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import Selector from 'components/Selector';
import analytics from '@react-native-firebase/analytics';
import {COUNTRY_CODE, SCREEN_TYPES} from 'utils/constant';
import {setCountry} from 'utils/commonVariable';

const QuizRegion = ({addCurrentUser, navigation, updateQuiz}) => {
  const [selectedCountry, setSelectedCountry] = useState('');
  const [options, setOptions] = useState([]);

  const [loading, setLoading] = useState(false);
  const sheetRef = useRef(null);
  const refCountries = useRef([]);

  useEffect(() => {
    getDataCountry();
  }, []);

  const getDataCountry = async () => {
    setLoading(true);
    try {
      const countries = await getCountries();
      if (countries) {
        refCountries.current = countries;
        const listName = countries.map(_item => _item.name);
        const usaIndex = countries.findIndex(
          item => item.code === COUNTRY_CODE.USA,
        );
        if (usaIndex > -1) {
          setSelectedCountry(listName[usaIndex]);
        } else {
          setSelectedCountry(listName[0]);
        }
        setOptions(listName);
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: error?.response?.data?.message,
      });
    } finally {
      setLoading(false);
    }
  };

  const GA_logOnboardingCountry = () => {
    try {
      analytics().logEvent('user_onboarding_country', {
        screen_name: `onboarding - country`,
        screen_type: SCREEN_TYPES.ONBOARDING,
        page_name: `onboarding - country`,
        page_type: SCREEN_TYPES.ONBOARDING,
        page_category: SCREEN_TYPES.ONBOARDING,
        user_country: selectedCountry,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const onNextScreen = async () => {
    // Update quiz in redux
    updateQuiz({country: true});
    setLoading(true);
    try {
      const selectedItem = refCountries.current.find(
        _item => _item.name === selectedCountry,
      );
      // Make request to update user's gender
      const updatedUser = await updateUser({
        userCountry: selectedItem.code,
      });
      setCountry(selectedItem.code);

      // Update user in redux
      addCurrentUser(updatedUser);
      GA_logOnboardingCountry();
      navigation.navigate('QuizName');
      // Stop loading state and navigate to next screen
    } catch (error) {
      showToast({
        type: 'error',
        message: t('An_error_occurred_updating_your_country'),
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTMd, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                quiz.region.please_select_your_country
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                textColor={'white'}
                borderColor={GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                text={selectedCountry}
                rightIcon="chevron-down"
                disabled={loading}
              />
            </Animatable.View>
          </View>

          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              text="quiz.cta.next"
              backgroundColor={selectedCountry ? '#fff' : GREY}
              onPress={onNextScreen}
              disabled={!selectedCountry}
              loading={loading}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      {options && (
        <Selector
          ref={sheetRef}
          type="text"
          value={selectedCountry}
          onChange={setSelectedCountry}
          options={options}
        />
      )}
    </>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizRegion);
