import React, {useState, useEffect, useRef} from 'react';
import {View, ScrollView, StyleSheet, Alert} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import analytics from '@react-native-firebase/analytics';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import Selector from 'components/Selector';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {handicapImprovement, averageImprovement} from 'requests/swing-index';
import {isEmpty} from 'validator';
import {
  convertAverageScoreToHandicap,
  convertCurrentAverageScoreToValue,
  convertTargetAverageScore,
  listOptionScores,
  transformHandicapToAverageScore,
} from 'utils/plans';
import {t} from 'i18next';
import {NOT_APPLICABLE, SCREEN_TYPES} from 'utils/constant';

let ignoreOnChange = true;

const QuizTargetHandicapOrAvg = ({navigation, route}) => {
  const planStarted = route?.params?.planStarted;
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  let currentScore =
    user.golferProfile?.averageScoreRange || quiz.averageScore || null;
  let currentHandicap =
    user?.golferProfile?.newHandicap?.tmCalculatedHandicap ||
    user?.golferProfile?.newHandicap?.userInputHandicap;
  if (
    !currentHandicap &&
    currentHandicap !== 0 &&
    currentScore &&
    currentScore?.length > 0
  ) {
    currentHandicap = convertAverageScoreToHandicap(currentScore);
  }

  const siUserProfile = useSelector(state => state.plans.siUserProfile);
  const initHandicap =
    siUserProfile?.desiredHandicap === null ||
    siUserProfile?.desiredHandicap === undefined
      ? 'None'
      : siUserProfile?.desiredHandicap;
  const [desiredHandicap, setDesiredHandicap] = useState(initHandicap);
  const [loading, setLoading] = useState(false);
  const sheetRef = useRef(null);
  const [isCurrentHandicap, setIsCurrentHandicap] = useState(true);
  const [desiredScore, setDesiredScore] = useState('');

  // selector will call onChange when init (library error), so we need to ignore first call by firstSecond value
  useEffect(() => {
    setTimeout(() => {
      ignoreOnChange = false;
    }, 1000);
    return () => {
      ignoreOnChange = true;
    };
  }, []);

  // hard-code data for mtm+
  // useEffect(() => {
  //   if (planStarted) {
  //     hardcodeInitializedData();
  //   }
  // }, []);

  const selectedAverageScore = !isEmpty(desiredScore || '');

  const handleSwitchTab = () => {
    setIsCurrentHandicap(!isCurrentHandicap);
  };

  const renderTabHeader = () => {
    return (
      <View
        style={[
          appStyles.flex,
          appStyles.row,
          appStyles.hCenter,
          appStyles.vCenter,
          {
            alignContent: 'center',
          },
        ]}
      >
        <Button
          style={styles.headerItem}
          text={t('coach.target_handicap')}
          textColor={'white'}
          borderColor={isCurrentHandicap ? GREEN : 'transparent'}
          backgroundColor={isCurrentHandicap ? GREEN : 'transparent'}
          disabled={loading}
          onPress={handleSwitchTab}
          loading={loading}
          centered
        />
        <Button
          style={styles.headerItem}
          text={t('coach_target_score')}
          textColor={'white'}
          borderColor={!isCurrentHandicap ? GREEN : 'transparent'}
          backgroundColor={!isCurrentHandicap ? GREEN : 'transparent'}
          disabled={loading}
          onPress={handleSwitchTab}
          loading={loading}
          centered
        />
      </View>
    );
  };

  const renderCurrentHandicap = () => {
    let text = 'overview.select';
    if (!isNaN(desiredHandicap)) {
      text = `${
        desiredHandicap < 0 ? `+${-desiredHandicap}` : desiredHandicap
      }`;
    }
    return (
      <Animatable.View animation="fadeInUp" delay={200}>
        <Button
          textColor={!isNaN(desiredHandicap) ? 'white' : GREY}
          borderColor={GREY}
          onPress={() => sheetRef.current?.snapTo(0)}
          text={text}
          rightIcon="chevron-down"
          disabled={loading}
        />
      </Animatable.View>
    );
  };

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return desiredScore === type ? 'white' : GREY;
      case 'border':
        return desiredScore === type ? GREEN : GREY;
      case 'background':
        return desiredScore === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const renderAverageScore = () => {
    const indexOfMaxOption = listOptionScores.indexOf(currentScore);
    const arrayOptions = listOptionScores.slice(0, indexOfMaxOption);
    return (
      <ScrollView showsVerticalScrollIndicator={false}>
        {arrayOptions.map((value, index) => {
          return (
            <Animatable.View animation="fadeInUp" delay={100 * (index + 1)}>
              <Button
                style={[appStyles.mBSm]}
                text={value}
                textColor={getButtonActiveColor(value, 'text')}
                borderColor={getButtonActiveColor(value, 'border')}
                backgroundColor={getButtonActiveColor(value, 'background')}
                onPress={() => setDesiredScore(value)}
                disabled={loading}
              />
            </Animatable.View>
          );
        })}
      </ScrollView>
    );
  };

  const GA_Onboarding_goals = async (targetHandicap, targetScore) => {
    try {
      await analytics().logEvent('coaching_onboarding_goal', {
        target_handicap:
          targetHandicap > 39.9
            ? 39.9
            : targetHandicap === 'None'
            ? NOT_APPLICABLE
            : targetHandicap,
        target_score: targetScore || NOT_APPLICABLE,
        app_screen_name: 'target handicap/average score', //e.g home, my orders
        screen_type: SCREEN_TYPES.ONBOARDING, //e.g checkout, pdp, plp, account
        page_name: 'target handicap/average score', //e.g home, my orders
        page_type: SCREEN_TYPES.ONBOARDING, //e.g basket, home, order
        page_category: SCREEN_TYPES.ONBOARDING,
      });
    } catch (error) {
      console.log('error', error);
    }
  };

  const updateTargetHandicap = async () => {
    try {
      let paramCurrentHcp = currentHandicap;
      paramCurrentHcp = paramCurrentHcp > 40 ? 40 : paramCurrentHcp;
      paramCurrentHcp = paramCurrentHcp < -4 ? -4 : paramCurrentHcp;
      let paramDesiredHcp = desiredHandicap;
      paramDesiredHcp = paramDesiredHcp > 39 ? 39 : paramDesiredHcp;
      paramDesiredHcp = paramDesiredHcp < -4 ? -4 : paramDesiredHcp;
      if (paramDesiredHcp >= paramCurrentHcp) {
        paramDesiredHcp = paramCurrentHcp - 1;
      }
      if (planStarted) {
        GA_Onboarding_goals(desiredHandicap, desiredScore);
        navigation.navigate('Quiz', {
          screen: 'QuizCommonMisHit',
          params: {
            currentHandicap: paramCurrentHcp,
            desiredHandicap: paramDesiredHcp,
            currentScore: null,
            desiredScore: null,
            desiredHandicapLabel:
              desiredHandicap > 39
                ? 39
                : desiredHandicap === 'None'
                ? NOT_APPLICABLE
                : desiredHandicap,
            desiredScoreLabel: desiredScore,
          },
        });
      } else {
        setLoading(true);
        // swing api only accept handicap value below 40
        await handicapImprovement(paramCurrentHcp, paramDesiredHcp);
        setLoading(false);
        navigation.goBack();
      }
    } catch (error) {
      setLoading(false);
      Alert.alert('Error', error?.causeMessage || error?.message, [
        {
          text: t('describe_shot.plan_coach_message.okay'),
          onPress: () => {},
          style: 'cancel',
        },
      ]);
    }
  };

  const updateTargetScore = async () => {
    try {
      // select average score tab
      let desiredScoredConverted = convertTargetAverageScore(desiredScore);
      let currentScoreConverted = 0;
      if (!currentScore || currentScore?.length === 0) {
        // don't have current score, convert current handicap to current score
        currentScoreConverted =
          transformHandicapToAverageScore(currentHandicap);
      } else {
        currentScoreConverted = convertCurrentAverageScoreToValue(currentScore);
      }
      if (desiredScoredConverted >= currentScoreConverted) {
        desiredScoredConverted = currentScoreConverted - 1;
      }
      if (planStarted) {
        GA_Onboarding_goals(desiredHandicap, desiredScore);
        navigation.navigate('Quiz', {
          screen: 'QuizCommonMisHit',
          params: {
            currentScore: currentScoreConverted,
            desiredScore: desiredScoredConverted,
            currentHandicap: null,
            desiredHandicap: null,
            desiredScoreLabel: desiredScore,
            desiredHandicapLabel:
              desiredHandicap > 39.9
                ? 39.9
                : desiredHandicap === 'None'
                ? NOT_APPLICABLE
                : desiredHandicap,
          },
        });
      } else {
        setLoading(true);
        await averageImprovement(currentScoreConverted, desiredScoredConverted);
        setLoading(false);
        navigation.goBack();
      }
    } catch (error) {
      setLoading(false);
      Alert.alert('Error', error?.causeMessage || error?.message, [
        {
          text: t('describe_shot.plan_coach_message.okay'),
          onPress: () => {},
          style: 'cancel',
        },
      ]);
    }
  };

  const onSelectorChange = value => {
    if (!ignoreOnChange) {
      setDesiredHandicap(value);
    }
  };

  let maxHandicap = 54;
  let minHandicap = -10;

  const enableButton =
    (isCurrentHandicap && !isNaN(desiredHandicap)) ||
    (!isCurrentHandicap && selectedAverageScore);
  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={[appStyles.flex]}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[
                  appStyles.white,
                  appStyles.textCenter,
                  appStyles.mBMd,
                  {marginHorizontal: 70},
                ]}
              >
                coach.target_handicap_avg.what_is_your_target
              </Text>
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={100}>
              <View style={[appStyles.mBMd, styles.tabHeader]}>
                {renderTabHeader()}
              </View>
            </Animatable.View>

            {isCurrentHandicap ? renderCurrentHandicap() : renderAverageScore()}
          </View>

          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              style={[appStyles.mTSm]}
              text={planStarted ? 'quiz.cta.next' : 'common.done'}
              backgroundColor={!enableButton ? GREY : 'white'}
              disabled={!enableButton}
              onPress={
                isCurrentHandicap ? updateTargetHandicap : updateTargetScore
              }
              loading={loading}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="handicap"
        value={
          !isNaN(desiredHandicap)
            ? desiredHandicap
            : currentHandicap > 16
            ? 16
            : maxHandicap
        }
        maxHandicap={maxHandicap}
        minHandicap={minHandicap} // SI allows higher than -5 handicap
        onChange={onSelectorChange}
        onCloseEnd={() =>
          setDesiredHandicap(
            !isNaN(desiredHandicap)
              ? desiredHandicap
              : currentHandicap > 16
              ? 16
              : maxHandicap,
          )
        }
      />
    </>
  );
};

const styles = StyleSheet.create({
  tabHeader: {
    backgroundColor: '#ffffff33',
    width: '100%',
    borderRadius: wp('20%'),
    borderWidth: wp('0.3%'),
    height: hp('7%'),
    padding: 10,
  },
  headerItem: {
    width: '50%',
    height: '100%',
    paddingHorizontal: 0,
  },
});

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizTargetHandicapOrAvg);
