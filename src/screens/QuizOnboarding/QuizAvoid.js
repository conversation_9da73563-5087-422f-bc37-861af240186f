import React, {useState, useEffect} from 'react';
import {View, ScrollView} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {updateQuiz} from 'reducers/quiz';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import BackButton from 'components/BackButton';

const QuizAvoid = ({navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const [avoidance, setAvoidance] = useState(
    fromGameProfile
      ? route.params?.avoidShot
      : user.golferProfile?.avoidShot || quiz.avoidShot,
  );
  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(avoidance || '');

  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete && !fromGameProfile) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() =>
              route.params?.origin === 'home'
                ? navigation.navigate('App')
                : navigation.navigate('Profile')
            }
          />
        ),
      });
    }
    if (fromGameProfile) {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return avoidance?.includes(type) ? 'white' : GREY;
      case 'border':
        return avoidance?.includes(type) ? GREEN : GREY;
      case 'background':
        return avoidance?.includes(type) ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const updateUserAvoidance = async () => {
    if (fromGameProfile) {
      route.params?.setAvoidShot(avoidance);
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({avoidShot: avoidance});

      navigation.navigate('QuizFear', {
        origin:
          route.params?.origin === 'home'
            ? 'home'
            : route.params?.origin
            ? 'profile'
            : null,
        isEdit,
      });
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <Animatable.View animation="fadeInUp">
        <Text
          style={[
            appStyles.sm,
            appStyles.white,
            appStyles.textCenter,
            appStyles.mBSm,
            appStyles.pHMd,
          ]}
        >
          quiz.avoid.headline
        </Text>
      </Animatable.View>
      <View style={[appStyles.flex, appStyles.mTSm, appStyles.pHSm]}>
        <ScrollView style={[appStyles.flex]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp" delay={100}>
              <Button
                style={[appStyles.mBSm]}
                text="quiz.avoid.supporting_copy.fat"
                textColor={getButtonActiveColor('Fat', 'text')}
                borderColor={getButtonActiveColor('Fat', 'border')}
                backgroundColor={getButtonActiveColor('Fat', 'background')}
                onPress={() => setAvoidance('Fat')}
                disabled={loading}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <Button
                style={[appStyles.mBSm]}
                text="quiz.avoid.supporting_copy.thin"
                textColor={getButtonActiveColor('Thin', 'text')}
                borderColor={getButtonActiveColor('Thin', 'border')}
                backgroundColor={getButtonActiveColor('Thin', 'background')}
                onPress={() => setAvoidance('Thin')}
                disabled={loading}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={300}>
              <Button
                style={[appStyles.mBSm]}
                text="quiz.avoid.supporting_copy.top"
                textColor={getButtonActiveColor('Top', 'text')}
                borderColor={getButtonActiveColor('Top', 'border')}
                backgroundColor={getButtonActiveColor('Top', 'background')}
                onPress={() => setAvoidance('Top')}
                disabled={loading}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={400}>
              <Button
                style={[appStyles.mBSm]}
                text="quiz.avoid.supporting_copy.shank"
                textColor={getButtonActiveColor('Shank', 'text')}
                borderColor={getButtonActiveColor('Shank', 'border')}
                backgroundColor={getButtonActiveColor('Shank', 'background')}
                onPress={() => setAvoidance('Shank')}
                disabled={loading}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={500}>
              <Button
                style={[appStyles.mBSm]}
                text="quiz.avoid.supporting_copy.hook"
                textColor={getButtonActiveColor('Hook', 'text')}
                borderColor={getButtonActiveColor('Hook', 'border')}
                backgroundColor={getButtonActiveColor('Hook', 'background')}
                onPress={() => setAvoidance('Hook')}
                disabled={loading}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={600}>
              <Button
                style={[appStyles.mBSm]}
                text="quiz.avoid.supporting_copy.draw"
                textColor={getButtonActiveColor('Draw', 'text')}
                borderColor={getButtonActiveColor('Draw', 'border')}
                backgroundColor={getButtonActiveColor('Draw', 'background')}
                onPress={() => setAvoidance('Draw')}
                disabled={loading}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={700}>
              <Button
                style={[appStyles.mBSm]}
                text="quiz.avoid.supporting_copy.slice"
                textColor={getButtonActiveColor('Slice', 'text')}
                borderColor={getButtonActiveColor('Slice', 'border')}
                backgroundColor={getButtonActiveColor('Slice', 'background')}
                onPress={() => setAvoidance('Slice')}
                disabled={loading}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={800}>
              <Button
                style={[appStyles.mBSm]}
                text="quiz.avoid.supporting_copy.fade"
                textColor={getButtonActiveColor('Fade', 'text')}
                borderColor={getButtonActiveColor('Fade', 'border')}
                backgroundColor={getButtonActiveColor('Fade', 'background')}
                onPress={() => setAvoidance('Fade')}
                disabled={loading}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={900}>
              <Button
                style={[appStyles.mBLg]}
                text="quiz.avoid.supporting_copy.cut"
                textColor={getButtonActiveColor('Cut', 'text')}
                borderColor={getButtonActiveColor('Cut', 'border')}
                backgroundColor={getButtonActiveColor('Cut', 'background')}
                onPress={() => setAvoidance('Cut')}
                disabled={loading}
              />
            </Animatable.View>
          </View>
        </ScrollView>
        <Animatable.View
          animation="fadeInUp"
          delay={1000}
          style={appStyles.pTSm}
        >
          <Button
            text={fromGameProfile ? 'common.update' : 'common.next'}
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={updateUserAvoidance}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {updateQuiz};

export default connect(null, mapDispatchToProps)(QuizAvoid);
