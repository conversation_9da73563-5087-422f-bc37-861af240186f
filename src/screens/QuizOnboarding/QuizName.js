import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import TextInput from 'components/TextInput';
import Button from 'components/Button';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {showToast} from 'utils/toast';
import {isFirstName, isLastName} from 'utils/validate';
import {t} from 'i18next';
import analytics from '@react-native-firebase/analytics';
import {SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';

const KEYBOARD_SHOW_BUTTON_MARGIN_BOTTOM = 120;

const QuizName = ({
  navigation: {navigate},
  addCurrentUser,
  updateQuiz,
  route,
}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const [firstName, setFirstName] = useState(
    user.firstName || quiz.firstName || '',
  );
  const [lastName, setLastName] = useState(
    user.lastName || quiz.lastName || '',
  );
  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(firstName || '') && !isEmpty(lastName || '');

  const GA_logOnboardingName = () => {
    try {
      analytics().logEvent('user_onboarding_name', {
        screen_name: `onboarding - first last name`,
        screen_type: SCREEN_TYPES.ONBOARDING,
        page_name: `onboarding - first last name`,
        page_type: SCREEN_TYPES.ONBOARDING,
        page_category: SCREEN_CLASS.SIGNUP,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const updateUserName = async () => {
    if (!isFirstName(firstName)) {
      return showToast({
        type: 'error',
        message: t('quiz.name.now_on_the_tee'),
        subText: t('quiz.name.an_issue_with_the_format_of_your_first_name'),
      });
    }

    if (!isLastName(lastName)) {
      return showToast({
        type: 'error',
        message: t('quiz.name.now_on_the_tee'),
        subText: t('quiz.name.an_issue_with_the_format_of_your_last_name'),
      });
    }

    // Update quiz in redux
    updateQuiz({firstName, lastName});
    // Update user in backend if this is an edit
    if (isEdit) {
      setLoading(true);

      try {
        // Make request to update user's name
        const updatedUser = await updateUser({
          firstName,
          lastName,
        });
        // Update user in redux
        addCurrentUser(updatedUser);
        // Stop loading state and navigate to next screen
        setLoading(false);
      } catch (error) {
        setLoading(false);
        return showToast({
          type: 'error',
          message: t('An_error_occurred_updating_your_name'),
        });
      }
    } else {
      GA_logOnboardingName();
    }

    navigate('QuizGender');
  };

  const contentQuizName = () => {
    return (
      <View style={appStyles.flex}>
        <FocusAwareStatusBar barStyle={'light-content'} />

        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View
            style={[
              appStyles.flex,
              appStyles.mTLg,
              appStyles.pHSm,
              {justifyContent: 'space-around'},
            ]}
          >
            <View style={appStyles.flex}>
              <Animatable.View animation="fadeInUp">
                <Text
                  style={[
                    appStyles.white,
                    appStyles.textCenter,
                    appStyles.mBMd,
                  ]}
                >
                  quiz.name.headline
                </Text>
              </Animatable.View>
              <Animatable.View animation="fadeInUp" delay={100}>
                <TextInput
                  style={[appStyles.mBSm]}
                  placeholder={t('quiz.name.supporting_copy.first')}
                  onChangeText={setFirstName}
                  defaultValue={user.firstName || quiz.firstName}
                  disabled={loading}
                  autoCorrect={false}
                />
              </Animatable.View>
              <Animatable.View animation="fadeInUp" delay={200}>
                <TextInput
                  style={[appStyles.mBMd]}
                  placeholder={t('quiz.name.supporting_copy.last')}
                  onChangeText={setLastName}
                  defaultValue={user.lastName || quiz.lastName}
                  disabled={loading}
                  autoCorrect={false}
                />
              </Animatable.View>
            </View>

            <Animatable.View animation="fadeInUp" delay={300}>
              <Button
                text="quiz.cta.next"
                backgroundColor={validated ? 'white' : GREY}
                disabled={!validated || loading}
                onPress={updateUserName}
                loading={loading}
                centered
                DINbold
              />
            </Animatable.View>
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  };
  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      {Platform.OS === 'ios' ? (
        <KeyboardAvoidingView
          behavior="padding"
          style={appStyles.flex}
          keyboardVerticalOffset={KEYBOARD_SHOW_BUTTON_MARGIN_BOTTOM}
        >
          {contentQuizName()}
        </KeyboardAvoidingView>
      ) : (
        contentQuizName()
      )}
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizName);
