import React, {useState, useEffect} from 'react';
import {View, Image} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {updateQuiz} from 'reducers/quiz';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import {t} from 'i18next';
import BackButton from 'components/BackButton';

const leftHandedIron = require('assets/imgs/iron-left-handed.png');
const rightHandedIron = require('assets/imgs/iron-right-handed.png');
const greenIndicator = require('assets/imgs/green-indicator.png');

const QuizBallStrike = ({navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const [ballStrike, setBallStrike] = useState(
    fromGameProfile
      ? route.params?.ballStrike
      : user.golferProfile?.ballStrike || quiz.ballStrike || '',
  );
  const isRightHanded =
    user.golferProfile?.handed === 'right' || quiz.handed === 'right';
  const [ironImg, setIronImg] = useState(
    isRightHanded ? rightHandedIron : leftHandedIron,
  );
  const [indicatorPosition, setIndicatorPostition] = useState();
  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(ballStrike || '');

  useEffect(() => {
    setBallStrikeInfo(
      fromGameProfile
        ? route.params?.ballStrike
        : user.golferProfile?.ballStrike || quiz.ballStrike,
    );
  }, []);

  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete && !fromGameProfile) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() =>
              route.params?.origin === 'home'
                ? navigation.navigate('App')
                : navigation.navigate('Profile')
            }
          />
        ),
      });
    }
    if (fromGameProfile) {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return ballStrike?.includes(type) ? 'white' : GREY;
      case 'border':
        return ballStrike?.includes(type) ? GREEN : GREY;
      case 'background':
        return ballStrike?.includes(type) ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const updateUserBallStrike = async () => {
    if (fromGameProfile) {
      route.params?.setBallStrike(ballStrike);
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({ballStrike});
      // Update user in backend if this is an edit

      navigation.navigate('QuizBallMiss', {
        origin:
          route.params?.origin === 'home'
            ? 'home'
            : route.params?.origin
            ? 'profile'
            : null,
        isEdit,
      });
    }
  };

  const setBallStrikeInfo = value => {
    setBallStrike(value);
    switch (value) {
      case 'Toe':
        setIndicatorPostition(
          isRightHanded ? appStyles.circleRightToe : appStyles.circleLeftToe,
        );
        break;
      case 'Pure':
        setIndicatorPostition(
          isRightHanded ? appStyles.circleRightPure : appStyles.circleLeftPure,
        );
        break;
      case 'Heel':
        setIndicatorPostition(
          isRightHanded ? appStyles.circleRightHeel : appStyles.circleLeftHeel,
        );
        break;
      case 'Fat':
        setIndicatorPostition(
          isRightHanded ? appStyles.circleRightFat : appStyles.circleLeftFat,
        );
        break;
      case 'Thin':
        setIndicatorPostition(
          isRightHanded ? appStyles.circleRightThin : appStyles.circleLeftThin,
        );
        break;
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View
        style={[
          appStyles.flex,
          appStyles.mTSm,
          appStyles.pHSm,
          appStyles.spaceBetween,
        ]}
      >
        <Animatable.View animation="fadeInUp">
          <Text
            style={[
              appStyles.sm,
              appStyles.white,
              appStyles.textCenter,
              appStyles.mBMd,
            ]}
          >
            quiz.ball_strike_complete.headline
          </Text>
        </Animatable.View>
        <Animatable.View animation="fadeIn" delay={100}>
          <Image
            style={[
              appStyles.alignCenter,
              appStyles.mBXxs,
              appStyles.responsiveImageBallStrike,
            ]}
            source={ironImg}
          />
          <Image
            style={[
              appStyles.circle,
              indicatorPosition,
              {opacity: ballStrike ? 1 : 0},
            ]}
            source={greenIndicator}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={200}>
          <View style={(appStyles.flex, appStyles.wrap)}>
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.ball_strike_complete.supporting_copy.toe"
              textColor={getButtonActiveColor('Toe', 'text')}
              borderColor={getButtonActiveColor('Toe', 'border')}
              backgroundColor={getButtonActiveColor('Toe', 'background')}
              onPress={() => setBallStrikeInfo('Toe')}
              disabled={loading}
              centered
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.ball_strike_complete.supporting_copy.pure"
              textColor={getButtonActiveColor('Pure', 'text')}
              borderColor={getButtonActiveColor('Pure', 'border')}
              backgroundColor={getButtonActiveColor('Pure', 'background')}
              onPress={() => setBallStrikeInfo('Pure')}
              disabled={loading}
              centered
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.ball_strike_complete.supporting_copy.heel"
              textColor={getButtonActiveColor('Heel', 'text')}
              borderColor={getButtonActiveColor('Heel', 'border')}
              backgroundColor={getButtonActiveColor('Heel', 'background')}
              onPress={() => setBallStrikeInfo('Heel')}
              disabled={loading}
              centered
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.ball_strike_complete.supporting_copy.fat"
              textColor={getButtonActiveColor('Fat', 'text')}
              borderColor={getButtonActiveColor('Fat', 'border')}
              backgroundColor={getButtonActiveColor('Fat', 'background')}
              onPress={() => setBallStrikeInfo('Fat')}
              disabled={loading}
              centered
            />
            <Button
              style={[appStyles.mBSm, appStyles.mRXs, appStyles.buttonWMd]}
              text="quiz.ball_strike_complete.supporting_copy.thin"
              textColor={getButtonActiveColor('Thin', 'text')}
              borderColor={getButtonActiveColor('Thin', 'border')}
              backgroundColor={getButtonActiveColor('Thin', 'background')}
              onPress={() => setBallStrikeInfo('Thin')}
              disabled={loading}
              centered
            />
          </View>
        </Animatable.View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text={fromGameProfile ? 'common.update' : 'common.next'}
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={updateUserBallStrike}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {updateQuiz};

export default connect(null, mapDispatchToProps)(QuizBallStrike);
