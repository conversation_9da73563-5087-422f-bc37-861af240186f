import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import Slider from '@react-native-community/slider';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {convertDriveLength} from 'utils/convert';
import {t} from 'i18next';
import BackButton from 'components/BackButton';

const QuizDriveLength = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const [driveLength, setDriveLength] = useState(
    fromGameProfile
      ? route.params?.maximumDriverDistance
      : user.golferProfile?.maximumDriverDistance || quiz.driveLength
      ? convertDriveLength(
          user.golferProfile?.maximumDriverDistance || quiz.driveLength,
        )
      : 210,
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (route.params?.origin === 'Setting') {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const updateUserDriveLength = async () => {
    if (fromGameProfile) {
      route.params?.setMaximumDriverDistance(driveLength);
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({driveLength: getDriveLength()});
      // Update user in backend if this is an edit
      if (isEdit) {
        setLoading(true);

        try {
          // Make request to update user's drive length
          const updatedUser = await updateUser({
            maximumDriverDistance: getDriveLength(),
          });
          // Update user in redux
          addCurrentUser(updatedUser);
          // Stop loading state and navigate to next screen
          setLoading(false);
        } catch (error) {
          setLoading(false);
          return showToast({
            type: 'error',
            message: t('An_error_occurred_updating_your_max_distance'),
          });
        }
      }

      navigation.navigate('QuizStrength');
    }
  };

  const getDriveLength = () => {
    if (driveLength === 90) {
      return t('quiz.drive_length.100_yards');
    } else if (driveLength === 360) {
      return t('quiz.drive_length.350_yards');
    } else {
      return `${driveLength} ${t('quiz.drive_length.yards')}`;
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              quiz.drive_length.headline
            </Text>
          </Animatable.View>

          <Animatable.View animation="fadeInUp" delay={100}>
            <Text
              style={[appStyles.white, appStyles.xxxl, appStyles.textCenter]}
              DINbold
            >
              {getDriveLength()}
            </Text>
            <Slider
              style={[{width: '100%'}]}
              minimumValue={90}
              maximumValue={360}
              step={10}
              minimumTrackTintColor="#ffffff"
              maximumTrackTintColor="#ffffff80"
              onValueChange={value =>
                setDriveLength(parseInt(value.toFixed(0)))
              }
              value={driveLength}
              disabled={loading}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={200}>
          <Button
            text={fromGameProfile ? 'common.update' : 'common.next'}
            backgroundColor="white"
            disabled={loading}
            onPress={updateUserDriveLength}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizDriveLength);
