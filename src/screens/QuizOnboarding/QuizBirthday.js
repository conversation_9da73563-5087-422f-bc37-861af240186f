import React, {useState, useRef, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import moment from 'moment';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Selector from 'components/Selector';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY, ERROR_RED} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import analytics from '@react-native-firebase/analytics';
import {SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';

const BIRTHDAY_RANGE = [
  {
    max: 24,
    min: 18,
    range: '18-24',
  },
  {
    max: 34,
    min: 25,
    range: '25-34',
  },
  {
    max: 44,
    min: 35,
    range: '35-44',
  },
  {
    max: 54,
    min: 45,
    range: '45-54',
  },
  {
    max: 64,
    min: 55,
    range: '55-64',
  },
];

const QuizBirthday = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const [birthday, setBirthday] = useState(user.dob || quiz.dob || '');
  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(birthday || '');
  const isOfAge = moment().diff(moment(birthday).format('YYYY'), 'years') >= 18;
  const sheetRef = useRef(null);

  const year = new Date().getFullYear();
  const month = new Date().getMonth();
  const day = new Date().getDate();
  const date = new Date(year - 18, month, day);

  const [selectValue, setSelectValue] = useState(user.dob || quiz.dob || '');

  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() => navigation.goBack()}
          />
        ),
      });
    }

    if (route.params?.origin === 'settings') {
      navigation.setOptions({
        headerLeft: () => null,
      });
    }
  }, []);

  const getRangeBirthday = userAge => {
    let userAgeConverted = Math.trunc(userAge);
    let rangeBirthday = '';
    if (userAgeConverted >= 65) {
      rangeBirthday = '65+';
    } else {
      BIRTHDAY_RANGE.forEach(element => {
        if (
          element.min <= userAgeConverted &&
          userAgeConverted <= element.max
        ) {
          rangeBirthday = element.range;
          return;
        }
      });
    }
    return rangeBirthday;
  };

  const GA_logOnboardingBirthday = userAge => {
    try {
      let range = getRangeBirthday(userAge);
      console.log('range', range);
      analytics().logEvent('user_onboarding_birthday', {
        user_age: range, // e.g. male, female or prefer not to say
        screen_name: `onboarding - dob`,
        screen_type: SCREEN_TYPES.ONBOARDING,
        page_name: `onboarding - dob`,
        page_type: SCREEN_TYPES.ONBOARDING,
        page_category: SCREEN_CLASS.SIGNUP,
      });
      // analytics().setUserProperty(
      //   'onboarding_user_age',
      //   Math.round(userAge) + '',
      // );
      analytics().setUserProperty('user_age', range);
    } catch (error) {
      console.log(error);
    }
  };

  const updateUserBirthday = async () => {
    // Update quiz in redux
    updateQuiz({dob: birthday});
    const userAge = moment().diff(moment(birthday), 'years', true);
    // Update user in backend if this is an edit
    if (isEdit) {
      setLoading(true);

      try {
        // Make request to update user's birthday
        const updatedUser = await updateUser({
          dob: birthday,
        });
        // Update user in redux
        addCurrentUser(updatedUser);
        // Stop loading state and navigate to next screen
        setLoading(false);
      } catch (error) {
        setLoading(false);
        return showToast({
          type: 'error',
          message: t('An_error_occurred_updating_your_birthday'),
        });
      }
      // analytics().setUserProperty('user_age', Math.round(userAge) + '');
    } else {
      GA_logOnboardingBirthday(userAge);
    }

    if (route.params?.origin === 'settings') {
      navigation.navigate('Settings');
    } else {
      navigation.navigate('QuizGolfCourse', {
        origin:
          route.params?.origin === 'home'
            ? 'home'
            : route.params?.origin
            ? 'profile'
            : null,
        isEdit,
        fromOnboarding: true,
      });
    }
  };

  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                quiz.birthday.headline
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={100}>
              <Text
                style={[
                  appStyles.grey,
                  appStyles.textCenter,
                  appStyles.mBMd,
                  appStyles.sm,
                ]}
              >
                You must be 18+ to use TaylorMade
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <Button
                text={
                  birthday?.length
                    ? moment(birthday).format('LL')
                    : t('quiz.birthday.supporting_copy')
                }
                textColor={birthday?.length ? 'white' : GREY}
                borderColor={birthday && !isOfAge ? ERROR_RED : GREY}
                onPress={() => sheetRef.current?.snapTo(0)}
                disabled={loading}
                rightIcon="chevron-down"
              />
            </Animatable.View>

            {birthday && !isOfAge ? (
              <Text
                style={[
                  appStyles.sm,
                  appStyles.red,
                  appStyles.textCenter,
                  appStyles.mTMd,
                ]}
              >
                quiz.birthday_error.supporting_copy.birthday_under_age_limit
              </Text>
            ) : null}
          </View>

          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              text={
                route.params?.origin === 'settings'
                  ? t('common.update')
                  : t('quiz.cta.next')
              }
              backgroundColor={validated && isOfAge ? 'white' : GREY}
              disabled={!validated || !isOfAge || loading}
              onPress={updateUserBirthday}
              loading={loading}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="date"
        value={selectValue}
        onCloseEnd={() => {
          setBirthday(selectValue || date?.toString());
          //if select the default value for the first time
          if (!selectValue?.length) setSelectValue(moment(date).toISOString());
        }}
        defaultValue={date?.toString()}
        onChange={value => setSelectValue(moment(value).toISOString())}
      />
    </>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizBirthday);
