import React, {useState, useEffect} from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
  Platform,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import Icon from 'react-native-vector-icons/FontAwesome5';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {addCurrentUser} from 'reducers/user';
import {updateUser, getProPlayers} from 'requests/accounts';
import {updateQuiz} from 'reducers/quiz';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {GREY} from 'config';
import {t} from 'i18next';
import BackButton from 'components/BackButton';

const QuizPro = ({navigation, addCurrentUser, route}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const [favoritePlayers, setFavoritePlayers] = useState(
    fromGameProfile
      ? route.params?.favoriteTeamMembers
        ? route.params?.favoriteTeamMembers?.split(', ')
        : []
      : user.golferProfile?.favoriteTeamMembers?.split(', ') ||
          quiz.favoriteTeamMembers?.split(', ') ||
          [],
  );
  const [playerList, setPlayerList] = useState([]);
  const [loading, setLoading] = useState(false);
  const validated = favoritePlayers?.length;

  useEffect(() => {
    setProPlayers();
  }, []);

  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete && !fromGameProfile) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() =>
              route.params?.origin === 'home'
                ? navigation.navigate('App')
                : navigation.navigate('Profile')
            }
          />
        ),
      });
    }
    if (fromGameProfile) {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const setProPlayers = async () => {
    try {
      // Make request to pull pro players
      const players = await getProPlayers();
      // Stop loading state and set player list
      setPlayerList(
        players.filter(player => player.id !== 23957 && player.id !== 26546),
      );
    } catch (error) {
      showToast({
        type: 'error',
        message: t('An_error_occurred_retrieving_players'),
      });
    }
  };

  const completeOnboarding = async () => {
    // Update quiz in redux
    updateQuiz({favoriteTeamMembers: favoritePlayers.join(', ')});

    setLoading(true);

    try {
      // Make request to update user
      const updatedUser = await updateUser({
        firstName: quiz.firstName,
        lastName: quiz.lastName,
        gender: quiz.gender,
        dob: quiz.dob,
        height: quiz.height,
        handed: quiz.handed,
        timePlayingGolf: quiz.yearsOfExperience,
        rpm: quiz.roundsPerMonth,
        handicapPreference: quiz.handicap ? 0 : 1,
        userInputHandicap: quiz.handicap,
        averageScoreRange: quiz.averageScore,
        targetScore: quiz.targetScore,
        maximumDriverDistance: quiz.driveLength,
        strongestArea: quiz.strengths,
        weakestArea: quiz.weaknesses,
        ballStrike: quiz.ballStrike,
        misHit: quiz.misHit,
        avoidShot: quiz.avoidShot,
        mostScaredShot: quiz.mostScaredShot,
        homeCourse: quiz.homeCourse,
        iGolfCourseId: quiz.iGolfCourseId,
        favoriteTeamMembers: favoritePlayers.join(', '),
        onboardingCompleteSteps: {
          firstName: true,
          lastName: true,
          gender: true,
          dob: true,
          height: true,
          handed: true,
          timePlayingGolf: true,
          rpmComplete: true,
          handicapPreference: quiz.handicap ? true : false,
          userInputHandicap: quiz.handicap ? true : false,
          averageScoreRange: quiz.averageScore ? true : false,
          targetScoreComplete: true,
          maximumDriverDistance: true,
          strongestArea: true,
          weakestArea: true,
          shotShape: true,
          ballStrike: true,
          misHit: true,
          avoidShot: true,
          mostScaredShot: true,
          homeCourse: true,
          favoriteTeamMembers: true,
        },
        onboardingComplete: true,
        signUpByDevice: Platform.OS,
      });
      // Update user in redux
      addCurrentUser(updatedUser);
      // Stop loading state and navigate to next screen
      setLoading(false);
    } catch (error) {
      setLoading(false);
      return showToast({
        type: 'error',
        message: t('An_error_occurred_with_your_onboarding'),
      });
    }
    if (user.myTMSubscriptionLevel) {
      navigation.replace('App', {screen: 'Home'});
      return;
    }
  };

  const updateUserFavoritePlayers = async () => {
    if (fromGameProfile) {
      route.params?.setFavoriteTeamMembers(favoritePlayers.join(', '));
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({favoriteTeamMembers: favoritePlayers.join(', ')});
      // Update user in backend if this is an edit
      if (route.params?.origin === 'home') {
        navigation.navigate('App');
      } else {
        navigation.navigate('Profile');
      }
    }
  };

  const updateFavoritePlayers = player => {
    // Check to see if incoming player exists
    const hasPlayer = favoritePlayers?.includes(player);
    if (hasPlayer) {
      // Remove player for deselect
      setFavoritePlayers(values => values.filter(value => value !== player));
    } else {
      // Add player for select
      setFavoritePlayers(values => [...values, player]);
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTXs, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <ScrollView style={appStyles.flex}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                quiz.pro.headline
              </Text>
            </Animatable.View>
            <View style={[appStyles.flex, appStyles.wrap]}>
              {playerList.map(player => {
                return (
                  <View
                    key={player.id}
                    style={[appStyles.hCenter, appStyles.mBMd, {width: '50%'}]}
                  >
                    <TouchableOpacity
                      onPress={() => updateFavoritePlayers(player.title)}
                      disabled={loading}
                    >
                      <ImageBackground
                        source={{uri: player.headshot}}
                        style={[{width: wp('37%'), height: wp('37%')}]}
                        imageStyle={{borderRadius: wp('100%')}}
                      >
                        {favoritePlayers.includes(player.title) ? (
                          <View
                            style={[
                              appStyles.fullWidth,
                              appStyles.center,
                              {
                                backgroundColor: 'rgba(15, 186, 86, 0.6)',
                                borderRadius: wp('100%'),
                              },
                            ]}
                          >
                            <Icon name="check" color="white" size={wp('4%')} />
                          </View>
                        ) : null}
                      </ImageBackground>
                    </TouchableOpacity>
                    <Text
                      style={[
                        appStyles.white,
                        appStyles.textCenter,
                        appStyles.mTMd,
                      ]}
                    >
                      {player.title}
                    </Text>
                  </View>
                );
              })}
            </View>
          </ScrollView>
          <View style={appStyles.pTSm}>
            <Button
              text={fromGameProfile ? 'common.update' : 'quiz.cta.complete'}
              backgroundColor={validated ? 'white' : GREY}
              disabled={!validated || loading}
              onPress={
                isEdit || fromGameProfile
                  ? updateUserFavoritePlayers
                  : completeOnboarding
              }
              loading={loading}
              centered
              DINbold
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizPro);
