import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty} from 'validator';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import HeaderRightButton from 'components/HeaderRightButton';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import analytics from '@react-native-firebase/analytics';
import {SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';

const QuizGender = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const [gender, setGender] = useState(user.gender || quiz.gender || '');
  const [loading, setLoading] = useState(false);
  const validated = !isEmpty(gender || '');

  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (user.onboardingComplete) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() => navigation.goBack()}
          />
        ),
      });
    }

    if (route.params?.origin === 'settings') {
      navigation.setOptions({
        headerLeft: () => null,
      });
    }
  }, []);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return gender === type ? 'white' : GREY;
      case 'border':
        return gender === type ? GREEN : GREY;
      case 'background':
        return gender === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const GA_logOnboardingGender = () => {
    analytics().logEvent('user_onboarding_gender', {
      user_gender: gender, // e.g. male, female or prefer not to say
      screen_name: `onboarding - gender`,
      screen_type: SCREEN_TYPES.ONBOARDING,
      page_name: `onboarding - gender`,
      page_type: SCREEN_TYPES.ONBOARDING,
      page_category: SCREEN_CLASS.SIGNUP,
    });
    // analytics().setUserProperty('onboarding_user_gender', gender);
    analytics().setUserProperty('user_gender', gender);
  };

  const updateUserGender = async () => {
    // Update quiz in redux
    updateQuiz({gender});
    // Update user in backend if this is an edit
    if (isEdit) {
      setLoading(true);
      try {
        // Make request to update user's gender
        const updatedUser = await updateUser({
          gender,
        });
        // Update user in redux
        addCurrentUser(updatedUser);
        // Stop loading state and navigate to next screen
        setLoading(false);
      } catch (error) {
        setLoading(false);
        return showToast({
          type: 'error',
          message: t('An_error_occurred_updating_your_gender'),
        });
      }
    } else {
      GA_logOnboardingGender();
    }

    if (route.params?.origin === 'settings') {
      navigation.navigate('Settings');
    } else {
      navigation.navigate('QuizBirthday');
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              quiz.gender.headline
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.gender.supporting_copy.male"
              textColor={getButtonActiveColor('male', 'text')}
              borderColor={getButtonActiveColor('male', 'border')}
              backgroundColor={getButtonActiveColor('male', 'background')}
              onPress={() => setGender('male')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.gender.supporting_copy.female"
              textColor={getButtonActiveColor('female', 'text')}
              borderColor={getButtonActiveColor('female', 'border')}
              backgroundColor={getButtonActiveColor('female', 'background')}
              onPress={() => setGender('female')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.gender.supporting_copy.notSay"
              textColor={getButtonActiveColor('prefer not to say', 'text')}
              borderColor={getButtonActiveColor('prefer not to say', 'border')}
              backgroundColor={getButtonActiveColor(
                'prefer not to say',
                'background',
              )}
              onPress={() => setGender('prefer not to say')}
              disabled={loading}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={400}>
          <Button
            text={
              route.params?.origin === 'settings'
                ? t('common.update')
                : t('common.next')
            }
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={updateUserGender}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizGender);
