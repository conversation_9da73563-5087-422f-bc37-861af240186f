import React from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect} from 'react-redux';
import * as Animatable from 'react-native-animatable';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Logo from 'components/Logo';
import Text from 'components/Text';
import Button from 'components/Button';

import appStyles from 'styles/global';

const QuizMoreInsights = ({navigation: {navigate}, user, quiz}) => {
  const handed = user.golferProfile?.handed || quiz?.handed;
  const driveLength = user.golferProfile?.driveLength || quiz?.driveLength;
  const strengths = user.golferProfile?.strengths || quiz?.strengths;
  const weaknesses = user.golferProfile?.weaknesses || quiz?.weaknesses;
  const mostScaredShot =
    user.golferProfile?.mostScaredShot || quiz?.mostScaredShot;
  const handleAction = () => {
    if (!handed) {
      navigate('QuizHanded');
    } else if (!driveLength) {
      navigate('QuizDriveLength');
    } else if (!strengths) {
      navigate('QuizStrength');
    } else if (!weaknesses) {
      navigate('QuizWeakness');
    } else if (!mostScaredShot) {
      navigate('QuizFear');
    } else {
      navigate('QuizHanded');
    }
  };
  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.pHMd]}>
        <View style={[appStyles.flex, appStyles.vCenter]}>
          <Animatable.View animation="fadeInUp">
            <Logo style={{bottom: hp('7%')}} intro />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Text style={[appStyles.white, appStyles.xxl]} DINbold>
              quiz.more_insights.know_your_game
            </Text>
            <Text
              style={[appStyles.white, appStyles.mBSm, appStyles.xxl]}
              DINbold
            >
              quiz.more_insights.grow_your_game
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Text style={[appStyles.white]}>
              quiz.more_insights.to_get_more_insights
            </Text>
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            text="quiz.more_insights.get_started"
            textColor="black"
            backgroundColor="white"
            borderColor="white"
            onPress={handleAction}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};
const mapStateToProps = state => ({
  user: state.user,
  quiz: state.quiz?.quiz,
});

export default connect(mapStateToProps, null)(QuizMoreInsights);
