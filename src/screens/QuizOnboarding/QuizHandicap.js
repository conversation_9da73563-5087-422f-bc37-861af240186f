import React, {useState, useEffect, useRef} from 'react';
import {View, ScrollView, StyleSheet, TouchableOpacity} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useDispatch, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';
import Selector from 'components/Selector';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import {GREY, GREEN} from 'config';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {isEmpty} from 'validator';
import analytics from '@react-native-firebase/analytics';
import {SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';
import Icon from 'react-native-vector-icons/Feather';
import {moderateScale} from 'react-native-size-matters';
import {convertHandicapToRangeAvgScore} from 'utils/user';

let ignoreOnChange = true;

const QuizHandicap = ({navigation}) => {
  const dispatch = useDispatch();
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const [handicap, setHandicap] = useState(
    user?.golferProfile?.newHandicap?.tmCalculatedHandicap ||
      user?.golferProfile?.newHandicap?.userInputHandicap ||
      quiz.handicap ||
      'None',
  );
  const [loading, setLoading] = useState(false);
  const sheetRef = useRef(null);
  const [isCurrentHandicap, setIsCurrentHandicap] = useState(false);
  const [averageScore, setAverageScore] = useState(
    user.golferProfile?.averageScoreRange || quiz.averageScore || '',
  );

  // selector will call onChange when init (library error), so we need to ignore first call by firstSecond value
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        <TouchableOpacity
          style={styles.iconContainer}
          onPress={() => navigation.goBack()}
        >
          <View
            style={[styles.closeIcon, appStyles.vCenter, appStyles.hCenter]}
          >
            <Icon name="x" size={moderateScale(20)} />
          </View>
        </TouchableOpacity>
      ),
    });
    setTimeout(() => {
      ignoreOnChange = false;
    }, 1000);
    return () => {
      ignoreOnChange = true;
    };
  }, []);

  const selectedAverageScore = !isEmpty(averageScore || '');

  // useEffect(() => {
  //   // Render exit button if onboarding is being edited
  //   if (
  //     user.onboardingComplete &&
  //     user.onboardingCompleteSteps?.userInputHandicap
  //   ) {
  //     navigation.setOptions({
  //       headerRight: () => (
  //         <HeaderRightButton
  //           text="common.exit"
  //           onPress={() =>
  //             route.params?.origin === 'home'
  //               ? navigation.navigate('App')
  //               : navigation.navigate('Profile')
  //           }
  //         />
  //       ),
  //     });
  //   }
  // }, []);

  const handleSwitchTab = () => {
    setIsCurrentHandicap(!isCurrentHandicap);
  };

  const renderTabHeader = () => {
    return (
      <View
        style={[
          appStyles.flex,
          appStyles.row,
          appStyles.hCenter,
          appStyles.vCenter,
          {
            alignContent: 'center',
          },
        ]}
      >
        <Button
          style={styles.headerItem}
          text="Handicap"
          textColor={'white'}
          borderColor={isCurrentHandicap ? GREEN : 'transparent'}
          backgroundColor={isCurrentHandicap ? GREEN : 'transparent'}
          disabled={loading}
          onPress={handleSwitchTab}
          loading={loading}
          centered
        />
        <Button
          style={styles.headerItem}
          text="Average Score"
          textColor={'white'}
          borderColor={!isCurrentHandicap ? GREEN : 'transparent'}
          backgroundColor={!isCurrentHandicap ? GREEN : 'transparent'}
          disabled={loading}
          onPress={handleSwitchTab}
          loading={loading}
          centered
        />
      </View>
    );
  };

  const renderCurrentHandicap = () => {
    let text = 'Select';
    if (!isNaN(handicap)) {
      text = `${handicap < 0 ? `+${-handicap}` : handicap}`;
    }
    return (
      <Animatable.View animation="fadeInUp" delay={200}>
        <Button
          textColor={!isNaN(handicap) ? 'white' : GREY}
          borderColor={GREY}
          onPress={() => sheetRef.current?.snapTo(0)}
          text={text}
          rightIcon="chevron-down"
          disabled={loading}
        />
      </Animatable.View>
    );
  };

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return averageScore === type ? 'white' : GREY;
      case 'border':
        return averageScore === type ? GREEN : GREY;
      case 'background':
        return averageScore === type ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const renderAverageScore = () => {
    return (
      <ScrollView showsVerticalScrollIndicator={false}>
        <Animatable.View animation="fadeInUp" delay={100}>
          <Button
            style={[appStyles.mBSm]}
            text="Under 71"
            textColor={getButtonActiveColor('Under 71', 'text')}
            borderColor={getButtonActiveColor('Under 71', 'border')}
            backgroundColor={getButtonActiveColor('Under 71', 'background')}
            onPress={() => setAverageScore('Under 71')}
            disabled={loading}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={200}>
          <Button
            style={[appStyles.mBSm]}
            text="72 - 77"
            textColor={getButtonActiveColor('72 - 77', 'text')}
            borderColor={getButtonActiveColor('72 - 77', 'border')}
            backgroundColor={getButtonActiveColor('72 - 77', 'background')}
            onPress={() => setAverageScore('72 - 77')}
            disabled={loading}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={300}>
          <Button
            style={[appStyles.mBSm]}
            text="78 - 85"
            textColor={getButtonActiveColor('78 - 85', 'text')}
            borderColor={getButtonActiveColor('78 - 85', 'border')}
            backgroundColor={getButtonActiveColor('78 - 85', 'background')}
            onPress={() => setAverageScore('78 - 85')}
            disabled={loading}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={400}>
          <Button
            style={[appStyles.mBSm]}
            text="86 - 90"
            textColor={getButtonActiveColor('86 - 90', 'text')}
            borderColor={getButtonActiveColor('86 - 90', 'border')}
            backgroundColor={getButtonActiveColor('86 - 90', 'background')}
            onPress={() => setAverageScore('86 - 90')}
            disabled={loading}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={500}>
          <Button
            style={[appStyles.mBSm]}
            text="91 - 95"
            textColor={getButtonActiveColor('91 - 95', 'text')}
            borderColor={getButtonActiveColor('91 - 95', 'border')}
            backgroundColor={getButtonActiveColor('91 - 95', 'background')}
            onPress={() => setAverageScore('91 - 95')}
            disabled={loading}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={600}>
          <Button
            style={[appStyles.mBSm]}
            text="96 - 100"
            textColor={getButtonActiveColor('96 - 100', 'text')}
            borderColor={getButtonActiveColor('96 - 100', 'border')}
            backgroundColor={getButtonActiveColor('96 - 100', 'background')}
            onPress={() => setAverageScore('96 - 100')}
            disabled={loading}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={700}>
          <Button
            style={[appStyles.mBSm]}
            text="Over 100"
            textColor={getButtonActiveColor('Over 100', 'text')}
            borderColor={getButtonActiveColor('Over 100', 'border')}
            backgroundColor={getButtonActiveColor('Over 100', 'background')}
            onPress={() => setAverageScore('Over 100')}
            disabled={loading}
          />
        </Animatable.View>
      </ScrollView>
    );
  };

  const updateUserHandicapAndTargetScore = async () => {
    setLoading(true);
    let objUpdate = {};
    if (!isNaN(handicap) && isCurrentHandicap) {
      objUpdate = {
        ...objUpdate,
        handicapPreference: handicap ? 0 : 1,
        userInputHandicap: handicap,
        averageScoreRange: convertHandicapToRangeAvgScore(handicap),
      };
      if (user?.isManualInputAverageScore === false) {
        objUpdate = {
          ...objUpdate,
          isManualInputAverageScore: true,
        };
      }
    }
    if (selectedAverageScore && !isCurrentHandicap) {
      objUpdate = {
        ...objUpdate,
        averageScoreRange: averageScore,
      };
      if (user?.isManualInputAverageScore === false) {
        objUpdate = {
          ...objUpdate,
          isManualInputAverageScore: true,
        };
      }
    }
    try {
      // Make request to update user's birthday
      const updatedUser = await updateUser(objUpdate);
      // Update user in redux
      dispatch(addCurrentUser({...updatedUser}));
      // Stop loading state and navigate to next screen
      setLoading(false);
    } catch (error) {
      setLoading(false);
      return showToast({
        type: 'error',
        message: t('An_error_occurred_updating_your_handicap_average_score'),
      });
    }

    analytics().logEvent('user_onboarding_ability', {
      handicap: handicap + '',
      average_score: averageScore + '',
      screen_name: `onboarding - club handicap`,
      screen_type: SCREEN_TYPES.ONBOARDING,
      page_name: `onboarding - club handicap`,
      page_type: SCREEN_TYPES.ONBOARDING,
      page_category: SCREEN_CLASS.SIGNUP,
    });
    analytics().setUserProperty('onboarding_handicap', handicap + '');
    analytics().setUserProperty('handicap', handicap + '');
    analytics().setUserProperty('onboarding_average_score', averageScore + '');
    analytics().setUserProperty('average_score', averageScore + '');
    // await updateTiles();
    navigation.goBack();
  };

  const onSelectorChange = value => {
    if (!ignoreOnChange) {
      setHandicap(value);
    }
  };
  return (
    <>
      <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
          <View style={[appStyles.flex]}>
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                {false
                  ? 'quiz.handicap.or.average.score'
                  : 'quiz.average.score.what_is_your_typical_average_score'}
              </Text>
            </Animatable.View>

            <Animatable.View animation="fadeInUp" delay={100}>
              <View style={[appStyles.mBMd, styles.tabHeader]}>
                {renderTabHeader()}
              </View>
            </Animatable.View>

            {isCurrentHandicap ? renderCurrentHandicap() : renderAverageScore()}
          </View>

          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              style={[appStyles.mTSm]}
              text="quiz.cta.done"
              backgroundColor={
                !selectedAverageScore && isNaN(handicap) ? GREY : 'white'
              }
              disabled={!selectedAverageScore && isNaN(handicap)}
              onPress={updateUserHandicapAndTargetScore}
              loading={loading}
              centered
              DINbold
            />
          </Animatable.View>
        </View>
      </SafeAreaView>
      <Selector
        ref={sheetRef}
        type="handicap"
        value={!isNaN(handicap) ? handicap : 16}
        onChange={onSelectorChange}
        onCloseEnd={() => setHandicap(!isNaN(handicap) ? handicap : 16)}
      />
    </>
  );
};

const styles = StyleSheet.create({
  tabHeader: {
    backgroundColor: '#ffffff33',
    width: '100%',
    borderRadius: wp('20%'),
    borderWidth: wp('0.3%'),
    height: hp('7%'),
    padding: 10,
  },
  headerItem: {
    width: '50%',
    height: '100%',
  },
  iconContainer: {
    width: moderateScale(40),
    height: moderateScale(40),
    right: 15,
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeIcon: {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    width: moderateScale(36),
    height: moderateScale(36),
    borderRadius: moderateScale(18),
  },
});

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizHandicap);
