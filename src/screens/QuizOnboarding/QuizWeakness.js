import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import BackButton from 'components/BackButton';

const QuizWeakness = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const weakestAreaProfile = route.params?.weakestArea
    ? route.params?.weakestArea?.split(', ')
    : [];
  const [weaknesses, setWeaknesses] = useState(
    fromGameProfile
      ? weakestAreaProfile
      : user.golferProfile?.weakestArea?.split(', ') ||
          quiz.weaknesses?.split(', ') ||
          [],
  );
  const [loading, setLoading] = useState(false);
  const validated = weaknesses?.length;

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return weaknesses?.includes(type) ? 'white' : GREY;
      case 'border':
        return weaknesses?.includes(type) ? GREEN : GREY;
      case 'background':
        return weaknesses?.includes(type) ? GREEN : 'transparent';
      default:
        break;
    }
  };

  useEffect(() => {
    if (route.params?.origin === 'Setting') {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const updateUserWeaknesses = async () => {
    if (fromGameProfile) {
      route.params?.setWeakestArea(weaknesses.join(', '));
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({weaknesses: weaknesses.join(', ')});
      // Update user in backend if this is an edit
      if (isEdit) {
        setLoading(true);

        try {
          // Make request to update user's weaknesses
          const updatedUser = await updateUser({
            weakestArea: weaknesses.join(', '),
          });
          // Update user in redux
          addCurrentUser(updatedUser);
          // Stop loading state and navigate to next screen
          setLoading(false);
        } catch (error) {
          setLoading(false);
          return showToast({
            type: 'error',
            message: t('An_error_occurred_updating_your_weaknesses'),
          });
        }
      }

      navigation.navigate('QuizFear');
    }
  };

  const updateWeaknesses = weakness => {
    // Check to see if incoming weakness exists
    const hasWeakness = weaknesses?.includes(weakness);
    if (hasWeakness) {
      // Remove weakness for deselect
      setWeaknesses(values => values.filter(value => value !== weakness));
    } else {
      // Add weakness for select
      setWeaknesses(values => [...values, weakness]);
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text style={[appStyles.sm, appStyles.white, appStyles.textCenter]}>
              {`${t('quiz.strength.what_is_the')} `}
              <Text style={[appStyles.bold]}>quiz.weakness.weakness</Text>
              {` ${t('quiz.strength.part_of_your_game')}`}
            </Text>
            <Text
              style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
            >
              quiz.strength.select_all_that_apply
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.weakness.supporting_copy.driving"
              textColor={getButtonActiveColor('Driving', 'text')}
              borderColor={getButtonActiveColor('Driving', 'border')}
              backgroundColor={getButtonActiveColor('Driving', 'background')}
              onPress={() => updateWeaknesses('Driving')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.weakness.supporting_copy.approach"
              textColor={getButtonActiveColor('Approach', 'text')}
              borderColor={getButtonActiveColor('Approach', 'border')}
              backgroundColor={getButtonActiveColor('Approach', 'background')}
              onPress={() => updateWeaknesses('Approach')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.weakness.supporting_copy.around_the_green"
              textColor={getButtonActiveColor('Around the Green', 'text')}
              borderColor={getButtonActiveColor('Around the Green', 'border')}
              backgroundColor={getButtonActiveColor(
                'Around the Green',
                'background',
              )}
              onPress={() => updateWeaknesses('Around the Green')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.weakness.supporting_copy.putting"
              textColor={getButtonActiveColor('Putting', 'text')}
              borderColor={getButtonActiveColor('Putting', 'border')}
              backgroundColor={getButtonActiveColor('Putting', 'background')}
              onPress={() => updateWeaknesses('Putting')}
              disabled={loading}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={500}>
          <Button
            text={fromGameProfile ? 'common.update' : 'common.next'}
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={updateUserWeaknesses}
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizWeakness);
