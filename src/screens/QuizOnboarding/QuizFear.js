import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY, GREEN} from 'config';
import {showToast} from 'utils/toast';
import BackButton from 'components/BackButton';
import {getStartDeeperInsightScreen} from 'utils/commonVariable';

const QuizFear = ({addCurrentUser, navigation, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const fromGameProfile = route.params?.origin === 'Setting';
  const [fears, setFears] = useState(
    fromGameProfile
      ? route.params?.mostScaredShot?.split(', ') || []
      : user.golferProfile?.mostScaredShot?.split(', ') ||
          quiz.mostScaredShot?.split(', ') ||
          [],
  );
  const [loading, setLoading] = useState(false);
  const validated = fears?.length;

  const completeOnboarding = async () => {
    // Update quiz in redux
    updateQuiz({mostScaredShot: fears.join(', ')});

    setLoading(true);

    try {
      // Make request to update user
      const updatedUser = await updateUser({
        handed: quiz.handed,
        maximumDriverDistance: quiz.driveLength,
        strongestArea: quiz.strengths,
        weakestArea: quiz.weaknesses,
        mostScaredShot: fears.join(', '),
        onboardingCompleteSteps: {
          handed: true,
          maximumDriverDistance: true,
          strongestArea: true,
          weakestArea: true,
          mostScaredShot: true,
        },
        onboardingComplete: true,
      });
      // Update user in redux
      addCurrentUser(updatedUser);
      // Stop loading state and navigate to next screen
      setLoading(false);
      if (getStartDeeperInsightScreen() === 'StartDeeperDrill') {
        navigation.navigate('App');
      } else {
        navigation.navigate('Insights', {
          screen: 'Insights',
          params: {
            origin: 'DeeperInsight',
          },
        });
      }
    } catch (error) {
      setLoading(false);
      // Navigate to Home
      navigation.navigate('Home');
      return showToast({
        type: 'error',
        message: 'An error occurred with your onboarding',
      });
    }
  };

  useEffect(() => {
    if (route.params?.origin === 'Setting') {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const getButtonActiveColor = (type, prop) => {
    switch (prop) {
      case 'text':
        return fears?.includes(type) ? 'white' : GREY;
      case 'border':
        return fears?.includes(type) ? GREEN : GREY;
      case 'background':
        return fears?.includes(type) ? GREEN : 'transparent';
      default:
        break;
    }
  };

  const updateUserFearShot = async () => {
    if (fromGameProfile) {
      route.params?.setMostScaredShot(fears.join(', '));
      navigation.goBack();
    } else {
      // Update quiz in redux
      updateQuiz({mostScaredShot: fears.join(', ')});

      navigation.navigate('Home');
    }
  };

  const updateFears = fear => {
    // Check to see if incoming weakness exists
    const hasWeakness = fears?.includes(fear);
    if (hasWeakness) {
      // Remove weakness for deselect
      setFears(values => values.filter(value => value !== fear));
    } else {
      // Add weakness for select
      setFears(values => [...values, fear]);
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.mTLg, appStyles.pHSm]}>
        <View style={appStyles.flex}>
          <Animatable.View animation="fadeInUp">
            <Text
              style={[
                appStyles.sm,
                appStyles.white,
                appStyles.textCenter,
                isEdit && appStyles.mBMd,
              ]}
            >
              quiz.fear.headline
            </Text>
            <Text
              style={[
                appStyles.sm,
                appStyles.white,
                appStyles.textCenter,
                appStyles.mBMd,
              ]}
            >
              Select all that apply.
            </Text>
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={100}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.fear.supporting_copy.narrow_airway"
              textColor={getButtonActiveColor('Narrow Fairway', 'text')}
              borderColor={getButtonActiveColor('Narrow Fairway', 'border')}
              backgroundColor={getButtonActiveColor(
                'Narrow Fairway',
                'background',
              )}
              onPress={() => updateFears('Narrow Fairway')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={200}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.fear.supporting_copy.doglegs"
              textColor={getButtonActiveColor('Doglegs', 'text')}
              borderColor={getButtonActiveColor('Doglegs', 'border')}
              backgroundColor={getButtonActiveColor('Doglegs', 'background')}
              onPress={() => updateFears('Doglegs')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={300}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.fear.supporting_copy.water_in_play"
              textColor={getButtonActiveColor('Water in Play', 'text')}
              borderColor={getButtonActiveColor('Water in Play', 'border')}
              backgroundColor={getButtonActiveColor(
                'Water in Play',
                'background',
              )}
              onPress={() => updateFears('Water in Play')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={400}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.fear.supporting_copy.fairway_bunkers"
              textColor={getButtonActiveColor('Fairway Bunkers', 'text')}
              borderColor={getButtonActiveColor('Fairway Bunkers', 'border')}
              backgroundColor={getButtonActiveColor(
                'Fairway Bunkers',
                'background',
              )}
              onPress={() => updateFears('Fairway Bunkers')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={500}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.fear.supporting_copy.greenside_bunkers"
              textColor={getButtonActiveColor('Greenside Bunkers', 'text')}
              borderColor={getButtonActiveColor('Greenside Bunkers', 'border')}
              backgroundColor={getButtonActiveColor(
                'Greenside Bunkers',
                'background',
              )}
              onPress={() => updateFears('Greenside Bunkers')}
              disabled={loading}
            />
          </Animatable.View>
          <Animatable.View animation="fadeInUp" delay={600}>
            <Button
              style={[appStyles.mBSm]}
              text="quiz.fear.supporting_copy.fast_greens"
              textColor={getButtonActiveColor('Fast Greens', 'text')}
              borderColor={getButtonActiveColor('Fast Greens', 'border')}
              backgroundColor={getButtonActiveColor(
                'Fast Greens',
                'background',
              )}
              onPress={() => updateFears('Fast Greens')}
              disabled={loading}
            />
          </Animatable.View>
        </View>

        <Animatable.View animation="fadeInUp" delay={700}>
          <Button
            text={fromGameProfile ? 'common.update' : 'quiz.cta.complete'}
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            onPress={
              isEdit || fromGameProfile
                ? updateUserFearShot
                : completeOnboarding
            }
            loading={loading}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizFear);
