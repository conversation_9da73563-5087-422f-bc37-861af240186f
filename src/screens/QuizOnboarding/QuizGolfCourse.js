import React, {useState, useEffect, useRef} from 'react';
import {
  KeyboardAvoidingView,
  View,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useSelector} from 'react-redux';
import * as Animatable from 'react-native-animatable';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import GolfCourseSearch from 'components/GolfCourseSearch';
import Button from 'components/Button';
import LoadingOverlay from 'components/LoadingOverlay';
import HeaderRightButton from 'components/HeaderRightButton';

import {addCurrentUser} from 'reducers/user';
import {updateQuiz} from 'reducers/quiz';
import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import BackButton from 'components/BackButton';
import analytics from '@react-native-firebase/analytics';
import {SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';

const KEYBOARD_SHOW_BUTTON_MARGIN_BOTTOM = 100;

const QuizGolfCourse = ({navigation, addCurrentUser, route, updateQuiz}) => {
  const quiz = useSelector(state => state.quiz?.quiz);
  const user = useSelector(state => state.user);
  const isEdit = route.params?.isEdit;
  const [loading, setLoading] = useState(false);
  const fromGameProfile = route.params?.origin === 'Setting';
  const [golfCourse, setGolfCourse] = useState(
    fromGameProfile ? route.params?.homeCourse : user.golferProfile?.homeCourse,
  );
  const [idCourse, setIdCourse] = useState(
    fromGameProfile
      ? route.params?.iGolfCourseId
      : user.golferProfile?.iGolfCourseId,
  );

  useEffect(() => {
    // Render exit button if onboarding is being edited
    if (
      (user.onboardingComplete ||
        user.golferProfile?.homeCourse ||
        quiz.homeCourse) &&
      !fromGameProfile
    ) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.exit"
            onPress={() =>
              route.params?.origin === 'home'
                ? navigation.navigate('App')
                : route.params?.origin === 'settings'
                ? navigation.navigate('Settings')
                : navigation.navigate('Profile')
            }
          />
        ),
      });
    } else if (route?.params?.fromOnboarding) {
      navigation.setOptions({
        headerRight: () => (
          <HeaderRightButton
            text="common.skip"
            onPress={() => {
              navigation.navigate('QuizYearsOfExperience', {
                origin:
                  route.params?.origin === 'home'
                    ? 'home'
                    : route.params?.origin
                    ? 'profile'
                    : null,
                isEdit,
              });
            }}
          />
        ),
      });
    }

    if (route.params?.origin === 'settings') {
      navigation.setOptions({
        headerLeft: () => null,
      });
    }

    if (fromGameProfile) {
      navigation.setOptions({
        headerLeft: () => <BackButton onPress={() => navigation.goBack()} />,
      });
    }
  }, []);

  const updateHomeCourseProfile = () => {
    route.params?.setHomeCourse(golfCourse);
    route.params?.setIGolfCourseId(idCourse);
    navigation.goBack();
  };

  const GA_logOnboardingGolfCourse = homeCourse => {
    try {
      analytics().logEvent('user_onboarding_home_course', {
        home_course: homeCourse + '',
        screen_name: `onboarding - home course`,
        screen_type: SCREEN_TYPES.ONBOARDING,
        page_name: `onboarding - home course`,
        page_type: SCREEN_TYPES.ONBOARDING,
        page_category: SCREEN_CLASS.SIGNUP,
      });
      analytics().setUserProperty('home_course', homeCourse);
    } catch (error) {
      console.log(error);
    }
  };

  const updateUserHomeCourse = async course => {
    if (fromGameProfile) {
      setGolfCourse(course?.courseName || '');
      setIdCourse(course?.idCourse || '');
    } else {
      // Update quiz in redux
      updateQuiz({
        homeCourse: course.courseName,
        iGolfCourseId: course.idCourse,
      });
      // Update user in backend if this is an edit
      if (isEdit) {
        setLoading(true);

        try {
          // Make request to update user's home course
          const updatedUser = await updateUser({
            homeCourse: course.courseName,
            iGolfCourseId: course.idCourse,
          });
          // Update user in redux
          addCurrentUser(updatedUser);
          // Stop loading state and navigate to next screen
          setLoading(false);
        } catch (error) {
          setLoading(false);
          return showToast({
            type: 'error',
            message: t('An_error_occurred_updating_your_home_course'),
          });
        }
      } else {
        let homeCourse = course?.courseName;
        if (typeof homeCourse === 'string' && homeCourse?.length > 36) {
          homeCourse = homeCourse.substr(0, 36);
        }
        GA_logOnboardingGolfCourse(homeCourse);
      }

      if (route.params?.origin === 'settings') {
        navigation.navigate('Settings');
      } else {
        navigation.navigate('QuizYearsOfExperience', {
          origin:
            route.params?.origin === 'home'
              ? 'home'
              : route.params?.origin
              ? 'profile'
              : null,
          isEdit,
        });
      }
    }
  };

  const contentQuizGolfCourse = () => {
    return (
      <View style={appStyles.flex}>
        <FocusAwareStatusBar barStyle={'light-content'} />
        {loading ? <LoadingOverlay transparent={loading} /> : null}
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View
            style={[
              appStyles.flex,
              appStyles.mTLg,
              appStyles.pHSm,
              {justifyContent: 'space-around'},
            ]}
          >
            <View style={[appStyles.flex]}>
              <Animatable.View animation="fadeInUp">
                <Text
                  style={[
                    appStyles.white,
                    appStyles.textCenter,
                    appStyles.mBMd,
                  ]}
                >
                  quiz.course.headline
                </Text>
              </Animatable.View>
              <Animatable.View
                style={appStyles.flex}
                animation="fadeInUp"
                delay={100}
              >
                <GolfCourseSearch
                  golfCourse={golfCourse}
                  setCourse={updateUserHomeCourse}
                  disabled={loading}
                  fromGameProfile={fromGameProfile}
                />
              </Animatable.View>
            </View>

            {user.golferProfile?.homeCourse ||
            quiz.homeCourse ||
            fromGameProfile ? (
              <Animatable.View animation="fadeInUp" delay={200}>
                <Button
                  text={
                    route.params?.origin === 'settings' || fromGameProfile
                      ? 'common.update'
                      : 'quiz.cta.next'
                  }
                  textColor="black"
                  backgroundColor="white"
                  borderColor="white"
                  onPress={() =>
                    fromGameProfile
                      ? updateHomeCourseProfile()
                      : navigation.navigate('QuizYearsOfExperience', {
                          origin:
                            route.params?.origin === 'home'
                              ? 'home'
                              : route.params?.origin
                              ? 'profile'
                              : null,
                          isEdit,
                        })
                  }
                  centered
                  DINbold
                />
              </Animatable.View>
            ) : null}
          </View>
        </TouchableWithoutFeedback>
      </View>
    );
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      {Platform.OS === 'ios' ? (
        <KeyboardAvoidingView
          behavior="padding"
          style={appStyles.flex}
          keyboardVerticalOffset={KEYBOARD_SHOW_BUTTON_MARGIN_BOTTOM}
        >
          {contentQuizGolfCourse()}
        </KeyboardAvoidingView>
      ) : (
        contentQuizGolfCourse()
      )}
    </SafeAreaView>
  );
};

const mapDispatchToProps = {addCurrentUser, updateQuiz};

export default connect(null, mapDispatchToProps)(QuizGolfCourse);
