import React from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import BG_Home from 'assets/imgs/intro/bg_home.png';
import BG_Home_Regions from 'assets/imgs/intro/bg_home_regions.png';
import SubTract_Home from 'assets/imgs/intro/subtract_home.png';
import SubTract_Home_Regions from 'assets/imgs/intro/subtract_home_regions.png';
import BG_Clubhouse from 'assets/imgs/intro/bg_clubhouse.png';
import BG_Clubhouse_Regions from 'assets/imgs/intro/bg_clubhouse_regions.png';
import Subtract_clubhouse from 'assets/imgs/intro/subtract_clubhouse.png';
import Subtract_clubhouse_regions from 'assets/imgs/intro/subtract_clubhouse_regions.png';
import BG_Play from 'assets/imgs/intro/bg_play.png';
import BG_Play_CA from 'assets/imgs/intro/bg_play_CA.png';
import BG_Play_Regions from 'assets/imgs/intro/bg_play_regions.png';
import SubTract_Play from 'assets/imgs/intro/subtract_play.png';
import SubTract_Play_Regions from 'assets/imgs/intro/subtract_play_regions.png';
import BG_Shop from 'assets/imgs/intro/bg_shop.png';
import Subtract_shop from 'assets/imgs/intro/subtract_shop.png';
import BG_Rewards from 'assets/imgs/intro/bg_rewards.png';
import SubTract_Rewards from 'assets/imgs/intro/subtract_rewards.png';

import BG_Home_xxl from 'assets/imgs/intro/bg_home_xxl.png';
import BG_Home_Regions_xxl from 'assets/imgs/intro/bg_home_regions_xxl.png';
import SubTract_Home_xxl from 'assets/imgs/intro/subtract_home_xxl.png';
import SubTract_Home_Regions_xxl from 'assets/imgs/intro/subtract_home_regions_xxl.png';
import BG_Clubhouse_xxl from 'assets/imgs/intro/bg_clubhouse_xxl.png';
import BG_Clubhouse_Regions_xxl from 'assets/imgs/intro/bg_clubhouse_regions_xxl.png';
import Subtract_clubhouse_xxl from 'assets/imgs/intro/subtract_clubhouse_xxl.png';
import Subtract_clubhouse_Regions_xxl from 'assets/imgs/intro/subtract_clubhouse_regions_xxl.png';
import BG_Play_xxl from 'assets/imgs/intro/bg_play_xxl.png';
import BG_Play_CA_xxl from 'assets/imgs/intro/bg_play_CA_xxl.png';
import BG_Play_Regions_xxl from 'assets/imgs/intro/bg_play_regions_xxl.png';
import SubTract_Play_xxl from 'assets/imgs/intro/subtract_play_xxl.png';
import SubTract_Play_Regions_xxl from 'assets/imgs/intro/subtract_play_regions_xxl.png';
import BG_Shop_xxl from 'assets/imgs/intro/bg_shop_xxl.png';
import Subtract_shop_xxl from 'assets/imgs/intro/subtract_shop_xxl.png';
import BG_Rewards_xxl from 'assets/imgs/intro/bg_rewards_xxl.png';
import SubTract_Rewards_xxl from 'assets/imgs/intro/subtract_rewards_xxl.png';

import BG_Home_s from 'assets/imgs/intro/bg_home_s.png';
import BG_Home_Regions_s from 'assets/imgs/intro/bg_home_regions_s.png';
import SubTract_Home_s from 'assets/imgs/intro/subtract_home_s.png';
import SubTract_Home_Regions_s from 'assets/imgs/intro/subtract_home_regions_s.png';
import BG_Clubhouse_s from 'assets/imgs/intro/bg_clubhouse_s.png';
import BG_Clubhouse_Regions_s from 'assets/imgs/intro/bg_clubhouse_regions_s.png';
import Subtract_clubhouse_s from 'assets/imgs/intro/subtract_clubhouse_s.png';
import Subtract_clubhouse_Regions_s from 'assets/imgs/intro/subtract_clubhouse_regions_s.png';
import BG_Play_s from 'assets/imgs/intro/bg_play_s.png';
import BG_Play_CA_s from 'assets/imgs/intro/bg_play_CA_s.png';
import BG_Play_Regions_s from 'assets/imgs/intro/bg_play_regions_s.png';
import SubTract_Play_s from 'assets/imgs/intro/subtract_play_s.png';
import SubTract_Play_Regions_s from 'assets/imgs/intro/subtract_play_regions_s.png';
import BG_Shop_s from 'assets/imgs/intro/bg_shop_s.png';
import Subtract_shop_s from 'assets/imgs/intro/subtract_shop_s.png';
import BG_Rewards_s from 'assets/imgs/intro/bg_rewards_s.png';
import SubTract_Rewards_s from 'assets/imgs/intro/subtract_rewards_s.png';
import {Platform} from 'react-native';
import {checkMainCountry, COUNTRY_CODE} from 'utils/constant';
const width = wp(100);
const height = hp(100);
export const checkRatioScreen = ({
  pageName,
  isSubtrack,
  userCountry = null,
}) => {
  switch (getSizeScreen()) {
    case 'XXL':
      return screenXXL(pageName, isSubtrack, userCountry);
    case 'S':
      return screenS(pageName, isSubtrack, userCountry);
    default:
      return screenL(pageName, isSubtrack, userCountry);
  }
};

export const getScaleImage = () => {
  switch (getSizeScreen()) {
    case 'XXL':
      return 0.83;
    case 'S':
      return Platform.OS === 'ios' ? 0.79 : 0.83;
    default:
      return Platform.OS === 'ios' ? 0.79 : 0.8;
  }
};

/**
 * Calculate the left position for the selected tab indicator based on tab index
 * @param {number} index - Index of the selected tab (0-based)
 * @param {string} userCountry - User's country code to determine UI layout
 * @return {number} - Left position of the tab indicator in pixels
 */
export const getLeftSelectedTab = (index, userCountry) => {
  // Determine if the user is from a main country (affects number of tabs)
  const isMainCountry = checkMainCountry(userCountry);

  // Number of tabs in the navigation bar (5 for main countries, 3 for others)
  const TAB_COUNT = isMainCountry ? 5 : 3;

  // Horizontal padding applied to both sides of the tab bar (16px)
  const twoSideTabPadding = 16;

  // Get current scale ratio for the background image
  const scaleRatio = getScaleImage();

  // Width of the background image after scaling
  const imageBehindWidth = wp(100) * scaleRatio;

  // Calculate horizontal padding on each side after scaling the background
  const padding = (wp(100) - imageBehindWidth) / 2;

  // Total width of the tab bar area after scaling
  const tabWidth = (wp(100) - twoSideTabPadding) * scaleRatio;

  /**
   * Calculate spacing units:
   * - Each tab occupies 2 units of space
   * - spaceInTabUnit represents half a tab's width
   */
  const spaceInTabUnit = tabWidth / (TAB_COUNT * 2);

  // Width between centers of adjacent tabs (2 units)
  const spaceBetweenTab = spaceInTabUnit * 2;

  // Radius of the tab indicator (33px)
  const indicatorRadius = 33;

  /**
   * Formula to calculate the left position:
   * 1. padding: Start with the side padding from screen edge to background image
   * 2. + (twoSideTabPadding/2) * scaleRatio: Add half of the tab bar's side padding (scaled)
   * 3. - indicatorRadius: Subtract the indicator radius to align its center with the tab
   * 4. + spaceInTabUnit: Add one unit to reach the first tab position
   * 5. + spaceBetweenTab * index: Add spacing based on selected tab index
   */
  const leftPadding =
    padding +
    (twoSideTabPadding / 2) * scaleRatio -
    indicatorRadius +
    spaceInTabUnit +
    spaceBetweenTab * index;

  return leftPadding;
};

const BG_Top_padding = Platform.OS === 'ios' ? hp(4) : hp(2.5);

export const getTranYImage = () => {
  const imageBehindHeight = hp(100) * getScaleImage();
  const translateY = (hp(100) - imageBehindHeight) / 2 / getScaleImage();

  return translateY - BG_Top_padding;
};

/**
 * Calculate the bottom position for the selected tab indicator
 * @return {number} - Bottom position of the tab indicator
 */
export const getBottomSelectedTab = () => {
  // Radius of the indicator (33px)
  const indicatorRadius = 33;

  // Height of the background image after scaling
  const scaleRatio = getScaleImage();
  const imageBehindHeight = hp(100) * scaleRatio;

  // Determine current screen size
  const screenSize = getSizeScreen();

  /**
   * Coefficients to calculate the distance from bottom of background image to center of tab icon
   * - XXL (large screen): 956px height, 66px distance
   * - S (small screen): 568px height, 54px distance
   * - XSL/XL/L (normal screens): 844px height, 66px distance
   *
   * These numbers are calculated based on the original design of the background image
   * and the position of tab icons on each screen type.
   */
  const sizeFactors = {
    XXL: 66 / 956, // ~0.069 - Distance ratio on large screens
    S: 54 / 568, // ~0.095 - Distance ratio on small screens
    XSL: 66 / 844, // ~0.078 - Distance ratio on normal screens
    XL: 66 / 844, // ~0.078 - Distance ratio on normal screens
    L: 66 / 844, // ~0.078 - Distance ratio on normal screens
  };

  // Get the coefficient corresponding to screen size, or use 'L' size if not found
  const sizeFactor = sizeFactors[screenSize] || sizeFactors.L;

  /**
   * Formula to calculate the bottom position:
   * 1. hp(100) - imageBehindHeight: Space from screen bottom to background image bottom
   * 2. + sizeFactor * imageBehindHeight: Add distance from image bottom to tab center
   * 3. - indicatorRadius: Subtract indicator radius to position it correctly
   * 4. - BG_Top_padding * scaleRatio: Adjust according to background top padding
   */
  return (
    hp(100) -
    imageBehindHeight +
    sizeFactor * imageBehindHeight -
    indicatorRadius -
    BG_Top_padding * scaleRatio
  );
};

// Calculates the height of the home subtract element based on screen size.
// For 'XXL', it adjusts the height by scaling and adding double the top padding.
// For 'S', it adjusts the height by scaling and adding triple the top padding as per the Figma design.
export const getHomeSubTractHeight = () => {
  switch (getSizeScreen()) {
    case 'XXL':
      return hp(100) * getScaleImage() + BG_Top_padding * 2; // For XXL screens, height is adjusted with double the top padding
    case 'S':
      return Platform.OS === 'ios'
        ? hp(100) * getScaleImage() + BG_Top_padding * 3
        : hp(94); // For small devices, the subtract image height is 3x the padding as per Figma design
    default:
      return Platform.OS === 'ios' ? hp(100) : hp(97);
  }
};

export const getTopTooltipHome = userCountry => {
  const screenSize = getSizeScreen();
  // Height of the background image after scaling
  const scaleRatio = getScaleImage();
  const imageBehindHeight = hp(100) * scaleRatio;

  // Define padding values for different screen sizes
  // These ratios (66/784, 60/426, 70/667) are derived from Figma design specifications
  // They represent the proportional padding needed for different screen dimensions
  const topPaddings = {
    XXL: BG_Top_padding + (imageBehindHeight * 66) / 784, // Ratio from Figma for XXL screens
    S: BG_Top_padding + (imageBehindHeight * 60) / 426, // Ratio from Figma for S screens
    default: BG_Top_padding + (imageBehindHeight * 70) / 667, // Ratio from Figma for default screen size
  };

  // Return the screen-size specific value or default if size not found
  return topPaddings[screenSize] || topPaddings.default;
};

export const getLeftTooltipIndicator = () => {
  const screenSize = getSizeScreen();
  // Width of the background image after scaling
  const scaleRatio = getScaleImage();
  const imageBehindWidth = wp(100) * scaleRatio;
  const spaceFromSubtract = (wp(100) * (1 - scaleRatio)) / 2;

  // Define padding values for different screen sizes
  // These ratios (88/100, 87/100) are derived from Figma design specifications
  // They represent the proportional padding needed for different screen dimensions
  const leftPaddings = {
    XXL: spaceFromSubtract + (imageBehindWidth * 88) / 100,
    S: spaceFromSubtract + (imageBehindWidth * 87) / 100,
    default: spaceFromSubtract + (imageBehindWidth * 87) / 100, // Ratio from Figma for default screen size
  };

  // Return the screen-size specific value or default if size not found
  return leftPaddings[screenSize] || leftPaddings.default;
};

export const getTopTooltipClubHouse = () => {
  const screenSize = getSizeScreen();
  // Height of the background image after scaling
  const scaleRatio = getScaleImage();
  const imageBehindHeight = hp(100) * scaleRatio;

  // Define padding values for different screen sizes
  // These ratios (405/784, 205/426, 392/667) are derived from Figma design specifications
  // They represent the proportional padding needed for different screen dimensions
  const topPaddings = {
    XXL: BG_Top_padding + (imageBehindHeight * 405) / 784, // Ratio from Figma for XXL screens
    S: BG_Top_padding + (imageBehindHeight * 205) / 426, // Ratio from Figma for S screens
    default: BG_Top_padding + (imageBehindHeight * 392) / 667, // Ratio from Figma for default screen size
  };

  // Return the screen-size specific value or default if size not found
  return topPaddings[screenSize] || topPaddings.default;
};

export const getTopTooltipPlay = () => {
  const screenSize = getSizeScreen();
  // Height of the background image after scaling
  const scaleRatio = getScaleImage();
  const imageBehindHeight = hp(100) * scaleRatio;

  // Define padding values for different screen sizes
  // These ratios (255/784, 165/426, 247/667) are derived from Figma design specifications
  // They represent the proportional padding needed for different screen dimensions
  const topPaddings = {
    XXL: BG_Top_padding + (imageBehindHeight * 255) / 784, // Ratio from Figma for XXL screens
    S: BG_Top_padding + (imageBehindHeight * 165) / 426, // Ratio from Figma for S screens
    default: BG_Top_padding + (imageBehindHeight * 247) / 667, // Ratio from Figma for default screen size
  };

  // Return the screen-size specific value or default if size not found
  return topPaddings[screenSize] || topPaddings.default;
};

export const getTopTooltipShop = () => {
  const screenSize = getSizeScreen();
  // Height of the background image after scaling
  const scaleRatio = getScaleImage();
  const imageBehindHeight = hp(100) * scaleRatio;

  // Define padding values for different screen sizes
  // These ratios (488/784, 252/426, 464/667) are derived from Figma design specifications
  // They represent the proportional padding needed for different screen dimensions
  const topPaddings = {
    XXL: BG_Top_padding + (imageBehindHeight * 488) / 784, // Ratio from Figma for XXL screens
    S: BG_Top_padding + (imageBehindHeight * 252) / 426, // Ratio from Figma for S screens
    default: BG_Top_padding + (imageBehindHeight * 464) / 667, // Ratio from Figma for default screen size
  };

  // Return the screen-size specific value or default if size not found
  return topPaddings[screenSize] || topPaddings.default;
};

export const getTopTooltipRewards = () => {
  const screenSize = getSizeScreen();
  // Height of the background image after scaling
  const scaleRatio = getScaleImage();
  const imageBehindHeight = hp(100) * scaleRatio;

  // Define padding values for different screen sizes
  // These ratios (184/784, 120/426, 175/667) are derived from Figma design specifications
  // They represent the proportional padding needed for different screen dimensions
  const topPaddings = {
    XXL: BG_Top_padding + (imageBehindHeight * 184) / 784, // Ratio from Figma for XXL screens
    S: BG_Top_padding + (imageBehindHeight * 120) / 426, // Ratio from Figma for S screens
    default: BG_Top_padding + (imageBehindHeight * 175) / 667, // Ratio from Figma for default screen size
  };

  // Return the screen-size specific value or default if size not found
  return topPaddings[screenSize] || topPaddings.default;
};

export const getBottomFooter = insets => {
  const screenSize = getSizeScreen();

  // Default value for iOS (bottom inset + 2)
  const iosValue = (insets?.bottom || 0) + 2;

  // Lookup table for Android values based on screen size
  const androidValues = {
    XXL: 22,
    XL: 42,
    L: 46,
    XSL: 46,
    S: 22,
  };

  // Return value based on platform
  return Platform.OS === 'android'
    ? androidValues[screenSize] || 22 // Default value of 22 if screenSize not found
    : iosValue; // Return iOS value for iOS platform
};
const getSizeScreen = () => {
  if (width / height <= 0.4614) {
    //0.46137339
    //iphone 15 pro max, 15 plus, 14 pro max
    return 'XXL';
  }
  if (width / height <= 0.4619) {
    //0.4618226600985222
    //iphone 12 mini
    return 'XL';
  }
  if (width / height <= 0.46206) {
    //0.46205357142857145
    //iphone XR, iPhone XS Max
    return 'XSL';
  }
  if (width / height <= 0.5029) {
    //0.462085
    //iphone 14, 13 pro, 12 pro, 12
    return 'L';
  }
  if (width / height > 0.5029) {
    //iphone 8, SE, iphone
    return 'S';
  }
};

//s10: 0.47368
//s20: 0.45
//note 20 ultra: 0.4665
//Samsung S21 Ultra 5G: 0.4496

export const TYPE_INTRO_PAGE = {
  HOME: 'HOME',
  CLUB_HOUSE: 'CLUB_HOUSE',
  PLAY: 'PLAY',
  SHOP: 'SHOP',
  REWARDS: 'REWARDS',
};

// Generic function to get background image based on screen size
const getBackgroundImage = (screenSize, pageName, isSubtrack, userCountry) => {
  // Define all assets by screen size
  const assets = {
    XXL: {
      subtract: {
        [TYPE_INTRO_PAGE.HOME]: {
          [COUNTRY_CODE.USA]: SubTract_Home_xxl,
          [COUNTRY_CODE.CAN]: SubTract_Home_xxl,
          default: SubTract_Home_Regions_xxl,
        },
        [TYPE_INTRO_PAGE.CLUB_HOUSE]: {
          [COUNTRY_CODE.USA]: Subtract_clubhouse_xxl,
          [COUNTRY_CODE.CAN]: Subtract_clubhouse_xxl,
          default: Subtract_clubhouse_Regions_xxl,
        },
        [TYPE_INTRO_PAGE.PLAY]: {
          [COUNTRY_CODE.USA]: SubTract_Play_xxl,
          [COUNTRY_CODE.CAN]: SubTract_Play_xxl,
          default: SubTract_Play_Regions_xxl,
        },
        [TYPE_INTRO_PAGE.SHOP]: {
          default: Subtract_shop_xxl,
        },
        default: {
          default: SubTract_Rewards_xxl,
        },
      },
      background: {
        [TYPE_INTRO_PAGE.HOME]: {
          [COUNTRY_CODE.USA]: BG_Home_xxl,
          [COUNTRY_CODE.CAN]: BG_Home_xxl,
          default: BG_Home_Regions_xxl,
        },
        [TYPE_INTRO_PAGE.CLUB_HOUSE]: {
          [COUNTRY_CODE.USA]: BG_Clubhouse_xxl,
          [COUNTRY_CODE.CAN]: BG_Clubhouse_xxl,
          default: BG_Clubhouse_Regions_xxl,
        },
        [TYPE_INTRO_PAGE.PLAY]: {
          [COUNTRY_CODE.USA]: BG_Play_xxl,
          [COUNTRY_CODE.CAN]: BG_Play_CA_xxl,
          default: BG_Play_Regions_xxl,
        },
        [TYPE_INTRO_PAGE.SHOP]: {
          default: BG_Shop_xxl,
        },
        default: {
          default: BG_Rewards_xxl,
        },
      },
    },
    L: {
      subtract: {
        [TYPE_INTRO_PAGE.HOME]: {
          [COUNTRY_CODE.USA]: SubTract_Home,
          [COUNTRY_CODE.CAN]: SubTract_Home,
          default: SubTract_Home_Regions,
        },
        [TYPE_INTRO_PAGE.CLUB_HOUSE]: {
          [COUNTRY_CODE.USA]: Subtract_clubhouse,
          [COUNTRY_CODE.CAN]: Subtract_clubhouse,
          default: Subtract_clubhouse_regions,
        },
        [TYPE_INTRO_PAGE.PLAY]: {
          [COUNTRY_CODE.USA]: SubTract_Play,
          [COUNTRY_CODE.CAN]: SubTract_Play,
          default: SubTract_Play_Regions,
        },
        [TYPE_INTRO_PAGE.SHOP]: {
          default: Subtract_shop,
        },
        default: {
          default: SubTract_Rewards,
        },
      },
      background: {
        [TYPE_INTRO_PAGE.HOME]: {
          [COUNTRY_CODE.USA]: BG_Home,
          [COUNTRY_CODE.CAN]: BG_Home,
          default: BG_Home_Regions,
        },
        [TYPE_INTRO_PAGE.CLUB_HOUSE]: {
          [COUNTRY_CODE.USA]: BG_Clubhouse,
          [COUNTRY_CODE.CAN]: BG_Clubhouse,
          default: BG_Clubhouse_Regions,
        },
        [TYPE_INTRO_PAGE.PLAY]: {
          [COUNTRY_CODE.USA]: BG_Play,
          [COUNTRY_CODE.CAN]: BG_Play_CA,
          default: BG_Play_Regions,
        },
        [TYPE_INTRO_PAGE.SHOP]: {
          default: BG_Shop,
        },
        default: {
          default: BG_Rewards,
        },
      },
    },
    S: {
      subtract: {
        [TYPE_INTRO_PAGE.HOME]: {
          [COUNTRY_CODE.USA]: SubTract_Home_s,
          [COUNTRY_CODE.CAN]: SubTract_Home_s,
          default: SubTract_Home_Regions_s,
        },
        [TYPE_INTRO_PAGE.CLUB_HOUSE]: {
          [COUNTRY_CODE.USA]: Subtract_clubhouse_s,
          [COUNTRY_CODE.CAN]: Subtract_clubhouse_s,
          default: Subtract_clubhouse_Regions_s,
        },
        [TYPE_INTRO_PAGE.PLAY]: {
          [COUNTRY_CODE.USA]: SubTract_Play_s,
          [COUNTRY_CODE.CAN]: SubTract_Play_s,
          default: SubTract_Play_Regions_s,
        },
        [TYPE_INTRO_PAGE.SHOP]: {
          default: Subtract_shop_s,
        },
        default: {
          default: SubTract_Rewards_s,
        },
      },
      background: {
        [TYPE_INTRO_PAGE.HOME]: {
          [COUNTRY_CODE.USA]: BG_Home_s,
          [COUNTRY_CODE.CAN]: BG_Home_s,
          default: BG_Home_Regions_s,
        },
        [TYPE_INTRO_PAGE.CLUB_HOUSE]: {
          [COUNTRY_CODE.USA]: BG_Clubhouse_s,
          [COUNTRY_CODE.CAN]: BG_Clubhouse_s,
          default: BG_Clubhouse_Regions_s,
        },
        [TYPE_INTRO_PAGE.PLAY]: {
          [COUNTRY_CODE.USA]: BG_Play_s,
          [COUNTRY_CODE.CAN]: BG_Play_CA_s,
          default: BG_Play_Regions_s,
        },
        [TYPE_INTRO_PAGE.SHOP]: {
          default: BG_Shop_s,
        },
        default: {
          default: BG_Rewards_s,
        },
      },
    },
  };

  // Get assets structure based on screen size
  const sizeAssets = assets[screenSize] || assets.L; // Fallback to L if size not found

  const pageSubtracts =
    sizeAssets.subtract[pageName] || sizeAssets.subtract.default;
  // If it is a subtrack, take the subtract image
  if (isSubtrack) {
    return pageSubtracts[userCountry] || pageSubtracts.default;
  }

  // If not subtrack, get a suitable background image
  const pageBackgrounds =
    sizeAssets.background[pageName] || sizeAssets.background.default;
  return pageBackgrounds[userCountry] || pageBackgrounds.default;
};

// Wrapper functions for each screen size
const screenXXL = (pageName, isSubtrack, userCountry) => {
  return getBackgroundImage('XXL', pageName, isSubtrack, userCountry);
};

const screenL = (pageName, isSubtrack, userCountry) => {
  return getBackgroundImage('L', pageName, isSubtrack, userCountry);
};

const screenS = (pageName, isSubtrack, userCountry) => {
  return getBackgroundImage('S', pageName, isSubtrack, userCountry);
};
