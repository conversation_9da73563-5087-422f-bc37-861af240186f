import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, BackHandler} from 'react-native';
import PagerView from 'react-native-pager-view';
import HomePage from './HomePage';
import ClubHousePage from './ClubHousePage';
import PlayPage from './PlayPage';
import ShopPage from './ShopPage';
import RewardsPage from './RewardsPage';
import {CommonActions, useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {SHOWED_INTRODUCE} from 'utils/constant';
import {updateUser} from 'requests/accounts';
import {addCurrentUser} from 'reducers/user';
import {useDispatch} from 'react-redux';
const TourUS = ({}) => {
  const [indexPage, setIndexPage] = useState(0);
  const refPagerView = useRef();
  const navigation = useNavigation();
  const dispatch = useDispatch();

  useEffect(() => {
    AsyncStorage.setItem(SHOWED_INTRODUCE, 'true');
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, []);

  const backAction = () => {
    return true;
  };
  const onNext = index => {
    if (index < 4) {
      const indexChange = index + 1;
      setIndexPage(indexChange);
      refPagerView.current?.setPage(indexChange);
    } else {
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
            {
              name: 'App',
              screen: 'Home',
            },
          ],
        }),
      );
      updateDefaultUserInfo();
    }
  };

  const updateDefaultUserInfo = async () => {
    const updatedUser = await updateUser({
      timePlayingGolf: '2 - 3',
      rpm: 3,
      weakestArea: 'Putting, Approach, Around the Green, Driving',
      favoriteTeamMembers: 'Tiger Woods, Rory McIlroy, Scottie Scheffler',
      misHit: 'Fat',
    });
    dispatch(addCurrentUser(updatedUser));
  };

  return (
    <PagerView
      ref={refPagerView}
      style={styles.pagerView}
      initialPage={0}
      scrollEnabled={false}
    >
      <HomePage onNext={onNext} indexPage={indexPage} />
      <ClubHousePage onNext={onNext} indexPage={indexPage} />
      <PlayPage onNext={onNext} indexPage={indexPage} />
      <ShopPage onNext={onNext} indexPage={indexPage} />
      <RewardsPage onNext={onNext} indexPage={indexPage} />
    </PagerView>
  );
};
const styles = StyleSheet.create({
  pagerView: {
    flex: 1,
  },
});
export default TourUS;
