import React from 'react';
import {View} from 'react-native';
import Text from 'components/Text';
import Ic_radiation from 'assets/imgs/intro/ic_radiation.svg';
import {getLeftSelectedTab, getBottomSelectedTab} from '../dimension';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import { useSelector } from 'react-redux';
const TabSelected = ({index, title}) => {
  const insets = useSafeAreaInsets();
  const user = useSelector(state => state.user);
  return (
    <View
      style={{
        position: 'absolute',
        bottom: getBottomSelectedTab(insets),
        left: getLeftSelectedTab(index, user?.userCountry),
        alignItems: 'center',
      }}
    >
      <Text
        white
        size={12}
        Din79Font
        weight={700}
        style={{
          position: 'absolute',
          top: -18,
          textTransform: 'uppercase',
          letterSpacing: 1.62,
          width: 100,
          textAlign: 'center',
        }}
      >
        {title}
      </Text>
      <Ic_radiation />
    </View>
  );
};

export default TabSelected;
