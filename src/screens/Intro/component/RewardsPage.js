import React, {useEffect, useState} from 'react';
import {View, StyleSheet, TouchableOpacity, Dimensions} from 'react-native';
import appStyles from 'styles/global';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Text from 'components/Text';
import IcPolygon from 'assets/imgs/intro/ic_polygon.svg';
import FooterIntro from './FooterIntro';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolation,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import TabSelected from './TabSelected';
import {useNavigation} from '@react-navigation/native';
import {
  TYPE_INTRO_PAGE,
  checkRatioScreen,
  getTopTooltipRewards,
  getHomeSubTractHeight,
  getScaleImage,
  getTranYImage,
} from '../dimension';
import {t} from 'i18next';
import {GA_logEvent, GA_logScreenViewV2} from 'utils/googleAnalytics';
import {
  GA_EVENT_NAME,
  SCREEN_TYPES,
  PAGE_CATEGORY,
  PAGE_NAME,
} from 'utils/constant';
import {useSelector} from 'react-redux';
const width = wp(100);
const RewardsPage = ({onNext, indexPage}) => {
  const user = useSelector(state => state?.user);
  const [isShow, setShow] = useState(false);
  const offset = useSharedValue(0);
  const navigation = useNavigation();
  const scaleImage = getScaleImage();
  const transY = getTranYImage();
  useEffect(() => {
    if (indexPage === 4) {
      GA_logScreenViewV2(
        PAGE_NAME.ONBOARDING_START,
        PAGE_CATEGORY.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
      );
      setTimeout(() => {
        setShow(true);
        offset.value = withTiming(1, {
          duration: 300,
          easing: Easing.elastic(1),
        });
      }, 300);
    }
  }, [indexPage]);

  const onPress = () => {
    navigation.navigate('IntroQuiz', {
      screen: 'UserQuiz',
      params: {
        origin: 'TourIntro',
      },
    });
    GA_logEvent(GA_EVENT_NAME.USER_ONBOARDING_START, {
      click_location: 'tour-intro',
      page_type: SCREEN_TYPES.ONBOARDING,
      page_category: PAGE_CATEGORY.ONBOARDING,
      page_name: PAGE_NAME.ONBOARDING_START,
      screen_type: SCREEN_TYPES.ONBOARDING,
    });
  };

  const styleImage = useAnimatedStyle(() => {
    const scale = interpolate(
      offset.value,
      [0, 1],
      [1, scaleImage],
      Extrapolation.CLAMP,
    );
    const translateY = interpolate(
      offset.value,
      [0, 1],
      [0, -transY],
      Extrapolation.CLAMP,
    );
    return {transform: [{scale}, {translateY}]};
  });
  const styleTooltip = useAnimatedStyle(() => {
    const translateX = interpolate(
      offset.value,
      [0, 1],
      [-width, 0],
      Extrapolation.CLAMP,
    );
    return {transform: [{translateX}]};
  });

  const renderCompleteMyProfile = () => {
    return (
      <Animated.View
        style={[
          {
            width: '100%',
            position: 'absolute',
            top: getTopTooltipRewards(),
          },
          styleTooltip,
        ]}
      >
        <View style={{left: width / 2 - 13.5, position: 'absolute'}}>
          <IcPolygon />
        </View>
        <View style={styles.viewContainerNotify}>
          <Text
            size={16}
            weight={400}
            style={{
              textAlign: 'center',
              color: 'rgba(0, 0, 0, 0.6)',
            }}
          >
            intro.view_your_taylormade_loyalty
          </Text>
          <Text
            size={16}
            weight={400}
            style={{
              textAlign: 'center',
              color: 'rgba(0, 0, 0, 0.6)',
              paddingTop: 10,
            }}
          >
            intro.to_get_started
          </Text>
          <TouchableOpacity style={styles.touchNotification} onPress={onPress}>
            <Text
              size={12}
              Din79Font
              weight={700}
              white
              style={{
                textTransform: 'uppercase',
                letterSpacing: 1,
              }}
            >
              intro.complete_my_profile
            </Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };
  return (
    <View
      key="Rewards"
      style={{
        backgroundColor: '#000',
        width: wp(100),
        height: hp(100),
        justifyContent: 'center',
      }}
    >
      <Animated.Image
        source={checkRatioScreen({
          pageName: TYPE_INTRO_PAGE.REWARDS,
          isSubtrack: false,
          userCountry: user?.userCountry,
        })}
        style={[
          {
            width: wp(100),
            height: hp(100),
          },
          styleImage,
        ]}
        resizeMode={'stretch'}
      />
      {isShow && (
        <Animated.Image
          source={checkRatioScreen({
            pageName: TYPE_INTRO_PAGE.REWARDS,
            isSubtrack: true,
            userCountry: user?.userCountry,
          })}
          style={{
            width: wp(100),
            height: getHomeSubTractHeight(),
            position: 'absolute',
            top: 0,
          }}
        />
      )}
      {isShow && <TabSelected index={4} title={t('app.tab_label.rewards')} />}
      {renderCompleteMyProfile()}
      {isShow && <FooterIntro index={4} onGotoHome={onNext} />}
    </View>
  );
};
const styles = StyleSheet.create({
  touchNotification: {
    backgroundColor: '#000',
    paddingVertical: 14,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 14,
    borderRadius: 24,
    ...appStyles.viewShadowLight,
  },
  viewContainerNotify: {
    backgroundColor: '#fff',
    borderRadius: 24,
    alignItems: 'center',
    marginTop: 11,
    marginHorizontal: 8,
    paddingVertical: 24,
    paddingHorizontal: 16,
    flex: 1,
  },
});
export default RewardsPage;
