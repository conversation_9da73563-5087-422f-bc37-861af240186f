import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Dimensions,
} from 'react-native';
import appStyles from 'styles/global';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Text from 'components/Text';
import IcPolygon from 'assets/imgs/intro/ic_polygon.svg';
import FooterIntro from './FooterIntro';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolation,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import TabSelected from './TabSelected';
import AccessGranted from './AccessGranted';
import {updateSignupGPS} from 'utils/home';
import {useDispatch, useSelector} from 'react-redux';
import {
  TYPE_INTRO_PAGE,
  checkRatioScreen,
  getTopTooltipPlay,
  getHomeSubTractHeight,
  getScaleImage,
  getTranYImage,
} from '../dimension';
import {t} from 'i18next';
import {
  PERMISSIONS,
  RESULTS,
  check,
  openSettings,
  request,
} from 'react-native-permissions';
import {
  checkMainCountry,
  GA_EVENT_NAME,
  getCurrentLocationEnabled,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_CLASS,
  SCREEN_TYPES,
} from 'utils/constant';
import useAppState from 'hooks/useAppState';
import {
  GA_logEvent,
  GA_logScreenViewV2,
  GA_setUserProperty,
} from 'utils/googleAnalytics';

const width = wp(100);

const PlayPage = ({onNext, indexPage}) => {
  const [isShow, setShow] = useState(false);
  const [isGranted, setGranted] = useState(false);
  const offset = useSharedValue(0);
  const user = useSelector(state => state?.user);
  const dispatch = useDispatch();
  const scaleImage = getScaleImage();
  const transY = getTranYImage();
  const hasOpenedSetting = useRef(false);
  const isMainCountry = checkMainCountry(user?.userCountry);
  const playPageIndex = isMainCountry ? 2 : 1;
  useEffect(() => {
    if (indexPage === playPageIndex) {
      GA_logScreenViewV2(
        PAGE_NAME.ENABLE_LOCATION,
        PAGE_CATEGORY.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
      );
      setTimeout(() => {
        setShow(true);
        offset.value = withTiming(1, {
          duration: 300,
          easing: Easing.elastic(1),
        });
      }, 300);
    }
  }, [indexPage]);

  const GA_logEnableLocationEvent = (isSetTrue = true) => {
    if (isSetTrue) {
      GA_logEvent(GA_EVENT_NAME.ENABLE_LOCATION, {
        callout_name: 'location enable',
        click_location: 'tour-intro',
        page_type: SCREEN_TYPES.ONBOARDING,
        page_category: PAGE_CATEGORY.ONBOARDING,
        page_name: PAGE_NAME.ONBOARDING,
        screen_type: SCREEN_TYPES.ONBOARDING,
      });
      GA_setUserProperty('location_enabled', 'true');
    } else {
      GA_setUserProperty('location_enabled', 'false');
    }
  };

  const checkLocationPermission = async () => {
    let isLocationEnabled = await getCurrentLocationEnabled();
    if (isLocationEnabled) {
      setGranted(true);
      GA_logEnableLocationEvent();
    } else {
      GA_logEnableLocationEvent(false);
    }
  };

  useAppState({
    onForeground: () => {
      if (hasOpenedSetting.current) {
        checkLocationPermission();
        hasOpenedSetting.current = false;
      }
    },
  });

  const openPermission = async () => {
    const permissionRequest = await request(
      Platform.OS === 'ios'
        ? PERMISSIONS.IOS.LOCATION_ALWAYS
        : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    );
    if (permissionRequest === RESULTS.GRANTED) {
      updateSignupGPS(user, dispatch);
      setGranted(true);
      GA_logEnableLocationEvent();
      setTimeout(() => {
        onNext(playPageIndex);
      }, 300);
    }

    if (permissionRequest === RESULTS.BLOCKED) {
      //When allow ONCE location permission (IOS)
      const locationWhenInUsePermission = await check(
        PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      );
      if (
        locationWhenInUsePermission === RESULTS.GRANTED &&
        Platform.OS === 'ios'
      ) {
        GA_logEnableLocationEvent();
        setGranted(true);
        setTimeout(() => {
          onNext(playPageIndex);
        }, 300);
      } else {
        hasOpenedSetting.current = true;
        openSettings();
      }
    }
  };

  const styleImage = useAnimatedStyle(() => {
    const scale = interpolate(
      offset.value,
      [0, 1],
      [1, scaleImage],
      Extrapolation.CLAMP,
    );
    const translateY = interpolate(
      offset.value,
      [0, 1],
      [0, -transY],
      Extrapolation.CLAMP,
    );
    return {transform: [{scale}, {translateY}]};
  });
  const styleTooltip = useAnimatedStyle(() => {
    const translateX = interpolate(
      offset.value,
      [0, 1],
      [-width, 0],
      Extrapolation.CLAMP,
    );
    return {transform: [{translateX}]};
  });

  const renderPermission = () => {
    return (
      <Animated.View
        style={[
          {width: '100%', position: 'absolute', top: getTopTooltipPlay()},
          styleTooltip,
        ]}
      >
        <View style={{left: width / 2 - 13.5, position: 'absolute'}}>
          <IcPolygon />
        </View>
        {isGranted ? (
          <AccessGranted style={{marginTop: 11}} />
        ) : (
          <View style={styles.viewContainerNotify}>
            <Text
              size={16}
              weight={400}
              style={{
                textAlign: 'center',
                color: 'rgba(0, 0, 0, 0.6)',
              }}
            >
              intro.enable_location_to_track_rounds
            </Text>
            <TouchableOpacity
              style={styles.touchNotification}
              onPress={openPermission}
            >
              <Text
                size={12}
                Din79Font
                weight={700}
                white
                style={{
                  textTransform: 'uppercase',
                  letterSpacing: 1,
                }}
              >
                home.permissions.location.cta
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </Animated.View>
    );
  };
  return (
    <View
      key="Shop"
      style={{
        backgroundColor: '#000',
        width: wp(100),
        height: hp(100),
        justifyContent: 'center',
      }}
    >
      <Animated.Image
        source={checkRatioScreen({
          pageName: TYPE_INTRO_PAGE.PLAY,
          isSubtrack: false,
          userCountry: user?.userCountry,
        })}
        style={[
          {
            width: wp(100),
            height: hp(100),
          },
          styleImage,
        ]}
        resizeMode={'stretch'}
      />
      {isShow && (
        <Animated.Image
          source={checkRatioScreen({
            pageName: TYPE_INTRO_PAGE.PLAY,
            isSubtrack: true,
            userCountry: user?.userCountry,
          })}
          style={{
            width: wp(100),
            height: getHomeSubTractHeight(),
            position: 'absolute',
            top: 0,
          }}
        />
      )}
      {isShow && (
        <TabSelected index={playPageIndex} title={t('app.tab_label.play')} />
      )}
      {renderPermission()}
      {isShow && <FooterIntro index={playPageIndex} onNext={onNext} />}
    </View>
  );
};
const styles = StyleSheet.create({
  touchNotification: {
    backgroundColor: '#000',
    paddingVertical: 14,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 14,
    borderRadius: 24,
    ...appStyles.viewShadowLight,
  },
  viewContainerNotify: {
    backgroundColor: '#fff',
    borderRadius: 24,
    alignItems: 'center',
    marginTop: 11,
    marginHorizontal: 8,
    paddingVertical: 24,
    paddingHorizontal: 16,
    flex: 1,
  },
});
export default PlayPage;
