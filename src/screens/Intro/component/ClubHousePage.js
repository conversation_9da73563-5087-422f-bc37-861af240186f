import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Text from 'components/Text';
import IcPolygon from 'assets/imgs/intro/ic_polygon.svg';
import FooterIntro from './FooterIntro';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolation,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import TabSelected from './TabSelected';
import {
  TYPE_INTRO_PAGE,
  checkRatioScreen,
  getTopTooltipClubHouse,
  getHomeSubTractHeight,
  getScaleImage,
  getTranYImage,
} from '../dimension';
import {t} from 'i18next';
import {GA_logScreenViewV2} from 'utils/googleAnalytics';
import {
  checkMainCountry,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_TYPES,
} from 'utils/constant';
import {useSelector} from 'react-redux';
const width = wp(100);
const ClubHousePage = ({onNext, indexPage}) => {
  const user = useSelector(state => state.user);
  const isMainCountry = checkMainCountry(user?.userCountry);
  const clubHousePageIndex = isMainCountry ? 1 : 2;
  const [isShow, setShow] = useState(false);
  const scaleImage = getScaleImage();
  const transY = getTranYImage();
  const offset = useSharedValue(0);
  useEffect(() => {
    if (indexPage === clubHousePageIndex) {
      GA_logScreenViewV2(
        PAGE_NAME.CLUBHOUSE_INTRO,
        PAGE_CATEGORY.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
        SCREEN_TYPES.ONBOARDING,
      );
      setTimeout(() => {
        setShow(true);
        offset.value = withTiming(1, {
          duration: 300,
          easing: Easing.elastic(1),
        });
      }, 300);
    }
  }, [indexPage]);

  const styleImage = useAnimatedStyle(() => {
    const scale = interpolate(
      offset.value,
      [0, 1],
      [1, scaleImage],
      Extrapolation.CLAMP,
    );
    const translateY = interpolate(
      offset.value,
      [0, 1],
      [0, -transY],
      Extrapolation.CLAMP,
    );
    return {transform: [{scale}, {translateY}]};
  });
  const styleTooltip = useAnimatedStyle(() => {
    const translateX = interpolate(
      offset.value,
      [0, 1],
      [-width, 0],
      Extrapolation.CLAMP,
    );
    return {transform: [{translateX}]};
  });
  const renderTooltip = () => {
    return (
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: getTopTooltipClubHouse(),
            width: '100%',
            alignSelf: 'center',
          },
          styleTooltip,
        ]}
      >
        <View style={styles.viewTooltip}>
          <Text
            size={16}
            weight={400}
            style={{color: 'rgba(0, 0, 0, 0.6)', textAlign: 'center'}}
          >
            intro.access_exclusive_taylormade_content
          </Text>
        </View>
        <View
          style={{
            left: width / 2 - 13.5,
            bottom: 0,
            position: 'absolute',
            transform: [{rotate: '180deg'}],
          }}
        >
          <IcPolygon />
        </View>
      </Animated.View>
    );
  };
  return (
    <View
      key="ClubHouse"
      style={{
        backgroundColor: '#000',
        width: wp(100),
        height: hp(100),
        justifyContent: 'center',
      }}
    >
      <Animated.Image
        source={checkRatioScreen({
          pageName: TYPE_INTRO_PAGE.CLUB_HOUSE,
          isSubtrack: false,
          userCountry: user?.userCountry,
        })}
        style={[
          {
            width: wp(100),
            height: hp(100),
          },
          styleImage,
        ]}
        resizeMode={'stretch'}
      />
      {isShow && (
        <Animated.Image
          source={checkRatioScreen({
            pageName: TYPE_INTRO_PAGE.CLUB_HOUSE,
            isSubtrack: true,
            userCountry: user?.userCountry,
          })}
          style={{
            width: wp(100),
            height: getHomeSubTractHeight(),
            position: 'absolute',
            top: 0,
          }}
        />
      )}
      {isShow && renderTooltip()}
      {isShow && (
        <TabSelected
          index={clubHousePageIndex}
          title={t('app.tab_label.clubHouse')}
        />
      )}
      {isShow && <FooterIntro index={clubHousePageIndex} onNext={onNext} />}
    </View>
  );
};
const styles = StyleSheet.create({
  viewTooltip: {
    borderRadius: 24,
    marginHorizontal: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    marginBottom: 11,
    flex: 1,
  },
});
export default ClubHousePage;
