import React from 'react';
import {View, StyleSheet, TouchableOpacity, Platform} from 'react-native';
import appStyles from 'styles/global';
import Text from 'components/Text';
import {hasNotch} from 'react-native-device-info';
import {useSelector} from 'react-redux';
import {checkMainCountry} from 'utils/constant';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {getBottomFooter} from '../dimension';
const isHasNotch = hasNotch();
const FooterIntro = ({index, onNext, onGotoHome}) => {
  const user = useSelector(state => state.user);
  const isMainCountry = checkMainCountry(user?.userCountry);
  const insets = useSafeAreaInsets();
  const renderLine = color => {
    return (
      <View style={[styles.viewLine, color && {backgroundColor: color}]} />
    );
  };
  return (
    <View
      style={[
        styles.viewContainer,
        {
          bottom: getBottomFooter(insets),
        },
      ]}
    >
      <Text Din79Font size={12} weight={700} white>
        {index + 1} OF {isMainCountry ? '5' : '3'}
      </Text>
      <View
        style={[
          appStyles.row,
          {width: '100%', paddingHorizontal: 14, marginTop: 10},
        ]}
      >
        {renderLine(index >= 0 ? 'rgba(255, 255, 255, 0.5))' : '#fff')}
        {renderLine(index >= 1 ? 'rgba(255, 255, 255, 0.5))' : '#fff')}
        {renderLine(index >= 2 ? 'rgba(255, 255, 255, 0.5))' : '#fff')}
        {renderLine(index >= 3 ? 'rgba(255, 255, 255, 0.5))' : '#fff')}
        {isMainCountry &&
          renderLine(index >= 4 ? 'rgba(255, 255, 255, 0.5))' : '#fff')}
        {isMainCountry &&
          renderLine(index >= 5 ? 'rgba(255, 255, 255, 0.5))' : '#fff')}
      </View>
      {onNext && (
        <TouchableOpacity style={styles.btnNext} onPress={() => onNext(index)}>
          <Text size={12} weight={700} black>
            quiz.cta.next
          </Text>
        </TouchableOpacity>
      )}
      {onGotoHome && (
        <View style={{width: '100%'}}>
          <TouchableOpacity
            style={{
              paddingVertical: 14,
              marginHorizontal: 24,
              borderRadius: 24,
              borderWidth: 1,
              marginTop: isHasNotch ? 26 : 16,
              borderColor: '#fff',
              alignItems: 'center',
            }}
            onPress={() => onGotoHome(index)}
          >
            <Text
              size={12}
              weight={700}
              white
              style={{
                textTransform: 'uppercase',
              }}
            >
              intro.no_thanks_take_me_home
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  btnNext: {
    paddingVertical: 14,
    paddingHorizontal: 22,
    backgroundColor: '#fff',
    borderRadius: 24,
    marginTop: 16,
    alignSelf: 'flex-end',
    marginHorizontal: 8,
  },
  viewContainer: {
    position: 'absolute',
    width: '100%',
    alignItems: 'center',
  },
  viewLine: {
    height: 4,
    borderRadius: 24,
    flex: 1,
    backgroundColor: '#fff',
    marginHorizontal: 2,
  },
});
export default FooterIntro;
