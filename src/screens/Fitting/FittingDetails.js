import React, {useState, useEffect} from 'react';
import {View, ActivityIndicator} from 'react-native';
import moment from 'moment';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import FormItem from 'components/FormItem';
import HeaderRightButton from 'components/HeaderRightButton';
import LoadingOverlay from 'components/LoadingOverlay';

import {getPastFittings, getUpcomingFittings} from 'requests/fittings';

import appStyles from 'styles/global';

import {showToast} from 'utils/toast';

import {covertFittingsTypeId} from 'utils/convert';
import {getAuth0AccessToken} from 'utils/user';
import {t} from 'i18next';
import {getConfig} from 'config/env';
import {useDispatch} from 'react-redux';

const FittingDetails = ({navigation, route}) => {
  const fittingId = route.params?.shotId;
  const [details, setDetails] = useState([]);
  const [filteredDetails, setFilteredDetails] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingWebView, setLoadingWebView] = useState(false);
  const dispatch = useDispatch();
  useEffect(() => {
    const navigationOptions = {headerRight: () => <View />};
    // Render edit button for upcoming fittings
    if (route.params?.type === 'upcoming') {
      navigationOptions.headerRight = () => (
        <HeaderRightButton
          text={t('fitting.details.edit')}
          dark
          onPress={navigateToWebView}
        />
      );
    }

    navigation.setOptions(navigationOptions);
  }, []);

  const navigateToWebView = async () => {
    setLoadingWebView(true);
    const accessToken = await getAuth0AccessToken(dispatch);
    const MFE_URL = await getConfig('MFE_URL');
    const uri = `${MFE_URL}/fitting/${fittingId}?accessToken=${accessToken}&iframe=true`;

    navigation.navigate('WebView', {
      screen: 'WebView',
      params: {
        title: t('fittings.edit_fitting'),
        uri,
        canGoBack: true,
      },
    });
    setLoadingWebView(false);
  };

  useEffect(() => {
    if (route.params?.type === 'past') {
      (async () => {
        setLoading(true);
        try {
          const pastFittings = await getPastFittings();
          setDetails(pastFittings);
          setLoading(false);
        } catch (error) {
          setLoading(false);
          showToast({
            type: 'error',
            message: t('An_error_occurred_retrieving_past_fittings'),
          });
        }
      })();
    }
    if (route.params?.type === 'upcoming') {
      (async () => {
        setLoading(true);
        try {
          const upcomingFittings = await getUpcomingFittings();
          setDetails(upcomingFittings);
          setLoading(false);
        } catch (error) {
          setLoading(false);
          showToast({
            type: 'error',
            message: t('An_error_occurred_retrieving_past_fittings'),
          });
        }
      })();
    }
  }, []);

  useEffect(() => {
    if (details) {
      const filteredRecommendedProducts = details.filter(
        element => element.id === route.params?.shotId,
      );

      setFilteredDetails(filteredRecommendedProducts);
    }
  }, [details]);

  return (
    <>
      {loadingWebView ? <LoadingOverlay transparent={loadingWebView} /> : null}
      <FocusAwareStatusBar barStyle={'dark-content'} />
      {loading ? (
        <ActivityIndicator style={appStyles.pTLg} />
      ) : (
        <View style={[appStyles.flex]}>
          <FormItem
            labelStyle={{flex: 0.7}}
            valueStyle={{textAlign: 'right'}}
            label={t('fitting.details.location')}
            value={`${filteredDetails[0]?.location.address1}${
              filteredDetails[0]?.location.address2
                ? filteredDetails[0]?.location.address2
                : ''
            } ${
              filteredDetails[0]?.location.city
                ? filteredDetails[0]?.location.city
                : ''
            } ${
              filteredDetails[0]?.location.postalCode
                ? filteredDetails[0]?.location.postalCode
                : ''
            } ${
              filteredDetails[0]?.location.state
                ? filteredDetails[0]?.location.state
                : ''
            }`}
            bottomBorder
            topBorder
          />
          <FormItem
            label={t('fitting.details.headline.date')}
            value={moment(
              filteredDetails[0]?.actualEnd ||
                filteredDetails[0]?.scheduledEnd ||
                filteredDetails[0]?.actualStart ||
                filteredDetails[0]?.scheduledStart,
            ).format('LL')}
            bottomBorder
          />
          <FormItem
            label={t('fitting.details.headline.type')}
            value={covertFittingsTypeId(filteredDetails[0]?.fittingTypeId)}
            bottomBorder
          />
        </View>
      )}
    </>
  );
};

export default FittingDetails;
