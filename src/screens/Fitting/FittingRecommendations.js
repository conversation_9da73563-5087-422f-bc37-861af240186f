import React, {useState, useEffect} from 'react';
import {
  View,
  FlatList,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';

import {getPastFittings, getRecommendationFromFitting} from 'requests/fittings';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';

import appStyles from 'styles/global';

import {showToast} from 'utils/toast';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image/src';
import FormItem from 'components/FormItem';
import moment from 'moment';
import {covertFittingsTypeId} from 'utils/convert';
import {moderateScale} from 'react-native-size-matters';

const FittingRecommendations = ({navigation, route}) => {
  const isTablet = DeviceInfo.isTablet();
  const [loading, setLoading] = useState(false);
  const [recommendations, setRecommendations] = useState([]);
  const [filteredRecommendations, setFilteredRecommendations] = useState([]);
  const [details, setDetails] = useState([]);
  const [filteredDetails, setFilteredDetails] = useState([]);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const fittingRecommendation = await getRecommendationFromFitting(
          route.params.shotId,
        );
        setRecommendations([fittingRecommendation]);
        const pastFittings = await getPastFittings();
        setDetails(pastFittings);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('An_error_occurred_retrieving_shot_data'),
        });
      }
    })();
  }, [route]);

  useEffect(() => {
    setFilteredRecommendations(recommendations[0]?.recommendLines);
  }, [recommendations]);

  useEffect(() => {
    if (details) {
      const filteredRecommendedProducts = details.filter(
        element => element.id === route.params?.shotId,
      );

      setFilteredDetails(filteredRecommendedProducts);
    }
  }, [details]);

  const renderItems = ({item}) => {
    const getShaftImage = () => {
      return item?.clubConfig?.shaftModelImageUrl
        ? {
            uri: item?.clubConfig?.shaftModelImageUrl,
            cache: FastImage.cacheControl.web,
          }
        : require('assets/imgs/silhouettes-shaft.jpg');
    };
    const getGripImage = () => {
      return item?.clubConfig?.gripModelImageUrl
        ? {
            uri: item?.clubConfig?.gripModelImageUrl,
            cache: FastImage.cacheControl.web,
          }
        : require('assets/imgs/silhouettes-grip.jpg');
    };
    return (
      <View style={[appStyles.pHSm, appStyles.mBSm]}>
        <TouchableWithoutFeedback
          onPress={() => {
            if (item.clubConfig !== null) {
              navigation.navigate('FittingShotData', {
                originalClubConfigId: item?.clubConfig?.originalClubConfigId,
                productName: item?.clubConfig?.headModelName,
                price: item?.clubConfig?.ebsModelPrice,
                image: item?.clubConfig?.headModelImageUrl,
                data: item,
                recommendedDate: item?.recommendedDate,
                title:
                  item.clubConfig !== null
                    ? item.clubConfig?.headModelName
                    : item.ballConfig
                    ? item.ballConfig?.ballModel?.model
                    : item.product?.name
                    ? item.product.name
                    : '',
              });
            }
          }}
        >
          <View
            style={[
              appStyles.whiteBg,
              appStyles.viewShadow,
              {
                borderRadius: isTablet ? wp('2%') : wp('3%'),
                marginBottom: hp('1%'),
                paddingVertical: 10,
              },
            ]}
          >
            <FastImage
              style={[
                {
                  borderTopLeftRadius: isTablet ? wp('2%') : wp('3%'),
                  borderTopRightRadius: isTablet ? wp('2%') : wp('3%'),
                  height: hp('31.5%'),
                  width: '100%',
                },
              ]}
              source={{
                uri:
                  item.clubConfig !== null
                    ? item.clubConfig?.headModelImageUrl
                    : item.ballConfig
                    ? item.ballConfig?.ballModel?.imageUrl
                    : item.product?.imageUrl || item.product?.imageURL
                    ? item.product?.imageUrl || item.product?.imageURL
                    : '',
                cache: FastImage.cacheControl.web,
              }}
              resizeMode="contain"
            />
            <FastImage
              style={{
                width: '100%',
                height: moderateScale(20),
                marginVertical: 5,
              }}
              source={getShaftImage()}
              resizeMode="cover"
            />
            <FastImage
              style={{
                width: '100%',
                height: moderateScale(20),
                borderBottomLeftRadius: isTablet ? wp('2%') : wp('3%'),
                borderBottomRightRadius: isTablet ? wp('2%') : wp('3%'),
              }}
              source={getGripImage()}
              resizeMode="cover"
            />
          </View>
        </TouchableWithoutFeedback>
        <Text style={[{fontWeight: '700'}, appStyles.pLXs]}>
          {item.clubConfig !== null
            ? item.clubConfig?.headModelName
            : item.ballConfig
            ? item.ballConfig?.ballModel?.model
            : item.product?.name
            ? item.product.name
            : ''}
        </Text>
      </View>
    );
  };
  return (
    <>
      <FocusAwareStatusBar barStyle={'dark-content'} />
      {loading ? (
        <ActivityIndicator style={appStyles.pTLg} />
      ) : (
        <View style={[appStyles.flex]}>
          <FlatList
            contentContainerStyle={[appStyles.pBSm]}
            data={filteredRecommendations}
            renderItem={renderItems}
            keyExtractor={item => item.id}
            ListHeaderComponentStyle={[appStyles.pBSm]}
            ListHeaderComponent={
              <>
                <FormItem
                  labelStyle={{flex: 0.7}}
                  valueStyle={{textAlign: 'right'}}
                  label={t('fitting.details.location')}
                  value={`${filteredDetails[0]?.location.address1}${
                    filteredDetails[0]?.location.address2
                      ? filteredDetails[0]?.location.address2
                      : ''
                  } ${
                    filteredDetails[0]?.location.city
                      ? filteredDetails[0]?.location.city
                      : ''
                  } ${
                    filteredDetails[0]?.location.postalCode
                      ? filteredDetails[0]?.location.postalCode
                      : ''
                  } ${
                    filteredDetails[0]?.location.state
                      ? filteredDetails[0]?.location.state
                      : ''
                  }`}
                  bottomBorder
                  topBorder
                />
                <FormItem
                  label={t('fitting.details.headline.date')}
                  value={moment(
                    filteredDetails[0]?.actualEnd ||
                      filteredDetails[0]?.scheduledEnd ||
                      filteredDetails[0]?.actualStart ||
                      filteredDetails[0]?.scheduledStart,
                  ).format('LL')}
                  bottomBorder
                />
                <FormItem
                  label={t('fitting.details.headline.type')}
                  value={covertFittingsTypeId(
                    filteredDetails[0]?.fittingTypeId,
                  )}
                  bottomBorder
                />
              </>
            }
          />
        </View>
      )}
    </>
  );
};

export default FittingRecommendations;
