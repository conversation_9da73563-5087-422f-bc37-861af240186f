import React, {useState, useEffect} from 'react';
import {ActivityIndicator} from 'react-native';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import FittingList from 'components/FittingList';

import {getUpcomingFittings} from 'requests/fittings';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';

const FittingUpcoming = ({navigation: {navigate}}) => {
  const [loading, setLoading] = useState(false);
  const [fittings, setFittings] = useState([]);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const upcomingFittings = await getUpcomingFittings();
        setFittings(upcomingFittings);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('An_error_occurred_retrieving_upcoming_fittings'),
        });
      }
    })();
  }, []);

  return (
    <>
      <FocusAwareStatusBar barStyle={'dark-content'} />
      {loading ? (
        <ActivityIndicator style={appStyles.pTLg} color="black" />
      ) : (
        <FittingList
          fittings={fittings}
          navigate={navigate}
          fittingsType={'upcoming'}
        />
      )}
    </>
  );
};

export default FittingUpcoming;
