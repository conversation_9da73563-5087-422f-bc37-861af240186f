import React, {useState, useEffect} from 'react';
import {ScrollView, View, StyleSheet, Platform, Pressable} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {useSelector, useDispatch} from 'react-redux';
import moment from 'moment';
import {moderateScale} from 'react-native-size-matters';
import {isTablet} from 'react-native-device-info';
import Button from 'components/Button';
import Text from 'components/Text';
import ClubConfiguratorModal from 'components/ClubConfiguratorModal';
import FittingShotDataTab from './FittingShotDataTab';
import {getTTBOrders} from 'requests/ttb';
import {updateTTBOrders} from 'reducers/ttb';

import appStyles from 'styles/global';
import {t} from 'i18next';
import {showToast} from 'utils/toast';
import FastImage from 'react-native-fast-image/src';
import useTrialSubscriptionTTB from 'hooks/useTrialSubscriptionTTB';

const FittingShotData = ({navigation, route}) => {
  const user = useSelector(state => state.user);
  const permissions = useSelector(state => state?.app?.permissions);
  const orders = useSelector(state => state?.ttb?.orders);
  const dispatch = useDispatch();
  const [clubImage, setClubImage] = useState();
  const [toggleSelector, setToggleSelector] = useState(false);
  const [orderType, setOrderType] = useState(null);
  const hasTTBPermission = permissions?.myTMPermission?.canTryThenBuy || false;
  const isTrialSubscriptionTTB = useTrialSubscriptionTTB();
  const hasTrialOrders = orders.some(order => {
    return (
      order.status !== 'PRODUCT_RETURNED' &&
      order.status !== 'BOUGHT' &&
      order.status !== 'PRODUCT_RECEIVED_TO_TM' &&
      order.status !== 'PRODUCT_RETURNING_TO_TM' &&
      order.status !== 'CHARGED' &&
      order.status !== 'CANCELLED' &&
      order.status !== 'CANCELED'
    );
  });
  const isAvailable =
    moment(route.params?.recommendedDate).year() === moment().year();

  useEffect(() => {
    const imageSet = [
      {
        link: route.params?.image
          ? route.params?.image
          : 'https://via.placeholder.com/335/8C8C91/8C8C91?text= ',
      },
    ];
    setClubImage(imageSet);
  }, [route]);

  useEffect(() => {
    (async () => {
      try {
        // Make request to retrieve TTB orders
        const ttbOrders = await getTTBOrders();
        dispatch(updateTTBOrders(ttbOrders));
      } catch (error) {
        showToast({
          type: 'error',
          message: t('An_error_occurred_retrieving_your_orders'),
        });
      }
    })();
  }, []);

  const getClubModelType = () => {
    switch (route?.params?.data?.clubConfig?.clubType?.name) {
      case 'Driver':
        return 'TM_DRIVER_MODEL';
      case 'Fairway':
        return 'TM_FAIRWAY_MODEL';
      case 'Irons':
        return 'TM_IRONS_MODEL';
      case 'Rescue':
        return 'TM_RESCUE_MODEL';
      case 'Putter':
        return 'TM_PUTTER_MODEL';
      default:
        break;
    }
  };
  const goToSpecPage = () => {
    navigation.push('FittingClubSpecs', {
      data: route.params?.data,
      clubImage: clubImage,
    });
  };
  const getShaftImage = () => {
    return route?.params?.data?.clubConfig?.shaftModelImageUrl
      ? {
          uri: route?.params?.data?.clubConfig?.shaftModelImageUrl,
          cache: FastImage.cacheControl.web,
        }
      : require('assets/imgs/silhouettes-shaft.jpg');
  };
  const getGripImage = () => {
    return route?.params?.data?.clubConfig?.gripModelImageUrl
      ? {
          uri: route?.params?.data?.clubConfig?.gripModelImageUrl,
          cache: FastImage.cacheControl.web,
        }
      : require('assets/imgs/silhouettes-grip.jpg');
  };
  return (
    <>
      {(route?.params?.data?.clubConfig?.clubType?.name === 'Driver' ||
        route?.params?.data?.clubConfig?.clubType?.name === 'Irons') &&
      toggleSelector ? (
        <ClubConfiguratorModal
          toggleSelector={toggleSelector}
          setToggleSelector={setToggleSelector}
          model={getClubModelType()}
          clubConfig={route?.params?.data?.clubConfig}
          origin="fittings"
          orderType={orderType}
          navigate={navigation.navigate}
        />
      ) : null}
      <SafeAreaView
        style={[appStyles.flex]}
        edges={['right', 'bottom', 'left']}
      >
        <ScrollView style={[appStyles.flex]}>
          <Pressable onPress={goToSpecPage}>
            <View
              style={[
                appStyles.whiteBg,
                appStyles.viewShadow,
                {
                  borderRadius: isTablet ? wp('2%') : wp('3%'),
                  marginBottom: hp('1%'),
                  alignSelf: 'center',
                  marginTop: 20,
                },
              ]}
            >
              <FastImage
                style={{
                  borderRadius: isTablet ? wp('2%') : wp('3%'),
                  height: wp(94),
                  width: wp(94),
                }}
                source={{
                  uri: clubImage?.[0]?.link,
                  cache: FastImage.cacheControl.web,
                }}
                resizeMode="contain"
              />
            </View>
            <Text
              style={[
                styles.spectText,
                {
                  fontWeight: Platform.OS === 'ios' ? '600' : 'bold',
                },
              ]}
              onPress={goToSpecPage}
            >
              Specs <Text style={[styles.spectText]}>􀄯</Text>
            </Text>
          </Pressable>
          <Pressable onPress={goToSpecPage}>
            <View
              style={[
                appStyles.whiteBg,
                appStyles.viewShadow,
                {
                  borderRadius: isTablet ? wp('2%') : wp('3%'),
                  marginBottom: hp('1%'),
                  alignSelf: 'center',
                },
              ]}
            >
              <FastImage
                style={{
                  width: wp(94),
                  height: moderateScale(20),
                  borderRadius: isTablet ? wp('2%') : wp('3%'),
                  marginVertical: 18,
                }}
                source={getShaftImage()}
                resizeMode="cover"
              />
              <Text style={[styles.arrowIcon]}>􀄯</Text>
            </View>
          </Pressable>
          <Pressable onPress={goToSpecPage}>
            <View
              style={[
                appStyles.whiteBg,
                appStyles.viewShadow,
                {
                  borderRadius: isTablet ? wp('2%') : wp('3%'),
                  marginBottom: hp('1%'),
                  alignSelf: 'center',
                },
              ]}
            >
              <FastImage
                style={{
                  width: wp(94),
                  height: moderateScale(20),
                  borderRadius: isTablet ? wp('2%') : wp('3%'),
                  marginVertical: 18,
                }}
                source={getGripImage()}
                resizeMode="cover"
              />
              <Text style={[styles.arrowIcon]}>􀄯</Text>
            </View>
          </Pressable>

          <FittingShotDataTab data={route.params?.data} />
        </ScrollView>
        {user?.features?.CLUBPURCHASE &&
        (user.features?.PASTFITTINGTTB || user.features?.PASTFITTINGPURCHASE) &&
        isAvailable &&
        (route?.params?.data?.clubConfig?.clubType?.name === 'Driver' ||
          route?.params?.data?.clubConfig?.clubType?.name === 'Irons') ? (
          <View
            style={[
              appStyles.pHSm,
              appStyles.mBXs,
              appStyles.pTXs,
              appStyles.row,
            ]}
          >
            {!!user.features?.PASTFITTINGPURCHASE && (
              <Button
                style={[appStyles.flex, appStyles.mRXs]}
                text="fitting.recommendations.shot_data.cta.buy_now"
                textColor="white"
                backgroundColor={'black'}
                onPress={() => {
                  setOrderType(null);
                  setToggleSelector(true);
                }}
                centered
                DINbold
              />
            )}
            {!hasTrialOrders &&
            hasTTBPermission &&
            !!user.features?.PASTFITTINGTTB &&
            !isTrialSubscriptionTTB ? (
              <Button
                style={[appStyles.flex, appStyles.mLXs]}
                text={t('overview.try_then_buy_upper')}
                textColor="white"
                backgroundColor={'black'}
                onPress={() => {
                  setOrderType('ttb');
                  setToggleSelector(true);
                }}
                centered
                DINbold
              />
            ) : null}
          </View>
        ) : null}
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  spectText: {
    position: 'absolute',
    top: wp(1) + 20,
    right: wp(4),
    color: 'rgba(140, 139, 143, 1)',
  },
  arrowIcon: {
    position: 'absolute',
    top: wp(0.5),
    right: wp(1),
    color: 'rgba(140, 139, 143, 1)',
  },
});

export default FittingShotData;
