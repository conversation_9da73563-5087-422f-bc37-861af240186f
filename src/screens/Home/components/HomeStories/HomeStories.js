import React, {useState, useImperativeHandle} from 'react';
import {
  View,
  StyleSheet,
  Platform,
  TouchableOpacity,
  Image,
} from 'react-native';
import Text from 'components/Text';
import {useDispatch, useSelector} from 'react-redux';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Carousel from 'react-native-snap-carousel';
import appStyles from 'styles/global';
import {TabActions, useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import moment from 'moment';
import {getEntertainmentList} from 'requests/content';
import {articlePressed, getThumbnail, isVideoArticle} from 'utils/article';
import {t} from 'i18next';
import {GA_EVENT_NAME, PAGE_CATEGORY, PAGE_NAME, SCREEN_TYPES, TAB_NAME} from 'utils/constant';
import CustomImage from 'components/CustomImage/CustomImage';
import { GA_logEvent } from 'utils/googleAnalytics';
import {updateEntertainmentData} from 'reducers/dataCache';

const HomeStories = ({vertical}, ref) => {
  const [articlesData, setArticlesData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [alignment, setAlignment] = useState('start');
  const navigation = useNavigation();
  const entertainmentDataCache = useSelector(state => state.dataCache?.entertainment);
  const {navigate} = navigation;
  const dispatch = useDispatch();
  useImperativeHandle(ref, () => ({
    refreshData: () => {
      getData();
    },
  }));
  const getData = async () => {
    try {
      setLoading(true);
      await getEntertainmentArticles();
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const onItemPress = async item => {
    articlePressed(navigation.navigate, item, 'home-stories');
  };

  const getEntertainmentArticles = async () => {
    try {
      if (entertainmentDataCache?.data?.length > 0) {
        const getFirst3Articles = entertainmentDataCache?.data?.slice?.(0, 3);
        setArticlesData(getFirst3Articles);
      }
      const articlesResponse = await getEntertainmentList();
      if (articlesResponse) {
        const data = articlesResponse?.data || [];
        const getFirst3Articles = data?.slice?.(0, 3);
        dispatch(updateEntertainmentData({
          data,
        }));
        setArticlesData(getFirst3Articles);
      }
    } catch (error) {}
  };

  const onPressSeeAll = () => {
    GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
      screen_type: SCREEN_TYPES.HOME,
      page_name: PAGE_NAME.HOME_MAIN_INFO,
      page_type: SCREEN_TYPES.HOME,
      page_category: PAGE_CATEGORY.HOME_MAIN,
      nav_type: 'home',
      nav_item_selected: 'see more stories',
      nav_level: 'latest article',
    });
    navigation.navigate('ClubHouse', {
      needScrollToEntertainment: true,
      articleFocusTab: 'Drills',
    });
    setTimeout(
      () => {
        const jumpToAction = TabActions.jumpTo('Drills');
        navigation.dispatch(jumpToAction);
      },
      Platform.OS === 'android' ? 500 : 300,
    );
  };

  const renderItem = ({item, index}) => {
    const thumbnail = getThumbnail(item);
    return (
      <TouchableOpacity
        delayPressIn={100}
        delayPressOut={100}
        activeOpacity={0.8}
        style={[
          styles.itemWrapper,
          appStyles.viewShadowLightBig,
          alignment === 'start'
            ? {marginLeft: 8, marginRight: 0}
            : {marginRight: 8, marginLeft: 0},
        ]}
        onPress={() => onItemPress(item)}
      >
        <CustomImage style={styles.itemImage} source={{uri: thumbnail}} />

        <View style={styles.contentView}>
          <Text
            Din79Font
            gray
            size={12}
            weight={700}
            style={{letterSpacing: 1.2}}
          >
            {moment(item.postDate).format('MM.DD.YY')}
          </Text>
          <Text
            style={styles.titleText}
            black
            size={16}
            numberOfLines={2}
            weight={700}
          >
            {item?.title}
          </Text>
          <View style={styles.buttonWatchNow}>
            <Text
              Din79Font
              black
              size={12}
              weight={700}
              style={{letterSpacing: 1.62, textTransform: 'uppercase'}}
            >
              {isVideoArticle(item)
                ? t('clubhouse.entertainment.watch_now')
                : t('clubhouse.entertainment.read_more')}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  const renderHeader = () => {
    return (
      <View
        style={[
          {marginHorizontal: 16, marginBottom: 8, justifyContent: 'center'},
          appStyles.row,
          appStyles.spaceBetween,
        ]}
      >
        <Text
          size={16}
          black
          style={{marginBottom: Platform.OS === 'ios' ? 4 : 0}}
        >
          home.top_stories_today
        </Text>
        <TouchableOpacity
          style={[{justifyContent: 'center'}, appStyles.row]}
          onPress={onPressSeeAll}
        >
          <Text
            size={16}
            black
            style={{marginBottom: 8, fontWeight: '700', marginRight: 5}}
          >
            home.see_all_stories
          </Text>
          <MaterialIcons name="arrow-forward" color={'#000'} size={20} />
        </TouchableOpacity>
      </View>
    );
  };
  if (articlesData.length === 0 && loading) {
    return (
      <View style={{marginTop: 24}}>
        {renderHeader()}
        <View style={[appStyles.row, {marginBottom: 10}]}>
          <ShimmerPlaceholder
            LinearGradient={LinearGradient}
            width={wp(90)}
            height={wp(90) - 20}
            shimmerStyle={{marginHorizontal: 8, borderRadius: 16}}
          />
          <ShimmerPlaceholder
            LinearGradient={LinearGradient}
            width={wp(90)}
            height={wp(90) - 20}
            shimmerStyle={{borderRadius: 16}}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={{marginTop: 24}}>
      {renderHeader()}
      <Carousel
        data={articlesData}
        renderItem={renderItem}
        sliderWidth={wp(100)}
        itemWidth={wp(90)}
        inactiveSlideScale={1}
        inactiveSlideOpacity={1}
        enableMomentum={true}
        decelerationRate={0.9}
        swipeThreshold={40}
        activeSlideAlignment={alignment}
        onBeforeSnapToItem={index => {
          if (index === 0) {
            setAlignment('start');
          } else if (index === articlesData.length - 1) {
            setAlignment('end');
          }
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  itemWrapper: {
    marginLeft: 8,
    borderRadius: 16,
    backgroundColor: '#fff',
    marginBottom: 6,
    paddingHorizontal: 4,
    paddingTop: 4,
  },
  itemImage: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    height: (wp(90) - 16) / 1.77,
    width: wp(90) - 16,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  contentView: {width: '100%', padding: 12},
  buttonWatchNow: {
    alignSelf: 'flex-start',
  },
  titleText: {lineHeight: 18, paddingTop: 10, paddingBottom: 8, minHeight: 54},
});

export default React.forwardRef(HomeStories);
