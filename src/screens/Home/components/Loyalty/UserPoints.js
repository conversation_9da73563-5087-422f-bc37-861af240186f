import Text from 'components/Text';
import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Platform,
  ImageBackground,
  Image,
} from 'react-native';
import appStyles from 'styles/global';
import DeviceInfo from 'react-native-device-info';
import {useSelector} from 'react-redux';
import ProgressBar from 'react-native-progress/Bar';
import {
  heightPercentageToDP,
  widthPercentageToDP,
} from 'react-native-responsive-screen';
import moment from 'moment';
import Button from 'components/Button';

const isTablet = DeviceInfo.isTablet();

const UserPoints = () => {
  const loyalty = useSelector(state => state?.loyalty);
  const {tier, points} = loyalty || {};
  return (
    <ImageBackground
      source={require('assets/imgs/loyalty_background.png')}
      style={{height: 429}}
    >
      <View
        style={[
          styles.container,
          {
            alignItems: 'center',
            paddingTop: 71,
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
          },
        ]}
      >
        <Image source={require('assets/imgs/new_home_logo.png')} />
        <Text
          white
          style={{fontWeight: 'bold', marginBottom: 10, fontSize: 24}}
        >
          {tier?.currentTier} Level
        </Text>
        <Text white style={{marginVertical: 20, fontWeight: '700'}} size={18}>
          <Image source={require('assets/imgs/new_home_logo_small.png')} />
          {points?.availablePoints || 0} | $
          {points?.creditsToCurrencyValue || 0}
        </Text>
        <ProgressBar
          progress={
            tier?.spendAmountInTier
              ? tier?.spendAmountInTier /
                (tier?.spendAmountToNextTier + tier?.spendAmountInTier)
              : 0
          }
          animated={true}
          width={widthPercentageToDP(90)}
          unfilledColor={'rgba(0, 0, 0, 0.7)'}
          color={'white'}
          borderWidth={1}
          height={heightPercentageToDP('1%')}
          borderRadius={20}
          animationType={'timing'}
          borderColor={'black'}
        />
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <Text white style={{fontWeight: 'bold'}}>
            {tier?.currentTier}
          </Text>
          <Text white>
            Spend {tier?.spendAmountToNextTier} more to make{' '}
            <Text style={{fontWeight: 'bold'}}>{tier?.nextTier}</Text>
          </Text>
        </View>
        {
          <Text white style={{marginVertical: 20}}>
            <Image source={require('assets/imgs/new_home_logo_small.png')} />
            {points?.pointsToExpire || 0} |{' '}
            <Text style={{fontWeight: '700'}} size={18}>
              $
              {points?.pointsToExpire && points?.creditsToCurrencyRatio
                ? points?.pointsToExpire * points?.creditsToCurrencyRatio
                : 0}
            </Text>
          </Text>
        }
        {
          <Text white size={12}>
            POINTS EXP. IN{' '}
            {points?.pointsToExpireDate
              ? moment(points?.pointsToExpireDate).diff(moment(), 'days')
              : 0}{' '}
            DAYS
          </Text>
        }
        <Button
          style={{
            paddingHorizontal: 16,
            paddingVertical: 10,
            marginTop: 8,
            height: 'auto',
          }}
          text={'SHOP NOW'}
          backgroundColor={'white'}
          borderColor={'white'}
          textStyle={{fontWeight: 'bold', fontSize: 13}}
        />
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 15,
    paddingTop: 15,
    paddingBottom: 20,
  },
});

export default UserPoints;
