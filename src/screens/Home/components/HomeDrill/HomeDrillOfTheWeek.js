import React, {useState, useImperativeHandle, useEffect} from 'react';
import {View, StyleSheet, TouchableOpacity, Platform} from 'react-native';
import {result} from 'lodash';
import Text from 'components/Text';
import {useDispatch, useSelector} from 'react-redux';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import appStyles from 'styles/global';
import IconDrillOfTheWeek from 'assets/imgs/home/<USER>';
import LogoMyTM from 'assets/imgs/home/<USER>';
import {useNavigation} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import ShimmerPlaceholder from 'react-native-shimmer-placeholder';
import {getInsights} from 'requests/content';
import {getBrowseContent} from 'requests/drills';
import {
  GA_EVENT_NAME,
  IMAGES,
  PAGE_CATEGORY,
  PAGE_NAME,
  SCREEN_TYPES,
  TAB_NAME,
} from 'utils/constant';
import {TabActions} from '@react-navigation/native';
import {GA_logEvent} from 'utils/googleAnalytics';
import {GA_logSelectContentEvent} from 'utils/article';
import FastImage from 'react-native-fast-image/src';
import { updateDrillOfTheWeek } from 'reducers/home';
const width = wp(96.5);
const HomeDrillOfTheWeek = ({}, ref) => {
  const dispatch = useDispatch();
  const appImagesCache = useSelector(state => state?.images);
  const customImages = appImagesCache?.data;
  const drillOfTheWeekFromCache = useSelector(
    state => state?.home?.drillOfTheWeek,
  );
  const [data, setData] = useState(drillOfTheWeekFromCache);
  const [loading, setLoading] = useState(false);
  const slug = result(
    drillOfTheWeekFromCache,
    'tags.mytmGenericGameArea[0].slug',
    null,
  );
  const imageFromCache = customImages?.[slug?.toUpperCase()];
  const [imageBackground, setImageBackground] = useState(
    imageFromCache || customImages?.[IMAGES.DRILL_OF_THE_WEEK] || null,
  );
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );
  const [imageSlug, setImageSlug] = useState(
    imageFromCache || IMAGES.DRILL_OF_THE_WEEK,
  );
  const navigation = useNavigation();

  useImperativeHandle(ref, () => ({
    refreshData: () => {
      getData();
    },
  }));
  useEffect(() => {
    if (customImages?.[imageSlug]) {
      setImageBackground(customImages?.[imageSlug]);
    }
  }, [imageSlug, customImages]);
  const getData = async () => {
    try {
      const insightsData = await getInsights(playService);
      const meaningData = insightsData?.filter(
        value => Object.keys(value)?.length,
      );
      if (meaningData?.length > 0) {
        const firstObj = meaningData[0];
        let videos = await getBrowseContent(
          firstObj?.drills_type[0],
          playService,
        );
        let videoData = null;
        if (videos?.length > 0) {
          videoData = videos[0];
          if (videoData?.id !== drillOfTheWeekFromCache?.id) {
            setLoading(true);
            const slugByVideo = result(
              videoData,
              'tags.mytmGenericGameArea[0].slug',
              null,
            );
            if (slugByVideo) {
              if (customImages?.[slugByVideo?.toUpperCase()]) {
                setImageBackground(customImages?.[slugByVideo?.toUpperCase()]);
              } else {
                setImageSlug(slugByVideo?.toUpperCase());
              }
            }
            setData(videoData);
            dispatch(updateDrillOfTheWeek(videoData));
            setTimeout(() => {
              setLoading(false);
            }, 1000);
          }
        }
      }
    } catch (error) {
      setLoading(false);
    }
  };

  const onPress = () => {
    GA_logSelectContentEvent({
      contentName: data?.title,
      contentType: 'video',
      clickLocation: 'home-drill-of-the-week',
    });
    navigation.navigate('Video', {
      video: {
        // contentId: 196534,
        host: data?.video_type,
        id: data?.video_id,
        title: data?.title,
        drillOfTheWeek: true,
        clickLocation: 'home-drill-of-the-week',
        contentName: data?.title,
      },
    });
  };
  const onPressSeeAll = () => {
    GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
      screen_type: SCREEN_TYPES.HOME,
      page_name: PAGE_NAME.HOME_MAIN_INFO,
      page_type: SCREEN_TYPES.HOME,
      page_category: PAGE_CATEGORY.HOME_MAIN,
      nav_type: 'home',
      nav_item_selected: 'see all drills',
      nav_level: 'drills of the day',
    });
    navigation.navigate('ClubHouse', {
      needScrollToEntertainment: true,
      articleFocusTab: 'Drills',
    });
    setTimeout(
      () => {
        const jumpToAction = TabActions.jumpTo('Drills');
        navigation.dispatch(jumpToAction);
      },
      Platform.OS === 'android' ? 500 : 300,
    );
  };
  if (loading) {
    return (
      <ShimmerPlaceholder
        LinearGradient={LinearGradient}
        width={width}
        height={width * 1.37}
        shimmerStyle={[
          {marginTop: 24, borderRadius: 24, alignSelf: 'center'},
          appStyles.viewShadowLightBig,
        ]}
      />
    );
  }
  if (!data) {
    return null;
  } else {
    return (
      <View style={[styles.viewContainer, appStyles.viewShadowLightBig]}>
        <FastImage
          source={{uri: imageBackground}}
          style={styles.viewImgBackground}
        />

        <View style={styles.viewDrillAndIcon}>
          <LogoMyTM />
          <View style={{marginLeft: 10}}>
            <IconDrillOfTheWeek />
          </View>
        </View>
        <View style={styles.viewLinear}>
          <View
            style={[
              StyleSheet.absoluteFill,
              {
                overflow: 'hidden',
              },
            ]}
          >
            <FastImage
              source={{uri: imageBackground}}
              blurRadius={5}
              style={styles.viewBlur}
              resizeMode="cover"
            />
            <LinearGradient
              colors={[
                'rgba(0, 0, 0, 0)',
                'rgba(0, 0, 0, 0.015)',
                'rgba(0, 0, 0, 0.275)',
              ]}
              style={[StyleSheet.absoluteFill]}
            />
          </View>
          {data?.tags?.mytmGenericGameArea?.length > 0 && (
            <Text
              size={12}
              Din79Font
              weight={800}
              numberOfLines={1}
              style={styles.textInstructor}
            >
              {data?.tags?.mytmGenericGameArea[0]?.title}
            </Text>
          )}
          <Text
            size={22}
            Din79Font
            weight={800}
            numberOfLines={2}
            style={styles.textTitleDrill}
          >
            {data?.title}
          </Text>
          <View style={[appStyles.row, {padding: 16}]}>
            <TouchableOpacity
              style={[
                {
                  backgroundColor: '#fff',
                },
                styles.touchWatchNow,
              ]}
              onPress={onPress}
            >
              <Text
                Din79Font
                weight={700}
                size={12}
                black
                style={{
                  letterSpacing: 1.2,
                }}
              >
                home.watch_now
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.touchSeeAllDrills]}
              onPress={onPressSeeAll}
            >
              <Text
                Din79Font
                weight={700}
                size={12}
                white
                style={{
                  letterSpacing: 1.2,
                }}
              >
                home.see_all_drills
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  viewContainer: {
    marginTop: 18,
    borderRadius: 24,
    width: width,
    height: width * 1.37,
    alignSelf: 'center',
    overflow: 'hidden',
  },
  viewDrillAndIcon: {
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
    top: 15,
    left: 16,
  },
  viewLinear: {
    left: 0,
    right: 0,
    bottom: 0,
    position: 'absolute',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    overflow: 'hidden',
  },
  touchWatchNow: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 40,
  },
  touchSeeAllDrills: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: '#fff',
    marginLeft: 8,
  },
  textTitleDrill: {
    color: '#fff',
    paddingHorizontal: 16,
    textTransform: 'uppercase',
    marginTop: 8,
  },
  textInstructor: {
    color: '#fff',
    paddingHorizontal: 16,
    marginTop: 8,
    textTransform: 'uppercase',
  },
  viewBlur: {
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    width: width,
    height: width * 1.37,
    position: 'absolute',
    bottom: 0,
  },
  viewImgBackground: {
    width: width,
    height: width * 1.37,
    borderRadius: 24,
    backgroundColor: '#ccc',
  },
});

export default React.forwardRef(HomeDrillOfTheWeek);
