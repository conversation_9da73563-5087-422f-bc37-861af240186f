import React, {useEffect, useMemo, useRef, useState} from 'react';
import {
  Platform,
  View,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  ScrollView,
} from 'react-native';
import appStyles from 'styles/global';
import Text from 'components/Text';
import {BlurView} from '@react-native-community/blur';
import Animated, {
  useSharedValue,
  withTiming,
  useAnimatedStyle,
} from 'react-native-reanimated';
import PercentageProgress from './PercentageProgress';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {postAnswer} from 'requests/home';
import {useSelector} from 'react-redux';
import {IMAGES, INTERNAL_ERROR_CODE} from 'utils/constant';
import {t} from 'i18next';
import Svg, {Text as SvgText} from 'react-native-svg';
import {isEmpty} from 'lodash';
import CustomImageBackground from 'components/CustomImageBackground/CustomImageBackground';
import {showToast} from 'utils/toast';
import {useInAppReview} from 'hooks/useInAppReview';

const TEXT_PERCENT_WIDTH = 65;
const TEXT_ANSWER_WIDTH = wp(100) - 64; //64 is padding of two side (32px each side)
const TEXT_ANSWER_WIDTH_ANSWERED = TEXT_ANSWER_WIDTH - TEXT_PERCENT_WIDTH + 5;
const QuestionPage = (
  {goToNextQuestion, question, loadNextQuestion, reloadQuestion},
  ref,
) => {
  const [showButtonAnother, setShowButtonAnother] = useState(false);
  const [enableSelectAnswer, setEnableSelectAnswer] = useState(true);
  const [currentQuestion, setCurrentQuestion] = useState(question);
  const [loading, setLoading] = useState(false);
  const textPercentWidth = useSharedValue(0);
  const textAnswerWidth = useSharedValue();
  const progressRefs = useRef([]);
  const appImagesCache = useSelector(state => state?.images);
  const imageBackground = appImagesCache?.data?.[IMAGES.CARTNER_CONVOS] || null;
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [scrollEnabled, setScrollEnabled] = useState(false); // Manage scroll enabling
  const [scrollViewHeight, setScrollViewHeight] = useState(0);
  const scrollRef = useRef(null);
  const heightBottomTab = wp(22) / 2;
  const [idSuffix, setIdSuffix] = useState(0);
  const [textColor, setTextColor] = useState('black');
  const {triggerInAppReview} = useInAppReview();
  useEffect(() => {
    if (question) {
      hideGreenPercentText();
      setShowButtonAnother(false);
      setCurrentQuestion(question);
      setEnableSelectAnswer(false);
      if (question?.isLastSummary) {
        setSelectedAnswer(
          question?.answers?.find(item => item.id === question?.userAnswerId),
        );
        setTimeout(() => {
          animateGreenPercentText();
        }, 200);
      } else {
        setEnableSelectAnswer(true);
      }
      //set ID suffix for re-rendering the image background if needed
      setIdSuffix(idSuffix + 1);
    }
  }, [question]);

  useEffect(() => {
    if (currentQuestion) {
      let options = null;
      try {
        options = currentQuestion?.options
          ? JSON.parse(currentQuestion.options)
          : null;
      } catch (error) {}
      if (options?.textColor) {
        setTextColor(options?.textColor);
      }
    }
  }, [currentQuestion]);

  const onPressAnswer = async item => {
    try {
      submitAnswer(item);
    } catch (error) {
    } finally {
    }
  };
  const animateGreenPercentText = () => {
    progressRefs.current.map(it => {
      it?.animateValue?.();
    });
    textPercentWidth.value = withTiming(TEXT_PERCENT_WIDTH, {duration: 400});
    textAnswerWidth.value = withTiming(TEXT_ANSWER_WIDTH_ANSWERED, {
      duration: 400,
    });
  };
  const hideGreenPercentText = () => {
    textPercentWidth.value = 0;
    textAnswerWidth.value = TEXT_ANSWER_WIDTH;
    progressRefs.current.map(it => {
      it?.reset?.();
    });
  };
  const showAnswerPercentage = qt => {
    if (qt?.isNextQuestion) {
      setShowButtonAnother(true);
    }
    animateGreenPercentText();
  };
  const pressAnother = async () => {
    try {
      goToNextQuestion?.(currentQuestion);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const submitAnswer = async itemSelect => {
    try {
      // Set loading state to true to indicate submission is in progress
      setLoading(true);
      let timeoutSubmit = null;
      let isHandled = false;

      // Handles the answer result if not already processed
      const answerSuccess = submitResult => {
        console.log('isHandled', isHandled);
        if (submitResult && !isHandled) {
          setCurrentQuestion(submitResult); // Update current question with response
          showAnswerPercentage(submitResult); // Display answer statistics/percentages
          isHandled = true; // Prevent duplicate handling
        }
      };

      // Prepares and transitions to the next question
      const prepareNextQuestion = async submitResult => {
        await loadNextQuestion?.(); // Load next question
        if (timeoutSubmit) {
          clearTimeout(timeoutSubmit); // Clear the timeout if it hasn't fired yet
          if (submitResult) {
            answerSuccess(submitResult); // Ensure answer is handled before proceeding
          }
        }
        setLoading(false); // Mark loading as finished
        // Trigger in-app review prompt after 1 second delay
        setTimeout(() => {
          triggerInAppReview();
        }, 1000);
      };

      setSelectedAnswer(itemSelect); // Store the selected answer
      setEnableSelectAnswer(false); // Disable answer selection to prevent changes

      // Submit the selected answer to the server
      const submitResult = await postAnswer({
        answerId: itemSelect.id,
        questionId: currentQuestion?.id,
      });

      // If submission is successful
      if (submitResult) {
        // Fallback: If server doesn't respond with next question fast enough, handle after 700ms
        timeoutSubmit = setTimeout(() => {
          answerSuccess(submitResult);
        }, 500);

        // Proceed to load the next question and finish submission flow
        prepareNextQuestion(submitResult);
      }
    } catch (error) {
      let errorCode = error?.response?.data?.response?.internalErrorCode;
      if (
        errorCode?.toUpperCase?.() === INTERNAL_ERROR_CODE.QUESTION_NOT_FOUND ||
        errorCode?.toUpperCase?.() === INTERNAL_ERROR_CODE.NO_LONGER_AVAILABLE
      ) {
        await reloadQuestion();
        setEnableSelectAnswer(true);
        showToast({
          type: 'error',
          message: t('home.cartner_convo.question_not_available'),
        });
      } else {
        if (
          errorCode?.toUpperCase?.() === INTERNAL_ERROR_CODE.ANSWER_NOT_FOUND
        ) {
          await reloadQuestion();
        }
        setEnableSelectAnswer(true);
        showToast({
          type: 'error',
          message: t('home.cartner_convo.submit_answer_error'),
          subText: t('home.cartner_convo.please_try_again'),
        });
      }
      setLoading(false);
    }
  };

  const textPercentStyle = useAnimatedStyle(() => {
    return {
      width: textPercentWidth.value,
    };
  });
  const textAnswerStyle = useAnimatedStyle(() => {
    return {
      width: textAnswerWidth.value,
    };
  });
  const buttonFixedHeight = useMemo(() => {
    let height = 63;
    switch (currentQuestion?.answers?.length) {
      case 4:
        height = 63;
        break;
      case 3:
        height = 70;
        break;
      case 1:
      case 2:
        height = 108;
        break;
      default:
        break;
    }
    return height;
  }, [currentQuestion]);

  const handleContentSizeChange = (contentWidth, contentHeight) => {
    // Enable scrolling if content height is larger than window height
    setScrollEnabled(contentHeight > scrollViewHeight);
    scrollRef.current.scrollTo({
      y: 0,
      animated: false,
    });
  };

  if (isEmpty(currentQuestion)) {
    return null;
  }
  return (
    <CustomImageBackground
      source={
        currentQuestion?.imageUrl
          ? {uri: currentQuestion?.imageUrl}
          : imageBackground
          ? {uri: imageBackground}
          : require('assets/imgs/cartnerConvos/cart_convo_default_image.jpg')
      }
      fallBackImg={require('assets/imgs/cartnerConvos/cart_convo_default_image.jpg')}
      fallbackUri={imageBackground}
      style={[appStyles.hCenter, styles.containerView]}
      key={currentQuestion?.id + idSuffix}
    >
      {loading && (
        <View style={[appStyles.absoluteFill, appStyles.center, {zIndex: 99}]}>
          <ActivityIndicator size="large" color={'black'} />
        </View>
      )}
      <ScrollView
        ref={scrollRef}
        style={{flex: 1, width: '100%'}}
        contentContainerStyle={{
          paddingHorizontal: 15,
          paddingBottom: 60 + heightBottomTab,
          paddingTop: 15,
        }}
        scrollEnabled={scrollEnabled}
        onContentSizeChange={handleContentSizeChange} // Trigger on content size change
        onLayout={event => {
          let {width, height} = event.nativeEvent.layout;
          setScrollViewHeight(height);
        }}
      >
        <Text
          Din79Font
          size={22}
          style={[styles.questionTitle, {color: textColor}]}
          numberOfLines={4}
        >
          {currentQuestion?.title}
        </Text>
        <View style={styles.answerSectionView}>
          {currentQuestion?.answers?.map?.((item, index) => {
            return (
              <View
                style={[
                  appStyles.viewShadowLight,
                  {
                    marginBottom: 8,
                    backgroundColor:
                      Platform.OS === 'ios'
                        ? 'rgba(255,255,255,0.3)'
                        : 'rgba(255,255,255,0.7)',
                    overflow: 'hidden',
                    borderRadius: 16,
                    elevation: 0,
                  },
                ]}
                key={item.id}
              >
                <TouchableOpacity
                  key={item.id}
                  style={[
                    styles.buttonAnswer,
                    {
                      minHeight: buttonFixedHeight,
                      overflow: 'hidden',
                    },
                  ]}
                  onPress={() => onPressAnswer(item)}
                  disabled={!enableSelectAnswer}
                >
                  {Platform.OS === 'ios' ? (
                    <BlurView
                      style={{
                        position: 'absolute',
                        width: '100%',
                        height: '100%',
                        borderRadius: 16,
                      }}
                      blurType="light"
                      blurAmount={10}
                      reducedTransparencyFallbackColor="white"
                    />
                  ) : null}
                  <View style={styles.buttonInnerView}>
                    <PercentageProgress
                      percentageValue={Math.round(item?.percent || 0)}
                      maxWidth={wp(100) - 30}
                      ref={ref => {
                        progressRefs.current[index] = ref;
                      }}
                      isSelectedAnswer={item.id === selectedAnswer?.id}
                    />
                    <Animated.View
                      style={[
                        textAnswerStyle,
                        styles.rightAnswerContainer,
                        {
                          minHeight: buttonFixedHeight,
                        },
                      ]}
                    >
                      <Text
                        Din79Font
                        size={12}
                        style={[styles.answerTitle, {color: textColor}]}
                        numberOfLines={2}
                      >
                        {item.answerTitle}
                      </Text>
                      <Text
                        numberOfLines={2}
                        size={12}
                        style={{color: textColor}}
                      >
                        {item.answerDescription}
                      </Text>
                    </Animated.View>
                    <Animated.View
                      style={[
                        textPercentStyle,
                        {
                          position: 'absolute',
                          right: 16,
                          zIndex: 5,
                          overflow: 'hidden',
                        },
                      ]}
                    >
                      <Svg height="20" width={TEXT_PERCENT_WIDTH}>
                        <SvgText
                          fill={
                            item.id === selectedAnswer?.id
                              ? 'rgba(2, 135, 0, 1)'
                              : 'rgba(153, 153, 153, 1)'
                          }
                          stroke="white"
                          strokeWidth={1.2}
                          fontSize="26"
                          x="100%"
                          y="19"
                          fontFamily={
                            Platform.OS === 'ios'
                              ? 'DIN Next 79'
                              : 'DINNext79_bold'
                          }
                          strokeLinejoin={'round'}
                          textAnchor={'end'}
                          fontWeight={'900'}
                          letterSpacing={1.1}
                        >
                          {`${Math.round(item.percent || 0)}%`}
                        </SvgText>
                      </Svg>
                      {/* <Text
                        size={26}
                        Din79Font
                        style={styles.textPercentage}
                        numberOfLines={1}
                        ellipsizeMode={'clip'}
                      >{`${Math.round(item.percent)}%`}</Text> */}
                    </Animated.View>
                  </View>
                </TouchableOpacity>
              </View>
            );
          })}
          {showButtonAnother && (
            <TouchableOpacity
              style={[
                appStyles.hCenter,
                appStyles.pHSm,
                styles.buttonAnotherAnswer,
              ]}
              onPress={pressAnother}
            >
              <Text size={12} style={styles.textAnotherAnswer} Din79Font black>
                {t('home.cartner_convos.answer_another')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </CustomImageBackground>
  );
};

const styles = StyleSheet.create({
  containerView: {
    minHeight: 673,
    marginBottom: -60,
  },
  questionTitle: {
    textAlign: 'center',
    lineHeight: 21.52,
    marginTop: 86,
    fontWeight: '800',
    letterSpacing: 1.1,
    textTransform: 'uppercase',
    minHeight: 63,
    marginBottom: 10,
  },
  answerSectionView: {width: '100%'},
  buttonAnswer: {
    width: '100%',
    overflow: 'hidden',
    justifyContent: 'center',
  },
  buttonAnotherAnswer: {
    width: '100%',
    borderRadius: 24,
    backgroundColor: 'white',
    paddingHorizontal: 15,
    paddingVertical: 14,
    marginTop: 23,
  },
  textAnotherAnswer: {
    textTransform: 'uppercase',
    fontWeight: '700',
    letterSpacing: 1.62,
  },
  answerTitle: {
    letterSpacing: 1.62,
    textTransform: 'uppercase',
    marginBottom: Platform.OS === 'ios' ? 4 : 2,
    fontWeight: '700',
  },
  textPercentage: {
    color: 'rgba(75, 165, 47, 1)',
    marginRight: 8,
    fontWeight: '800',
    letterSpacing: 1.1,
  },
  buttonInnerView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  rightAnswerContainer: {
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingVertical: 10,
    zIndex: 3,
  },
});

export default React.forwardRef(QuestionPage);
