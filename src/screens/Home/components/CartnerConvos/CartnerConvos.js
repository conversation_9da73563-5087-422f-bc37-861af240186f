import React, {useEffect, useImperativeHandle, useRef, useState} from 'react';
import {
  Image,
  View,
  StyleSheet,
  Platform,
  ActivityIndicator,
} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import {widthPercentageToDP} from 'react-native-responsive-screen';
import {getLastSummary, getUserCurrentQuestion} from 'requests/home';
import {showToast} from 'utils/toast';
import QuestionPage from './QuestionPage';
import {t} from 'i18next';
import {isEmpty} from 'lodash';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import appStyles from 'styles/global';
import {preloadImagesAsync} from 'utils/image';
import FastImage from 'react-native-fast-image/src';

const LOAD_IMAGE_TIMEOUT = 4000;
const CartnerConvos = ({}, ref) => {
  const [firstQuestion, setFirstQuestion] = useState(null);
  const [secondQuestion, setSecondQuestion] = useState(null);
  const [loadNextQuestionDone, setLoadNextQuestionDone] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const tabBarheight = useBottomTabBarHeight();
  useImperativeHandle(ref, () => ({
    refreshData: async () => {
      return await reloadData();
    },
  }));
  const reloadData = async () => {
    let dataUserQuestion = null;
    try {
      if (isLoading) return;
      setLoading(true);
      const currentIndex = carouselRef?.current?.getCurrentIndex() || 0;
      const isFirstPage = currentIndex % 2 === 0;
      if (isFirstPage) {
        dataUserQuestion = await loadFirstQuestion(true);
      } else {
        dataUserQuestion = await loadSecondQuestion(true);
      }
      if (isEmpty(dataUserQuestion)) {
        dataUserQuestion = await getLastSummary();
        if (dataUserQuestion && !isEmpty(dataUserQuestion)) {
          dataUserQuestion.isLastSummary = true;
        }
      }

      if (isFirstPage) {
        setFirstQuestion(dataUserQuestion);
      } else {
        setSecondQuestion(dataUserQuestion);
      }
      setLoading(false);
    } catch (error) {
      if (isLoading) {
        setLoading(false);
      }
    }

    return dataUserQuestion;
  };

  const timeoutPromise = timeout => {
    return new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Timeout exceeded')), timeout),
    );
  };

  const runWithTimeout = async (task, timeout) => {
    try {
      await Promise.race([task, timeoutPromise(timeout)]);
      console.log('Task completed');
    } catch (error) {
      console.log(error.message);
    }
  };

  const loadFirstQuestion = async (initialLoad = false) => {
    try {
      //get Data question
      const userCurrentQuestion = await getUserCurrentQuestion();
      let questionImage = [];
      if (userCurrentQuestion && userCurrentQuestion?.imageUrl) {
        questionImage.push({
          uri: userCurrentQuestion.imageUrl,
          priority: FastImage.priority.high,
          id: userCurrentQuestion.id,
        });
      }
      if (questionImage?.length > 0) {
        const loadImgTask = preloadImagesAsync(questionImage);
        try {
          await runWithTimeout(loadImgTask, LOAD_IMAGE_TIMEOUT);
        } catch (error) {}
      }

      if (!initialLoad) {
        setFirstQuestion(userCurrentQuestion);
      }
      return userCurrentQuestion;
    } catch (error) {
      return null;
    }
  };
  const loadSecondQuestion = async (initialLoad = false) => {
    try {
      //get Data question
      const userCurrentQuestion = await getUserCurrentQuestion();
      let questionImage = [];
      if (userCurrentQuestion && userCurrentQuestion.imageUrl) {
        questionImage.push({
          uri: userCurrentQuestion?.imageUrl,
          priority: FastImage.priority.high,
          id: userCurrentQuestion?.id,
        });
      }
      if (questionImage?.length > 0) {
        const loadImgTask = preloadImagesAsync(questionImage);
        try {
          await runWithTimeout(loadImgTask, LOAD_IMAGE_TIMEOUT);
        } catch (error) {}
      }
      if (!initialLoad) {
        setSecondQuestion(userCurrentQuestion);
      }
      return userCurrentQuestion;
    } catch (error) {
      return null;
    }
  };

  const loadNextQuestion = async () => {
    const currentIndex = carouselRef?.current?.getCurrentIndex() || 0;
    let dtNextQuestion = null;
    const isFirstPage = currentIndex % 2 === 0;
    if (isFirstPage) {
      dtNextQuestion = await loadSecondQuestion();
    } else {
      dtNextQuestion = await loadFirstQuestion();
    }
    if (!isEmpty(dtNextQuestion)) {
      setLoadNextQuestionDone(true);
    } else {
      setLoadNextQuestionDone(false);
    }
  };

  const carouselRef = useRef(null);
  const goToNextQuestion = (index, currentQuestion) => {
    //when user has next question
    if (currentQuestion?.isNextQuestion && loadNextQuestionDone) {
      carouselRef?.current?.scrollTo?.({count: 1, animated: true});
    } else {
      //try to get next question again
      if (!loadNextQuestionDone) {
        showToast({
          type: 'error',
          message: t('home.cartner_convo.get_next_question_fail'),
          subText: t('home.cartner_convo.please_try_again'),
        });
        loadNextQuestion();
      }
    }
  };

  const renderItem = ({item, index}) => {
    return (
      <QuestionPage
        question={item}
        goToNextQuestion={currentQuestion =>
          goToNextQuestion(index, currentQuestion)
        }
        loadNextQuestion={loadNextQuestion}
        reloadQuestion={reloadData}
      />
    );
  };

  if (isEmpty(firstQuestion) && isEmpty(secondQuestion)) {
    return null;
  }

  return (
    <View
      style={{
        marginTop: 15,
        marginBottom: -(tabBarheight + (Platform.OS === 'ios' ? 30 : 0)),
      }}
    >
      {isLoading && (
        <View style={[appStyles.absoluteFill, appStyles.center, {zIndex: 99}]}>
          <ActivityIndicator size="large" color={'black'} />
        </View>
      )}
      <Carousel
        data={[firstQuestion, secondQuestion]}
        renderItem={renderItem}
        ref={carouselRef}
        enabled={false}
        width={widthPercentageToDP(100)}
        height={673}
        loop
      />
      <View
        style={{
          width: '100%',
          position: 'absolute',
          top: 15,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Image
          source={require('assets/imgs/cartnerConvos/cart_convos_logo.png')}
          style={{width: 218, height: 52}}
          resizeMode={'contain'}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({});

export default React.forwardRef(CartnerConvos);
