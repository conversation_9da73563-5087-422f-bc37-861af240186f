import React, {useRef, useImperativeHandle, useState} from 'react';
import {View, Platform} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import LoyaltyPointHeader from 'screens/Shop/components/ShopHeader';
import PageTheDaily from './PageTheDaily';

const HomeShop = ({}, ref) => {
  const [hasData, setHasData] = useState(false);
  const pageTheDailyRef = useRef(null);

  const insets = useSafeAreaInsets();

  useImperativeHandle(ref, () => ({
    refreshData: async () => {
      await pageTheDailyRef?.current?.refreshData();
    },
    playVideo: () => {
      pageTheDailyRef?.current?.playVideo();
    },
    pauseVideo: () => {
      pageTheDailyRef?.current?.pauseVideo();
    },
  }));

  return (
    <View>
      <View
        style={{
          paddingTop: insets.top + (Platform.OS === 'ios' ? 5 : 15),
          position: hasData ? 'absolute' : 'relative',
          zIndex: 4,
          width: '100%',
        }}
      >
        <LoyaltyPointHeader
          colorPTS={'#ccc'}
          isDarkBackground={hasData}
          headerStyle={{flex: 1}}
        />
      </View>
      <PageTheDaily ref={pageTheDailyRef} setHasData={setHasData} />
    </View>
  );
};

export default React.forwardRef(HomeShop);
