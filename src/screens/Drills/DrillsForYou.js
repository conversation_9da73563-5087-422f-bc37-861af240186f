import React, {useEffect} from 'react';
import {
  View,
  StyleSheet,
  TouchableWithoutFeedback,
  FlatList,
  Platform,
  SafeAreaView,
} from 'react-native';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import Carousel from 'react-native-snap-carousel';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {connect, useSelector} from 'react-redux';
import {moderateScale} from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Ionicons';
import {useIsFocused} from '@react-navigation/native';
import analytics from '@react-native-firebase/analytics';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';

import {getForYouContent} from 'requests/drills';
import {updateForYouContent} from 'reducers/drills';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import DeeperDrill from './components/DeeperDrill';
import FastImage from 'react-native-fast-image/src';
import {isOtherPayment, openOtherPayment} from 'utils/user';

const DrillsForYou = ({navigation, drillsForYou, updateForYouContent}) => {
  const permissions = useSelector(state => state?.app?.permissions);
  const tabBarheight = useBottomTabBarHeight();
  const isFocused = useIsFocused();
  const user = useSelector(state => state.user);
  const handed = user.golferProfile?.handed;
  const driveLength = user.golferProfile?.maximumDriverDistance;
  const strengths = user.golferProfile?.strongestArea;
  const weaknesses = user.golferProfile?.weakestArea;
  const mostScaredShot = user.golferProfile?.mostScaredShot;
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );

  const hasDeeper =
    handed && driveLength && strengths && weaknesses && mostScaredShot
      ? true
      : false;

  useEffect(() => {
    (async () => {
      try {
        const forYouContent = await getForYouContent(playService);
        updateForYouContent(
          forYouContent.map(category => {
            return {
              ...category,
              data: category?.data?.map(content => {
                return {
                  ...content,
                  playable: permissions?.myTMSubscriptionLevel
                    ? true
                    : content.playable,
                };
              }),
            };
          }),
        );
      } catch (error) {
        showToast({
          type: 'error',
          message: t('drills.browse.working_on_our_swing'),
          subText: t('drills.collections.videos_ready_soon'),
        });
      }
    })();
  }, [isFocused]);

  const getVideos = () => {
    // Map all the for you videos
    return drillsForYou
      .map(category => {
        return category.data;
      })
      .flat();
  };
  const onPressOpen = async item => {
    if (item.playable || item.freeAccess) {
      navigation.navigate('Video', {
        video: {
          id: item.video_id,
          title: item.title,
          host: item.video_type,
          contentId: item.id,
          origin: 'DrillsForYou',
          videoUrl: item.video_url,
          videoProvider: item.dataSource,
          forYouVideos: getVideos(),
        },
      });
      await analytics().logEvent('drills_video_open', {
        id: item.id,
        video_id: item.video_id,
        title: item.title,
      });
    } else {
      if (isOtherPayment(permissions)) {
        openOtherPayment();
        return;
      }
    }
  };
  const renderImages = ({item}) => {
    return (
      <TouchableWithoutFeedback
        onPress={() =>
          navigation.push('DrillsCollections', {
            title: item.title,
          })
        }
      >
        <View style={[appStyles.mRSm]}>
          <FastImage
            style={[styles.image, appStyles.borderRadius]}
            source={{
              uri: item.image,
              cache: FastImage.cacheControl.web,
            }}
          />
          <View style={[styles.imageDetails]}>
            <Text
              DINbold
              style={[
                appStyles.white,
                appStyles.pBMd,
                appStyles.xxl,
                appStyles.textCenter,
                appStyles.pHLg,
                appStyles.uppercase,
              ]}
            >
              {item.title}
            </Text>
            {item.descritpion && (
              <Text style={[appStyles.white, appStyles.pBMd]}>
                {item.descritpion}
              </Text>
            )}
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  const renderVideo = ({item}) => {
    return (
      <TouchableWithoutFeedback onPress={() => onPressOpen(item)}>
        <View
          style={[
            {
              opacity: !item.playable && !item.freeAccess ? 0.5 : 1,
              marginLeft: '4%',
            },
          ]}
        >
          <FastImage
            style={[styles.video, appStyles.darkGreyBg, {marginBottom: 10}]}
            source={{
              uri: item.primaryImage,
              cache: FastImage.cacheControl.web,
            }}
          />
          <View style={[appStyles.row]}>
            {!item.playable && !item.freeAccess && (
              <Icon
                name="md-lock-closed"
                color={'#BDBDBD'}
                size={16}
                style={{
                  marginRight: 2,
                  marginTop:
                    item?.tags?.mytmInstructor?.length > 0
                      ? Platform.OS === 'ios'
                        ? hp('1.2%') + 8
                        : hp('1.2%') + 9
                      : Platform.OS === 'ios'
                      ? -2
                      : 1,
                }}
              />
            )}
            <View
              style={{
                paddingLeft: !item.playable && !item.freeAccess ? 0 : 15,
                paddingRight: 15,
              }}
            >
              {item?.tags?.mytmInstructor?.length > 0 && (
                <Text style={[appStyles.xxs, styles.txtAthlete]}>
                  {item?.tags.mytmInstructor[0].title}
                </Text>
              )}
              <Text style={[appStyles.white, appStyles.xsm]}>{item.title}</Text>
              {item.description && (
                <Text style={[appStyles.grey, appStyles.xs]}>
                  {item.description}
                </Text>
              )}
              {item?.duration && (
                <Text style={[appStyles.xsm, {color: '#BDBDBD'}]} italicFont>
                  {`(${item.duration})`}
                </Text>
              )}
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  const renderItem = ({item}) => {
    return (
      <View style={[styles.section, appStyles.pTMd]}>
        <View
          style={[{paddingHorizontal: 25}, appStyles.hCenter, styles.heading]}
        >
          <Text
            style={[appStyles.white, appStyles.bold, {fontSize: hp('2.35%')}]}
          >
            {item.category}
          </Text>
        </View>
        <Carousel
          data={item.data}
          renderItem={item.category === 'images' ? renderImages : renderVideo}
          sliderWidth={wp('100%')}
          itemWidth={item.category === 'images' ? wp('88%') : wp('70%')}
          enableSnap={false}
          layout={'default'}
          activeSlideAlignment={'start'}
          removeClippedSubviews={false}
          inactiveSlideScale={1}
          inactiveSlideOpacity={1}
        />
      </View>
    );
  };

  return (
    <>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.lightSlateGray]}>
        {!hasDeeper ? (
          <DeeperDrill />
        ) : (
          <FlatList
            style={appStyles.flex}
            contentContainerStyle={{paddingBottom: tabBarheight}}
            data={drillsForYou}
            renderItem={renderItem}
            keyExtractor={item => item.category}
            ListHeaderComponent={() => {
              return (
                <View style={[appStyles.pTMd, {paddingHorizontal: 25}]}>
                  <Text
                    style={[
                      {fontSize: hp('3.5%')},
                      appStyles.pBSm,
                      appStyles.white,
                      appStyles.textLeft,
                    ]}
                    DINbold
                  >
                    drills.these_drills_were_made_for_you
                  </Text>
                  <Text
                    style={[
                      {fontSize: wp('3.65%')},
                      appStyles.light,
                      appStyles.lightGrey,
                      appStyles.textLeft,
                    ]}
                  >
                    drills.hand_selected_drills
                  </Text>
                </View>
              );
            }}
          />
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  section: {
    paddingBottom: 40,
  },
  heading: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 16,
  },
  video: {
    height: moderateScale(140),
    width: '100%',
    borderRadius: 6,
  },
  image: {
    height: moderateScale(390),
    width: moderateScale(325),
  },
  imageDetails: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  txtAthlete: {
    color: '#BDBDBD',
    fontWeight: '500',
    paddingVertical: Platform.OS === 'android' ? 0 : 4,
  },
});

const mapStateToProps = state => ({
  drillsForYou: state.drills.forYouContent,
});

const mapDispatchToProps = {updateForYouContent};

export default connect(mapStateToProps, mapDispatchToProps)(DrillsForYou);
