import React, {useEffect, useState} from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  TouchableWithoutFeedback,
  Platform,
} from 'react-native';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {connect, useSelector} from 'react-redux';
import {moderateScale} from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/Ionicons';
import DeviceInfo from 'react-native-device-info';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';
import Button from 'components/Button';

import {getSavedContent} from 'requests/drills';
import {updateUserSavedContent} from 'reducers/drills';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image/src';
import {isOtherPayment, openOtherPayment} from 'utils/user';

const DrillsSaved = ({
  navigation: {navigate},
  drillsSavedContent,
  updateUserSavedContent,
}) => {
  const permissions = useSelector(state => state?.app?.permissions);
  const tabBarheight = useBottomTabBarHeight();
  const isTablet = DeviceInfo.isTablet();
  const [count, setCount] = useState(5);

  const getUserSavedContent = async take => {
    try {
      // Make request to retrieve user's saved content
      const userSavedContent = await getSavedContent(take);
      // Update saved content in redux
      updateUserSavedContent(
        userSavedContent.map(content => {
          return {
            ...content,
            playable: permissions?.myTMSubscriptionLevel
              ? true
              : content.playable,
          };
        }),
      );
    } catch (error) {
      showToast({
        type: 'error',
        message: t('drills.browse.working_on_our_swing'),
        subText: t('drills.saved.videos_back_to_you_soon'),
      });
    }
  };

  useEffect(() => {
    getUserSavedContent(count);
  }, []);

  const renderVideos = ({item, index}) => {
    return (
      <TouchableWithoutFeedback
        onPress={() => {
          if (item.playable || item.data?.freeAccess) {
            navigate('Video', {
              video: {
                id: item.data?.video_id,
                title: item.data?.title,
                host: item.data?.video_type,
                contentId: item.data?.id,
              },
            });
            return;
          } else if (isOtherPayment(permissions)) {
            openOtherPayment();
            return;
          }
        }}
      >
        <View
          style={[
            appStyles.flex,
            appStyles.row,
            appStyles.mBSm,
            index === 0 ? appStyles.pTSm : {},
            {
              opacity: !item.playable && !item.data?.freeAccess ? 0.5 : 1,
              paddingHorizontal: 12,
            },
          ]}
        >
          <View style={appStyles.mRXs}>
            <FastImage
              style={[styles.image]}
              source={{
                uri: item.data?.primaryImage,
                cache: FastImage.cacheControl.web,
              }}
            />
          </View>
          <View style={appStyles.flex}>
            {item?.tags?.mytmInstructor.length > 0 && (
              <Text style={[appStyles.xxs, styles.txtAthlete]}>
                {item?.tags.mytmInstructor[0].title}
              </Text>
            )}
            <Text numberOfLines={3} style={[appStyles.white, appStyles.xsm]}>
              {item.data?.title}
            </Text>
            {item.data?.duration && (
              <Text style={[appStyles.xsm, {color: '#BDBDBD'}]} italicFont>
                {`(${item.data.duration})`}
              </Text>
            )}
            {!item.playable && !item.data?.freeAccess ? (
              <View
                style={[
                  appStyles.row,
                  appStyles.hCenter,
                  appStyles.mTSm,
                  Platform.OS === 'ios' ? appStyles.mBXs : {},
                ]}
              >
                <Icon
                  name="md-lock-closed"
                  color={'#BDBDBD'}
                  size={16}
                  style={{marginRight: 2}}
                />
                <Text
                  style={[
                    appStyles.grey,
                    appStyles.semiBold,
                    appStyles.mLXs,
                    appStyles.xs,
                  ]}
                >
                  Upgrade to Unlock Access
                </Text>
              </View>
            ) : null}
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  const onEndReached = async () => {
    const newCount = count + 5;
    setCount(newCount);
    await getUserSavedContent(newCount);
  };
  return (
    <>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View
        style={[appStyles.flex, appStyles.lightSlateGray]}
        edges={['right', 'bottom', 'left']}
      >
        <FlatList
          style={appStyles.flex}
          contentContainerStyle={{paddingBottom: tabBarheight}}
          data={drillsSavedContent}
          renderItem={renderVideos}
          keyExtractor={item => item.data?.id}
          onEndReached={onEndReached}
          ListEmptyComponent={() => {
            return (
              <View
                style={[
                  appStyles.flex,
                  appStyles.hCenter,
                  appStyles.pTLg,
                  appStyles.pHSm,
                ]}
              >
                <Text
                  style={[
                    appStyles.white,
                    appStyles.bold,
                    appStyles.md,
                    appStyles.mBSm,
                    appStyles.textCenter,
                  ]}
                >
                  drills.not_save_videos
                </Text>
                <Button
                  style={appStyles.fullWidth}
                  text={'drills.saved.view_videos'}
                  backgroundColor="white"
                  textColor="black"
                  borderColor="white"
                  onPress={() => navigate('DrillsBrowse')}
                  centered
                  DINbold
                />
              </View>
            );
          }}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  image: {
    height: moderateScale(87),
    width: moderateScale(158),
    borderRadius: 6,
  },
  txtAthlete: {
    color: '#BDBDBD',
    paddingVertical: Platform.OS === 'android' ? 0 : 4,
    fontWeight: '500',
  },
});

const mapStateToProps = state => ({
  drillsSavedContent: state.drills.savedContent,
});

const mapDispatchToProps = {updateUserSavedContent};

export default connect(mapStateToProps, mapDispatchToProps)(DrillsSaved);
