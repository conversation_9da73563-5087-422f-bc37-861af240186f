import React, {useEffect, useState} from 'react';
import {
  View,
  TouchableWithoutFeedback,
  FlatList,
  StyleSheet,
  ImageBackground,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {connect} from 'react-redux';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import analytics from '@react-native-firebase/analytics';

import Text from 'components/Text';
import FocusAwareStatusBar from 'components/FocusAwareStatusBar';

import {getBrowseCategories} from 'requests/drills';
import {updateBrowseCategories} from 'reducers/drills';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import IconMismatched from 'assets/imgs/logo_sftw.svg';
import bg_Approach from 'assets/imgs/drillBrowse/bg_Approach.jpg';
import bg_AroundtheGreen from 'assets/imgs/drillBrowse/bg_AroundtheGreen.jpg';
import bg_Fitness from 'assets/imgs/drillBrowse/bg_Fitness.jpg';
import bg_MentalGame from 'assets/imgs/drillBrowse/bg_MentalGame.jpg';
import bg_OfftheTee from 'assets/imgs/drillBrowse/bg_OfftheTee.jpg';
import bg_Overall from 'assets/imgs/drillBrowse/bg_Overall.jpg';
import bg_Putting from 'assets/imgs/drillBrowse/bg_Putting.jpg';
import bg_SFTW from 'assets/imgs/drillBrowse/bg_SFTW.jpg';

const DrillsBrowse = ({navigation, drillsBrowse, updateBrowseCategories}) => {
  const tabBarheight = useBottomTabBarHeight();
  const [loading, setLoading] = useState();

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const browseCategories = await getBrowseCategories();
        updateBrowseCategories(browseCategories);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('drills.browse.working_on_our_swing'),
          subText: t('drills.browse.drill_categories_ready_soon'),
        });
      }
    })();
  }, []);

  const getImageFromLocal = item => {
    switch (item.slug) {
      case 'overall':
        return bg_Overall;
      case 'off-the-tee':
        return bg_OfftheTee;
      case 'approach':
        return bg_Approach;
      case 'around-the-green':
        return bg_AroundtheGreen;
      case 'putting':
        return bg_Putting;
      case 'mental-game':
        return bg_MentalGame;
      case 'fitness':
        return bg_Fitness;
      default:
        return bg_SFTW;
    }
  };

  const renderCategories = ({item, index}) => (
    <TouchableWithoutFeedback
      onPress={async () => {
        let title = item?.title;
        if (!title && item.slug === 'sftw') {
          title = 'SFTW';
        }

        navigation.push('DrillsCollections', {
          title: title,
          origin: 'collection',
          tags: item.slug,
        });
        await analytics().logEvent('drills_browse_open', {
          category: item.title,
        });
      }}
    >
      <View
        style={[
          appStyles.flex,
          appStyles.hCenter,
          appStyles.vCenter,
          index === 0 ? {marginTop: 15} : {marginTop: 10},
        ]}
      >
        <ImageBackground
          style={[styles.image, appStyles.darkGreyBg, {borderRadius: wp('2%')}]}
          source={getImageFromLocal(item)}
          imageStyle={{borderRadius: wp('2%')}}
        >
          <View
            style={[
              appStyles.absoluteFill,
              {
                backgroundColor: 'black',
                opacity: 0.2,
                borderRadius: wp('2%'),
              },
            ]}
          />
          <View style={[styles.imageDetails]}>
            {item?.title ? (
              <Text DINbold style={[appStyles.xl, appStyles.white]}>
                {item.title?.toUpperCase()}
              </Text>
            ) : (
              <IconMismatched />
            )}
          </View>
        </ImageBackground>
      </View>
    </TouchableWithoutFeedback>
  );
  return (
    <>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.lightSlateGray]}>
        {loading ? (
          <ActivityIndicator style={appStyles.pTLg} color="white" />
        ) : (
          <FlatList
            style={appStyles.flex}
            contentContainerStyle={{paddingBottom: tabBarheight + 15}}
            data={drillsBrowse}
            renderItem={renderCategories}
            keyExtractor={item => item.title}
          />
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  image: {
    height: wp('95%') / 1.33,
    width: wp('95%'),
  },
  imageDetails: {
    position: 'absolute',
    bottom: wp('8%'),
    alignSelf: 'center',
  },
});

const mapStateToProps = state => ({
  drillsBrowse: state.drills.browse,
});

const mapDispatchToProps = {updateBrowseCategories};

export default connect(mapStateToProps, mapDispatchToProps)(DrillsBrowse);
