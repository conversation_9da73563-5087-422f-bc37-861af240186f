import React from 'react';
import {View} from 'react-native';
import Text from '../../../components/Text';
import Button from '../../../components/Button';
import appStyles from '../../../styles/global';
import stylesFont from '../../../components/Text/styles';
import {useSelector} from 'react-redux';
import {setStartDeeperInsightScreen} from '../../../utils/commonVariable';
import {useNavigation} from '@react-navigation/native';

const DeeperDrill = () => {
  const navigation = useNavigation();
  const user = useSelector(state => state.user);
  const quiz = useSelector(state => state.quiz?.quiz);
  const handed = user.golferProfile?.handed || quiz?.handed;
  const driveLength = user.golferProfile?.driveLength || quiz?.driveLength;
  const strengths = user.golferProfile?.strengths || quiz?.strengths;
  const weaknesses = user.golferProfile?.weaknesses || quiz?.weaknesses;
  const mostScaredShot =
    user.golferProfile?.mostScaredShot || quiz?.mostScaredShot;
  const startDeeperDrill = () => {
    setStartDeeperInsightScreen('StartDeeperDrill');
    if (!handed) {
      return navigation.navigate('Quiz', {
        screen: 'QuizHanded',
      });
    } else if (!driveLength) {
      return navigation.navigate('Quiz', {
        screen: 'QuizDriveLength',
      });
    } else if (!strengths) {
      return navigation.navigate('Quiz', {
        screen: 'QuizStrength',
      });
    } else if (!weaknesses) {
      return navigation.navigate('Quiz', {
        screen: 'QuizWeakness',
      });
    } else if (!mostScaredShot) {
      return navigation.navigate('Quiz', {
        screen: 'QuizFear',
      });
    } else {
      return navigation.navigate('Quiz', {
        screen: 'QuizFear',
      });
    }
  };

  return (
    <View style={[appStyles.mHSm, appStyles.mTMd]}>
      <Text style={[appStyles.white, appStyles.xxl]} DINbold>
        deeper_drill.know_your_game
      </Text>
      <Text style={[appStyles.white, appStyles.xxl]} DINbold>
        deeper_drill.grow_your_game
      </Text>
      <Text
        style={[
          appStyles.white,
          appStyles.mTXs,
          appStyles.mBSm,
          stylesFont.font,
        ]}
      >
        deeper_drill.questions
      </Text>
      <Button
        text="deeper_drill.get_started"
        backgroundColor="white"
        centered
        DINbold
        onPress={() => startDeeperDrill()}
      />
    </View>
  );
};

export default DeeperDrill;
