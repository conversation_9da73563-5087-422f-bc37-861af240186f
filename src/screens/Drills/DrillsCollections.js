import React, {useEffect, useState} from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Platform,
  SafeAreaView,
} from 'react-native';
import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {moderateScale} from 'react-native-size-matters';
import Icon from 'react-native-vector-icons/FontAwesome5';
import DeviceInfo from 'react-native-device-info';
import {connect, useSelector} from 'react-redux';
import analytics from '@react-native-firebase/analytics';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Text from 'components/Text';

import {getBrowseContent} from 'requests/drills';
import {updateCollectionContent} from 'reducers/drills';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import FastImage from 'react-native-fast-image/src';
import {isOtherPayment, openOtherPayment} from 'utils/user';

const DrillsCollections = ({
  navigation: {navigate},
  route,
  updateCollectionContent,
}) => {
  const videos = useSelector(state => state?.drills?.collectionContent);
  const permissions = useSelector(state => state?.app?.permissions);
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );

  const tags = route.params?.tags;
  const isTablet = DeviceInfo.isTablet();
  const tabBarheight = useBottomTabBarHeight();
  const [loading, setLoading] = useState();

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const contentVideos = await getBrowseContent(tags, playService);
        updateCollectionContent(
          contentVideos.map(content => {
            return {
              ...content,
              playable: permissions?.myTMSubscriptionLevel
                ? true
                : content.playable,
            };
          }),
        );
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('drills.browse.working_on_our_swing'),
          subText: t('drills.collections.videos_ready_soon'),
        });
      }
    })();
  }, []);
  const renderVideos = ({item, index}) => (
    <TouchableWithoutFeedback
      onPress={
        item.playable || item.freeAccess
          ? async () => {
              navigate('Video', {
                video: {
                  id: item.video_id,
                  title: item.title,
                  host: item.video_type,
                  contentId: item.id,
                  videoUrl: item.video_url,
                  origin: 'DrillsCollections',
                  videoProvider: item.dataSource,
                  tags: tags,
                },
              });
              await analytics().logEvent('drills_video_open', {
                id: item.id,
                video_id: item.video_id,
                title: item.title,
              });
            }
          : () => {
              if (isOtherPayment(permissions)) {
                openOtherPayment();
                return;
              }
            }
      }
    >
      <View
        style={[
          appStyles.flex,
          appStyles.row,
          appStyles.mBSm,
          index === 0 ? appStyles.pTMd : {},
          {
            opacity: !item.playable && !item.freeAccess ? 0.5 : 1,
            paddingHorizontal: 12,
          },
        ]}
      >
        <View style={appStyles.mRXs}>
          <FastImage
            style={[styles.image, appStyles.darkGreyBg]}
            source={{
              uri: item.primaryImage,
              cache: FastImage.cacheControl.web,
            }}
          />
        </View>
        <View style={[appStyles.flex, {paddingVertical: 5}]}>
          {item?.tags?.mytmInstructor.length > 0 && (
            <Text
              style={[
                appStyles.xxs,
                {
                  color: '#BDBDBD',
                  paddingVertical: Platform.OS === 'android' ? 0 : 4,
                  fontWeight: '500',
                },
              ]}
            >
              {item?.tags.mytmInstructor[0].title}
            </Text>
          )}
          <Text numberOfLines={3} style={[appStyles.white, appStyles.xsm]}>
            {item.title}
          </Text>
          {!item.playable && !item.freeAccess ? (
            <View
              style={[
                appStyles.row,
                appStyles.hCenter,
                appStyles.mTSm,
                Platform.OS === 'ios' ? appStyles.mBXs : {},
              ]}
            >
              <Icon
                name="lock"
                color={GREY}
                size={isTablet ? wp('2%') : wp('3%')}
                style={{transform: [{scaleX: -1}]}}
              />
              <Text
                style={[
                  appStyles.grey,
                  appStyles.semiBold,
                  appStyles.mLXs,
                  appStyles.xs,
                ]}
              >
                shop.upgrade_to_unlock_access
              </Text>
            </View>
          ) : null}
        </View>
      </View>
    </TouchableWithoutFeedback>
  );

  return (
    <>
      <FocusAwareStatusBar barStyle={'light-content'} />
      <View style={[appStyles.flex, appStyles.lightSlateGray]}>
        {loading ? (
          <ActivityIndicator style={appStyles.pTLg} color="white" />
        ) : (
          <FlatList
            style={[appStyles.flex]}
            contentContainerStyle={{paddingBottom: tabBarheight}}
            data={videos}
            renderItem={renderVideos}
            keyExtractor={item => item.id}
          />
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  image: {
    height: moderateScale(87),
    width: moderateScale(158),
    borderRadius: 6,
  },
});

const mapDispatchToProps = {updateCollectionContent};

export default connect(null, mapDispatchToProps)(DrillsCollections);
