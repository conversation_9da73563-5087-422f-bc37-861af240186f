import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  View,
  ImageBackground,
  ScrollView,
  TouchableOpacity,
  Image,
  StatusBar,
  StyleSheet,
  Platform,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import Header from 'components/Header';
import Text from 'components/Text';
// import Button from 'components/Button';

import appStyles from 'styles/global';

import {showToast} from 'utils/toast';
import {t} from 'i18next';
import DeviceInfo from 'react-native-device-info';
import Carousel from 'react-native-snap-carousel';
import {
  convertProductIdToImageURL,
  formatProductName,
  openEcomWebview,
  prepareLink,
} from 'utils/shop';
import {getAuth0AccessToken} from 'utils/user';
import {useDispatch} from 'react-redux';
import Images from '../../../assets/imgs/Images';
import HashtagList from './components/HashtagList';
import {getSingleCmsContent} from 'requests/content';
import {productDataList} from 'requests/club-house';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import { getConfig } from 'config/env';

const VideoArticle = ({navigation, route}) => {
  const [loading, setLoading] = useState(false);
  const [article, setArticle] = useState({});
  const [productsData, setProductsData] = useState();
  const hasNotch = DeviceInfo.hasNotch();
  const item = route?.params?.item || route?.params?.params?.item;
  const videoData = article?.data ? article?.data : item;
  const title = videoData?.title;
  const products = videoData?.products;
  const insets = useSafeAreaInsets();

  const dispatch = useDispatch();

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const item = route?.params?.item || route?.params?.params?.item;
        if (item?.entry_type) {
          const articleContent = await getSingleCmsContent(
            item?.entry_type,
            item?.id,
          );
          setArticle(articleContent);
        } else {
          setArticle(item);
        }
        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('feedAritcle.lost_our_yardage_book'),
          subText: t('feedAritcle.able_to_find_this_article'),
        });
      }
    })();
  }, [route.params]);

  useEffect(() => {
    const getProductData = async () => {
      const data = await productDataList(products?.toString());
      setProductsData(data);
    };
    if (products?.length > 0) {
      getProductData();
    }
  }, [products]);

  const onItemPress = async item => {
    const accessToken = await getAuth0AccessToken(dispatch);
    const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
    const productLink = `https://${ECOM_HOST_URL}/${item}.html`;
    const linkUrl = await prepareLink(productLink, accessToken);
    const imageURL = getImageURL(item);
    openEcomWebview(navigation.navigate, {
      title: item?.productName,
      uri: linkUrl,
      canGoBack: true,
      originUri: productLink,
      imageUrl: imageURL,
    });
  };

  const getImageURL = item => {
    try {
      let imageURL = '';
      if (
        item?.imageGroups.length > 0 &&
        item?.imageGroups[0]?.images.length > 0
      ) {
        imageURL = item?.imageGroups[0]?.images[0]?.disBaseLink;
      }
      if (!imageURL) {
        imageURL = convertProductIdToImageURL(item?.id);
      }
    } catch (error) {
      return '';
    }
  };

  const carouselItem = ({item}) => {
    const imageURL = getImageURL(item);
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => onItemPress(item)}
        style={[styles.itemWrapper, appStyles.viewShadowLightBig]}
      >
        <View style={{flex: 1}}>
          <Image style={[styles.itemImage]} source={{uri: imageURL}} />
          <View style={{marginHorizontal: 4}}>
            <Text Din79Font style={styles.copyText} white size={16}>
              {item?.name || formatProductName(item?.productName)}
            </Text>
            {item?.price && (
              <View style={{flexDirection: 'row'}}>
                <Text size={12} black>
                  ${item?.priceDiscount}
                </Text>
                <Text size={12} style={styles.priceText}>
                  ${item?.price}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const openVideo = () => {
    navigation.navigate('Video', {
      video: {
        id: videoData?.video_id,
        title: videoData?.title,
        host: videoData?.video_type,
        contentId: videoData?.id,
        origin: 'ArticleVideo',
        videoUrl: videoData?.video_url,
        videoProvider: videoData?.dataSource,
      },
    });
  };

  const showHashtagView = () => {
    if (!article?.tags) {
      return;
    }
    let arrTags = [];
    Object.keys(article?.tags)?.forEach(key => {
      arrTags = arrTags.concat(article?.tags[key]);
    });
    let tags = arrTags.map(item => item?.title);
    tags = tags.filter(tag => !!tag);

    return <HashtagList hashtags={tags} light />;
  };

  return (
    <View style={[appStyles.flex, appStyles.blackBg]}>
      <StatusBar barStyle={'light-content'} />
      <TouchableOpacity
        style={{
          flex: 1,
          alignItems: 'center',
          paddingVertical: 10,
          paddingHorizontal: 10,
          borderRadius: 24,
          position: 'absolute',
          right: 6,
          zIndex: 9999,
          top: insets.top + (Platform.OS === 'ios' ? -10 : 10),
        }}
        onPress={() => navigation.goBack()}
      >
        <View
          style={{
            borderRadius: 24,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            padding: 1,
          }}
        >
          <Icon name="close" size={20} color={'#fff'} />
        </View>
      </TouchableOpacity>
      {loading || !article ? (
        <View style={[appStyles.pSm, appStyles.white]}>
          <ActivityIndicator style={[appStyles.pTLg]} color={'white'} />
        </View>
      ) : (
        <ScrollView style={[appStyles.blackBg]}>
          <View style={[appStyles.pBMd]}>
            <ImageBackground
              source={{
                uri: videoData?.primaryImage,
              }}
              style={[
                appStyles.darkGreyBg,
                {width: wp('100%'), height: wp('120%')},
              ]}
            >
              <TouchableOpacity
                style={styles.playVideoButton}
                onPress={() => openVideo()}
              >
                <Image source={Images.PlayControls} style={styles.playIcon} />
              </TouchableOpacity>
            </ImageBackground>

            <View style={[appStyles.white, styles.textContainer]}>
              {!!title && (
                <Text
                  DINbold
                  style={[appStyles.black, appStyles.bold, styles.title]}
                >
                  {title}
                </Text>
              )}
              {videoData?.synopsis && (
                <Text style={[appStyles.black, styles.synopsis]}>
                  {videoData?.synopsis}
                </Text>
              )}
            </View>

            {productsData?.length > 0 && (
              <View
                style={[
                  appStyles.pTMd,
                  appStyles.flex,
                  appStyles.row,
                  appStyles.vCenter,
                ]}
              >
                {/* <Text style={{color: 'white'}}>{t('explore_more')}</Text> */}
                <Carousel
                  layout={'default'}
                  data={productsData}
                  sliderWidth={190}
                  itemWidth={176}
                  renderItem={carouselItem}
                  inactiveSlideScale={1}
                  inactiveSlideOpacity={1}
                />
              </View>
            )}
            {showHashtagView()}
          </View>
        </ScrollView>
      )}
    </View>
  );
};

export default VideoArticle;

const styles = StyleSheet.create({
  itemWrapper: {
    width: 168,
    marginLeft: 8,
    flexDirection: 'column',
    borderRadius: 16,
    // backgroundColor: 'white',
    marginBottom: 10,
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 16,
    backgroundColor: 'rgb(45,45,45)',
    alignItems: 'flex-start',
  },
  itemImage: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    width: 160,
    aspectRatio: 1,
    marginBottom: 14,
  },
  copyText: {
    fontWeight: '800',
    textTransform: 'uppercase',
    marginBottom: 14,
    letterSpacing: 1.28,
    lineHeight: 20.68,
  },
  priceText: {
    textDecorationLine: 'line-through',
    color: 'rgba(0, 0, 0, 0.3)',
    marginLeft: 8,
  },
  playVideoButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  playIcon: {width: 80, height: 80},
  textContainer: {marginHorizontal: 16, marginTop: 22},
  title: {
    textTransform: 'uppercase',
    fontSize: 22,
    fontWeight: '800',
    color: 'white',
  },
  synopsis: {
    fontWeight: '400',
    lineHeight: 18,
    fontSize: 16,
    marginTop: 24,
    color: 'white',
  },
});
