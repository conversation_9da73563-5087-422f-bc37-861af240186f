/* eslint-disable react-hooks/exhaustive-deps */
import FeedTile from 'components/HomeTiles/FeedTile';
import React, {useEffect, useState} from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import {getHomeExplorer} from 'requests/content';
import {useSelector} from 'react-redux';

const AllArticle = ({navigation: {navigate}, route}) => {
  const [data, setData] = useState(route.params?.vertical);
  const [loading, setLoading] = useState(false);
  const playService = useSelector(
    state => state?.user?.tmUserIds?.playServicePreference,
  );

  useEffect(() => {
    reloadData();
  }, []);
  const reloadData = async () => {
    setLoading(true);
    const response = await getHomeExplorer(playService);
    setData(response);
    setLoading(false);
  };
  const feed = route.params?.vertical;
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={reloadData} />
        }
      >
        {data?.map((tile, index) => {
          if (tile) {
            return (
              <FeedTile
                key={index}
                navigate={navigate}
                index={index}
                item={tile}
                tilesLength={feed?.length}
              />
            );
          }
          return null;
        })}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default AllArticle;
