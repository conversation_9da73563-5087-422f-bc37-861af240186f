import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  View,
  ImageBackground,
  ScrollView,
  TouchableOpacity,
  Image,
  StatusBar,
  Platform,
  StyleSheet,
  FlatList,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import LinearGradient from 'react-native-linear-gradient';
import HtmlParser from 'components/HtmlView';
import Text from 'components/Text';
import {productDataList} from 'requests/club-house';

import appStyles from 'styles/global';

import {showToast} from 'utils/toast';
import {t} from 'i18next';
import {Instagram} from 'components/Instagram/Instagram';
import Carousel from 'react-native-snap-carousel';
import HashtagList from './components/HashtagList';
import {
  convertProductIdToImageURL,
  formatProductName,
  openEcomWebview,
  prepareLink,
} from 'utils/shop';
import {getAuth0AccessToken} from 'utils/user';
import {useDispatch} from 'react-redux';
import {getSingleCmsContent} from 'requests/content';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {decode} from 'html-entities';
import { getConfig } from 'config/env';
import { GA_logSelectContentEvent } from 'utils/article';

const FeedArticle = ({navigation, route}) => {
  const [loading, setLoading] = useState(false);
  const [article, setArticle] = useState({});
  const [productsData, setProductsData] = useState();
  const [productsPlayer, setProductsPlayer] = useState(null);

  const insets = useSafeAreaInsets();
  const title = article?.data?.title;
  const htmlFields = article?.data?.fields || article?.htmlFields;
  const dispatch = useDispatch();
  const clickLocation = route?.params?.clickLocation;

  const products = article?.data?.products;

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const item = route?.params?.item || route?.params?.params?.item;
        let entryType = item?.entry_type;
        let contentId = item?.id;
        if (!item?.entry_type) {
          entryType = item?.options?.cmsType;
          contentId = item?.options?.ctaCMS;
        }
        if (entryType && contentId) {
          const articleContent = await getSingleCmsContent(
            entryType,
            contentId,
          );
          if (articleContent) {
            setArticle(articleContent);
          } else {
            showToast({
              type: 'error',
              message: t('feedAritcle.lost_our_yardage_book'),
              subText: t('feedAritcle.able_to_find_this_article'),
            });
          }
        } else {
          if (item?.extraData?.content) {
            setArticle({
              data: item?.extraData?.content,
            });
          }
        }

        setLoading(false);
      } catch (error) {
        setLoading(false);
        showToast({
          type: 'error',
          message: t('feedAritcle.lost_our_yardage_book'),
          subText: t('feedAritcle.able_to_find_this_article'),
        });
      }
    })();
  }, [route.params]);

  useEffect(() => {
    const getProductData = async () => {
      const data = await productDataList(products?.toString());
      setProductsData(data);
    };
    if (products?.length > 0) {
      getProductData();
    }
  }, [products]);

  useEffect(() => {
    if (htmlFields) {
      let arrProducts = [];
      for (let i = 0; i < htmlFields.length; i++) {
        if (htmlFields[i]?.type === 'player') {
          htmlFields[i]?.players?.forEach(_val => {
            _val?.products?.forEach(_element => {
              arrProducts = [...arrProducts, _element];
            });
          });
          break;
        }
      }
      const getProductsPlayer = async () => {
        const data = await productDataList(arrProducts.toString());
        if (data?.length > 0) {
          const objProducts = data.reduce((pre, cur) => {
            return {...pre, [cur?.id]: cur};
          }, []);
          setProductsPlayer(objProducts);
        } else {
          setProductsData(null);
        }
      };
      if (arrProducts?.length > 0) {
        getProductsPlayer();
      }
    }
  }, [htmlFields]);

  const getSocialContent = url => {
    if (url.includes('instagram.com')) {
      const arrayURL = url.split('/');
      let index = arrayURL.findIndex(value => value === 'p');
      const indexReel = arrayURL.findIndex(value => value === 'reel');
      if (index < 0 && indexReel >= 0) {
        index = indexReel;
      }
      if (index >= 0 && arrayURL.length > index) {
        const instagramId = arrayURL[index + 1];
        return <Instagram id={instagramId} containerBorderRadius={8} />;
      }
    } else if (url.includes('twitter.com')) {
    } else if (url.includes('youtube.com')) {
    }
    return null;
  };

  const onItemPress = async item => {
    const accessToken = await getAuth0AccessToken(dispatch);
    const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
    const productLink = `https://${ECOM_HOST_URL}/${item}.html`;

    const linkUrl = await prepareLink(productLink, accessToken);
    const imageURL = getImageURL(item);
    openEcomWebview(navigation.navigate, {
      title: item?.productName,
      uri: linkUrl,
      canGoBack: true,
      originUri: productLink,
      imageUrl: imageURL,
      clickLocation: clickLocation,
    });
  };

  const carouselItemImages = ({item}) => {
    return (
      <View style={{flex: 1}}>
        <Image
          style={[styles.fullImage, {width: wp(90)}]}
          source={{uri: item}}
        />
      </View>
    );
  };

  const getImageURL = item => {
    try {
      let imageURL = '';
      if (
        item?.imageGroups.length > 0 &&
        item?.imageGroups[0]?.images.length > 0
      ) {
        imageURL = item?.imageGroups[0]?.images[0]?.disBaseLink;
      }
      if (!imageURL) {
        imageURL = convertProductIdToImageURL(item?.id);
      }
      return imageURL;
    } catch (error) {
      return '';
    }
  };

  const carouselItem = ({item}) => {
    const imageURL = getImageURL(item);
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => onItemPress(item)}
        style={[styles.itemWrapper, appStyles.viewShadowLightBig]}
      >
        <View style={{flex: 1}}>
          <Image
            style={[styles.itemImage, {marginBottom: 14}]}
            source={{uri: imageURL}}
          />
          <View style={{marginHorizontal: 4}}>
            <Text Din79Font style={styles.copyText} size={16}>
              {item?.name || formatProductName(item?.productName)}
            </Text>
            {item?.price && (
              <View style={{flexDirection: 'row'}}>
                <Text size={12} black>
                  ${item?.priceDiscount}
                </Text>
                <Text size={12} style={styles.priceText} black>
                  ${item?.price}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  const showHashtagView = () => {
    if (!article?.tags) {
      return;
    }
    let arrTags = [];
    Object.keys(article?.tags)?.forEach(key => {
      arrTags = arrTags.concat(article?.tags[key]);
    });
    let tags = arrTags.map(item => item?.title);
    tags = tags.filter(tag => !!tag);

    return <HashtagList hashtags={tags} />;
  };

  return (
    <View style={[appStyles.flex, appStyles.whiteBg]}>
      <StatusBar barStyle={'light-content'} />
      <TouchableOpacity
        style={{
          flex: 1,
          alignItems: 'center',
          paddingVertical: 10,
          paddingHorizontal: 10,
          borderRadius: 24,
          position: 'absolute',
          right: 6,
          zIndex: 9999,
          top: insets.top + (Platform.OS === 'ios' ? -10 : 10),
        }}
        onPress={() => navigation.goBack()}
      >
        <View
          style={{
            borderRadius: 24,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            padding: 1,
          }}
        >
          <Icon name="close" size={20} color={'#fff'} />
        </View>
      </TouchableOpacity>
      {loading || !article ? (
        <View style={[appStyles.pSm, appStyles.white]}>
          <ActivityIndicator style={[appStyles.pTLg]} color={'black'} />
        </View>
      ) : (
        <ScrollView style={[appStyles.whiteBg]}>
          <View style={[appStyles.pBMd]}>
            <LinearGradient
              colors={[
                'rgba(255, 255, 255, 0) 100%)',
                'rgba(212, 212, 212, 1)',
              ]}
              style={[
                {
                  width: wp('100%'),
                  top: 1000,
                  bottom: 0,
                  left: 0,
                  position: 'absolute',
                },
              ]}
            />
            <ImageBackground
              source={{
                uri: article?.data?.primaryImage,
              }}
              style={[
                appStyles.darkGreyBg,
                {width: wp('100%'), height: wp(100) * 1.17},
              ]}
            />
            <View
              style={[appStyles.white, {marginHorizontal: 16, marginTop: 22}]}
            >
              {!!title && (
                <Text
                  DINbold
                  style={[
                    appStyles.black,

                    appStyles.bold,
                    {textTransform: 'uppercase', fontSize: 22},
                  ]}
                >
                  {title}
                </Text>
              )}
            </View>
            {!!article?.data?.synopsis && (
              <View
                style={[appStyles.white, {marginHorizontal: 16, marginTop: 12}]}
              >
                <Text style={[appStyles.black, appStyles.bold, {fontSize: 14}]}>
                  {decode(article?.data?.synopsis)
                    ?.replace?.('<strong>', '')
                    .replace?.('</strong>', '')
                    .replace?.('<em>', '')
                    .replace?.('</em>', '')
                    .replace?.('<s>', '')
                    .replace?.('</s>', '')
                    .replace?.('<u>', '')
                    .replace?.('</u>', '')}
                </Text>
              </View>
            )}

            <View style={[appStyles.pVSm, {paddingHorizontal: 16}]}>
              {htmlFields &&
                htmlFields.map((field, index) => {
                  if (field.type === 'heading') {
                    return (
                      <View
                        key={`${field.type}-${index}`}
                        style={[appStyles.pVSm]}
                      >
                        <Text
                          style={[
                            appStyles.bold,
                            {fontSize: hp('2.35')},
                            appStyles.black,
                          ]}
                        >
                          {field.text}
                        </Text>
                      </View>
                    );
                  } else if (field.type === 'paragraph') {
                    if (field.text?.startsWith('<table>')) {
                      return null;
                    }
                    return (
                      <View
                        key={`${field.type}-${index}`}
                        style={[appStyles.pVSm]}
                      >
                        <HtmlParser
                          html={field.text?.replace(/(\r\n|\n|\r)/gm, '')}
                          textComponentProps={{
                            style: appStyles.black,
                            notTranslateKey: true,
                          }}
                          TextComponent={Text}
                          navigation={navigation}
                        />
                      </View>
                    );
                  } else if (field.type === 'quote') {
                    let quote = field.quotation?.replace(/(\r\n|\n|\r)/gm, '');
                    return (
                      <View
                        key={`${field.type}-${index}`}
                        style={[appStyles.pVSm]}
                      >
                        <HtmlParser
                          html={quote}
                          textComponentProps={{
                            style: [
                              appStyles.greyText,
                              {
                                fontFamily:
                                  Platform.OS === 'android'
                                    ? 'NewYork-Regular-Italic'
                                    : 'New York Small Regular Italic',
                              },
                            ],
                            notTranslateKey: true,
                          }}
                          TextComponent={Text}
                          navigation={navigation}
                        />
                        <Text
                          italicFont
                          style={[
                            appStyles.greyText,
                            appStyles.xs,
                            appStyles.pTSm,
                          ]}
                        >
                          {field.author}
                        </Text>
                      </View>
                    );
                  } else if (field?.type === 'images') {
                    if (field?.images.length > 1) {
                      return (
                        <Carousel
                          layout={'default'}
                          data={field?.images}
                          sliderWidth={wp('100%')}
                          itemWidth={wp('92%')}
                          renderItem={carouselItemImages}
                          inactiveSlideScale={1}
                          inactiveSlideOpacity={1}
                          activeSlideAlignment={'start'}
                        />
                      );
                    } else if (field?.images.length === 1) {
                      return (
                        <View style={{flex: 1}}>
                          <Image
                            style={[styles.fullImage, {width: '100%'}]}
                            source={{uri: field?.images[0]}}
                          />
                        </View>
                      );
                    }
                  } else if (field.type === 'socialMedia') {
                    return getSocialContent(field.url);
                  } else if (field.type === 'feature') {
                    return (
                      <View style={{marginTop: 16}}>
                        {field?.image && (
                          <Image
                            style={[styles.fullImage, {width: '100%'}]}
                            source={{uri: field?.image}}
                          />
                        )}
                        {field?.heading && (
                          <View
                            key={`${field.type}-${index}`}
                            style={[appStyles.pVSm]}
                          >
                            <Text
                              style={[
                                appStyles.bold,
                                {fontSize: hp('2.35')},
                                appStyles.black,
                              ]}
                            >
                              {field?.heading}
                            </Text>
                          </View>
                        )}
                        {field?.text && (
                          <View
                            key={`${field.type}-${index}`}
                            style={[appStyles.pVSm]}
                          >
                            <HtmlParser
                              html={field.text?.replace(/(\r\n|\n|\r)/gm, '')}
                              textComponentProps={{
                                style: appStyles.black,
                                notTranslateKey: true,
                              }}
                              TextComponent={Text}
                              navigation={navigation}
                            />
                          </View>
                        )}
                      </View>
                    );
                  } else if (field?.type === 'player') {
                    return (
                      <FlatList
                        data={field?.players}
                        horizontal
                        keyExtractor={(item, _index) => _index}
                        showsHorizontalScrollIndicator={false}
                        renderItem={({item}) => {
                          return (
                            <View style={{marginRight: 5, width: wp(30)}}>
                              <Image
                                source={{uri: item?.headshot}}
                                style={{width: wp(30), height: wp(30)}}
                              />
                              <Text
                                size={16}
                                black
                                style={{
                                  paddingTop: 4,
                                  paddingBottom: 7,
                                  textDecorationLine: 'underline',
                                }}
                              >
                                {item?.title}
                              </Text>
                              {item?.products?.map(product => {
                                if (
                                  productsPlayer &&
                                  productsPlayer?.[product]
                                ) {
                                  return (
                                    <Text
                                      key={product}
                                      size={12}
                                      black
                                      style={{
                                        marginTop: 3,
                                      }}
                                    >
                                      {productsPlayer[product]?.name}
                                    </Text>
                                  );
                                }
                                return null;
                              })}
                            </View>
                          );
                        }}
                      />
                    );
                  }
                })}
            </View>
            {productsData?.length > 0 && (
              <View
                style={[
                  appStyles.pTMd,
                  appStyles.flex,
                  appStyles.row,
                  appStyles.vCenter,
                ]}
              >
                <Carousel
                  layout={'default'}
                  data={productsData}
                  sliderWidth={190}
                  itemWidth={176}
                  renderItem={carouselItem}
                  inactiveSlideScale={1}
                  inactiveSlideOpacity={1}
                />
              </View>
            )}
            {article?.witb && (
              <View>
                <View
                  style={[
                    appStyles.pTMd,
                    appStyles.flex,
                    appStyles.row,
                    appStyles.vCenter,
                  ]}
                >
                  <Carousel
                    layout={'default'}
                    data={article?.witb?.products}
                    sliderWidth={wp('80%')}
                    itemWidth={wp('67%')}
                    renderItem={carouselItemImages}
                    inactiveSlideScale={1}
                    inactiveSlideOpacity={1}
                  />
                </View>
                {article.witb.title && article.witb.cta && (
                  <View
                    style={[
                      appStyles.pHMd,
                      appStyles.flex,
                      appStyles.row,
                      appStyles.spaceBetween,
                      appStyles.hCenter,
                    ]}
                  >
                    {article.witb.title && (
                      <Text style={[appStyles.sm, appStyles.bold]}>
                        {article.witb.title}
                      </Text>
                    )}
                    <TouchableOpacity onPress={() => {}}>
                      <Text style={[appStyles.xs, {color: '#8C8C91'}]}>
                        {article.witb.cta.label}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            )}
            {showHashtagView()}
            {/* {article.cta && (
              <View style={[appStyles.pHMd, appStyles.pTLg]}>
                <Button
                  text={article.cta.label}
                  backgroundColor={'black'}
                  textColor={'white'}
                  centered
                  DINbold
                  onPress={() => {}}
                />
              </View>
            )} */}
          </View>
        </ScrollView>
      )}
    </View>
  );
};

export default FeedArticle;

const styles = StyleSheet.create({
  itemWrapper: {
    width: 168,
    marginLeft: 8,
    flexDirection: 'column',
    borderRadius: 16,
    backgroundColor: 'white',
    marginBottom: 10,
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 16,
    alignItems: 'flex-start',
  },
  itemImage: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    width: 160,
    aspectRatio: 1,
  },
  fullImage: {
    borderRadius: 12,
    aspectRatio: 1,
    marginBottom: 14,
    marginRight: 12,
  },
  copyText: {
    fontWeight: '800',
    textTransform: 'uppercase',
    marginBottom: 14,
    letterSpacing: 1.28,
    lineHeight: 20.68,
  },
  priceText: {
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
});
