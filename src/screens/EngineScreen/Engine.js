import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {EngineView, useEngine} from '@babylonjs/react-native';

const Engine = () => {
  return (
    <EngineView
      //   onInitialized={(e)=>{console.log(e)}}
      // isTransparent={true}
      // androidView='SurfaceView'
      displayFrameRate={false}
      antiAliasing={1}
      adaptToDeviceRatio={true}
      nativeID={'igolf'}
    />
  );
};

export default React.memo(Engine);

const styles = StyleSheet.create({});
