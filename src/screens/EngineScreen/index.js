import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import {EngineView, useEngine} from '@babylonjs/react-native';
import {
  View,
  StyleSheet,
  Alert,
  Platform,
  Animated,
  Easing,
  BackHandler,
  ActivityIndicator,
} from 'react-native';
import {IViewer} from 'viewer-library';
import normalize, {SCREEN_HEIGHT, SCREEN_WIDTH} from 'utils/dimen';
import {SpriteManager, Texture} from '@babylonjs/core';
// import LocationViewType from '../../components/LocationViewType';
// import DirectionPad from '../../assets/DirectionPad';
// import LoadingScren from '../../components/LoadingScren';
// import {hasLocationPermission} from '../../utlity/common';
import Geolocation from 'react-native-geolocation-service';
// import CourseDrawer from '../../components/CourseDrawer';
// import HolesList from '../../components/HolesList';
// import GPSViewFooter from '../../components/GPSViewFooter';
// import VirtualFloatingHeader from '../../components/VirtualFLoatingHeader';
// import SettingsPageComponent from '../../components/SettingsPageComponent';
import {useSelector} from 'react-redux';
// import SelectTeeBoxFragment from '../../components/SelectTeeBoxFragment';
import {useNavigation} from '@react-navigation/native';
import Engine from './Engine';
import LoadingOverlay from 'components/LoadingOverlay';

global.lb_holesListState = false;

const EngineScreen = forwardRef(
  (
    {
      courseId,
      selectedTeeBoxDetails,
      selectHole,
      courseVectorGPS,
      courseScoreCard,
      courseElevation,
    },
    ref,
  ) => {
    console.log('courseId', courseId);
    // const courseId = route.params.courseId;
    // const viewType = route.params.type;
    const navigation = useNavigation();

    const engine = useEngine();
    const [scene, setScene] = useState();
    const [viewer, setViewer] = useState(false);
    const [viewerData, setViewerData] = useState({});
    const [lb_isLoading, setLoading] = useState('black');
    const [lb_nativeCameraAnimation, setNativeCameraAnimation] =
      useState(false);
    const [lo_courseData, setCourseData] = useState({});
    const [lb_holesList, setHolesList] = useState(false);
    const [lo_drawerActions, setDrawerActions] = useState({
      mode: '3D',
      sleepMode: true,
      autoAdvance: true,
      unit: 'Yards',
      autoZoom: true,
      distanceOverlay: true,
    });
    let gpsVectorDetails = useSelector(state => state.teeBox.gpsVectorDetails),
      elevationDetails = useSelector(state => state.teeBox.elevationAwsValue),
      scoreCardDetails = useSelector(state => state.teeBox.scoreCardDetails);

    useEffect(() => {
      goToHoleNumber(selectHole);
    }, [selectHole]);

    const viewerInit = engine => {
      new IViewer(
        {
          apikey: '',
          secretkey: '',
          courseID: courseId,
          singleHole: true,
          holeNumber: selectHole || 1,
          style: 'd',
          platform: Platform.OS,
          engine: engine,
          hostOrigin: 'https://api-connect.igolf.com/',

          assets: 'https://viewer-library-ui.dedicateddevelopers.us',
          size: {
            height: SCREEN_HEIGHT - normalize(65) - normalize(60),
            width: SCREEN_WIDTH,
          },
          ...lo_drawerActions,
          data: {
            course: gpsVectorDetails, //courseVectorGPS
            heights: elevationDetails, //courseElevation
            CourseScorecardDetails: scoreCardDetails, //courseScoreCard
            teebox: selectedTeeBoxDetails,
            // CourseScorecardDetails: t.CourseScorecardDetails
          },
        },
        t => {
          if (t) {
            setViewer(t);
            t.on('spritePrevent', () => {
              new SpriteManager('bugsHandling', null, 0, 0, t.scene);
            });
            t.on('load', () => {
              setLoading('black');
            });
            t.on('holeInfo', d => {
              setCourseData(d);
            });
            t.on('ready', () => {
              // const hasPermissions = hasLocationPermission();
              startLocationTracking(t, true);
              setScene(t.scene);
              setTimeout(() => {
                setLoading('');
              }, 2000);
            });
            t.on('autoLocationAutoOff', e => {
              !e.isInCurrentCourse &&
                Alert.alert('You are not in this clubhouse.');
            });
            t.on('HoleChange', e => {
              setViewerData({...viewerData, currentHole: e.currentHole});
            });
            t.on('cameraFlyStart', e => {
              setNativeCameraAnimation(true);
            });
            t.on('cameraFlyPause', e => {
              setNativeCameraAnimation(false);
            });
            t.on('finishCameraFly', e => {
              setNativeCameraAnimation(false);
            });
          }
        },
      );
    };

    useEffect(() => {
      if (engine) {
        viewerInit(engine);
        // engine.unload
        // engine.cullBackFaces = false;
        // console.log(engine._pending);
        // engine.forcePOTTextures = true;
        //   engine._caps.blendMinMax = true,
        //   engine._caps.maxTextureSize = 16384,
        //   engine._caps.maxRenderTextureSize = 16384, engine._caps.textureMaxLevel = true;
        //  engine._caps.textureFloatRender = true;
        //  engine._caps.textureFloatLinearFiltering = true;
        //  engine._caps.colorBufferFloat = true;
        //  engine._caps.depthTextureExtension = true;
        //  engine._caps.drawBuffersExtension = true;
        //  engine._caps.maxMSAASamples = 4;

        //   console.log(engine.getCaps());
        //  console.log(engine.framebufferDimensionsObject, ' engine._storageBuffers');
        // engine.adaptToDeviceRatio = true;
        // engine.deleteInstancesBuffer()
      }
    }, [engine]);

    useImperativeHandle(
      ref,
      () => {
        return {
          onViewChange: v => {
            viewer.main.viewModeInit({mode: v});
            // v.autoAdvance && viewer.main.viewer.locationInit(Geolocation, true);
          },
          showLoadingOnMap: () => {
            setLoading('transparent');
          },
          renderUserLocation: location => {
            viewer.main.renderCurrentLocation(location);
          },
        };
      },
      [viewer],
    );

    const startLocationTracking = (t, e) => {
      // t.main.viewer.locationInit(Geolocation, e);
    };

    let holeProgression = null;

    const gotoHole = e => {
      clearTimeout(holeProgression);

      holeProgression = setTimeout(() => {
        e === 'next' && viewer.main.viewer.goToNextHole();
        e === 'prev' && viewer.main.viewer.goToPrevHole();
      }, 200);
    };

    const onPlayPauseButtonLick = () => {
      lb_nativeCameraAnimation
        ? viewer.main.holeAnimationPause()
        : viewer.main.holeAnimationPlay();
    };

    const goToHoleNumber = selectHole => {
      viewer?.main?.viewer?.goToNextHole?.(selectHole);
    };

    const handleFlagSection = b => {
      setHolesList(b);
      global.lb_holesListState = b;
    };

    return (
      <View style={{flex: 1}}>
        {/* {scene && !lb_isLoading && viewType === 'GPS' && (
        <LocationViewType
          courseData={lo_courseData}
          onFlagSectionPress={() => handleFlagSection(true)}></LocationViewType>
      )}
      {lb_isLoading && <LoadingScren />} */}

        <Engine />
        {lb_isLoading && (
          <View
            style={[
              {
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: lb_isLoading,
              },
              StyleSheet.absoluteFill,
            ]}
          >
            <ActivityIndicator style={{}} size="large" color={'white'} />
          </View>
        )}
        {/* {scene && !lb_isLoading && viewType === 'GPS' && (
        <GPSViewFooter
          handleMenuPress={handleInfoPress}
          handlePlayPauseButton={() => onPlayPauseButtonLick()}
          animationStatus={lb_nativeCameraAnimation}></GPSViewFooter>
      )} */}

        {/* absolute stuff */}

        {/* {lb_holesList && (
        <HolesList
          onHoleNumberPress={v => handleHoleNumberPress(v)}
          currentHoleNumber={lo_courseData.holeNumber}
          onBackPressed={() => handleFlagSection(false)}></HolesList>
      )} */}

        {/* {lb_settingsStatus && (
        <SettingsPageComponent
          viewType={viewType}
          onBackPressed={() => {
            setSettingsStatus(false);
          }}
          drawerItemInitialStatus={lo_drawerActions}
          handleDrawerActions={v =>
            handleDrawerActions(v)
          }></SettingsPageComponent>
      )} */}

        {/* {scene && !lb_isLoading && viewType === 'virtual' && (
        <View style={styles.directionPadBg}>
          <DirectionPad
            buttonStatus={lb_nativeCameraAnimation}
            playPauseButtonStatus={lo_drawerActions.mode}
            handleInfoPress={handleInfoPress}
            handlePlayPauseButton={() => onPlayPauseButtonLick()}
            handlePrevPress={() => gotoHole('prev')}
            handleNextPress={() => gotoHole('next')}></DirectionPad>
        </View>
      )} */}

        {/* {scene && !lb_isLoading && viewType === 'virtual' && (
        <VirtualFloatingHeader
          courseData={lo_courseData}></VirtualFloatingHeader>
      )} */}
        {/* <View style={{flex: 1, position: 'absolute'}}>
        <Animated.View
          style={[
            {
              transform: [{translateX: translateX}],
              height: '100%',
              flexDirection: 'row',
              width: SCREEN_WIDTH,
            },
          ]}>
          <CourseDrawer
            handleExitPress={() => {
              handleExitPress();
            }}
            onTeeBoxSelectionPress={() => toggleTeeBoxSelection(true)}
            courseId={courseId}
            handleSelectHolePress={handleHoleSelectionPress}
            viewType={viewType}
            handleSettingsPress={() => {
              handleSettingsPress();
            }}
            onCloseDrawer={() => toggleDrawer(false)}></CourseDrawer>
        </Animated.View>
      </View> */}

        {/* <View style={{flex: 1, zIndex: 2, position: 'absolute'}}>
        <Animated.View
          style={[
            {
              transform: [{translateX: teeBoxSelectionTranslateX}],
              height: '100%',
              width: SCREEN_WIDTH,
              flexDirection: 'row',
            },
          ]}>
          <SelectTeeBoxFragment
            handleTeeBoxPress={v => handleTeeBoxSelectionChange(v)}
            selectedTeeBox={selectedTeeBoxDetailsState}
            onBackPress={() =>
              toggleTeeBoxSelection(false)
            }></SelectTeeBoxFragment>
        </Animated.View>
      </View> */}
      </View>
    );
  },
);

export default EngineScreen;

const styles = StyleSheet.create({
  directionPadBg: {
    position: 'absolute',
    top: SCREEN_HEIGHT / 1.34,
    right: normalize(20),
    height: normalize(80),
    width: normalize(80),
  },
});
