import React, {useState, useEffect, useRef, useMemo} from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  Platform,
  Image,
} from 'react-native';
import ConfirmModal from 'components/Modal/ConfirmModal';
import {setModePlayed} from 'utils/commonVariable';
import Text from 'components/Text';
import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import {useSelector} from 'react-redux';
import {parseDataEditClassic} from 'screens/PlayCourseMap/DataEditDefault';
import {prepareScoreCard} from 'utils/scorecard';
import {
  calculateClubDistanceRangeAxis,
  getRoundDetailAndScoreCardInfo,
  getRoundStat,
  getRoundStatsAndClubStats,
  parseRoundDetail,
} from 'utils/singleRoundStat';
import {
  filterValidHoles,
  getParFromHoles,
  getScoreFromHoles,
  validateGhinRoundScore,
  validateRound,
} from 'utils/validate';
import {GA_logCtaEvent} from 'utils/googleAnalytics';
import {SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';
import {checkCanPostRoundToUSGA, checkConnectedWHS} from 'utils/user';
import RoundOverviewStats from './components/RoundOverviewStats';
import ButtonCancelGrey from 'screens/Play/components/ButtonCancelGrey';
import {GHIN_MAIN_COLOR, GRAY_BACKGROUND, GOLF_CANADA_COLOR} from 'config';
import RoundOverviewHeader from './components/RoundOverviewHeader';
import Scorecard from 'screens/PlayCourseMap/components/Scorecard';
import {
  isConnectedNetwork,
  showToastErrorInternet,
} from 'utils/queueAndNetwork';
import {
  getCourseDetailsGps,
  endRound,
  getCourseDetails,
} from 'requests/add-round';
import {
  getTotalScore,
  MODE_ROUND,
  parseListDataDefault,
} from 'screens/PlayCourseMap/DataSubmitDefault';
import {parseDataEdit} from 'screens/PlayCourseMap/DataEditDefault';
import {parseDataGps} from 'screens/PlayCourseMap/iGolfFormat';
import ScoreInputModal from 'screens/PlayCourseMap/components/ScoreInputModal';
import {deleteCoursePlaying} from 'utils/realmHelper';
import ShareRoundIcon from 'assets/imgs/playScore/share-round-icon.png';
import ShareRoundPreview from 'screens/Play/components/ShareRoundPreview';
import BottomSheetSubmitGhinConfirm from 'components/BottomSheetSubmitGhinConfirm';
import {convertDistanceFromYards} from 'utils/convert';

const RoundOverview = ({
  navigation,
  route: {
    params: {
      roundInfo,
      allowBackToScores,
      pausedRound,
      isFrom3rdPartyRound,
      reloadListData,
    },
  },
}) => {
  const [loading, setLoading] = useState(false);
  const [scoreDetail, setScoreDetail] = useState(null);
  const [scoreCardCourse, setScoreCardCourse] = useState([]);
  const [stats, setStats] = useState(null);
  const [teeSelected, setTeeSelected] = useState(null);
  const [numPar, setNumPar] = useState(null);
  const [nineHolesPlayType, setNineHolesPlayType] = useState('all');
  const [dataRoundDetail, setDataRoundDetail] = useState({
    roundDetail: roundInfo,
  });
  const [isShowModalResumeRound, setShowModalResumeRound] = useState(0);
  const [ghinRoundId, setGhinRoundId] = useState(roundInfo?.ghinRoundId);
  const [golfnetRoundId, setGolfnetRoundId] = useState(
    roundInfo?.golfnetRoundId,
  );
  const [selectedHole, setSelectedHole] = useState(0);
  const [roundDataSubmit, setRoundDataSubmit] = useState(null);
  const refRoundDataCache = useRef(null);
  const refHolesEdited = useRef(null);

  const user = useSelector(state => state.user);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);

  const dataHoles = useRef(null);
  const pausedRoundId = useRef(pausedRound?.id);
  const [roundTee, setRoundTee] = useState(null);
  const canPostToUSGA = checkCanPostRoundToUSGA(showHideFeatures, user);
  const whs = useSelector(state => state.whs);
  const isGolfCanada = checkConnectedWHS(whs);
  const SCREEN_NAME = 'round stats detail';

  const reloadRoundDetail = useSelector(
    state => state?.play?.reloadRoundDetail,
  );
  const [isShareRoundVisible, setShareRoundVisible] = useState(false);
  const [buttonPanelHeight, setButtonPanelHeight] = useState(0);
  const isWHS = golfnetRoundId;
  const isUSGA = ghinRoundId;
  const isBasicMode = roundInfo?.roundMode === MODE_ROUND.BASIC;
  const [isInvalidCourse, setInvalidCourse] = useState(false);
  const submitGhinSheetRef = useRef(null);
  const [isModalShowSubmit, setModalShowSubmit] = useState(false);
  const [ghinCourseId, setGhinCourseId] = useState(null);
  const [selectGhinCourseDone, setSelectGhinCourseDone] = useState(false);
  const [isShowModalNoGhinConfirm, setShowModalNoGhinConfirm] = useState(false);
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';
  useEffect(() => {
    if (!isFrom3rdPartyRound) {
      setLoading(true);
      loadDataFromAPI();
    }
  }, [reloadRoundDetail]);

  useEffect(() => {
    if (roundInfo?.roundMode) {
      setModePlayed(roundInfo?.roundMode);
    }
  }, [roundInfo]);

  useEffect(() => {
    if (selectGhinCourseDone && ghinCourseId && ghinCourseId !== 0) {
      setTimeout(() => {
        setModalShowSubmit(true);
        submitGhinSheetRef?.current?.snapTo(0);
      }, 300);
    }
  }, [ghinCourseId, selectGhinCourseDone]);

  const loadDataFromAPI = async () => {
    try {
      let loadDataApis = [
        getRoundStatsAndClubStats(roundInfo),
        getRoundDetailAndScoreCardInfo(roundInfo),
        getCourseDetails(roundInfo?.igolfCourseId),
      ];
      Promise.all(loadDataApis)
        .then(async resultFromApi => {
          const roundStatsAndClubStats = resultFromApi[0];
          const roundDetailAndScoreCard = resultFromApi[1];
          const roundDetailData = roundDetailAndScoreCard?.[0];
          const roundTees = roundDetailAndScoreCard?.[1];
          const scoreCardData = roundDetailAndScoreCard?.[2];
          if (roundTees?.errorDetails && scoreCardData?.errorDetails) {
            setInvalidCourse(true);
          } else {
            setInvalidCourse(false);
          }
          let roundScoreCard =
            scoreCardData?.[
              user.gender === 'male' ? 'menScorecardList' : 'wmnScorecardList'
            ]?.[0];
          let {
            roundDetail,
            scoreCardCommonData,
            parNumber,
            teeValueDisplay,
            teeData,
            nineHolesPlayed,
          } = parseRoundDetail(roundDetailData, roundTees, roundScoreCard);
          if (roundDetailData?.ghinRoundId != null) {
            setGhinRoundId(roundDetailData?.ghinRoundId);
          }
          if (roundDetailData?.golfnetRoundId != null) {
            setGolfnetRoundId(roundDetailData?.golfnetRoundId);
          }
          setNineHolesPlayType(nineHolesPlayed);
          setNumPar(parNumber);
          setScoreCardCourse(scoreCardCommonData);
          setDataRoundDetail({
            roundDetail,
          });
          let holesData = [];
          //#region prepare data for SCORECARD
          if (
            // roundInfo.roundMode === 'Classic' &&
            roundDetail?.holes?.length > 0
          ) {
            setTeeSelected(teeData);
            setRoundTee(teeValueDisplay);
            holesData = prepareScoreCard(roundDetail, teeData);
          }
          dataHoles.current = holesData;
          setRoundDetailAndStats(roundStatsAndClubStats, holesData);
          const courseDetail = resultFromApi[2];
          setGhinCourseId(courseDetail?.ghinId);
        })
        .catch(() => {
          setLoading(false);
        });
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: t('score_details.crunching_the_numbers'),
        subText: t('score_details.please_check_back_later_for_full_statistics'),
      });
    }
  };

  const getBunkerAndSand = holes => {
    if (!holes || !holes.length) return;
    let bunkerHit = 0;
    let sand = 0;
    holes.map(hole => {
      if (hole?.bunkerHit) {
        bunkerHit += 1;
        if (hole?.totalScore <= hole?.par) {
          sand += 1;
        }
      }
    });
    return {bunkerHit, sand};
  };

  const setRoundDetailAndStats = async (values, holesData) => {
    if (values[0]?.roundIds) {
      const data = holesData ? holesData : dataHoles.current;
      const {bunkerHit, sand} = getBunkerAndSand(data);
      let overallStats = {
        ...values[0],
        sand,
        bunkerHit,
      };
      //#endregion
      setScoreDetail({
        roundStats: overallStats,
        clubStats: values[1],
        holesData: data,
        maxClubDistance: maxClubDistance(values[1]),
      });
      setStats({
        overallStats: overallStats,
        clubStats: values[1],
        maxClubDistance: maxClubDistanceStats(values[1]),
        holesData: data,
        clubDistanceRangeAxis: calculateClubDistanceRangeAxis(
          maxClubDistanceByRange(values[1]),
        ),
      });
      setLoading(false);
    } else {
      showToast({
        type: 'error',
        message: t('error'),
        subText: t('error.unable_get_data'),
      });
      setLoading(false);
    }
  };
  const maxClubDistance = clubs => {
    if (!clubs) {
      return null;
    }
    const distances = [];
    clubs.map(club => {
      if (club.avg) {
        distances.push(Math.round(club.avg));
      }
    });
    return Math.max(...distances);
  };
  const maxClubDistanceStats = clubs => {
    if (!clubs) {
      return null;
    }
    const distances = [];
    clubs.map(club => {
      if (club.avg) {
        distances.push(Math.round(club.close));
      }
    });
    return Math.max(...distances);
  };
  const maxClubDistanceByRange = clubs => {
    if (!clubs) {
      return null;
    }
    const distances = [];
    clubs.map(club => {
      if (club.high) {
        distances.push(Math.round(club.high));
      }
    });

    return Math.max(...distances);
  };

  const showSheetSubmitRound = () => {
    const numberOfHolesPlayed = dataRoundDetail?.roundDetail?.holes?.filter?.(
      item => item.completeHole,
    )?.length;
    try {
      const validateRs = validateGhinRoundScore(
        dataRoundDetail?.roundDetail?.totalScore,
        dataRoundDetail?.roundDetail?.holes?.length,
        dataRoundDetail?.roundDetail?.numberOfHolesPlayed ||
          numberOfHolesPlayed,
        dataRoundDetail?.roundDetail?.holes,
      );
      if (validateRs?.success) {
        const checkNetwork = isConnectedNetwork();
        if (!checkNetwork) {
          showToastErrorInternet();
          return;
        }
        GA_logCtaEvent(
          'usga_submit_round',
          t('roundOverview.btn.submit_round_to_USGA'),
          SCREEN_NAME,
          SCREEN_CLASS.STATS,
          SCREEN_TYPES.PLAY,
        );
        if (isGolfCanada) {
          navigation.navigate('ScoresStats', {
            screen: 'ScoreAddCourse',
            params: {
              roundId: roundInfo?.id,
              igolfRoundInfo: roundInfo,
              onReloadListUSGA: reloadListData,
            },
          });
        } else {
          if (ghinCourseId && ghinCourseId !== '0') {
            setModalShowSubmit(true);
            setTimeout(() => {
              submitGhinSheetRef?.current?.snapTo(0);
            }, 300);
          } else {
            setShowModalNoGhinConfirm(true);
          }
        }
      }
    } catch (error) {}
  };

  const onPressContinueNoGHIN = () => {
    setSelectGhinCourseDone(false);
    navigation.navigate('ScoresStats', {
      screen: 'ScoreAddCourse',
      params: {
        isSelectingGhinCourse: true,
        setSelectedGhinCourseId: setGhinCourseId,
        setSelectGhinCourseDone: setSelectGhinCourseDone,
      },
    });
  };

  const renderModalNoGhinConfirm = () => {
    return (
      <ConfirmModal
        visible={isShowModalNoGhinConfirm}
        title={t('score.confirm_no_ghin')}
        textCancel={'Cancel'}
        textConfirm={'Continue'}
        backdropPress={() => setShowModalNoGhinConfirm(false)}
        onPressConfirm={onPressContinueNoGHIN}
        isNoPadding={true}
      />
    );
  };

  const editHoleScore = async holeSelected => {
    if (pausedRoundId.current) {
      setShowModalResumeRound(holeSelected);
      return;
    }
    const {id, igolfCourseId, roundMode} = dataRoundDetail.roundDetail;
    const checkNetwork = isConnectedNetwork();
    if (!checkNetwork) {
      setLoading(false);
      showToastErrorInternet();
      return;
    }
    setLoading(true);
    const gpsList = await getCourseDetailsGps(igolfCourseId);
    //Round create on watch have only holes already complete
    const gpsGreens = parseDataGps(gpsList);
    const dataHolesDefault = parseListDataDefault(scoreCardCourse, gpsGreens);
    const roundData = parseDataEdit(
      dataRoundDetail.roundDetail,
      dataHolesDefault,
    );
    setModePlayed(roundMode);
    setSelectedHole(holeSelected);
    refHolesEdited.current = {
      ...refHolesEdited.current,
      [holeSelected]: holeSelected,
    };
    setRoundDataSubmit(roundData);
    if (!refRoundDataCache.current) {
      refRoundDataCache.current = JSON.stringify(roundData);
    }
    setLoading(false);
  };

  const saveScore = async roundData => {
    setLoading(true);
    roundData.total_score = getTotalScore(roundData.holes);
    const holeUpdate = roundData.holes.filter(
      _item => _item.number === selectedHole,
    );
    await endRound(dataRoundDetail.roundDetail.id, {
      ...roundData,
      holes: holeUpdate,
    });
    reloadListData();
    await loadDataFromAPI();
  };

  const onCloseScreen = async () => {
    if (roundDataSubmit && reloadListData) {
      if (pausedRound && !pausedRoundId.current) {
        reloadListData();
      } else {
        reloadListData({...dataRoundDetail.roundDetail});
      }
    }
    navigation.goBack();
  };

  const onPressConfirmEndPausingRoundModal = async () => {
    try {
      setLoading(true);
      await deleteCoursePlaying();
      await endRound(pausedRoundId.current, {paused: true});
      pausedRoundId.current = null;
      editHoleScore(isShowModalResumeRound);
      setShowModalResumeRound(0);
      reloadListData();
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: error[0],
      });
    }
  };

  const onOpenShareRound = () => {
    setShareRoundVisible(true);
  };

  const renderModalConfirm = () => {
    return (
      <ConfirmModal
        visible={isShowModalResumeRound}
        title={'play.score_detail.end_paused_round_before'}
        textCancel={'cancel'}
        textConfirm={'play.end_round'}
        backdropPress={() => setShowModalResumeRound(0)}
        onPressConfirm={onPressConfirmEndPausingRoundModal}
      />
    );
  };

  const roundHoles = dataRoundDetail?.roundDetail?.holes?.map(item => {
    item.number = item.holeNumber;
    item.score = item.totalScore;
    item.bunker_hit = item.bunkerHit;
    item.fw_stats = item.fwStats;
    item.gr_stats = item.grStats;
    item.penalty_hit = item.penaltyHit;
    item.completeHole = item.totalScore > 0;
    item.putts_number = item.puttsNumber;
    return item;
  });
  const shouldShowShareRound = validateRound(roundHoles);
  //disable edit round when posted to USGA/WHS
  const isQuickEditDisabled =
    ghinRoundId != null || golfnetRoundId != null || isInvalidCourse;

  const parValueInSubmitUSGA = useMemo(() => {
    if (canPostToUSGA) {
      const numberOfHolesPlayed = roundHoles?.filter?.(
        item => item.completeHole,
      )?.length;
      // Check if only all front 9 holes (holes 1–9) are completed
      const onlyFrontNineCompleted =
        roundHoles.filter(holeItem => holeItem.completeHole).length === 9 &&
        roundHoles.slice(0, 9).every(holeItem => holeItem.completeHole);

      // Check if only all back 9 holes (holes 10–18) are completed
      const onlyBackNineCompleted =
        roundHoles.filter(holeItem => holeItem.completeHole).length === 9 &&
        roundHoles.slice(9, 18).every(holeItem => holeItem.completeHole);
      if (numberOfHolesPlayed === 9) {
        if (onlyFrontNineCompleted) {
          return scoreCardCourse?.parOut;
        }
        if (onlyBackNineCompleted) {
          return scoreCardCourse?.parIn;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }, [canPostToUSGA, roundHoles, scoreCardCourse]);

  const renderButtonGroup = () => {
    return (
      <View
        style={[
          appStyles.viewShadowTop,
          styles.buttonsContainer,
          {
            paddingBottom: Platform.OS === 'ios' ? 40 : 30,
          },
        ]}
        onLayout={event => {
          setButtonPanelHeight(event.nativeEvent.layout.height);
        }}
      >
        <View style={{flexDirection: 'row'}}>
          {shouldShowShareRound && (
            <TouchableOpacity
              onPress={onOpenShareRound}
              style={[
                styles.shareRoundButton,
                {
                  marginRight: canShowButtonUSGA ? 8 : 0,
                },
              ]}
            >
              <Image
                source={ShareRoundIcon}
                style={{width: 16, height: 16, marginRight: 12}}
              />
              <Text
                style={styles.shareRoundText}
                Din79Font
                size={12}
                white
                numberOfLines={1}
              >
                play.share_round
              </Text>
            </TouchableOpacity>
          )}
          {canShowButtonUSGA && (
            <TouchableOpacity
              onPress={showSheetSubmitRound}
              style={[
                styles.buttonPostToUSGA,
                {
                  backgroundColor: isGolfCanada
                    ? GOLF_CANADA_COLOR
                    : GHIN_MAIN_COLOR,
                  borderColor: isGolfCanada
                    ? GOLF_CANADA_COLOR
                    : GHIN_MAIN_COLOR,
                },
              ]}
            >
              <Text
                style={styles.postToUSGAText}
                Din79Font
                size={12}
                white
                numberOfLines={1}
              >
                {isGolfCanada
                  ? t('roundOverview.btn.submit_round_to_Golf_Canada')
                  : t('play.btn.post_to_usga')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
        {shouldShowNotEnoughHoleForUSGA && (
          <Text size={12} style={{marginTop: 12}}>
            {t('roundOverview.text.not_enough_holes_for_USGA_posting')}
          </Text>
        )}
      </View>
    );
  };

  const renderHeader = () => {
    let totalScore = roundInfo?.totalScore;
    if (isFrom3rdPartyRound) {
      totalScore = roundInfo?.golfNetScore ?? roundInfo?.ghinScore;
    } else {
      totalScore =
        dataRoundDetail?.roundDetail?.totalScore ?? roundInfo?.totalScore;
    }
    const scoreToPar =
      dataRoundDetail?.roundDetail?.scoreToPar ?? roundInfo?.scoreToPar;

    let numberOfHolesPlayed = roundInfo?.holes?.length;
    if (nineHolesPlayType !== 'all' && !isFrom3rdPartyRound) {
      numberOfHolesPlayed = 9;
    }

    let displayTeeName = roundInfo?.teeName;
    if (isUSGA && isFrom3rdPartyRound) {
      displayTeeName = roundInfo?.ghinTeeSetName;
    }

    if (isWHS && isFrom3rdPartyRound) {
      displayTeeName = roundInfo?.golfnetTeeSetName;
    }
    if (!isFrom3rdPartyRound && displayTeeName) {
      if (nineHolesPlayType === 'front') {
        displayTeeName += ' - Front 9';
      }
      if (nineHolesPlayType === 'back') {
        displayTeeName += ' - Back 9';
      }
    }
    return (
      <RoundOverviewHeader
        holes={
          isFrom3rdPartyRound
            ? roundInfo?.numberOfHolesPlayed
            : numberOfHolesPlayed
        }
        par={isFrom3rdPartyRound ? roundInfo?.coursePar : numPar}
        teeName={displayTeeName}
        teeDistance={teeDistanceObj?.value}
        datePlayed={roundInfo?.playedOnUtc || roundInfo?.playedOn}
        totalScore={totalScore}
        scoreToPar={scoreToPar}
        courseName={
          isFrom3rdPartyRound && isGolfCanada
            ? roundInfo?.golfnetCourseName
            : roundInfo?.courseName
        }
        parAvgStats={stats?.overallStats}
        isFrom3rdPartyRound={isFrom3rdPartyRound}
        isWHS={isWHS}
        isUSGA={isUSGA}
      />
    );
  };
  const teeDistanceObj = convertDistanceFromYards({
    distanceInYards: isFrom3rdPartyRound ? roundInfo?.courseYards : roundTee,
    userUnit: userDistanceUnit,
  });
  const dataShare = useMemo(() => {
    if (isFrom3rdPartyRound) return;
    let totalScore = roundInfo?.totalScore;
    if (isFrom3rdPartyRound) {
      totalScore = roundInfo?.golfNetScore ?? roundInfo?.ghinScore;
    } else {
      totalScore =
        dataRoundDetail?.roundDetail?.totalScore ?? roundInfo?.totalScore;
    }
    let roundDetail = dataRoundDetail?.roundDetail;
    const validHolesForShareRound = filterValidHoles(roundHoles);
    const scoreForShareRound = getScoreFromHoles(validHolesForShareRound);
    const parForShareRound = getParFromHoles(validHolesForShareRound);
    const teeDistanceShareObj = convertDistanceFromYards({
      distanceInYards: teeSelected?.ydsTotal || roundDetail?.courseYards,
      userUnit: userDistanceUnit,
    });
    return {
      scoreCard: {
        scoreDetail: {...roundDetail, holes: roundHoles},
        teeSelected: teeSelected,
        scoreCardDefault: scoreCardCourse,
      },
      roundDetail: {
        holesLength: roundDetail?.holes?.length,
        par: isFrom3rdPartyRound ? roundInfo?.coursePar : numPar,
        teeName: teeSelected?.teeName,
        teeDistance: teeDistanceShareObj?.value,
        distanceUnit: teeDistanceShareObj?.unit,
        datePlayed: roundInfo?.playedOnUtc || roundInfo?.playedOn,
        totalScore: parseInt(scoreForShareRound ?? totalScore ?? 0, 10),
        scoreToPar: scoreForShareRound - parForShareRound,
        courseName: roundDetail?.course_name || roundDetail.courseName,
      },
      overallStatsData: getRoundStat(validHolesForShareRound)?.roundStats,
      isBasicMode: isBasicMode,
    };
  }, [
    dataRoundDetail?.roundDetail,
    isBasicMode,
    isFrom3rdPartyRound,
    numPar,
    roundHoles,
    roundInfo,
    scoreCardCourse,
    teeSelected,
  ]);

  const validateGhinRoundResult = useMemo(() => {
    const numberOfHolesPlayed = dataRoundDetail?.roundDetail?.holes?.filter?.(
      item => item.completeHole,
    )?.length;
    const rs = validateGhinRoundScore(
      dataRoundDetail?.roundDetail?.totalScore,
      dataRoundDetail?.roundDetail?.holes?.length,
      dataRoundDetail?.roundDetail?.numberOfHolesPlayed || numberOfHolesPlayed,
      dataRoundDetail?.roundDetail?.holes,
      false,
    );
    if (rs?.success || rs?.error?.type !== 'holes') {
      return true;
    } else {
      return false;
    }
  }, [dataRoundDetail?.roundDetail]);

  const canShowButtonUSGA =
    //Can not show Button USGA with Advance Round because the default Practice Mode is true
    (validateGhinRoundResult && ghinRoundId == null && canPostToUSGA) ||
    (golfnetRoundId == null && isGolfCanada);
  const shouldShowNotEnoughHoleForUSGA =
    !validateGhinRoundResult && ghinRoundId == null && canPostToUSGA;
  return (
    <>
      <ButtonCancelGrey onPress={onCloseScreen} />
      <ScrollView
        style={[
          appStyles.flex,
          {
            backgroundColor: GRAY_BACKGROUND,
          },
        ]}
        contentContainerStyle={{paddingBottom: buttonPanelHeight}}
      >
        {renderHeader()}
        {!loading && !isFrom3rdPartyRound && (
          <ScrollView style={[appStyles.flex]}>
            <View style={[appStyles.pBMd]}>
              <Scorecard
                scoreDetail={parseDataEditClassic(dataRoundDetail?.roundDetail)}
                scoreCardDefault={scoreCardCourse}
                teeSelected={teeSelected}
                onPressEditScore={isQuickEditDisabled ? null : editHoleScore}
              />
              <View
                style={[
                  {paddingHorizontal: '3%', marginBottom: -20},
                  appStyles.mTXs,
                ]}
              >
                {scoreDetail?.roundStats !== '' && (
                  <View>
                    {(scoreDetail?.roundStats?.classicStatsFairwaysHit !=
                      null ||
                      scoreDetail?.roundStats?.classicStatsGreensInRegulation !=
                        null ||
                      scoreDetail?.roundStats?.classicStatsPuttsPerRound !=
                        null ||
                      scoreDetail?.roundStats?.classicStatsSandSaves !=
                        null) && (
                      <RoundOverviewStats
                        overallStatsData={scoreDetail?.roundStats}
                        courseHolesLength={roundInfo?.holes?.length}
                      />
                    )}
                  </View>
                )}
              </View>
            </View>
            {pausedRound && renderModalConfirm()}
          </ScrollView>
        )}
      </ScrollView>
      {!isFrom3rdPartyRound &&
        !loading &&
        (shouldShowShareRound || canShowButtonUSGA) &&
        renderButtonGroup()}
      {roundDataSubmit && selectedHole ? (
        <ScoreInputModal
          isScoreInputVisible={selectedHole !== 0}
          selectHole={selectedHole}
          teeSelected={teeSelected}
          setScoreInputVisible={() => setSelectedHole(0)}
          setRoundData={setRoundDataSubmit}
          roundData={roundDataSubmit}
          onCompleteHole={saveScore}
        />
      ) : null}
      {loading && (
        <View
          style={[
            appStyles.absoluteFill,
            appStyles.center,
            {backgroundColor: '#00000090', zIndex: 99},
          ]}
          pointerEvents={'none'}
        >
          <ActivityIndicator size="large" color={'white'} />
        </View>
      )}
      {!isFrom3rdPartyRound && (
        <ShareRoundPreview
          isShareRoundVisible={isShareRoundVisible}
          setShareRoundVisible={setShareRoundVisible}
          dataShare={dataShare}
        />
      )}
      {(canPostToUSGA || isGolfCanada) && (
        <BottomSheetSubmitGhinConfirm
          currentRoundInfo={{
            ...roundInfo,
            playedOn: roundInfo?.playedOnUtc || roundInfo?.playedOn,
            ...dataRoundDetail?.roundDetail,
            coursePar: parValueInSubmitUSGA
              ? parValueInSubmitUSGA
              : dataRoundDetail?.roundDetail?.coursePar,
          }}
          isPostWithRound
          teeValueDisplay={teeDistanceObj?.value}
          ref={submitGhinSheetRef}
          navigation={navigation}
          origin={'RoundOverview'}
          isModalShow={isModalShowSubmit}
          onCloseModal={() => {
            setModalShowSubmit(false);
            //reset ghin course Id
            if (selectGhinCourseDone) {
              setGhinCourseId(null);
            }
          }}
          onReloadListUSGA={reloadListData}
          ghinCourseId={ghinCourseId}
        />
      )}
      {isShowModalNoGhinConfirm && renderModalNoGhinConfirm()}
    </>
  );
};

const styles = StyleSheet.create({
  buttonsContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    position: 'absolute',
    bottom: 0,
    paddingTop: 8,
    backgroundColor: 'rgba(229, 229, 229, 1)',
    width: '100%',
  },
  buttonPostToUSGA: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 24,
    backgroundColor: GHIN_MAIN_COLOR,
    borderColor: GHIN_MAIN_COLOR,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  postToUSGAText: {
    letterSpacing: 1.62,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  shareRoundButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 24,
    backgroundColor: 'black',
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  shareRoundText: {
    letterSpacing: 1.62,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
});

export default RoundOverview;
