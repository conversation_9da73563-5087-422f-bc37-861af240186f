import React, {useRef, memo, useEffect, useImperativeHandle} from 'react';
import {
  Platform,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
  Keyboard,
  Animated,
} from 'react-native';
import moment from 'moment';
import FormItem from 'components/FormItem';
import Text from 'components/Text';

import appStyles from 'styles/global';
import {t} from 'i18next';
import {GREY} from 'config';
import RightArrowIcon from 'assets/imgs/playScore/ic_right_arrow.svg';
import {NOT_APPLICABLE} from 'utils/constant';
import TabRoundDetail from './TabRoundDetail';
import Scorecard from 'screens/PlayCourseMap/components/Scorecard';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {MODE_ROUND} from 'screens/PlayCourseMap/DataSubmitDefault';
import {useSelector} from 'react-redux';
import {checkCanPostRoundToUSGA} from 'utils/user';

const BORDER_LINE_COLOR = '#00000050';

export const GHIN_POST_SCORE_MODE = {
  HBH: 'Hole by Hole',
  GROSS: 'Gross',
};

const AnimationDuration = 500;

const PostScoreDetailForm = (
  {
    ghinPostScoreMode,
    roundDataSubmit,
    courseHoleNumber,
    setHolesPlayed,
    holesPlayed,
    courseScoreCard,
    teePlayed,
    canPost3rdParty,
    setEditFromScoreCard,
    setHoleSelected,
    setTooltipVisible,
    courseScore,
    setCourseScore,
    scoreType,
    teeListOptions,
    coursePar,
    datePlayed,
    isEditing,
    scoreTypeRef,
    teesRef,
    datePlayedRef,
    onPressPostScoreHBH,
    teeRating,
    deleteScore,
    setGhinPostScoreMode,
    resetRoundData,
    ghinTeesRef,
    ghinTeeSelected,
  },
  ref,
) => {
  const courseScoreRef = useRef(null);
  const insets = useSafeAreaInsets();
  const user = useSelector(state => state.user);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
  const canPostToUSGA = checkCanPostRoundToUSGA(showHideFeatures, user);

  // Hole By Hole Animation values
  const scoreCardTranslateY = useRef(new Animated.Value(0)).current;
  const HBH_Opacity = useRef(new Animated.Value(1)).current;
  const HBH_TextBackTranslateY = useRef(new Animated.Value(0)).current;
  const HBH_LineTranslateY_1 = useRef(new Animated.Value(0)).current;
  const HBH_LineTranslateY_2 = useRef(new Animated.Value(0)).current;
  const HBH_LineTranslateY_345 = useRef(new Animated.Value(0)).current;
  const HBH_ScoreTypeTranslateY = useRef(new Animated.Value(0)).current;
  const HBH_ScoreTypeValueScale = useRef(new Animated.Value(1)).current;
  const HBH_ScoreTypeValueTranslateY = useRef(new Animated.Value(0)).current;

  const HBH_OtherInfoTranslateY = useRef(new Animated.Value(0)).current;
  const HBH_TeeValueTranslateX = useRef(new Animated.Value(0)).current;
  const HBH_ParValueTranslateX = useRef(new Animated.Value(0)).current;
  const HBH_DatePlayedValueTranslateX = useRef(new Animated.Value(0)).current;
  const HBH_DatePlayedIconTranslateX = useRef(new Animated.Value(0)).current;

  //Initial GROSS Animation values
  const GRO_Opacity = useRef(new Animated.Value(1)).current;
  const GRO_TranslateY = useRef(new Animated.Value(0)).current;
  const GRO_ScoreTypeValueScale = useRef(new Animated.Value(1)).current;
  const GRO_ScoreTypeValueTranslateX = useRef(new Animated.Value(0)).current;
  const GRO_TeeValueTranslateX = useRef(new Animated.Value(0)).current;
  const GRO_ParValueTranslateX = useRef(new Animated.Value(0)).current;
  const GRO_DatePlayedTranslateY = useRef(new Animated.Value(0)).current;

  useImperativeHandle(ref, () => ({
    //Reset GROSS View Animation value
    resetAnimation: () => {
      GRO_Opacity.setValue(1);
      GRO_TranslateY.setValue(0);
      GRO_ScoreTypeValueScale.setValue(1);
      GRO_DatePlayedTranslateY.setValue(0);
      GRO_ScoreTypeValueTranslateX.setValue(0);
      GRO_TeeValueTranslateX.setValue(0);
      GRO_ParValueTranslateX.setValue(0);
    },
  }));

  const onOpenModal = type => {
    setTimeout(() => {
      switch (type) {
        case 'SCORE_TYPE':
          scoreTypeRef.current.snapTo(0);
          break;
        case 'TEES_TYPE':
          if (canPostToUSGA) {
            ghinTeesRef.current.snapTo(0);
          } else {
            teesRef.current.snapTo(0);
          }
          break;
        case 'DATE_TYPE':
          datePlayedRef.current.snapTo(0);
          break;
        default:
          break;
      }
      Keyboard.dismiss();
    }, 300);
  };

  const onPressChangeInputScoreMode = () => {
    try {
      if (ghinPostScoreMode === GHIN_POST_SCORE_MODE.GROSS) {
        onPressPostScoreHBH();
        setTimeout(() => {
          setGhinPostScoreMode(GHIN_POST_SCORE_MODE.HBH);
          //Initial set GROSS components positions when is currently in Hole by Hole Mode
          GRO_Opacity.setValue(0);
          GRO_Opacity.setValue(0);
          GRO_TranslateY.setValue(150);
          GRO_ScoreTypeValueScale.setValue(0);
          GRO_DatePlayedTranslateY.setValue(
            roundDataSubmit?.round_mode === MODE_ROUND.CLASSIC ? -250 : -150,
          );
          GRO_ScoreTypeValueTranslateX.setValue(-50);
          GRO_TeeValueTranslateX.setValue(80);
          GRO_ParValueTranslateX.setValue(-30);
        }, 100);
      } else {
        Animated.parallel([
          Animated.timing(scoreCardTranslateY, {
            toValue: 100,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_Opacity, {
            toValue: 0,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_TextBackTranslateY, {
            toValue: -30,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_LineTranslateY_1, {
            toValue: -10,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_LineTranslateY_2, {
            toValue: -70,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_LineTranslateY_345, {
            toValue: -70,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_ScoreTypeTranslateY, {
            toValue: -100,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_ScoreTypeValueScale, {
            toValue: 2,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_ScoreTypeValueTranslateY, {
            toValue: -50,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_OtherInfoTranslateY, {
            toValue: -70,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_TeeValueTranslateX, {
            toValue: 30,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_ParValueTranslateX, {
            toValue: -20,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_DatePlayedValueTranslateX, {
            toValue: 10,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(HBH_DatePlayedIconTranslateX, {
            toValue: 40,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          //Animate GROSS VIEW to Initial Position
          Animated.timing(GRO_Opacity, {
            toValue: 1,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(GRO_TranslateY, {
            toValue: 0,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(GRO_ScoreTypeValueScale, {
            toValue: 1,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(GRO_DatePlayedTranslateY, {
            toValue: 0,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(GRO_ScoreTypeValueTranslateX, {
            toValue: 0,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(GRO_TeeValueTranslateX, {
            toValue: 0,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
          Animated.timing(GRO_ParValueTranslateX, {
            toValue: 0,
            duration: AnimationDuration,
            useNativeDriver: true,
          }),
        ]).start(() => {
          resetRoundData();
          HBH_Opacity.setValue(1);
          scoreCardTranslateY.setValue(0);
          HBH_TextBackTranslateY.setValue(0);
          HBH_LineTranslateY_1.setValue(0);
          HBH_LineTranslateY_2.setValue(0);
          HBH_LineTranslateY_345.setValue(0);
          HBH_ScoreTypeTranslateY.setValue(0);
          HBH_ScoreTypeValueScale.setValue(1);
          HBH_ScoreTypeValueTranslateY.setValue(0);
          HBH_OtherInfoTranslateY.setValue(0);
          HBH_TeeValueTranslateX.setValue(0);
          HBH_ParValueTranslateX.setValue(0);
          HBH_DatePlayedValueTranslateX.setValue(0);
          HBH_DatePlayedIconTranslateX.setValue(0);
        });
      }
    } catch (error) {}
  };

  const renderTextPostHoleByHole = () => {
    if (
      !canPostToUSGA ||
      (canPostToUSGA && roundDataSubmit?.holes?.length === 0)
    ) {
      return null;
    }
    return (
      <Animated.View
        style={{
          flexDirection: 'row',
          justifyContent: 'flex-end',
          paddingBottom: 8,
          opacity: GRO_Opacity,
        }}
      >
        <TouchableOpacity
          onPress={onPressChangeInputScoreMode}
          style={{
            padding: 16,
          }}
        >
          <Text
            Din79Font
            style={{
              textTransform: 'uppercase',
              fontWeight: '700',
              letterSpacing: 1.62,
            }}
            size={12}
            black
          >
            {t('play.button.text.post_hole_by_hole')}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderTextBackToAdjGross = () => {
    return (
      <Animated.View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          paddingBottom: 8,
          opacity: HBH_Opacity,
          transform: [{translateY: HBH_TextBackTranslateY}],
        }}
      >
        <TouchableOpacity
          onPress={onPressChangeInputScoreMode}
          style={{
            padding: 24,
          }}
        >
          <Text
            Din79Font
            style={{
              textTransform: 'uppercase',
              fontWeight: '700',
              letterSpacing: 1.62,
            }}
            size={12}
            black
          >
            {t('play.button.text.back_to_adj_gross_score')}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };
  const isGrossMode = ghinPostScoreMode === GHIN_POST_SCORE_MODE.GROSS;
  const isHBHMode = ghinPostScoreMode === GHIN_POST_SCORE_MODE.HBH;

  const renderGrossDetailMode = () => {
    return (
      <View>
        <Animated.View style={[appStyles.pSm, {opacity: GRO_Opacity}]}>
          <TabRoundDetail
            courseHoleNumber={courseHoleNumber}
            setHolesPlayed={setHolesPlayed}
            holesPlayed={holesPlayed}
          />
        </Animated.View>
        <Animated.View
          pointerEvents={isGrossMode ? 'auto' : 'none'}
          style={{
            opacity: GRO_Opacity,
            transform: [{translateY: GRO_TranslateY}],
          }}
        >
          <Animated.View
            style={[styles.separateLine, {}]}
            key={'line-gross-1'}
          />
          <Animated.View>
            <FormItem
              label={t(
                canPost3rdParty
                  ? 'home.post_score_detail.supporting_copy.adj_gross_score'
                  : 'home.post_score_detail.supporting_copy.score',
              )}
              value={courseScore?.toString() || '00'}
              valueStyle={[
                {
                  marginTop: -20,
                  color: courseScore ? '#000' : '#8C8C91',
                  fontSize: 54,
                  fontWeight: '700',
                  transform: [{scale: GRO_ScoreTypeValueScale}],
                },
              ]}
              Din79Font
              onPress={() => courseScoreRef.current.focus()}
              hasTooltip={canPost3rdParty}
              onPressTooltip={() => {
                setTooltipVisible(true);
              }}
              labelStyle={{color: 'black', fontSize: 16}}
              style={{paddingBottom: 0}}
              customRightIcon={<RightArrowIcon style={{marginBottom: 20}} />}
            />
            <TextInput
              ref={courseScoreRef}
              style={{
                position: 'absolute',
                right: -200,
                opacity: 0,
              }}
              onChangeText={setCourseScore}
              defaultValue={courseScore?.toString()}
              placeholder={'00'}
              keyboardType={'number-pad'}
              returnKeyType={'done'}
              maxLength={3}
            />
            {renderTextPostHoleByHole()}
          </Animated.View>
          <Animated.View
            style={[
              styles.separateLine,
              {
                // transform: [{translateY: HBH_LineTranslateY_1}],
              },
            ]}
            key={'line-gross-2'}
          />
          {canPost3rdParty && (
            <Animated.View>
              <FormItem
                label={'Score Type'}
                value={scoreType}
                onPress={() => {
                  onOpenModal('SCORE_TYPE');
                }}
                labelStyle={{
                  color: 'black',
                  fontSize: 16,
                }}
                valueStyle={{
                  fontSize: 16,
                  transform: [{translateX: GRO_ScoreTypeValueTranslateX}],
                }}
                customRightIcon={<RightArrowIcon />}
              />
              <Animated.View style={styles.separateLine} key={'line-gross-3'} />
            </Animated.View>
          )}
          <Animated.View>
            <FormItem
              label={t('home.post_score_detail.supporting_copy.tees')}
              value={
                teeListOptions.length > 0
                  ? canPostToUSGA
                    ? ghinTeeSelected?.teeSetRatingName
                    : teePlayed
                  : NOT_APPLICABLE
              }
              onPress={() => {
                if (teeListOptions.length > 0) {
                  onOpenModal('TEES_TYPE');
                }
              }}
              style={
                canPost3rdParty && {
                  paddingBottom: Platform.OS === 'ios' ? 5 : 2,
                }
              }
              labelStyle={{color: 'black', fontSize: 16}}
              styleIcon={{marginTop: canPost3rdParty ? 10 : 0}}
              valueStyle={{
                fontSize: 16,
                transform: [{translateX: GRO_TeeValueTranslateX}],
              }}
              customRightIcon={
                teeListOptions.length > 0 ? (
                  <Animated.View
                    style={{transform: [{translateX: GRO_TeeValueTranslateX}]}}
                  >
                    <RightArrowIcon style={{top: canPost3rdParty ? 10 : 0}} />
                  </Animated.View>
                ) : null
              }
            />

            {canPost3rdParty && (
              <View
                style={[
                  appStyles.pHSm,
                  appStyles.pBSm,
                  {
                    flexDirection: 'row-reverse',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  },
                ]}
              >
                <Text
                  size={12}
                  style={[
                    appStyles.textRight,
                    {
                      color: GREY,
                      marginRight: teeListOptions.length > 0 ? 20 : 0,
                    },
                  ]}
                  onPress={() => {
                    if (teeListOptions.length > 0) {
                      onOpenModal('TEES_TYPE');
                    }
                  }}
                >
                  {teeRating}
                </Text>
                {canPost3rdParty && (
                  <Text size={12} style={[appStyles.textRight]} black>
                    {t('ghin.tee_rating_title')}
                  </Text>
                )}
              </View>
            )}
          </Animated.View>
          <Animated.View style={styles.separateLine} key={'line-gross-4'} />
          <Animated.View>
            <FormItem
              label={t('home.post_score.course_par')}
              value={coursePar || '0'}
              iconGrey
              labelStyle={{color: 'black', fontSize: 16}}
              valueStyle={{
                fontSize: 16,
                transform: [{translateX: GRO_ParValueTranslateX}],
              }}
            />
          </Animated.View>
          <Animated.View
            style={[
              styles.separateLine,
              {transform: [{translateY: GRO_DatePlayedTranslateY}]},
            ]}
            key={'line-gross-5'}
          />
          <Animated.View
            style={{transform: [{translateY: GRO_DatePlayedTranslateY}]}}
          >
            <FormItem
              label={t('home.post_score_detail.supporting_copy.date_played')}
              value={datePlayed && moment(datePlayed).format('ll')}
              onPress={() => {
                onOpenModal('DATE_TYPE');
              }}
              labelStyle={{color: 'black', fontSize: 16}}
              valueStyle={{fontSize: 16}}
              customRightIcon={<RightArrowIcon />}
            />
          </Animated.View>
          <Animated.View
            style={[
              styles.separateLine,
              {transform: [{translateY: GRO_DatePlayedTranslateY}]},
            ]}
            key={'line-gross-6'}
          />
        </Animated.View>
      </View>
    );
  };

  const renderHoleByHoleDetailMode = () => {
    return (
      <View pointerEvents={isHBHMode ? 'auto' : 'none'}>
        {roundDataSubmit ? (
          <Animated.View
            style={{
              transform: [{translateY: scoreCardTranslateY}],
              opacity: HBH_Opacity,
            }}
          >
            <Scorecard
              scoreDetail={roundDataSubmit}
              scoreCardDefault={courseScoreCard}
              teeSelected={teePlayed}
              onPressEditScore={holeSelectedNumber => {
                setEditFromScoreCard(true);
                onPressPostScoreHBH();
                setHoleSelected(holeSelectedNumber);
              }}
            />
          </Animated.View>
        ) : null}
        <View>{renderTextBackToAdjGross()}</View>
        <Animated.View
          style={[
            styles.separateLine,
            {
              opacity: HBH_Opacity,
              transform: [{translateY: HBH_LineTranslateY_1}],
            },
          ]}
          key={'line-hbh-1'}
        />
        {canPost3rdParty && (
          <Animated.View
            style={{
              opacity: HBH_Opacity,
              transform: [{translateY: HBH_ScoreTypeTranslateY}],
            }}
          >
            <FormItem
              label={'Score Type'}
              value={scoreType}
              onPress={() => {
                onOpenModal('SCORE_TYPE');
              }}
              labelStyle={{
                color: 'black',
                fontSize: 16,
              }}
              valueStyle={{
                fontSize: 16,
                transform: [{scale: HBH_ScoreTypeValueScale}],
              }}
              customRightIcon={<RightArrowIcon />}
            />
          </Animated.View>
        )}
        <Animated.View
          style={[
            styles.separateLine,
            {
              opacity: HBH_Opacity,
              transform: [{translateY: HBH_LineTranslateY_2}],
            },
          ]}
          key={'line-hbh-2'}
        />
        <Animated.View
          style={{
            opacity: HBH_Opacity,
            transform: [{translateY: HBH_OtherInfoTranslateY}],
          }}
        >
          <FormItem
            label={t('home.post_score_detail.supporting_copy.tees')}
            value={
              teeListOptions.length > 0
                ? canPostToUSGA
                  ? ghinTeeSelected?.teeSetRatingName
                  : teePlayed
                : NOT_APPLICABLE
            }
            onPress={() => {
              // if (teeListOptions.length > 0) {
              //   onOpenModal('TEES_TYPE');
              // }
            }}
            style={
              canPost3rdParty && {paddingBottom: Platform.OS === 'ios' ? 5 : 2}
            }
            labelStyle={{color: 'black', fontSize: 16}}
            styleIcon={{marginTop: canPost3rdParty ? 10 : 0}}
            valueStyle={{
              fontSize: 16,
              transform: [{translateX: HBH_TeeValueTranslateX}],
            }}
            // customRightIcon={
            //   teeListOptions.length > 0 ? (
            //     <RightArrowIcon style={{top: canPost3rdParty ? 10 : 0}} />
            //   ) : null
            // }
          />

          {canPost3rdParty && (
            <View
              style={[
                appStyles.pHSm,
                appStyles.pBSm,
                {
                  flexDirection: 'row-reverse',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                },
              ]}
            >
              <Animated.Text
                size={12}
                style={[
                  appStyles.textRight,
                  {
                    color: GREY,
                    // marginRight: teeListOptions.length > 0 ? 20 : 0,
                    transform: [{translateX: HBH_TeeValueTranslateX}],
                  },
                ]}
              >
                {teeRating}
              </Animated.Text>
              {canPost3rdParty && (
                <Animated.Text
                  size={12}
                  style={[appStyles.textRight, {color: 'black'}]}
                >
                  {t('ghin.tee_rating_title')}
                </Animated.Text>
              )}
            </View>
          )}
        </Animated.View>
        <Animated.View
          style={[
            styles.separateLine,
            {
              opacity: HBH_Opacity,
              transform: [{translateY: HBH_LineTranslateY_345}],
            },
          ]}
          key={'line-hbh-3'}
        />
        <Animated.View
          style={{
            opacity: HBH_Opacity,
            transform: [{translateY: HBH_OtherInfoTranslateY}],
          }}
        >
          <FormItem
            label={t('home.post_score.course_par')}
            value={coursePar || '0'}
            iconGrey
            labelStyle={{color: 'black', fontSize: 16}}
            valueStyle={{
              fontSize: 16,
              transform: [{translateX: HBH_ParValueTranslateX}],
            }}
          />
        </Animated.View>
        <Animated.View
          style={[
            styles.separateLine,
            {
              opacity: HBH_Opacity,
              transform: [{translateY: HBH_LineTranslateY_345}],
            },
          ]}
          key={'line-hbh-4'}
        />
        <Animated.View
          style={{
            opacity: HBH_Opacity,
            transform: [{translateY: HBH_OtherInfoTranslateY}],
          }}
        >
          <FormItem
            label={t('home.post_score_detail.supporting_copy.date_played')}
            value={datePlayed && moment(datePlayed).format('ll')}
            onPress={() => {
              onOpenModal('DATE_TYPE');
            }}
            labelStyle={{color: 'black', fontSize: 16}}
            valueStyle={{
              fontSize: 16,
              transform: [{translateX: HBH_DatePlayedValueTranslateX}],
            }}
            customRightIcon={
              <Animated.View
                style={{
                  transform: [{translateX: HBH_DatePlayedIconTranslateX}],
                }}
              >
                <RightArrowIcon />
              </Animated.View>
            }
          />
        </Animated.View>
        <Animated.View
          style={[
            styles.separateLine,
            {
              opacity: HBH_Opacity,
              transform: [{translateY: HBH_LineTranslateY_345}],
            },
          ]}
          key={'line-hbh-5'}
        />
      </View>
    );
  };
  return (
    <View
      style={{
        paddingBottom: insets.bottom > 0 ? 88 : 72,
      }}
    >
      <View
        style={{
          position: isGrossMode ? 'relative' : 'absolute',
          width: '100%',
          zIndex: 1,
        }}
      >
        {renderGrossDetailMode()}
      </View>

      {isHBHMode && (
        <View
          style={{
            position: 'relative',
            width: '100%',
            zIndex: isHBHMode ? 2 : 0,
          }}
        >
          {renderHoleByHoleDetailMode()}
        </View>
      )}
      {isEditing && (
        <View style={[appStyles.flex, appStyles.vCenter]}>
          <View
            style={[
              {
                borderBottomWidth: 1,
                borderBottomColor: BORDER_LINE_COLOR,
              },
            ]}
          >
            <TouchableOpacity onPress={() => deleteScore()}>
              <Text style={[appStyles.red, appStyles.pSm, appStyles.sm]}>
                {t(
                  'home.post_score_detail_score_input_post_edit.cta.delete_round',
                )}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  separateLine: {
    borderBottomWidth: 1,
    borderBottomColor: BORDER_LINE_COLOR,
  },
});

export default memo(React.forwardRef(PostScoreDetailForm));
