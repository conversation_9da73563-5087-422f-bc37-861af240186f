import React, {useState, useEffect, useRef, useMemo} from 'react';
import {View, StyleSheet} from 'react-native';
import ApproachClassicStats from 'screens/Play/components/ApproachClassicStats';
import DrivingClassicStats from 'screens/Play/components/DrivingClassicStats';
import PuttsClassicStats from 'screens/Play/components/PuttsClassicStats';
import ShortGameClassicStats from 'screens/Play/components/ShortGameClassicStats';
const RoundOverviewStats = ({overallStatsData, courseHolesLength}) => {
  return (
    <View style={{alignItems: 'center'}}>
      <DrivingClassicStats overallStatsData={overallStatsData} />
      <View style={styles.separator} />
      <ApproachClassicStats overallStatsData={overallStatsData} />
      <View style={styles.separator} />
      <ShortGameClassicStats overallStatsData={overallStatsData} isHorizontal />
      <View style={styles.separator} />
      <PuttsClassicStats
        overallStatsData={overallStatsData}
        isHorizontal
        courseHolesLength={courseHolesLength}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  separator: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    width: '90%',
  },
});

export default RoundOverviewStats;
