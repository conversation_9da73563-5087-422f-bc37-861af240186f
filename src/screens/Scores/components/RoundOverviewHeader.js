import React from 'react';
import {View, StyleSheet, Platform} from 'react-native';

import Text from 'components/Text';
import ParAverage from 'screens/Play/components/ParAverage';
import appStyles from 'styles/global';
import moment from 'moment';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import GHIN_USGA_Icon from 'assets/imgs/usga_horz_stack.svg';
import WHS_Double_Check_Icon from 'assets/imgs/ic_ghin_double_check_red.svg';
import {t} from 'i18next';
import {NOT_APPLICABLE} from 'utils/constant';

const RoundOverviewHeader = ({
  holes,
  par,
  teeName,
  teeDistance,
  datePlayed,
  totalScore,
  scoreToPar,
  courseName,
  parAvgStats,
  isFrom3rdPartyRound,
  isUSGA,
  isWHS,
}) => {
  const insets = useSafeAreaInsets();
  return (
    <View style={{backgroundColor: 'white'}}>
      <View style={{marginHorizontal: 8}}>
        <View style={{marginTop: Platform.OS === 'ios' ? insets.top + 5 : insets.top + 13}}>
          <View
            style={[
              appStyles.row,
              {alignItems: 'center', justifyContent: 'center'},
            ]}
          >
            <View style={styles.viewHole}>
              <Text size={8} style={{color: 'rgba(0, 0, 0, 0.6)'}}>
                Holes
              </Text>
              <Text Din79Font style={styles.textNumber}>
                {holes}
              </Text>
            </View>
            <View style={styles.viewHole}>
              <Text size={8} style={{color: 'rgba(0, 0, 0, 0.6)'}}>
                Par
              </Text>
              <Text Din79Font style={styles.textNumber}>
                {par}
              </Text>
            </View>
            {teeName ? (
              <View style={styles.viewHole}>
                <Text size={8} style={{color: 'rgba(0, 0, 0, 0.6)'}}>
                  {teeName} Tee
                </Text>
                <Text Din79Font style={styles.textNumber}>
                  {teeDistance || NOT_APPLICABLE}
                </Text>
              </View>
            ) : null}
            <View
              style={{
                width: 1,
                height: 29,
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                marginHorizontal: 17,
                marginTop: Platform.OS === 'ios' ? 0 : 10,
              }}
            />
            <View style={styles.viewHole}>
              <Text size={8} style={{color: 'rgba(0, 0, 0, 0.6)'}}>
                Date
              </Text>
              <Text Din79Font style={styles.textNumber}>
                {moment(datePlayed).format('MM.DD.YYYY')}{' '}
              </Text>
            </View>
          </View>
          <View style={[appStyles.row, {justifyContent: 'center'}]}>
            <Text Din79Font black style={[styles.courseName]}>
              {courseName}
            </Text>
          </View>
          {isUSGA && (
            <View style={[appStyles.row, styles.viewImageUSGA]}>
              <GHIN_USGA_Icon />
            </View>
          )}
          {isWHS && (
            <View style={[appStyles.row, styles.viewImageUSGA]}>
              <WHS_Double_Check_Icon />
              <Text
                Din79Font
                style={{color: '#B5121B', letterSpacing: 0.135 * 12}}
                size={12}
                weight={700}
              >
                {t('score.golf_canada')}
              </Text>
            </View>
          )}
          <View
            style={[
              appStyles.row,
              {
                justifyContent: 'center',
              },
            ]}
          >
            <Text black Din79Font style={[styles.textAvg]}>
              {totalScore}
            </Text>
            <View style={styles.scoreToParContainer}>
              <Text black Din79Font style={[styles.scoreToPar]}>
                {scoreToPar > 0 ? '+' : ''}
                {scoreToPar === 0 ? 'E' : scoreToPar}
              </Text>
            </View>
          </View>
        </View>
        {!isFrom3rdPartyRound && <ParAverage overallStatsData={parAvgStats} />}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  courseName: {
    fontSize: 22,
    fontWeight: '800',
    textAlign: 'center',
    marginVertical: 15,
    letterSpacing: 1.1,
    textTransform: 'uppercase',
  },
  viewHole: {
    alignItems: 'center',
    marginHorizontal: 8,
    height: 30,
  },
  textNumber: {
    fontSize: 16,
    fontWeight: '800',
    letterSpacing: 1.28,
    textTransform: 'uppercase',
    color: 'rgba(0, 0, 0, 0.6)',
  },
  textAvg: {
    fontSize: 54,
    fontWeight: '700',
    letterSpacing: 1.89,
    marginTop: Platform.OS === 'ios' ? -10 : -15,
  },
  scoreToPar: {
    fontSize: 22,
    fontWeight: '800',
    letterSpacing: 1.1,
    textAlign: 'center',
    marginTop: -2,
    paddingBottom: Platform.OS === 'ios' ? 0 : 3,
  },
  scoreToParContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    minWidth: 36,
    maxHeight: 25,
    marginTop: Platform.OS === 'ios' ? 22 : 16,
    borderRadius: 5,
    paddingHorizontal: Platform.OS === 'ios' ? 3 : 4,
    marginBottom: Platform.OS === 'ios' ? 0 : 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewImageUSGA: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Platform.OS === 'ios' ? -5 : -5,
    marginBottom: Platform.OS === 'ios' ? 5 : 10,
  },
});

export default RoundOverviewHeader;
