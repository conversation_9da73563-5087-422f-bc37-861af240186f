import React from 'react';
import {View, StyleSheet, TouchableWithoutFeedback} from 'react-native';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import Text from 'components/Text';
import {t} from 'i18next';
import appStyles from 'styles/global';
import InsetShadow from 'components/ShadowBox';

const TabRoundDetail = ({courseHoleNumber, setHolesPlayed, holesPlayed}) => {
  return (
    <View style={styles.viewContainerTab}>
      <InsetShadow
        containerStyle={{borderRadius: 24}}
        shadowRadius={10}
        shadowOffset={20}
        elevation={20}
        shadowOpacity={0.2}
      >
        <View
          style={[
            styles.viewInnerShadow,
            appStyles.row,
            appStyles.hCenter,
            appStyles.full,
            {paddingHorizontal: 8},
          ]}
        >
          {courseHoleNumber === 18 ? (
            <>
              <TouchableWithoutFeedback onPress={() => setHolesPlayed(18)}>
                <View
                  style={[
                    appStyles.center,
                    styles.viewItemTab,
                    {
                      backgroundColor:
                        holesPlayed === 18 ? '#000' : 'transparent',
                    },
                  ]}
                >
                  <Text
                    Din79Font
                    style={[
                      styles.textTab,
                      {
                        color: holesPlayed === 18 ? '#ffffff' : '#000',
                      },
                    ]}
                  >
                    {t(
                      'home.post_score_detail.supporting_copy.18_holes',
                    ).toUpperCase()}
                  </Text>
                </View>
              </TouchableWithoutFeedback>
              <TouchableWithoutFeedback onPress={() => setHolesPlayed(9)}>
                <View
                  style={[
                    appStyles.center,
                    styles.viewItemTab,
                    {
                      backgroundColor:
                        holesPlayed === 9 ? '#000' : 'transparent',
                    },
                  ]}
                >
                  <Text
                    Din79Font
                    style={[
                      styles.textTab,
                      {
                        color: holesPlayed === 9 ? '#ffffff' : '#000',
                      },
                    ]}
                  >
                    {t(
                      'home.post_score_detail.supporting_copy.9_holes',
                    ).toUpperCase()}
                  </Text>
                </View>
              </TouchableWithoutFeedback>
            </>
          ) : (
            <TouchableWithoutFeedback onPress={() => setHolesPlayed(9)}>
              <View
                style={[
                  appStyles.center,
                  styles.viewItemTab,
                  {
                    backgroundColor: holesPlayed === 9 ? '#000' : 'transparent',
                  },
                ]}
              >
                <Text
                  style={[
                    styles.textTab,
                    {
                      color: holesPlayed === 9 ? '#ffffff' : '#8C8C91',
                    },
                  ]}
                >
                  {t(
                    'home.post_score_detail.supporting_copy.9_holes',
                  ).toUpperCase()}
                </Text>
              </View>
            </TouchableWithoutFeedback>
          )}
        </View>
      </InsetShadow>
    </View>
  );
};
const styles = StyleSheet.create({
  viewItemTab: {
    width: '50%',
    height: 40,
    borderRadius: 50,
  },
  viewContainerTab: {
    height: 56,
    borderRadius: 24,
    backgroundColor: 'rgba(235, 235, 235, 1)',
  },
  textTab: {
    fontSize: 12,
    fontWeight: '700',
    letterSpacing: 1.2,
  },
  viewInnerShadow: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.01)',
  },
});
export default TabRoundDetail;
