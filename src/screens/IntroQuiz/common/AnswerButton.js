import Images from '../../../../assets/imgs/Images';
import Button from 'components/Button';
import React from 'react';
import {StyleSheet, View, Image} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

const AnswerButton = ({
  text,
  index,
  isSelected,
  setSelectedIndex,
  style,
  isRadioButton,
}) => {
  return (
    <View key={`${index}`}>
      <Button
        text={text}
        textColor={isSelected ? '#fff' : 'black'}
        textStyle={{fontWeight: '700', fontSize: 12, letterSpacing: 12 * 0.135}}
        backgroundColor={isSelected ? 'black' : '#fff'}
        borderColor={isSelected ? 'black' : '#fff'}
        onPress={() => setSelectedIndex(index)}
        style={[styles.answerOption, style]}
        centered
        Din79Font
      />
      {!isRadioButton && <Image style={styles.tick} source={Images.check} />}
    </View>
  );
};
const styles = StyleSheet.create({
  answerOption: {
    width: wp(29),
    marginHorizontal: 4,
    marginBottom: 16,
    height: 40,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 4,
    shadowRadius: 9,
    elevation: 4,
    shadowColor: '#d8d7d7',
  },
  tick: {
    position: 'absolute',
    top: 16,
    right: 17,
    width: 10,
    height: 8,
  },
});
export default AnswerButton;
