const {CommonActions} = require('@react-navigation/native');

export const navigateToHome = ({navigation, isEdit, fromIntro}) => {
  if (!isEdit) {
    if (fromIntro) {
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
            {
              name: 'App',
              screen: 'Home',
            },
          ],
        }),
      );
    } else {
      navigation.navigate('Rewards');
    }
  } else {
    navigation.goBack();
  }
};
