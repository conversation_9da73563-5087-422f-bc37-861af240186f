import Button from 'components/Button';
import TextComponent from 'components/Text/Text';
import React from 'react';
import {Platform, StyleSheet, View} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import LinearGradient from 'react-native-linear-gradient';
const widthFull = wp(100);
const MARGIN_CELL = 4;
const MARGIN_VIEW = 16;

const StepQuiz = ({
  total,
  currentStep,
  disableStep,
  nextPress,
  disabled,
  buttonText,
  btnLoading,
  showGradient = false,
}) => {
  const lineWidth =
    (widthFull - (total - 1) * MARGIN_CELL - 2 * MARGIN_VIEW) / total;
  return (
    <View style={styles.container}>
      {showGradient && !disableStep && (
        <LinearGradient
          colors={['rgba(246,246,246, 0.1)', 'rgb(225, 225, 225)']}
          style={[
            {
              position: 'absolute',
              top: -20,
              width: wp(100),
              height: 20,
              alignItems: 'center',
            },
          ]}
        >
          <TextComponent
            style={styles.textStep}
            Din79Font
          >{`${currentStep} OF ${total}`}</TextComponent>
        </LinearGradient>
      )}
      {!disableStep && (
        <View style={styles.container}>
          {!showGradient && (
            <TextComponent
              style={styles.textStep}
              Din79Font
            >{`${currentStep} OF ${total}`}</TextComponent>
          )}
          <View style={styles.lineContainer}>
            {[...Array(total).keys()].map((item, index) => {
              return (
                <View
                  key={`${index}`}
                  style={[
                    styles.line,
                    {width: lineWidth},
                    index < currentStep && {backgroundColor: 'white'},
                    index === 0 && {marginLeft: 0},
                  ]}
                />
              );
            })}
          </View>
        </View>
      )}
      <Button
        text={buttonText || 'quiz.cta.next'}
        textColor={disabled ? 'white' : '#000'}
        textStyle={{fontWeight: '700', fontSize: 12}}
        backgroundColor={disabled ? 'rgb(225, 225, 225)' : '#fff'}
        borderColor={'rgb(225, 225, 225)'}
        onPress={nextPress}
        loading={btnLoading}
        style={[
          styles.nextButton,
          disabled && {shadowColor: 'rgb(200, 200, 200)'},
          disabled && Platform.OS === 'android' && styles.borderAndroids,
        ]}
        disabled={disabled}
        centered
        Din79Font
      />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  textStep: {
    color: 'black',
    fontWeight: '700',
    fontSize: 13,
  },
  lineContainer: {
    flexDirection: 'row',
    marginTop: 10,
  },
  line: {
    height: 4,
    backgroundColor: 'black',
    borderRadius: 24,
    marginLeft: MARGIN_CELL,
  },
  nextButton: {
    width: wp(100) - 32,
    height: 40,
    marginTop: 24,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 7,
    shadowColor: '#d8d7d7',
    marginBottom: 8,
    elevation: 6,
  },
  borderAndroids: {
    borderWidth: 1,
    borderColor: 'white',
  },
});
export default StepQuiz;
