import React, {useEffect, useRef, useState} from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity,
  Platform,
  FlatList,
  Dimensions,
} from 'react-native';
import StepQuiz from './common/StepQuiz';
import Images from '../../../assets/imgs/Images';
import {t} from 'i18next';
import AnswerButton from './common/AnswerButton';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {useDispatch, useSelector} from 'react-redux';
import {navigateToHome} from './common/commonQuiz';
import Slider from 'components/Slider';
import PagerView from 'react-native-pager-view';
import {Trans} from 'react-i18next';
import {
  GA_EVENT_NAME,
  LOYALTY_ACTION_KEY,
  QUIZ_TYPE,
  SCREEN_TYPES,
  PAGE_CATEGORY,
  PAGE_NAME,
} from 'utils/constant';
import appStyles from 'styles/global';
import GolfCourseSearch from 'components/GolfCourseSearch';
import {getNearbyCoursesList} from 'utils/play';
import {checkLocationPermission} from 'utils/home';
import {updatePlayLocation} from 'reducers/play';
import Geolocation from 'react-native-geolocation-service';
import {getProPlayers, updateUser} from 'requests/accounts';
import {showToast} from 'utils/toast';
import {saveCompletedLoyaltyAction} from 'utils/asyncStorage';
import {updateUserLoyaltyData} from 'utils/loyalty';
import {addCurrentUser} from 'reducers/user';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import * as Animatable from 'react-native-animatable';
import TextComponent from 'components/Text/Text';
import {
  GA_logEvent,
  GA_logScreenViewV2,
  GA_setUserProperty,
} from 'utils/googleAnalytics';
import {setUserLocation} from 'utils/commonVariable';

const answerYearsOptions = [
  'quiz.years.supporting_copy.0_1_years',
  'quiz.years.supporting_copy.2_3_years',
  'quiz.years.supporting_copy.4_5_years',
  'quiz.years.supporting_copy.6_7_years',
  'quiz.years.supporting_copy.8_9_years',
  'quiz.years.supporting_copy.10_years',
];

const quizValues = ['0 - 1', '2 - 3', '4 - 5', '6 - 7', '8 - 9', '10+'];

const answerWeakestOptions = [
  'quiz.strength.supporting_copy.driving',
  'quiz.strength.supporting_copy.approach',
  'quiz.strength.supporting_copy.around_the_green',
  'quiz.strength.supporting_copy.putting',
];
const valueWeakestOptions = [
  'Driving',
  'Approach',
  'Around the Green',
  'Putting',
];

const leftHandedIron = require('assets/imgs/iron-left-handed.png');
const rightHandedIron = require('assets/imgs/iron-right-handed.png');
const greenIndicator = require('assets/imgs/green-indicator.png');

const answerMishitOptions = [
  'quiz.ball_miss.supporting_copy.toe',
  'quiz.ball_miss.supporting_copy.heel',
  'quiz.ball_miss.supporting_copy.shank',
  'quiz.ball_miss.supporting_copy.fat',
  'quiz.ball_miss.supporting_copy.thin',
];
const valueMishitOptions = ['Toe', 'Heel', 'Shank', 'Fat', 'Thin'];

const WIDTH = Dimensions.get('window').width;

const UserQuiz = ({navigation, route}) => {
  const user = useSelector(state => state.user);
  const loyalty = useSelector(state => state.loyalty);
  const quizType = route.params?.quizType;
  const fromGameProfile = route.params?.origin === 'Setting';
  const fromIntro = route.params?.origin === 'TourIntro';
  let indexOf = -1;
  const displayData = fromGameProfile
    ? route.params?.timePlayingGolf
    : user?.golferProfile?.timePlayingGolf?.value;

  answerYearsOptions.forEach((element, index) => {
    if (displayData?.includes(t(element))) {
      indexOf = index;
    }
  });
  const misHit = fromGameProfile
    ? route.params?.misHit
    : user?.golferProfile?.misHit;
  const indexOfMishit = valueMishitOptions.indexOf(misHit);
  const [selectedIndexYears, setSelectedIndexYears] = useState(indexOf);
  const [selectedIndexMishit, setSelectedIndexMishit] = useState(indexOfMishit);
  const [indicatorPosition, setIndicatorPosition] = useState();
  const isRightHanded = true;
  const [currentStep, setCurrentStep] = useState(1);
  const [gender, setGender] = useState(user?.gender);
  const [distanceUnit, setDistanceUnit] = useState(user?.measurementUnits);
  const [rounds, setRounds] = useState(
    fromGameProfile
      ? route.params?.roundsPerMonth
      : user.golferProfile?.roundsPerMonth || 4,
  );
  const weakestAreaProfile = route.params?.weakestArea
    ? route.params?.weakestArea?.split(', ')
    : [];

  const [weaknesses, setWeaknesses] = useState(
    fromGameProfile
      ? weakestAreaProfile
      : user.golferProfile?.weakestArea?.split(', ') || [],
  );

  const coordinates = useSelector(state => state?.play?.playLocation);
  const dispatch = useDispatch();
  const [nearbyCourses, setNearbyCourses] = useState([]);
  const [loadingHomeCourse, setLoadingHomeCourse] = useState(false);
  const [golfCourse, setGolfCourse] = useState(
    fromGameProfile ? null : user.golferProfile?.homeCourse,
  );

  const [idCourse, setIdCourse] = useState(
    fromGameProfile
      ? null
      : user.golferProfile?.iGolfCourseId,
  );
  const [favoritePlayers, setFavoritePlayers] = useState(
    fromGameProfile
      ? route.params?.favoriteTeamMembers
        ? route.params?.favoriteTeamMembers?.split(', ')
        : []
      : user.golferProfile?.favoriteTeamMembers?.split(', ') || [],
  );

  const [playerList, setPlayerList] = useState([]);
  const [loadingSave, setLoadingSave] = useState(false);
  const [finishOnboarding, setFinishOnboarding] = useState(false);
  const [selectedData, setSelectedData] = useState({});
  const refPagerView = useRef();
  const appCacheVersions = useSelector(state => state.appCacheVersions);

  useEffect(() => {
    setBallMissInfo(valueMishitOptions[selectedIndexMishit]);
  }, [selectedIndexMishit]);

  useEffect(() => {
    let pageName,
      pageCategory,
      pageType = null;
    if (fromGameProfile) {
      switch (quizType) {
        case QUIZ_TYPE.GENDER:
          pageName = PAGE_NAME.ACCOUNT_GENDER_SETTING;
          pageCategory = PAGE_CATEGORY.ACCOUNT_GENDER;
          pageType = SCREEN_TYPES.ACCOUNT;
          break;
        case QUIZ_TYPE.YEARS:
          pageName = PAGE_NAME.ONBOARDING_YEARS_EXPERIENCE;
          pageCategory = PAGE_CATEGORY.ONBOARDING;
          pageType = SCREEN_TYPES.ONBOARDING;
          break;
        case QUIZ_TYPE.ROUNDS:
          pageName = PAGE_NAME.ONBOARDING_FREQUENCY;
          pageCategory = PAGE_CATEGORY.ONBOARDING;
          pageType = SCREEN_TYPES.ONBOARDING;
          break;
        case QUIZ_TYPE.WEAKEST:
          pageName = PAGE_NAME.ONBOARDING_WEAKEST_AREA;
          pageCategory = PAGE_CATEGORY.ONBOARDING;
          pageType = SCREEN_TYPES.ONBOARDING;
          break;
        case QUIZ_TYPE.MISHIT:
          pageName = PAGE_NAME.ONBOARDING_MIS_HIT;
          pageCategory = PAGE_CATEGORY.ONBOARDING;
          pageType = SCREEN_TYPES.ONBOARDING;
          break;
        case QUIZ_TYPE.HOMECOURSE:
          pageName = PAGE_NAME.ONBOARDING_HOME_COURSE;
          pageCategory = PAGE_CATEGORY.ONBOARDING;
          pageType = SCREEN_TYPES.ONBOARDING;
          break;
        case QUIZ_TYPE.FOLLOWPLAYER:
          pageName = PAGE_NAME.ONBOARDING_FOLLOW_PLAYER;
          pageCategory = PAGE_CATEGORY.ONBOARDING;
          pageType = SCREEN_TYPES.ONBOARDING;
          break;
        default:
          break;
      }
      if (pageName && pageCategory && pageType) {
        GA_logScreenViewV2(pageName, pageCategory, pageType, pageType);
      }
    }
  }, [fromGameProfile]);

  useEffect(() => {
    const getDataNearbyCourse = async () => {
      if (!coordinates) {
        return;
      }
      setLoadingHomeCourse(true);
      try {
        if (Object.keys(coordinates)?.length) {
          const nearbyCoursesFromApi = await getNearbyCoursesList({
            latitude: coordinates?.latitude,
            longitude: coordinates?.longitude,
            dispatch,
            isGettingFirst3Courses: true,
          });
          setNearbyCourses(nearbyCoursesFromApi);
          setLoadingHomeCourse(false);
        } else {
          setLoadingHomeCourse(false);
        }
      } catch (error) {
        setLoadingHomeCourse(false);
      }
    };
    if (
      !fromGameProfile ||
      (fromGameProfile && quizType === QUIZ_TYPE.HOMECOURSE)
    ) {
      getDataNearbyCourse();
    }
  }, [coordinates, fromIntro, fromGameProfile, quizType]);

  useEffect(() => {
    if (Object.keys(coordinates)?.length === 0) {
      (async () => {
        try {
          setLoadingHomeCourse(true);
          const checkPermission = await checkLocationPermission();
          if (checkPermission) {
            getLocation();
          } else {
            setLoadingHomeCourse(false);
          }
        } catch (error) {
          setLoadingHomeCourse(false);
        }
      })();
    }
  }, []);

  const getLocation = () => {
    Geolocation.getCurrentPosition(
      position => {
        const posCoordinates = position.coords;
        setUserLocation(posCoordinates);
        dispatch(updatePlayLocation(posCoordinates));
        setLoadingHomeCourse(false);
      },
      error => {
        setLoadingHomeCourse(false);
      },
      {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
    );
  };

  const setBallMissInfo = value => {
    switch (value) {
      case 'Toe':
        setIndicatorPosition(
          isRightHanded ? appStyles.circleRightToe : appStyles.circleLeftToe,
        );
        break;
      case 'Shank':
        setIndicatorPosition(
          isRightHanded
            ? appStyles.circleRightShank
            : appStyles.circleLeftShank,
        );
        break;
      case 'Heel':
        setIndicatorPosition(
          isRightHanded ? appStyles.circleRightHeel : appStyles.circleLeftHeel,
        );
        break;
      case 'Fat':
        setIndicatorPosition(
          isRightHanded ? appStyles.circleRightFat : appStyles.circleLeftFat,
        );
        break;
      case 'Thin':
        setIndicatorPosition(
          isRightHanded ? appStyles.circleRightThin : appStyles.circleLeftThin,
        );
        break;
    }
  };

  useEffect(() => {
    if (
      !fromGameProfile ||
      (fromGameProfile && quizType === QUIZ_TYPE.FOLLOWPLAYER)
    ) {
      requestProPlayers();
    }
  }, [fromIntro, fromGameProfile, quizType]);

  const requestProPlayers = async () => {
    try {
      // Make request to pull pro players
      const players = await getProPlayers();
      // Stop loading state and set player list
      setPlayerList(
        players.filter(player => player.id !== 23957 && player.id !== 26546),
      );
    } catch (error) {
      // showToast({
      //   type: 'error',
      //   message: t('An_error_occurred_retrieving_players'),
      // });
    }
  };
  const nextPress = () => {
    const updatedSelectedData = {
      ...selectedData,
      timePlayingGolf: quizValues[selectedIndexYears] + ' Years',
      gender: gender,
      roundsPerMonth: rounds,
      rmp: rounds,
      misHit: valueMishitOptions[selectedIndexMishit],
      weakestArea: weaknesses.join(', '),
      homeCourse: golfCourse,
      iGolfCourseId: idCourse,
      favoriteTeamMembers: favoritePlayers.join(', '),
    };
    setSelectedData(updatedSelectedData);
    if (currentStep < 6) {
      logGAEvents(currentStep);
      refPagerView.current?.setPage(currentStep);
      setCurrentStep(currentStep + 1);
    } else {
      logGAEvents(currentStep);
      completeOnboarding(updatedSelectedData);
    }
  };

  const logGAEvents = async step => {
    try {
      switch (step) {
        case 1:
          GA_logEvent(GA_EVENT_NAME.USER_ONBOARDING_GENDER, {
            user_gender: gender,
            page_type: SCREEN_TYPES.ONBOARDING,
            page_category: PAGE_CATEGORY.ONBOARDING,
            page_name: PAGE_NAME.ONBOARDING_GENDER,
            screen_type: SCREEN_TYPES.ONBOARDING,
          });
          GA_setUserProperty('user_gender', gender);
          GA_logEvent(GA_EVENT_NAME.USER_ONBOARDING_EXPERIENCE, {
            years_playing: quizValues[selectedIndexYears],
            page_type: SCREEN_TYPES.ONBOARDING,
            page_category: PAGE_CATEGORY.ONBOARDING,
            page_name: PAGE_NAME.ONBOARDING_YEARS_EXPERIENCE,
            screen_type: SCREEN_TYPES.ONBOARDING,
          });
          GA_setUserProperty('years_playing', quizValues[selectedIndexYears]);
          GA_logScreenViewV2(
            PAGE_NAME.ONBOARDING_GENDER,
            PAGE_CATEGORY.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
          );
          break;
        case 2:
          GA_logEvent(GA_EVENT_NAME.USER_ONBOARDING_PLAYING_FREQUENCY, {
            round_per_month: rounds + '',
            page_type: SCREEN_TYPES.ONBOARDING,
            page_category: PAGE_CATEGORY.ONBOARDING,
            page_name: PAGE_NAME.ONBOARDING_FREQUENCY,
            screen_type: SCREEN_TYPES.ONBOARDING,
          });
          GA_setUserProperty('rounds_per_month', rounds + '');
          GA_logScreenViewV2(
            PAGE_NAME.ONBOARDING_FREQUENCY,
            PAGE_CATEGORY.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
          );
          break;
        case 3:
          GA_logEvent(GA_EVENT_NAME.USER_ONBOARDING_WEAKEST_GAME_AREA, {
            game_area: weaknesses.join(', '),
            page_type: SCREEN_TYPES.ONBOARDING,
            page_category: PAGE_CATEGORY.ONBOARDING,
            page_name: PAGE_NAME.ONBOARDING_WEAKEST_AREA,
            screen_type: SCREEN_TYPES.ONBOARDING,
          });
          GA_logScreenViewV2(
            PAGE_NAME.ONBOARDING_WEAKEST_AREA,
            PAGE_CATEGORY.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
          );
          break;
        case 4:
          GA_logEvent(GA_EVENT_NAME.USER_ONBOARDING_MISHIT, {
            mis_hit: valueMishitOptions[selectedIndexMishit],
            page_type: SCREEN_TYPES.ONBOARDING,
            page_category: PAGE_CATEGORY.ONBOARDING,
            page_name: PAGE_NAME.ONBOARDING_MIS_HIT,
            screen_type: SCREEN_TYPES.ONBOARDING,
          });
          GA_logScreenViewV2(
            PAGE_NAME.ONBOARDING_MIS_HIT,
            PAGE_CATEGORY.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
          );
          break;
        case 5:
          GA_logEvent(GA_EVENT_NAME.USER_ONBOARDING_HOME_COURSE, {
            home_course: golfCourse,
            page_type: SCREEN_TYPES.ONBOARDING,
            page_category: PAGE_CATEGORY.ONBOARDING,
            page_name: PAGE_NAME.ONBOARDING_HOME_COURSE,
            screen_type: SCREEN_TYPES.ONBOARDING,
          });
          GA_setUserProperty('home_course', golfCourse + '');
          GA_logScreenViewV2(
            PAGE_NAME.ONBOARDING_HOME_COURSE,
            PAGE_CATEGORY.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
          );
          break;
        case 6:
          GA_logEvent(GA_EVENT_NAME.USER_ONBOARDING_FOLLOW_PLAYERS, {
            followed_players: favoritePlayers.join(', '),
            page_type: SCREEN_TYPES.ONBOARDING,
            page_category: PAGE_CATEGORY.ONBOARDING,
            page_name: PAGE_NAME.ONBOARDING_FOLLOW_PLAYER,
            screen_type: SCREEN_TYPES.ONBOARDING,
          });
          GA_logEvent(GA_EVENT_NAME.USER_ONBOARDING_COMPLETED, {
            years_playing: quizValues[selectedIndexYears],
            round_per_month: rounds,
            game_area: weaknesses.join(', '),
            mis_hit: valueMishitOptions[selectedIndexMishit],
            home_course: golfCourse,
            followed_players: favoritePlayers.join(', '),
            page_type: SCREEN_TYPES.ONBOARDING,
            page_category: PAGE_CATEGORY.ONBOARDING,
            page_name: PAGE_NAME.ONBOARDING_FOLLOW_PLAYER,
            screen_type: SCREEN_TYPES.ONBOARDING,
          });
          GA_logScreenViewV2(
            PAGE_NAME.ONBOARDING_FOLLOW_PLAYER,
            PAGE_CATEGORY.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
            SCREEN_TYPES.ONBOARDING,
          );
          break;
        default:
          break;
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const updateWeaknesses = weakness => {
    // Check to see if incoming weakness exists
    const hasWeakness = weaknesses?.includes(weakness);
    if (hasWeakness) {
      // Remove weakness for deselect
      setWeaknesses(values => values.filter(value => value !== weakness));
    } else {
      // Add weakness for select
      setWeaknesses(values => [...values, weakness]);
    }
  };

  const updatePress = () => {
    switch (quizType) {
      case QUIZ_TYPE.GENDER:
        updateUserGender();
        return;
      case QUIZ_TYPE.DISTANCE:
        updateDistanceUnit();
        return;
      case QUIZ_TYPE.YEARS:
        if (quizValues[selectedIndexYears]) {
          route.params?.setTimePlayingGolf?.(quizValues[selectedIndexYears]);
        } else {
          route.params?.setTimePlayingGolf?.('');
        }
        break;
      case QUIZ_TYPE.ROUNDS:
        route.params?.setRoundsPerMonth?.(rounds);
        break;
      case QUIZ_TYPE.WEAKEST:
        route.params?.setWeakestArea?.(weaknesses.join(', '));
        break;
      case QUIZ_TYPE.MISHIT:
        route.params?.setMisHit?.(valueMishitOptions[selectedIndexMishit]);
        break;
      case QUIZ_TYPE.HOMECOURSE:
        route.params?.setHomeCourse?.(golfCourse);
        route.params?.setIGolfCourseId?.(idCourse);
        break;
      case QUIZ_TYPE.FOLLOWPLAYER:
        route.params?.setFavoriteTeamMembers?.(favoritePlayers.join(', '));
        break;
      default:
        break;
    }

    navigation.goBack();
  };

  const updateUserGender = async () => {
    setLoadingSave(true);
    let previousGender = user?.gender;
    let isGenderChanged = previousGender !== gender;
    try {
      // Make request to update user's gender
      const updatedUser = await updateUser({
        gender,
      });
      if (isGenderChanged) {
        GA_logEvent(GA_EVENT_NAME.ACCOUNT_SETTINGS_CHANGED, {
          setting_name: 'gender',
          previous_setting: previousGender + '',
          new_setting: gender + '',
          page_type: SCREEN_TYPES.ACCOUNT,
          page_category: PAGE_CATEGORY.ACCOUNT_GENDER,
          page_name: PAGE_NAME.ACCOUNT_GENDER_SETTING,
          screen_type: SCREEN_TYPES.ACCOUNT,
        });
        GA_setUserProperty('user_gender', gender);
      }

      // Update user in redux
      dispatch(addCurrentUser(updatedUser));
      // Stop loading state and navigate to next screen
      navigation.goBack();
      setLoadingSave(false);
    } catch (error) {
      setLoadingSave(false);
      return showToast({
        type: 'error',
        message: t('An_error_occurred_updating_your_gender'),
      });
    }
  };

  const updateDistanceUnit = async () => {
    setLoadingSave(true);
    let previousDistanceUnit = user?.measurementUnits;
    let isDistanceUnitChanged = previousDistanceUnit !== distanceUnit;
    try {
      // Make request to update user's gender
      const updatedUser = await updateUser({
        measurementUnits: distanceUnit,
      });
      if (isDistanceUnitChanged) {
        GA_logEvent(GA_EVENT_NAME.ACCOUNT_SETTINGS_CHANGED, {
          setting_name: 'Distance Preference',
          previous_setting: previousDistanceUnit + '',
          new_setting: distanceUnit + '',
          page_type: SCREEN_TYPES.ACCOUNT,
          page_category: PAGE_CATEGORY.ACCOUNT_DISTANCE_UNIT,
          page_name: PAGE_NAME.ACCOUNT_DISTANCE_UNIT_SETTING,
          screen_type: SCREEN_TYPES.ACCOUNT,
        });
        GA_setUserProperty('user_distance_preference', distanceUnit);
      }

      // Update user in redux
      dispatch(addCurrentUser(updatedUser));
      // Stop loading state and navigate to next screen
      navigation.goBack();
      setLoadingSave(false);
    } catch (error) {
      setLoadingSave(false);
      return showToast({
        type: 'error',
        message: t('An_error_occurred_updating_your_distance_preference'),
      });
    }
  };

  const completeOnboarding = async dataSave => {
    setLoadingSave(true);
    try {
      // Make request to update user
      const updatedUser = await updateUser({
        ...dataSave,
        onboardingCompleteSteps: {
          firstName: true,
          lastName: true,
          gender: true,
          rpmComplete: true,
          timePlayingGolf: true,
          weakestArea: true,
          misHit: true,
          homeCourse: true,
          favoriteTeamMembers: true,
        },
        onboardingComplete: true,
        signUpByDevice: Platform.OS,
      });
      if (updatedUser && updatedUser.id) {
        saveCompletedLoyaltyAction(
          LOYALTY_ACTION_KEY.COMPLETE_QUIZ,
          loyalty?.completedActions,
          loyalty?.loyaltyActions?.data,
          'user-quiz',
        );
        updateUserLoyaltyData(
          dispatch,
          user?.userCountry,
          loyalty,
          appCacheVersions,
        );
      }
      // Update user in redux
      setFinishOnboarding(true);
      dispatch(addCurrentUser(updatedUser));
      // Stop loading state and navigate to next screen
      setLoadingSave(false);
      setTimeout(() => {
        navigateToHome({navigation, isEdit: fromGameProfile, fromIntro});
      }, 2000);
    } catch (error) {
      setLoadingSave(false);
      showToast({
        type: 'error',
        message: t('An_error_occurred_with_your_onboarding'),
      });
      setTimeout(() => {
        navigateToHome({navigation, isEdit: fromGameProfile, fromIntro});
      }, 2000);
    }
  };

  const noHomeCourseAction = () => {
    if (fromGameProfile) {
      route.params?.setHomeCourse('');
      route.params?.setIGolfCourseId('');
      navigation.goBack();
    } else {
      const updatedSelectedData = {
        ...selectedData,
        homeCourse: '',
        iGolfCourseId: '',
      };
      setSelectedData(updatedSelectedData);
      refPagerView.current?.setPage(5);
      setCurrentStep(6);
    }
  };

  const updateUserHomeCourse = async course => {
    setGolfCourse(course?.courseName || '');
    setIdCourse(course?.idCourse || '');
  };

  const renderGender = () => {
    return (
      <View style={styles.answerContainer}>
        <AnswerButton
          text={t('quiz.gender.supporting_copy.male').toUpperCase()}
          index={gender}
          isSelected={gender === 'male'}
          setSelectedIndex={() => setGender('male')}
          style={{width: (wp(100) - 40) / 2}}
        />
        <AnswerButton
          text={t('quiz.gender.supporting_copy.female').toUpperCase()}
          index={gender}
          isSelected={gender === 'female'}
          setSelectedIndex={() => setGender('female')}
          style={{width: (wp(100) - 40) / 2}}
        />
        <AnswerButton
          text={t('quiz.gender.supporting_copy.notSay').toUpperCase()}
          index={gender}
          isSelected={gender === 'prefer not to say'}
          setSelectedIndex={() => setGender('prefer not to say')}
          style={{width: wp(100) - 32}}
        />
      </View>
    );
  };

  const renderDistancePreference = () => {
    return (
      <View style={styles.answerContainer}>
        <AnswerButton
          text={t('settings.distance_preference.yards').toUpperCase()}
          index={distanceUnit}
          isSelected={distanceUnit === 'yards'}
          setSelectedIndex={() => setDistanceUnit('yards')}
          style={{width: wp(100) - 32}}
        />
        <AnswerButton
          text={t('settings.distance_preference.meters').toUpperCase()}
          index={distanceUnit}
          isSelected={distanceUnit === 'meters'}
          setSelectedIndex={() => setDistanceUnit('meters')}
          style={{width: wp(100) - 32}}
        />
      </View>
    );
  };

  const renderAnswers = () => {
    return (
      <View style={styles.answerContainer}>
        {answerYearsOptions.map((item, index) => {
          return (
            <AnswerButton
              text={t(item)}
              index={index}
              isSelected={index === selectedIndexYears}
              setSelectedIndex={setSelectedIndexYears}
            />
          );
        })}
      </View>
    );
  };

  const closeAction = () => {
    navigateToHome({navigation, isEdit: fromGameProfile, fromIntro});
  };

  const renderGenderYearsQuiz = () => {
    return (
      <View style={[styles.contentView, {paddingTop: 62 + insets.top}]}>
        {(!fromGameProfile ||
          (fromGameProfile && quizType === QUIZ_TYPE.GENDER)) && (
          <View style={{alignItems: 'center'}}>
            <Text style={styles.title}>{t('quiz.gender.question')}</Text>
            {renderGender()}
          </View>
        )}
        {(!fromGameProfile ||
          (fromGameProfile && quizType === QUIZ_TYPE.YEARS)) && (
          <>
            <Text style={styles.title}>{t('quiz.years.headline')}</Text>
            {renderAnswers()}
          </>
        )}
        {((fromGameProfile && quizType === QUIZ_TYPE.DISTANCE)) && (
          <View style={{alignItems: 'center'}}>
            <Text style={styles.title}>{t('settings.headline.distance_preference')}</Text>
            {renderDistancePreference()}
          </View>
        )}
        <View style={{flex: 1}} />
      </View>
    );
  };

  //#region ROUND QUIZ
  const renderRoundQuizAnswers = () => {
    return (
      <View style={styles.answerRoundQuizContainer}>
        <Slider
          min={1}
          max={31}
          minimumTrackTintColor="#000"
          maximumTrackTintColor="rgb(177,177,177)"
          value={rounds}
          onValueChange={value => setRounds(Math.round(value))}
          textStyle={{color: 'black'}}
          thumbTintColor={'black'}
        />
      </View>
    );
  };

  const renderRoundQuiz = () => {
    return (
      <View style={[styles.contentView, {paddingTop: 62 + insets.top}]}>
        <Text style={styles.titleRoundQuiz}>{t('quiz.rounds.headline')}</Text>
        {renderRoundQuizAnswers()}
        <View style={{flex: 1}} />
      </View>
    );
  };
  //#endregion

  //#region WEAKEST QUIZ
  const renderWeakestAnswers = () => {
    return (
      <View style={styles.answerWeakestQuizContainer}>
        {answerWeakestOptions.map((item, index) => {
          return (
            <AnswerButton
              text={t(item).toUpperCase()}
              index={index}
              key={item}
              isSelected={weaknesses?.includes(valueWeakestOptions[index])}
              setSelectedIndex={i => updateWeaknesses(valueWeakestOptions[i])}
              style={{width: wp(100) - 16 * 2}}
            />
          );
        })}
      </View>
    );
  };

  const renderWeakestQuiz = () => {
    return (
      <View style={[styles.contentView, {paddingTop: 62 + insets.top}]}>
        <Trans
          defaults={'quiz.weakness.question'}
          parent={Text}
          style={styles.titleWeakestQuiz}
          components={{
            bold: <Text black size={16} style={{fontWeight: 'bold'}} />,
          }}
        />
        {renderWeakestAnswers()}
        <View style={{flex: 1}} />
      </View>
    );
  };
  //#endregion

  //#region MISHIT
  const renderMishitAnswers = () => {
    return (
      <View style={styles.answerMishitQuizContainer}>
        <View>
          <Image
            style={[
              appStyles.alignCenter,
              appStyles.mBXxs,
              appStyles.responsiveImageBallStrike,
            ]}
            source={rightHandedIron}
          />
          <Image
            style={[
              appStyles.circle,
              indicatorPosition,
              {opacity: selectedIndexMishit >= 0 ? 1 : 0},
            ]}
            source={greenIndicator}
          />
        </View>
        <View style={styles.optionContainer}>
          {answerMishitOptions.map((item, index) => {
            return (
              <AnswerButton
                text={t(item).toUpperCase()}
                index={index}
                isSelected={index === selectedIndexMishit}
                setSelectedIndex={setSelectedIndexMishit}
                style={{width: (wp(100) - 16 * 2 - 8 * 2) / 3}}
              />
            );
          })}
        </View>
      </View>
    );
  };
  const renderMishitQuiz = () => {
    return (
      <View style={[styles.contentView, {paddingTop: 62 + insets.top}]}>
        <Text style={styles.titleMishitQuiz}>
          {t('quiz.ball_miss.headline')}
        </Text>
        {renderMishitAnswers()}
        <View style={{flex: 1}} />
      </View>
    );
  };
  //#endregion

  //#region HOME COURSE QUIZ
  const renderHomeCourseAnswers = () => {
    return (
      <View style={styles.answerHomeCourseContainer}>
        <GolfCourseSearch
          golfCourse={golfCourse}
          idCourse={idCourse}
          setCourse={updateUserHomeCourse}
          disabled={loadingHomeCourse}
          initLoading={loadingHomeCourse}
          fromGameProfile={fromGameProfile}
          nearbyCourses={nearbyCourses}
          noHomeCourseAction={noHomeCourseAction}
        />
      </View>
    );
  };

  const renderHomeCourseQuiz = () => {
    return (
      <View style={[styles.contentView, {paddingTop: 62 + insets.top}]}>
        <Text style={styles.titleHomeCourseQuiz}>
          {t('quiz.homeCourse.headline')}
        </Text>
        {renderHomeCourseAnswers()}

        <View style={{flex: 1}} />
      </View>
    );
  };
  //#endregion

  //#region FOLLOW PLAYER
  const updateFavoritePlayers = player => {
    // Check to see if incoming player exists
    const hasPlayer = favoritePlayers?.includes(player);
    if (hasPlayer) {
      // Remove player for deselect
      setFavoritePlayers(values => values.filter(value => value !== player));
    } else {
      // Add player for select
      setFavoritePlayers(values => [...values, player]);
    }
  };
  const renderItem = item => {
    const isInclude = favoritePlayers.includes(item?.title);
    return (
      <TouchableOpacity
        style={[
          styles.itemWrapper,
          appStyles.viewShadowLightBig,
          isInclude && {backgroundColor: 'black'},
        ]}
        onPress={() => updateFavoritePlayers(item?.title)}
      >
        <Image style={[styles.itemImage]} source={{uri: item?.headshot}} />

        <Text
          style={[styles.name, isInclude && {color: 'white'}]}
          black
          size={16}
        >
          {item?.title}
        </Text>
        <Image style={styles.tick} source={Images.check} />
      </TouchableOpacity>
    );
  };
  const renderFollowPlayerQuiz = () => {
    return (
      <View
        style={[
          styles.contentView,
          {
            backgroundColor: 'rgb(225, 225, 225)',
          },
        ]}
      >
        <FlatList
          style={styles.answerPlayersContainer}
          data={playerList}
          numColumns={2}
          keyExtractor={item => item?.id}
          renderItem={({item}) => renderItem(item)}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={() => {
            return (
              <View
                style={{
                  paddingTop: insets.top,
                  alignItems: 'center',
                  paddingBottom: 31,
                }}
              >
                <Image style={styles.logo} source={Images.logo_Black} />
                <Text style={styles.titleFollowPlayer}>
                  {t('quiz.homeCourse.favorite_players.headline')}
                </Text>
              </View>
            );
          }}
          ListFooterComponent={<View style={{height: 50}} />}
        />
        <View style={{flex: 1}} />
      </View>
    );
  };
  //#endregion

  const checkDisableUpdateButton = () => {
    if (loadingSave && fromGameProfile) {
      return true;
    }
    if (fromGameProfile) {
      return false;
    } else {
      switch (currentStep) {
        case 1:
          return fromGameProfile ? false : selectedIndexYears < 0 || !gender;
        case 2:
          return false;
        case 3:
          return weaknesses?.length === 0;
        case 4:
          return selectedIndexMishit < 0;
        case 5:
          return false;
        case 6:
          return !favoritePlayers.length;
        default:
          break;
      }
    }
  };

  const getInitialPage = () => {
    switch (quizType) {
      case QUIZ_TYPE.YEARS:
      case QUIZ_TYPE.GENDER:
        return 0;
      case QUIZ_TYPE.ROUNDS:
        return 1;
      case QUIZ_TYPE.WEAKEST:
        return 2;
      case QUIZ_TYPE.MISHIT:
        return 3;
      case QUIZ_TYPE.HOMECOURSE:
        return 4;
      case QUIZ_TYPE.FOLLOWPLAYER:
        return 5;
      default:
        return 0;
    }
  };
  const insets = useSafeAreaInsets();
  return (
    <View
      style={[
        styles.container,
        {
          paddingBottom: insets.bottom,
        },
      ]}
    >
      <Image
        style={[
          styles.logo,
          {
            marginTop: 12 + insets.top,
          },
        ]}
        source={Images.logo_Black}
      />
      <TouchableOpacity style={styles.closeButton} onPress={closeAction}>
        <Image style={styles.closeImage} source={Images.closeQuiz} />
      </TouchableOpacity>
      <PagerView
        ref={refPagerView}
        style={[
          styles.pagerView,
          {
            marginTop: -(62 + insets.top),
          },
        ]}
        initialPage={getInitialPage()}
        scrollEnabled={false}
      >
        {renderGenderYearsQuiz()}
        {renderRoundQuiz()}
        {renderWeakestQuiz()}
        {renderMishitQuiz()}
        {renderHomeCourseQuiz()}
        {renderFollowPlayerQuiz()}
      </PagerView>
      <StepQuiz
        total={6}
        currentStep={currentStep}
        disableStep={fromGameProfile}
        disabled={checkDisableUpdateButton()}
        nextPress={fromGameProfile ? updatePress : nextPress}
        buttonText={
          fromGameProfile
            ? t('common.update')
            : currentStep === 6
            ? t('quiz.homeCourse.done').toUpperCase()
            : null
        }
        btnLoading={loadingSave}
      />
      {finishOnboarding && (
        <Animatable.View
          style={styles.earnPointWrapper}
          deviceWidth={wp(100)}
          animation={'slideInUp'}
          duration={700}
        >
          <View style={{flex: 1, alignItems: 'center'}}>
            <TextComponent Din79Font style={styles.successText} black>
              {t('quiz.followPlayer.success')}
            </TextComponent>
            <View style={styles.pointWrapper}>
              <TextComponent Din79Font style={styles.point} black>
                {t('quiz.followPlayer.point')}
              </TextComponent>
              <TextComponent Din79Font style={styles.pointSymbol} black>
                {t('quiz.followPlayer.pts')}
              </TextComponent>
            </View>
          </View>
        </Animatable.View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: 'rgb(225, 225, 225)',
    flex: 1,
  },
  logo: {
    with: 50,
    height: 50,
    marginTop: 12,
  },
  title: {
    marginTop: 67,
    marginHorizontal: 16,
    fontSize: 16,
    fontWeight: '400',
    color: 'black',
  },
  answerContainer: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 32,
  },
  closeButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 40 : 4,
    right: 16,
    justifyContent: 'center',
    alignItems: 'center',
    width: 44,
    height: 44,
    zIndex: 100,
  },
  closeImage: {
    width: 24,
    height: 24,
  },
  contentView: {alignItems: 'center', width: wp(100)},
  titleRoundQuiz: {
    marginTop: 67,
    marginHorizontal: 40,
    fontSize: 16,
    fontWeight: '400',
    color: 'black',
    textAlign: 'center',
  },
  answerRoundQuizContainer: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 32,
    marginHorizontal: 16,
  },
  answerWeakestQuizContainer: {
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: 32,
    marginHorizontal: 16,
  },
  answerMishitQuizContainer: {
    justifyContent: 'center',
    marginTop: 32,
  },
  pagerView: {
    width: '100%',
    flex: 1,
    zIndex: 2,
  },
  titleWeakestQuiz: {
    marginTop: 67,
    marginHorizontal: 40,
    fontSize: 16,
    fontWeight: '400',
    color: 'black',
    textAlign: 'center',
  },
  titleMishitQuiz: {
    marginTop: 67,
    marginHorizontal: 40,
    fontSize: 16,
    fontWeight: '400',
    color: 'black',
    textAlign: 'center',
  },
  optionContainer: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 32,
  },
  titleHomeCourseQuiz: {
    marginTop: 62,
    marginHorizontal: 40,
    fontSize: 16,
    fontWeight: '400',
    color: 'black',
    textAlign: 'center',
  },
  answerPlayersContainer: {
    marginLeft: 8,
  },
  titleFollowPlayer: {
    marginTop: 26,
    marginHorizontal: 40,
    fontSize: 16,
    fontWeight: '400',
    color: 'black',
    textAlign: 'center',
  },
  itemWrapper: {
    backgroundColor: 'white',
    width: 168,
    height: 281,
    borderRadius: 16,
    padding: 4,
    alignItems: 'center',
    marginRight: 8,
    marginBottom: 8,
  },
  itemImage: {
    width: 160,
    height: 213,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: '700',
    marginTop: 16,
    marginHorizontal: 4,
  },
  tick: {
    position: 'absolute',
    bottom: 12,
    right: 78,
    width: 10,
    height: 8,
  },
  earnPointWrapper: {
    backgroundColor: 'white',
    position: 'absolute',
    width: WIDTH,
    height: 150,
    left: 0,
    bottom: 0,
    alignItems: 'center',
    borderTopLeftRadius: 46,
    borderTopRightRadius: 46,
    zIndex: 10,
  },
  answerHomeCourseContainer: {
    justifyContent: 'center',
    marginTop: 24,
    marginHorizontal: 16,
    width: wp(100) - 32,
  },
  successText: {
    fontSize: 22,
    fontWeight: '800',
    marginTop: 17,
  },
  pointWrapper: {
    // marginTop: 19,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  point: {
    fontSize: 54,
    fontWeight: '700',
  },
  pointSymbol: {
    fontSize: 22,
    fontWeight: '800',
    marginLeft: 10,
    paddingBottom: 8,
  },
});
export default UserQuiz;
