import React, {useEffect, useRef, useState} from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  Platform,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Images from '../../../assets/imgs/Images';
import {t} from 'i18next';
import AnswerButton from './common/AnswerButton';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import {useDispatch, useSelector} from 'react-redux';
import {Trans} from 'react-i18next';
import {getCountries, getUser, sendOTP, updateUser} from 'requests/accounts';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {getDeviceCountry, setCountry, setNewUser} from 'utils/commonVariable';
import Button from 'components/Button';
import {addCurrentUser} from 'reducers/user';
import {CommonActions} from '@react-navigation/native';
import {showToast} from 'utils/toast';
import TaylorMadeBlack from 'assets/imgs/onBoarding/TaylorMadeBlack.svg';
import appStyles from 'styles/global';
import LinearGradient from 'react-native-linear-gradient';
import {checkMainCountry, COUNTRY_CODE} from 'utils/constant';

const CountryQuiz = ({navigation, route}) => {
  const user = useSelector(state => state.user);

  const [options, setOptions] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [loading, setLoading] = useState(false);
  const [loadingSave, setLoadingSave] = useState(false);
  const [isLoadingFull, setLoadingFull] = useState(false);
  const dispatch = useDispatch();
  const refCountries = useRef([]);

  useEffect(() => {
    getDataCountries();
  }, []);

  const getDataCountries = async () => {
    try {
      setLoading(true);
      const countries = await getCountries();
      if (countries) {
        refCountries.current = countries;
        const listName = countries.map(_item => _item.name);
        const autoSelectIndex = countries.findIndex(
          item => item.code === (getDeviceCountry() || COUNTRY_CODE.USA),
        );
        if (autoSelectIndex > -1) {
          setSelectedCountry(listName[autoSelectIndex]);
        } else {
          setSelectedCountry(listName[0]);
        }
        setOptions(listName);
      }
      setLoading(false);
    } catch (error) {}
  };

  const updateCountry = async index => {
    setSelectedCountry(options[index]);
  };

  const onConfirmPress = async () => {
    try {
      setLoadingSave(true);
      // setLoadingFull(true);
      const selectedItem = refCountries?.current?.find(
        _item => _item.name === selectedCountry,
      );
      const selectedUserCountry = selectedItem?.code;
      let updateUserResponse = await updateUser({
        userCountry: selectedUserCountry,
      });
      setCountry(updateUserResponse?.userCountry);
      dispatch(
        addCurrentUser({...user, userCountry: updateUserResponse?.userCountry}),
      );
      getUser();
      if (!checkMainCountry(updateUserResponse?.userCountry)) {
        navigation.navigate('VerifyBirthDay', {
          screen: 'BirthdayVerificationScreen',
          params: {},
        });
      } else {
        sendOTP(user?.email).catch(() => {});
        navigation.navigate('VerifyOtp', {
          screen: 'VerifyOtp',
          params: {email: user?.email},
        });
      }
      setLoadingSave(false);
      // setLoadingFull(false);
    } catch (error) {
      setLoadingSave(false);
      setLoadingFull(false);
      const errorMessage =
        error.response?.data?.errorMessage || error.response?.data?.message
          ? error.response?.data?.errorMessage ||
            error.response?.data?.message ||
            error.message
          : t('An_error_occurred_signing_up');
      showToast({
        type: 'error',
        message: errorMessage,
      });
    }
  };

  const renderCountryOptions = () => {
    return (
      <View style={styles.answerContainer}>
        {options.map((item, index) => {
          return (
            <AnswerButton
              text={t(item).toUpperCase()}
              index={index}
              key={item}
              isSelected={selectedCountry?.includes(options[index])}
              setSelectedIndex={i => updateCountry(i)}
              style={{width: wp(100) - 16 * 2}}
              isRadioButton
            />
          );
        })}
      </View>
    );
  };

  const renderCountryQuiz = () => {
    return (
      <ScrollView style={{width: '100%'}}>
        <Trans
          defaults={'quiz.country.question'}
          parent={Text}
          style={styles.title}
          components={{
            bold: <Text black size={16} style={{fontWeight: 'bold'}} />,
          }}
        />
        {loading ? (
          <View style={{height: '100%', marginTop: 67}}>
            <ActivityIndicator color="black" size={'large'} />
          </View>
        ) : (
          renderCountryOptions()
        )}
        <View style={{flex: 1}} />
      </ScrollView>
    );
  };

  const LoadingFull = () => {
    return (
      <SafeAreaView
        style={[
          appStyles.flex,
          appStyles.hCenter,
          {paddingTop: hp(33), backgroundColor: '#fff'},
        ]}
      >
        <LinearGradient
          start={{x: 1, y: 0.1}}
          end={{x: 1, y: 1}}
          colors={['#fff', 'rgba(0, 0, 0, 1)']}
          style={[
            {
              width: wp('100%'),
              top: hp(33),
              bottom: 0,
              left: 0,
              opacity: 0.25,
              position: 'absolute',
            },
          ]}
        />
        <View style={[{marginBottom: 40}]}>
          <TaylorMadeBlack />
        </View>
        <View>
          <ActivityIndicator
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            }}
            size={'large'}
            color={'black'}
          />
        </View>
      </SafeAreaView>
    );
  };

  const insets = useSafeAreaInsets();
  return isLoadingFull ? (
    <LoadingFull />
  ) : (
    <View
      style={[
        styles.container,
        {
          paddingBottom: insets.bottom,
        },
      ]}
    >
      <Image
        style={[
          styles.logo,
          {
            marginTop: 12 + insets.top,
          },
        ]}
        source={Images.logo_Black}
      />
      {renderCountryQuiz()}
      <Button
        text={'quiz.country.confirm'}
        textColor={loadingSave ? 'white' : '#000'}
        textStyle={{fontWeight: '700', fontSize: 12, letterSpacing: 1.62}}
        backgroundColor={loadingSave ? 'rgb(225, 225, 225)' : '#fff'}
        borderColor={'rgb(225, 225, 225)'}
        onPress={onConfirmPress}
        loading={loadingSave}
        style={[
          styles.nextButton,
          loadingSave && {shadowColor: 'rgb(200, 200, 200)'},
          loadingSave && Platform.OS === 'android' && styles.borderAndroids,
        ]}
        disabled={loadingSave}
        centered
        Din79Font
      />
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: 'rgb(225, 225, 225)',
    flex: 1,
  },
  logo: {
    with: 50,
    height: 50,
    marginTop: 12,
  },
  answerContainer: {
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: 32,
    marginHorizontal: 12,
  },
  title: {
    marginTop: 67,
    marginHorizontal: 40,
    fontSize: 16,
    fontWeight: '400',
    color: 'black',
    textAlign: 'center',
  },
  nextButton: {
    width: wp(100) - 32,
    height: 40,
    marginTop: 24,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 7,
    shadowColor: '#d8d7d7',
    marginBottom: 8,
    elevation: 6,
  },
  borderAndroids: {
    borderWidth: 1,
    borderColor: 'white',
  },
});
export default CountryQuiz;
