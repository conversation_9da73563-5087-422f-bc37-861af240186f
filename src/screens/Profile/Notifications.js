import React, {useState, useEffect} from 'react';
import {connect, useSelector} from 'react-redux';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Linking,
} from 'react-native';
import BackButton from 'components/BackButton';
import {t} from 'i18next';
import appStyles from 'styles/global';
import Text from 'components/Text';
import stylesFont from 'components/Text/styles';
import {useDispatch} from 'react-redux';
import LogoMyTMBlackNoPlus from 'assets/imgs/profile/loglo_tm_black_no_plus.svg';
import moment from 'moment';
import {
  getNotifications,
  getTotalNotificationsUnRead,
  markReadNotification,
} from 'requests/notifications';
import {showErrorToast} from 'utils/toast';
import useAppState from 'hooks/useAppState';
import {setDeepLink, setVerticalId} from 'reducers/app';
import {updateNotificationCount} from 'reducers/home';
import {COACH_NOTIFICATION_CODE} from 'utils/constant';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';

const smallScreenIphone = hp(100) < 700 ? true : false;
const Notifications = ({
  navigation,
  route,
  setVerticalId,
  updateNotificationCount,
}) => {
  const dispatch = useDispatch();
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [totalRecord, setTotalRecord] = useState(0);
  const [notificationList, setNotificationList] = useState([]);
  const totalNotificationUnread = useSelector(
    state => state.home?.totalNotificationUnread,
  );
  useEffect(() => {
    onRefresh();
  }, []);

  useAppState({
    onForeground: () => {
      setTimeout(() => {
        onRefresh();
      }, 500);
    },
  });

  useEffect(() => {
    navigation.setOptions({
      headerTitle:
        totalNotificationUnread > 0
          ? `${t('notification.inbox')} (${totalNotificationUnread})`
          : t('notification.inbox'),
      headerLeft: () => (
        <BackButton
          color="black"
          onPress={() => {
            navigation.goBack();
          }}
        />
      ),
    });
  }, []);

  const onRefresh = async () => {
    try {
      setLoading(true);
      const total = await getTotalNotificationsUnRead();
      if (total?.total > 0) {
        updateNotificationCount(total?.total || 0);
      }
      // Make request to get notification
      const data = await getNotifications(1);
      if (parseInt(data?.total) > 0) {
        setTotalRecord(data?.total);
      }
      if (data?.notifications?.length > 0) {
        setNotificationList(data?.notifications);
      }
      // Set current page number of request
      if (data?.page) {
        setPage(parseInt(data?.page));
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      showErrorToast({
        error,
        title: t('notification.error'),
        description: t('notification.error.description'),
      });
    }
  };

  const getMoreNotification = async pageCount => {
    if (loading) {
      return;
    }
    try {
      setLoading(true);
      if (
        notificationList?.length > 0 &&
        notificationList.length < totalRecord
      ) {
        // Make request to get notification
        const data = await getNotifications(pageCount);
        if (data?.total?.length > 0) {
          setTotalRecord(data?.total);
        }
        if (data?.notifications?.length > 0) {
          const oldList = [...notificationList];
          const newNotifications = oldList.concat(data.notifications);
          setNotificationList(newNotifications);
        }
        // Set current page number of request
        if (data?.page) {
          setPage(parseInt(data?.page));
        }
      }

      setLoading(false);
    } catch (error) {
      setLoading(false);
      showErrorToast({
        error,
        title: t('notification.error'),
        description: t('notification.error.description'),
      });
    }
  };

  const selectedItem = async item => {
    try {
      if (!item.isRead) {
        const status = await markReadNotification(item?.id);
        if (status.success) {
          const updatedArray = notificationList.map(p =>
            p.id === item?.id ? {...p, isRead: true} : p,
          );
          setNotificationList(updatedArray);
          const newTotalNotificationUnread =
            totalNotificationUnread > 0 ? totalNotificationUnread - 1 : 0;
          updateNotificationCount(newTotalNotificationUnread);
        }
      }
      const ctaLink = item?.notification?.ctaLink;
      if (ctaLink) {
        if (ctaLink?.includes('fedex.com') || ctaLink?.includes('ups.com')) {
          navigation?.navigate('WebView', {
            screen: 'WebView',
            params: {
              uri: ctaLink,
              canGoBack: true,
            },
          });
          return;
        }
        if (
          ctaLink?.includes('https://v28z.test-app.link') ||
          ctaLink?.includes('https://v28z.app.link')
        ) {
          Linking.openURL(item?.ctaLink);
          return;
        }
        if (ctaLink?.includes('verticalId=')) {
          const paramsDeelinks = ctaLink?.split('verticalId=');
          if (paramsDeelinks.length > 1) {
            setVerticalId(paramsDeelinks[1]);
          }
          return;
        }
        //deeplink native coach
        const variables = JSON.parse(item?.notification?.variables);
        if (variables && variables?.service === 'SWING_INDEX') {
          const code = variables.code;
          let deeplink = 'coachingPlans';
          switch (code) {
            case COACH_NOTIFICATION_CODE.RETURNED_SWING_SHOT_PASSED_ALL:
            case COACH_NOTIFICATION_CODE.RETURNED_SWING_SHOT_SKIPPED:
            case COACH_NOTIFICATION_CODE.RETURNED_SWING_SHOT_SKIPPED_LAST:
            case COACH_NOTIFICATION_CODE.RETURNED_SWING_SHOT_FAILED:
            case COACH_NOTIFICATION_CODE.RETURNED_SWING_SHOT_PASSED:
            case COACH_NOTIFICATION_CODE.ROADMAP_GENERATED:
              deeplink = 'road-map';
              break;
            case COACH_NOTIFICATION_CODE.SWING_MAINT_SCORED:
            case COACH_NOTIFICATION_CODE.SWING_MAINT_RETURNED_SCORED:
            case COACH_NOTIFICATION_CODE.SINGLE_SWING_SHOT_SCORED:
            case COACH_NOTIFICATION_CODE.MARKED_AS_BEST:
            case COACH_NOTIFICATION_CODE.VIDEO_REJECTED:
            case COACH_NOTIFICATION_CODE.SINGLE_SWING_SHOT_RETURNED_SCORED:
              deeplink = 'quick-fix';
              break;
            case COACH_NOTIFICATION_CODE.LONG_PUTT_SCORED:
            case COACH_NOTIFICATION_CODE.SHORT_PUTT_SCORED:
              deeplink = 'putting';
              break;
            default:
              break;
          }
          dispatch(setDeepLink(deeplink));
          return;
        }
        const paramsDeelinks = ctaLink.split('/');
        if (paramsDeelinks.length > 0) {
          const lastItem = paramsDeelinks[paramsDeelinks.length - 1];
          if (lastItem && lastItem?.length > 0) {
            dispatch(setDeepLink(lastItem));
          }
        }
      }
    } catch (error) {
      showErrorToast({
        error,
        title: t('notification.error'),
        description: t('notification.error.description'),
      });
    }
  };

  const renderItem = ({item, index}) => {
    const isEnd = index === notificationList.length - 1;

    return (
      <TouchableOpacity
        onPress={() => selectedItem(item)}
        style={[
          appStyles.flex,
          appStyles.row,
          appStyles.hCenter,
          index === 0 && styles.borderTopItem,
          !isEnd && styles.borderBottomItem,
          styles.contain,
        ]}
      >
        <View style={appStyles.hr} />
        <View style={[appStyles.hCenter, appStyles.vCenter, styles.imageItem]}>
          <LogoMyTMBlackNoPlus width={25} height={25} />
        </View>
        <Text
          style={[
            styles.textCommon,
            stylesFont.font,
            !item.isRead && styles.textUnRead,
          ]}
        >
          {item?.notification?.message}
        </Text>

        <View style={styles.boxDate}>
          <Text style={styles.textDate}>
            {moment(item.createdAt, 'YYYY-MM-DD').format('ll')}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={appStyles.flex}>
      <FlatList
        data={notificationList}
        keyExtractor={(item, i) => `${i}`}
        renderItem={renderItem}
        style={appStyles.flex}
        onEndReachedThreshold={0.4}
        onEndReached={() => getMoreNotification(page + 1)}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={onRefresh} />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  contain: {
    paddingTop: 16,
    paddingBottom: 15,
    paddingLeft: 15,
    paddingRight: 14,
  },
  textCommon: {
    fontSize: 13,
    marginLeft: 15,
    width: '73%',
  },
  textUnRead: {
    fontWeight: 'bold',
  },
  textDate: {
    fontSize: 8,
  },
  borderTopItem: {
    borderTopWidth: 1,
    borderColor: 'rgba(0, 0, 0, .3)',
  },
  borderBottomItem: {
    borderBottomWidth: 1,
    borderColor: 'rgba(0, 0, 0, .3)',
  },
  imageItem: {
    backgroundColor: 'white',
    width: 35,
    height: 35,
    borderRadius: 4,
  },
  boxDate: {
    width: 150,
    paddingLeft: smallScreenIphone ? 0 : 10,
  },
});
const mapDispatchToProps = {
  setVerticalId,
  updateNotificationCount,
};

export default connect(null, mapDispatchToProps)(Notifications);
