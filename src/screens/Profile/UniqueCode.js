import React, {useState} from 'react';
import {View} from 'react-native';
import {connect, useSelector} from 'react-redux';
import {isEmpty} from 'validator';
import Icon from 'react-native-vector-icons/FontAwesome';
import {moderateScale} from 'react-native-size-matters';

import FormItem from 'components/FormItem';
import Button from 'components/Button';
import Text from 'components/Text';
import {t} from 'i18next';

import {verifyAccessCode, getPermissions, getUser} from 'requests/accounts';
import {updatePermissions} from 'reducers/app';
import {registerSwingPlayer, getSwingCredentials} from 'requests/swing-index';

import appStyles from 'styles/global';
import {showToast} from 'utils/toast';
import {GREY} from 'config';
import {addCurrentUser} from 'reducers/user';

const UniqueCode = ({updatePermissions, addCurrentUser}) => {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const profileImageUrl = useSelector(
    state => state?.plans?.planCoach?.profileImageUrl,
  );
  const validated = !isEmpty(code || '');

  const verifyCode = async () => {
    setLoading(true);
    try {
      // Make request to verify access code
      const accessCodeResponse = await verifyAccessCode(code);
      // Retrieve updated user
      const userPermissions = await getPermissions();
      const user = await getUser();
      addCurrentUser(user);
      // Update permissions in redux
      updatePermissions({
        myTMSubscriptionLevel: userPermissions?.myTMSubscriptionLevel,
        myTMPermission: userPermissions?.myTMPermission,
        subscriptionService: userPermissions?.subscriptionService,
        isTrialSubscription: userPermissions?.isTrialSubscription,
      });
      if (accessCodeResponse?.subscriptionPlan > 0 && !profileImageUrl) {
        await registerSwingPlayer();
        await getSwingCredentials();
      }

      setLoading(false);
      setIsSuccess(true);
    } catch (error) {
      setLoading(false);
      const errorMessage =
        error.response?.data?.errorMessage || error.response?.data?.message
          ? error.response?.data?.errorMessage || error.response?.data?.message
          : t('An_error_occurred_with_your_code');
      showToast({
        type: 'error',
        message: errorMessage,
      });
    }
  };

  return (
    <View style={[appStyles.pTMd]}>
      <FormItem
        label="uniquecode.unique_code"
        onChangeText={setCode}
        placeholder={t('uniquecode.placeholder_unique_code')}
        defaultValue={code}
        disabled={loading || isSuccess}
        autoCapitalize="characters"
      />

      {isSuccess ? (
        <View style={[appStyles.row, appStyles.alignCenter, appStyles.mTMd]}>
          <Icon name="check" size={moderateScale(16)} />
          <Text style={[appStyles.mLXs, {bottom: -4}]} DINbold>
            uniquecode.code_applied
          </Text>
        </View>
      ) : (
        <View style={[appStyles.pHSm, appStyles.mTMd]}>
          <Button
            text="uniquecode.apply_code"
            textColor="white"
            backgroundColor={validated ? 'black' : GREY}
            borderColor={validated ? 'black' : GREY}
            onPress={verifyCode}
            loading={loading}
            disabled={loading || !validated}
            loadingMode="dark"
            centered
            DINbold
          />
        </View>
      )}
    </View>
  );
};

const mapDispatchToProps = {updatePermissions, addCurrentUser};

export default connect(null, mapDispatchToProps)(UniqueCode);
