import React, {useRef, useState} from 'react';
import {
  View,
  StatusBar,
  Keyboard,
  TouchableOpacity,
  StyleSheet,
  Platform,
  ScrollView,
} from 'react-native';
import {isEmpty} from 'validator';
import * as Animatable from 'react-native-animatable';

import Text from 'components/Text';
import TextInput from 'components/TextInput';
import Button from 'components/Button';

import {updateUser} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import Selector from 'components/Selector';
import {useDispatch, useSelector} from 'react-redux';
import {isCanadaMarket} from 'utils/commonVariable';
import {getAddressString} from 'utils/user';
import {addCurrentUser} from 'reducers/user';
import {heightPercentageToDP} from 'react-native-responsive-screen';
import {useHeaderHeight} from '@react-navigation/stack';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {GA_logCtaEvent} from 'utils/googleAnalytics';
import {SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';

//  https://3sshf.app.link/mytaylormadeplus/address

const CAN_STATES = [
  'Alberta',
  'British Columbia',
  'Manitoba',
  'New Brunswick',
  'Newfoundland and Labrador',
  'Northwest Territories',
  'Nova Scotia',
  'Nunavut',
  'Ontario',
  'Prince Edward Island',
  'Quebec',
  'Saskatchewan',
  'Yukon Territory',
];

const US_STATES = [
  'Alabama',
  'Alaska',
  'Arizona',
  'Arkansas',
  'California',
  'Colorado',
  'Connecticut',
  'Delaware',
  'Florida',
  'Georgia',
  'Hawaii',
  'Idaho',
  'Illinois',
  'Indiana',
  'Iowa',
  'Kansas',
  'Kentucky',
  'Louisiana',
  'Maine',
  'Maryland',
  'Massachusetts',
  'Michigan',
  'Minnesota',
  'Mississippi',
  'Missouri',
  'Montana',
  'Nebraska',
  'Nevada',
  'New Hampshire',
  'New Jersey',
  'New Mexico',
  'New York',
  'North Carolina',
  'North Dakota',
  'Ohio',
  'Oklahoma',
  'Oregon',
  'Pennsylvania',
  'Rhode Island',
  'South Carolina',
  'South Dakota',
  'Tennessee',
  'Texas',
  'Utah',
  'Vermont',
  'Virginia',
  'Washington',
  'West Virginia',
  'Wisconsin',
  'Wyoming',
];

const ShippingAddress = ({navigation}) => {
  const headerHeight = useHeaderHeight();

  const user = useSelector(state => state?.user);
  const addressObject =
    user?.address && user?.address !== '' ? JSON.parse(user?.address) : {};
  const [email, setEmail] = useState(user?.email);
  const [address, setAddress] = useState(addressObject?.address);
  const [address2, setAddress2] = useState(addressObject?.address2);
  const [zipCode, setZipCode] = useState(addressObject?.zipCode);
  const [city, setCity] = useState(addressObject?.city);
  const [shippingState, setShippingState] = useState(addressObject?.state);
  const [phoneNumber, setPhoneNumber] = useState(
    addressObject?.phoneNumber || user?.phoneNumber,
  );

  const [loading, setLoading] = useState(false);

  const sheetRef = useRef(null);
  const listInputRef = useRef(null);
  const dispatch = useDispatch();

  const validated =
    !isEmpty(email || '') &&
    !isEmpty(phoneNumber || '') &&
    phoneNumber.length === 10 &&
    !isEmpty(address || '') &&
    !isEmpty(zipCode || '') &&
    !isEmpty(city || '') &&
    !isEmpty(shippingState || '');

  const submitAddress = async () => {
    Keyboard.dismiss();
    try {
      GA_logCtaEvent(
        'update_address',
        t('settings.submit_address'),
        'input_address',
        SCREEN_CLASS.USER_PROFILE,
        SCREEN_TYPES.SETTINGS,
      );
      setLoading(true);
      const updatedUser = await updateUser({
        address: getAddressString({
          address,
          zipCode,
          city,
          shippingState,
          address2,
          phoneNumber,
        }),
      });
      // Update user in redux
      dispatch(addCurrentUser(updatedUser));
      setLoading(false);
      showToast({
        type: 'success',
        message: t('submit_address_success'),
      });
      setTimeout(() => {
        navigation.goBack();
      }, 2000);
    } catch (error) {
      showToast({
        type: 'error',
        message: t('submit_address_fail'),
        subText: t('submit_address_fail_retry'),
      });
      setLoading(false);
    }
  };

  const states = isCanadaMarket() ? CAN_STATES : US_STATES;

  return (
    <View style={[appStyles.flex]}>
      <StatusBar barStyle="light-content" />

      <KeyboardAwareScrollView
        style={{
          marginTop: 34,
        }}
        extraScrollHeight={heightPercentageToDP('10%')}
      >
        <ScrollView>
          <View
            style={[
              appStyles.pBSm,
              appStyles.pHMd,
              {
                paddingTop: 30,
                height: heightPercentageToDP('100%') - headerHeight - 34,
              },
            ]}
          >
            <Animatable.View animation="fadeInUp">
              <Text
                style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}
              >
                settings.shipping_address_title
              </Text>
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={100}>
              <TextInput
                style={[appStyles.mBSm]}
                defaultValue={email}
                placeholder={t('settings.shipping_email')}
                onChangeText={setEmail}
                inputProps={{editable: false}}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <TextInput
                style={[appStyles.mBSm]}
                defaultValue={phoneNumber}
                placeholder={'Phone #'}
                onChangeText={setPhoneNumber}
                keyboardType={'number-pad'}
                autoCorrect={false}
                onFocus={() => {
                  sheetRef.current?.snapTo(1);
                }}
                onSubmitEditing={() => {
                  listInputRef.current?.address?.focus();
                }}
                inputProps={{blurOnSubmit: false}}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={200}>
              <TextInput
                style={[appStyles.mBSm]}
                defaultValue={address}
                placeholder={t('settings.shipping_address')}
                onChangeText={setAddress}
                returnKeyType="next"
                autoCorrect={false}
                onFocus={() => {
                  sheetRef.current?.snapTo(1);
                }}
                refInput={ref => {
                  listInputRef.current = {
                    ...listInputRef.current,
                    address: ref,
                  };
                }}
                inputProps={{blurOnSubmit: false}}
                onSubmitEditing={() => {
                  listInputRef.current?.address2?.focus();
                }}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={300}>
              <TextInput
                style={[appStyles.mBSm]}
                defaultValue={address2}
                placeholder={t('settings.shipping_address2')}
                onChangeText={setAddress2}
                returnKeyType="next"
                autoCorrect={false}
                onFocus={() => {
                  sheetRef.current?.snapTo(1);
                }}
                refInput={ref => {
                  listInputRef.current = {
                    ...listInputRef.current,
                    address2: ref,
                  };
                }}
                inputProps={{blurOnSubmit: false}}
                onSubmitEditing={() => {
                  listInputRef.current?.zipcode?.focus();
                }}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={300}>
              <TextInput
                style={[appStyles.mBSm]}
                defaultValue={zipCode}
                placeholder={t('settings.shipping_zipCode')}
                onChangeText={setZipCode}
                returnKeyType="next"
                autoCorrect={false}
                onFocus={e => {
                  sheetRef.current?.snapTo(1);
                }}
                refInput={ref => {
                  listInputRef.current = {
                    ...listInputRef.current,
                    zipcode: ref,
                  };
                }}
                inputProps={{blurOnSubmit: false}}
                onSubmitEditing={() => {
                  listInputRef.current?.shippingCity?.focus();
                }}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={300}>
              <TextInput
                style={[appStyles.mBSm]}
                defaultValue={city}
                placeholder={t('settings.shipping_city')}
                onChangeText={setCity}
                autoCorrect={false}
                onFocus={e => {
                  sheetRef.current?.snapTo(1);
                }}
                returnKeyType="next"
                refInput={ref => {
                  listInputRef.current = {
                    ...listInputRef.current,
                    shippingCity: ref,
                  };
                }}
                inputProps={{blurOnSubmit: false}}
                onSubmitEditing={() => {
                  Keyboard.dismiss();
                  sheetRef.current?.snapTo(0);
                }}
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={300}>
              <Button
                textColor={shippingState ? 'white' : GREY}
                borderColor={GREY}
                onPress={() => {
                  sheetRef.current?.snapTo(0);
                  Keyboard.dismiss();
                }}
                text={shippingState || t('settings.shipping_state')}
                rightIcon="chevron-down"
                disabled={loading}
              />
            </Animatable.View>
            <View style={[appStyles.flex, {minHeight: 50}]} />
            <Animatable.View animation="fadeInUp" delay={400}>
              <Button
                text="settings.submit_address"
                backgroundColor={validated ? 'white' : GREY}
                disabled={!validated || loading}
                loading={loading}
                onPress={submitAddress}
                style={{marginBottom: 10}}
                centered
                DINbold
              />
            </Animatable.View>
            <Animatable.View animation="fadeInUp" delay={400}>
              <View style={[appStyles.pSm]}>
                <TouchableOpacity
                  onPress={() =>
                    navigation.navigate('WebView', {
                      screen: 'WebView',
                      params: {
                        title: t('settings.term_conditions'),
                        uri: 'https://www.taylormadegolf.com/mytaylormadeplus-terms-and-conditions.html?lang=en_US',
                        origin: 'TERMCONDITION',
                        canGoBack: true,
                      },
                    })
                  }
                  style={[styles.txtTerm]}
                >
                  <Text
                    style={[
                      appStyles.xs,
                      {
                        fontWeight: Platform.OS === 'android' ? 'bold' : '600',
                        color: 'rgba(140, 140, 144, 1)',
                      },
                    ]}
                  >
                    tour_trash.terms_conditions
                  </Text>
                </TouchableOpacity>
              </View>
            </Animatable.View>
          </View>
        </ScrollView>
      </KeyboardAwareScrollView>
      <Selector
        ref={sheetRef}
        type="text"
        value={shippingState}
        options={isCanadaMarket() ? CAN_STATES : US_STATES}
        onChange={setShippingState}
        onCloseEnd={() => {
          if (!shippingState) {
            setShippingState(states[0]);
          }
        }}
        origin={'StrokesUpdate'}
      />
    </View>
  );
};

export default ShippingAddress;

const styles = StyleSheet.create({
  txtTerm: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(140, 140, 144, 1)',
    paddingBottom: 1,
    alignSelf: 'center',
  },
});
