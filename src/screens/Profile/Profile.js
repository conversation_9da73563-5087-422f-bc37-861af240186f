import React, {useState, useEffect} from 'react';
import {
  View,
  TouchableOpacity,
  ScrollView,
  Platform,
  StyleSheet,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {connect, useDispatch, useSelector} from 'react-redux';
import moment from 'moment';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import Config from 'react-native-config';
import AsyncStorage from '@react-native-async-storage/async-storage';
import analytics from '@react-native-firebase/analytics';

import FocusAwareStatusBar from 'components/FocusAwareStatusBar';
import Header from 'components/Header';
import Text from 'components/Text';
import Button from 'components/Button';
import LoadingOverlay from 'components/LoadingOverlay';

import {getPermissions} from 'requests/accounts';
import {clearFocusTab, updatePermissions} from 'reducers/app';
import {createSFCCSession} from 'requests/ecom';

import appStyles from 'styles/global';
import {
  canShowGHIN,
  getAuth0AccessToken,
  isOtherPayment,
  openOtherPayment,
} from 'utils/user';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import {getConfig, ENVIRONMENTS, decryptConfig} from 'config/env';

import {clearPermissions} from 'reducers/app';
import {clearUser} from 'reducers/user';
import {
  clearHomeTiles,
  clearNotificationCount,
  updateNotificationCount,
} from 'reducers/home';
import {clearBasket} from 'reducers/basket';
import {
  clearClubRecommender,
  clearClubLaunchMonitor,
} from 'reducers/clubRecommender';
import {
  clearDrillsForYou,
  clearDrillsSavedContent,
  clearDrillsCollectionContent,
} from 'reducers/drills';
import {clearBagList} from 'reducers/myBag';
import {
  clearSwingProfile,
  clearSwingRoadMap,
  clearCurrentStep,
  clearPlanZeroState,
  setLoadingCoachingPlan,
  clearSIUserProfile,
} from 'reducers/plans';
import {clearQuiz} from 'reducers/quiz';
import {
  clearScores,
  clearRecentCourses,
  clearNearbyCourses,
} from 'reducers/play';
import {clearTTBOrders} from 'reducers/ttb';
import {getAuth0Client} from 'utils/auth0';
import {CommonActions} from '@react-navigation/native';
import DeviceInfo from 'react-native-device-info';
import {
  ALREADY_FORCE_LOGOUT,
  SCREEN_CLASS,
  SCREEN_TYPES,
  GA_EVENT_NAME,
  PAGE_NAME,
  PAGE_CATEGORY,
  checkMainCountry,
  COUNTRY_CODE,
} from 'utils/constant';
import {getTotalNotificationsUnRead} from 'requests/notifications';
import {
  getCountry,
  getModeFakeGps,
  isCanadaMarket,
  resetSwingShotData,
  setCountry,
  setLanguage,
  setSwingShotData,
} from 'utils/commonVariable';
import {setAvgScore} from 'utils/commonVariable';
import {
  clearAsyncStorage,
  getResultPermission,
  saveModeFakeGps,
  saveResultPermission,
} from 'utils/asyncStorage';
import {clearGHINHandicapIndex} from 'reducers/ghin';
import {
  GA_logCtaEvent,
  GA_logEvent,
  GA_logScreenViewV2,
} from 'utils/googleAnalytics';
import {clearWHSHandicapIndex} from 'reducers/whs';
import {getEcomSite} from 'utils/countries';
import {clearLoyalty} from 'reducers/loyalty';
import LogoMyTMBlackNoPlus from 'assets/imgs/profile/loglo_tm_black_no_plus.svg';
import CloseGray from 'assets/imgs/profile/close_gray.svg';
import {clearTrackingOrders} from 'reducers/trackingOrders';
import {resetTourStory} from 'reducers/dataCache';
import {clearDataTourTrash} from 'reducers/rewards';

const TIER_LEVEL = [
  {
    name: 'PAR',
    level: 'Par Level',
  },
  {
    name: 'BIRDIE',
    level: 'Birdie Level',
  },
  {
    name: 'EAGLE',
    level: 'Eagle Level',
  },
];

const smallScreenIphone = hp(100) < 700 ? true : false;
const Profile = ({
  navigation,
  updatePermissions,
  clearUser,
  clearHomeTiles,
  clearBasket,
  clearClubRecommender,
  clearClubLaunchMonitor,
  clearDrillsForYou,
  clearDrillsSavedContent,
  clearBagList,
  clearSwingProfile,
  clearSwingRoadMap,
  clearCurrentStep,
  clearQuiz,
  clearPlanZeroState,
  clearScores,
  clearRecentCourses,
  clearNearbyCourses,
  clearPermissions,
  clearDrillsCollectionContent,
  clearTTBOrders,
  setLoadingCoachingPlan,
  clearNotificationCount,
  updateNotificationCount,
  clearSIUserProfile,
  clearGHINHandicapIndex,
  clearWHSHandicapIndex,
  clearLoyalty,
  resetTourStory,
  clearDataTourTrash,
}) => {
  const user = useSelector(state => state?.user);
  const permissions = useSelector(state => state?.app?.permissions);
  const [loading, setLoading] = useState(false);
  const {tier, points} = useSelector(state => state?.loyalty);
  const currentTier = tier?.currentTier;
  const [fakeGps, setFakeGPS] = useState(getModeFakeGps());

  const versionApp = DeviceInfo.getVersion();
  const buildNumber = DeviceInfo.getBuildNumber();

  const [env, setEnv] = useState(ENVIRONMENTS.DEV);
  const totalNotificationUnread = useSelector(
    state => state.home?.totalNotificationUnread,
  );
  const dispatch = useDispatch();

  const refCount = React.useRef(0);

  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);

  const isHideOrders = !checkMainCountry(user?.userCountry);

  const findTierLevel = currentTier => {
    if (currentTier) {
      const item = TIER_LEVEL.find(
        val => val.name === currentTier.toUpperCase(),
      );
      if (item) {
        return item.level;
      }
    }

    return 'Par Level';
  };
  const tierLevel = findTierLevel(currentTier);

  useEffect(() => {
    getCurrentPermissions();
    getEnviroment();
    getTotalUnRead();
  }, []);

  const getTotalUnRead = async () => {
    const notificationUnread = await getTotalNotificationsUnRead();
    // Update notification unread in redux
    updateNotificationCount(notificationUnread?.total || 0);
  };

  const getEnviroment = async () => {
    const enviroment = await AsyncStorage.getItem('env');
    setEnv(enviroment);
  };

  const getCurrentPermissions = async () => {
    try {
      // Make request to get user data
      const userPermissions = await getPermissions();
      updatePermissions({
        myTMSubscriptionLevel: userPermissions?.myTMSubscriptionLevel,
        myTMPermission: userPermissions?.myTMPermission,
        subscriptionService: userPermissions?.subscriptionService,
        isTrialSubscription: userPermissions?.isTrialSubscription,
      });
    } catch (error) {
      throw error;
    }
  };

  const getMembershipLevel = () => {
    switch (permissions?.myTMSubscriptionLevel) {
      case 0:
        return t('profile.free');
      case 1:
        return `${t('profile.champion')} - ${
          permissions?.myTMPermission?.subscriptionLength > 0 &&
          permissions?.myTMPermission?.subscriptionLength < 12
            ? t('profile.monthly')
            : t('profile.annual')
        }`;
      case 2:
        return `${t('profile.champion')} - ${
          permissions?.myTMPermission?.subscriptionLength > 0 &&
          permissions?.myTMPermission?.subscriptionLength < 12
            ? t('profile.monthly')
            : t('profile.annual')
        }`;
      default:
        break;
    }
  };

  const navigateToECOM = async type => {
    try {
      setLoading(true);
      const accessToken = await getAuth0AccessToken(dispatch);
      const ECOM_HOST_URL = await getConfig('ECOM_HOST_URL');
      const ecomSite = getEcomSite();
      // Make request to create session and get dwsid session identifier cookie
      // Navigate to ecom page
      navigation.navigate('WebView', {
        screen: 'WebView',
        params: {
          title:
            type === 'orders'
              ? t('profile.supporting_copy.settings.my_orders')
              : t('profile.supporting_copy.settings.my_loyalty_points'),
          uri: `https://${ECOM_HOST_URL}/on/demandware.store/Sites-${ecomSite}-Site/en_US/MyTM-OpenSession?provider=auth0&page=${type}&token=${accessToken}&iframe=true`,
          origin: 'PROFILE_ORDERS',
          canGoBack: true,
        },
      });
      setLoading(false);
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: t('profile.putting_my_shoes_on'),
        subText: t('profile.be_on_the_tee_in_a_second'),
      });
    }
  };

  const onPressMode = () => {
    refCount.current += 1;
    if (env !== ENVIRONMENTS.PROD && refCount.current === 5) {
      const mode = getModeFakeGps();
      saveModeFakeGps(!mode);
      setFakeGPS(!mode);
    }
  };

  const logOut = async () => {
    const auth0 = await getAuth0Client();
    const resultPermission = await getResultPermission();
    GA_logEvent(GA_EVENT_NAME.LOGOUT, {
      page_type: SCREEN_TYPES.ACCOUNT,
      page_category: PAGE_CATEGORY.ACCOUNT,
      page_name: PAGE_NAME.ACCOUNT_MAIN_INFO,
      screen_type: SCREEN_TYPES.ACCOUNT,
    });
    // Clear redux, async storage, and auth0
    clearUser();
    clearHomeTiles();
    clearBasket();
    clearClubRecommender();
    clearClubLaunchMonitor();
    clearDrillsForYou();
    clearDrillsSavedContent();
    clearBagList();
    clearSwingProfile();
    clearSwingRoadMap();
    clearCurrentStep();
    clearQuiz();
    clearPlanZeroState();
    clearScores();
    clearRecentCourses();
    clearNearbyCourses();
    clearPermissions();
    clearDrillsCollectionContent();
    clearTTBOrders();
    clearNotificationCount();
    setLoadingCoachingPlan(false);
    setAvgScore(0);
    resetSwingShotData();
    clearSIUserProfile();
    clearGHINHandicapIndex();
    clearWHSHandicapIndex();
    clearLoyalty();
    clearTrackingOrders();
    clearFocusTab();
    resetTourStory();
    clearDataTourTrash();
    setSwingShotData(null);
    auth0.webAuth.clearSession();
    await clearAsyncStorage();
    await AsyncStorage.setItem(ALREADY_FORCE_LOGOUT, 'true');
    await saveResultPermission(resultPermission);
    setCountry(COUNTRY_CODE.USA);
    setLanguage('en');
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{name: 'Onboarding'}],
      }),
    );
  };

  const navigateHandicapUSGA = () => {
    try {
      navigation.navigate('HandicapUSGA', {
        clickLocation: 'account-settings',
      });
      analytics().logEvent('profile_cta_open', {
        name: 'Handicap Index',
      });
      analytics().logEvent('cta_open', {
        name: 'Handicap Index',
      });
      GA_logCtaEvent(
        'handicap_index_button_click',
        t('ghin.handicap_index'),
        'user profile',
        SCREEN_CLASS.USER_PROFILE,
        SCREEN_TYPES.SETTINGS,
      );
      GA_logNavClick('handicap index');
    } catch (error) {}
  };

  const GA_logNavClick = item_selected => {
    GA_logEvent(GA_EVENT_NAME.NAV_CLICK, {
      screen_type: SCREEN_TYPES.ACCOUNT,
      page_name: PAGE_NAME.ACCOUNT_MAIN_INFO,
      page_type: SCREEN_TYPES.ACCOUNT,
      page_category: PAGE_CATEGORY.ACCOUNT,
      nav_type: 'account',
      nav_item_selected: item_selected,
      nav_level: 'header',
    });
  };

  const isHideFittings =
    !checkMainCountry(user?.userCountry) ||
    (!showHideFeatures?.data?.CLUBREC &&
      !showHideFeatures?.data?.FITTINGEVENTS &&
      !showHideFeatures?.data?.VIRTUALFITTINGS);

  // @ts-ignore
  return (
    <SafeAreaView
      style={{flex: 1, backgroundColor: '#fff'}}
      edges={['right', 'top', 'left']}
    >
      {loading ? <LoadingOverlay transparent={loading} /> : null}
      <View
        style={[
          {
            backgroundColor: '#fff',
            marginTop:
              Platform.OS === 'android' || smallScreenIphone ? '4%' : 0,
          },
        ]}
      >
        <View style={[{paddingHorizontal: 16}]}>
          <View style={[appStyles.row, appStyles.spaceBetween]}>
            <LogoMyTMBlackNoPlus />
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <CloseGray />
            </TouchableOpacity>
          </View>
          <View
            style={{
              paddingTop: 12,
              paddingBottom: Platform.OS === 'ios' ? 5 : 0,
            }}
          >
            <Text black size={16}>
              {user.firstName} {user.lastName}
            </Text>
          </View>
        </View>
      </View>
      <View style={[appStyles.flex]}>
        {checkMainCountry(user?.userCountry) && (
          <>
            <View style={[styles.viewTierLevel]}>
              <View style={[appStyles.row, appStyles.spaceBetween]}>
                <Text size={16} style={[appStyles.black, {marginLeft: 16}]}>
                  {tierLevel}
                </Text>
                <View style={appStyles.row}>
                  <Text
                    size={16}
                    style={[
                      appStyles.black,
                      {marginRight: 8, color: '#00000080'},
                    ]}
                  >
                    {`${points?.availablePoints || 0}pts`}
                  </Text>
                  <Text size={16} style={[appStyles.black, {marginRight: 16}]}>
                    {`$${points?.creditsToCurrencyValue || 0.0}`}
                  </Text>
                </View>
              </View>
            </View>
            <View style={styles.borderBottom} />
          </>
        )}
        <TouchableOpacity
          style={styles.viewItem}
          onPress={async () => {
            navigation.navigate('Settings');
            await analytics().logEvent('profile_cta_open', {
              name: 'Settings',
            });
            await analytics().logEvent('cta_open', {
              name: 'Settings',
            });
            GA_logNavClick('settings');
          }}
        >
          <Text size={16} style={[appStyles.black, {marginLeft: 16}]}>
            profile.settings
          </Text>
        </TouchableOpacity>
        <View style={styles.borderBottom} />
        {!isHideOrders && (
          <>
            <TouchableOpacity
              style={styles.viewItem}
              onPress={async () => {
                navigateToECOM('orders');
                await analytics().logEvent('profile_cta_open', {
                  name: 'My Orders',
                });
                await analytics().logEvent('cta_open', {
                  name: 'My Orders',
                });
                GA_logNavClick('orders');
              }}
            >
              <Text size={16} style={[appStyles.black, {marginLeft: 16}]}>
                profile.supporting_copy.settings.my_orders
              </Text>
            </TouchableOpacity>

            <View style={styles.borderBottom} />
          </>
        )}
        <TouchableOpacity
          style={styles.viewItem}
          onPress={async () => {
            navigation.navigate('Notifications', {
              totalUnRead: totalNotificationUnread,
            });
            GA_logNavClick('inbox');
          }}
        >
          <Text size={16} style={[appStyles.black, {marginLeft: 16}]}>
            {totalNotificationUnread > 0
              ? `${t('notification.inbox')} (${totalNotificationUnread})`
              : t('notification.inbox')}
          </Text>
        </TouchableOpacity>
        <View style={styles.borderBottom} />
        {(canShowGHIN(showHideFeatures, user) ||
          getCountry() === COUNTRY_CODE.CAN) && (
          <>
            <TouchableOpacity
              style={styles.viewItem}
              onPress={navigateHandicapUSGA}
            >
              <Text size={16} style={[appStyles.black, {marginLeft: 16}]}>
                {isCanadaMarket() ? t('ghin.whs') : t('ghin.usga')}
              </Text>
            </TouchableOpacity>
            <View style={styles.borderBottom} />
          </>
        )}
        <TouchableOpacity
          style={styles.viewItem}
          onPress={async () => {
            navigation.navigate('MyGameProfile');
            await analytics().logEvent('profile_cta_open', {
              name: 'My Game Profile',
            });
            await analytics().logEvent('cta_open', {
              name: 'My Game Profile',
            });
            GA_logNavClick('my game profile');
          }}
        >
          <Text size={16} style={[appStyles.black, {marginLeft: 16}]}>
            profile.supporting_copy.settings.game_profile
          </Text>
        </TouchableOpacity>
        <View style={styles.borderBottom} />
        <TouchableOpacity
          style={styles.viewItem}
          onPress={async () => {
            navigation.navigate('MyBag', {screen: 'MyBagScreens'});
            await analytics().logEvent('profile_cta_open', {
              name: 'My Bag',
            });
            await analytics().logEvent('cta_open', {
              name: 'My Bag',
            });
            GA_logNavClick('my bag');
          }}
        >
          <Text size={16} style={[appStyles.black, {marginLeft: 16}]}>
            profile.supporting_copy.settings.wit_bag
          </Text>
        </TouchableOpacity>
        <View style={styles.borderBottom} />
        {!isHideFittings && (
          <TouchableOpacity
            style={styles.viewItem}
            onPress={async () => {
              navigation.navigate('Fittings', {screen: 'Fittings'});
              await analytics().logEvent('profile_cta_open', {
                name: 'My Fittings',
              });
              await analytics().logEvent('cta_open', {
                name: 'My Fittings',
              });
              GA_logNavClick('fittings');
            }}
          >
            <Text size={16} style={[appStyles.black, {marginLeft: 16}]}>
              profile.fittings
            </Text>
          </TouchableOpacity>
        )}
        {!isHideFittings && <View style={styles.borderBottom} />}
        <TouchableOpacity
          style={styles.viewItem}
          onPress={async () => {
            navigation.navigate('WebView', {
              screen: 'WebView',
              params: {
                title: t('profile.supporting_copy.settings.support'),
                uri: 'https://help.taylormadegolf.com',
                origin: 'SUPPORT',
                canGoBack: true,
              },
            });
            await analytics().logEvent('profile_cta_open', {
              name: 'Support',
            });
            await analytics().logEvent('cta_open', {
              name: 'Support',
            });
            GA_logNavClick('support');
          }}
        >
          <Text size={16} style={[appStyles.black, {marginLeft: 16}]}>
            profile.supporting_copy.settings.support
          </Text>
        </TouchableOpacity>
        <View style={styles.borderBottom} />

        <TouchableOpacity
          style={styles.viewItem}
          onPress={async () => {
            logOut();
          }}
        >
          <Text style={[appStyles.black, appStyles.textCenter]}>
            {t('settings.headline.log_out')}
          </Text>
        </TouchableOpacity>
        <View style={styles.borderBottom} />

        <View
          style={[
            {
              position: 'absolute',
              bottom: 0,
              marginBottom: 20,
              width: '100%',
            },
          ]}
        >
          <Text
            size={13}
            style={[
              appStyles.textCenter,
              {
                color: '#00000066',
              },
            ]}
          >
            {`${t('profile.software')} v ${versionApp} (${buildNumber}) ${
              env === ENVIRONMENTS.STAGING
                ? `- ${t('profile.staging')}`
                : env === ENVIRONMENTS.DEV
                ? `- ${t('profile.dev')}`
                : ''
            }`}
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  borderBottom: {
    borderBottomColor: '#00000080',
    borderBottomWidth: 1,
  },
  viewTierLevel: {
    paddingTop: Platform.OS === 'android' || smallScreenIphone ? 14 : 20,
    paddingBottom: Platform.OS === 'android' || smallScreenIphone ? 14 : 20,
  },
  viewItem: {
    paddingTop: Platform.OS === 'android' || smallScreenIphone ? 14 : 18,
    paddingBottom: Platform.OS === 'android' || smallScreenIphone ? 14 : 18,
  },
});

const mapDispatchToProps = {
  updatePermissions,
  clearUser,
  clearHomeTiles,
  clearBasket,
  clearClubRecommender,
  clearClubLaunchMonitor,
  clearDrillsForYou,
  clearDrillsSavedContent,
  clearBagList,
  clearSwingProfile,
  clearSwingRoadMap,
  clearCurrentStep,
  clearQuiz,
  clearPlanZeroState,
  clearScores,
  clearRecentCourses,
  clearNearbyCourses,
  clearPermissions,
  clearDrillsCollectionContent,
  setLoadingCoachingPlan,
  clearTTBOrders,
  clearNotificationCount,
  updateNotificationCount,
  clearSIUserProfile,
  clearGHINHandicapIndex,
  clearWHSHandicapIndex,
  clearLoyalty,
  resetTourStory,
  clearDataTourTrash,
};

export default connect(null, mapDispatchToProps)(Profile);
