import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  ScrollView,
  Alert,
  Platform,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import appStyles from 'styles/global';
import Button from 'components/Button';
import FormItem from 'components/FormItem';
import {connect, useSelector} from 'react-redux';
import {moderateScale} from 'react-native-size-matters';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import BackButton from 'components/BackButton';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import {addCurrentUser} from 'reducers/user';
import {updateUser} from 'requests/accounts';
import {convertDriveLength} from 'utils/convert';
import {
  GA_EVENT_NAME,
  QUIZ_TYPE,
  VALUE_STROKES_GAINED_PROFILE,
  SCREEN_TYPES,
  PAGE_NAME,
  PAGE_CATEGORY,
} from 'utils/constant';
import IconBack from 'assets/imgs/profile/icon_back.svg';
import Text from 'components/Text';
import FormItemProfile from 'components/FormItemProfile';
import {GA_logEvent, GA_setUserProperty} from 'utils/googleAnalytics';

const smallScreenIphone = hp(100) < 700 ? true : false;
const MyGameProfile = ({navigation, addCurrentUser}) => {
  const golferProfile = useSelector(state => state.user?.golferProfile);
  const [timePlayingGolf, setTimePlayingGolf] = useState(
    golferProfile?.timePlayingGolf?.value?.replace('Years', ''),
  );
  const [roundsPerMonth, setRoundsPerMonth] = useState(
    golferProfile?.roundsPerMonth,
  );
  const [handicap, setHandicap] = useState(
    golferProfile?.newHandicap?.tmCalculatedHandicap ||
      golferProfile?.newHandicap?.userInputHandicap,
  );
  const [strokesGainedBaseline, setStrokesGainedBaseline] = useState(
    golferProfile?.strokesGainedBaseline,
  );
  const [targetScore, setTargetScore] = useState(golferProfile?.targetScore);
  const [maximumDriverDistance, setMaximumDriverDistance] = useState(
    convertDriveLength(golferProfile?.maximumDriverDistance),
  );
  const [strongestArea, setStrongestArea] = useState(
    golferProfile?.strongestArea,
  );
  const [weakestArea, setWeakestArea] = useState(golferProfile?.weakestArea);
  const [shotShape, setShotShape] = useState(golferProfile?.shotShape);
  const [ballStrike, setBallStrike] = useState(golferProfile?.ballStrike);
  const [misHit, setMisHit] = useState(golferProfile?.misHit);
  const [avoidShot, setAvoidShot] = useState(golferProfile?.avoidShot);
  const [mostScaredShot, setMostScaredShot] = useState(
    golferProfile?.mostScaredShot,
  );
  const [homeCourse, setHomeCourse] = useState(golferProfile?.homeCourse);
  const [favoriteTeamMembers, setFavoriteTeamMembers] = useState(
    golferProfile?.favoriteTeamMembers,
  );
  const [iGolfCourseId, setIGolfCourseId] = useState(
    golferProfile?.iGolfCourseId,
  );
  const [loading, setLoading] = useState(false);

  const getDriveLength = () => {
    if (maximumDriverDistance === 90) {
      return t('quiz.drive_length.100_yards');
    } else if (maximumDriverDistance === 360) {
      return t('quiz.drive_length.350_yards');
    } else {
      return `${maximumDriverDistance} ${t('quiz.drive_length.yards')}`;
    }
  };

  const getStrokesGainedName = value => {
    let strokesName = VALUE_STROKES_GAINED_PROFILE.find(
      item => item.value === value,
    )?.name;
    return strokesName;
  };

  const changed =
    timePlayingGolf !==
      golferProfile?.timePlayingGolf?.value?.replace('Years', '') ||
    roundsPerMonth !== golferProfile?.roundsPerMonth ||
    (golferProfile?.newHandicap?.tmCalculatedHandicap &&
      handicap !== golferProfile?.newHandicap?.tmCalculatedHandicap) ||
    (!golferProfile?.newHandicap?.tmCalculatedHandicap &&
      handicap !== golferProfile?.newHandicap?.userInputHandicap) ||
    targetScore !== golferProfile?.targetScore ||
    maximumDriverDistance !==
      convertDriveLength(golferProfile?.maximumDriverDistance) ||
    strongestArea !== golferProfile?.strongestArea ||
    weakestArea !== golferProfile?.weakestArea ||
    shotShape !== golferProfile?.shotShape ||
    ballStrike !== golferProfile?.ballStrike ||
    misHit !== golferProfile?.misHit ||
    avoidShot !== golferProfile?.avoidShot ||
    mostScaredShot !== golferProfile?.mostScaredShot ||
    homeCourse !== golferProfile?.homeCourse ||
    favoriteTeamMembers !== golferProfile?.favoriteTeamMembers ||
    strokesGainedBaseline !== golferProfile?.strokesGainedBaseline;

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <BackButton
          color="black"
          onPress={() => {
            goBack();
          }}
        />
      ),
    });
  }, [changed]);

  const goBack = () => {
    if (changed) {
      alertCheckForSave();
    } else {
      navigation.goBack();
    }
  };
  const alertCheckForSave = () => {
    return Alert.alert(
      t('game_profile.alert.unsaved_title'),
      t('game_profile.alert.text'),
      [
        {
          text: t('game_profile.alert.cancel'),
          onPress: () => {},
          style: 'cancel',
        },
        {text: t('game_profile.alert.ok'), onPress: () => navigation.goBack()},
      ],
    );
  };

  const GA_logEventChangeGameProfile = async () => {
    if (
      timePlayingGolf !==
      golferProfile?.timePlayingGolf?.value?.replace('Years', '')
    ) {
      await GA_logEvent(GA_EVENT_NAME.GAME_PROFILE_UPDATED, {
        game_setting_name: 'years playing',
        previous_game_setting: golferProfile?.timePlayingGolf?.value?.replace(
          'Years',
          '',
        ),
        new_game_setting: timePlayingGolf ? `${timePlayingGolf?.trim()}` : null,
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_MY_GAME_PROFILE,
        page_name: PAGE_NAME.ACCOUNT_MY_GAME_PROFILE_INFO,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
      await GA_setUserProperty(
        'years_playing',
        timePlayingGolf ? `${timePlayingGolf?.trim()}` : null,
      );
    }
    if (roundsPerMonth !== golferProfile?.roundsPerMonth) {
      await GA_logEvent(GA_EVENT_NAME.GAME_PROFILE_UPDATED, {
        game_setting_name: 'rounds per month',
        previous_game_setting: golferProfile?.roundsPerMonth + '',
        new_game_setting: roundsPerMonth + '',
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_MY_GAME_PROFILE,
        page_name: PAGE_NAME.ACCOUNT_MY_GAME_PROFILE_INFO,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
      await GA_setUserProperty('rounds_per_month', roundsPerMonth + '');
    }
    if (weakestArea !== golferProfile?.weakestArea) {
      await GA_logEvent(GA_EVENT_NAME.GAME_PROFILE_UPDATED, {
        game_setting_name: 'weakest area',
        previous_game_setting: golferProfile?.weakestArea + '',
        new_game_setting: weakestArea + '',
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_MY_GAME_PROFILE,
        page_name: PAGE_NAME.ACCOUNT_MY_GAME_PROFILE_INFO,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
    }
    if (misHit !== golferProfile?.misHit) {
      await GA_logEvent(GA_EVENT_NAME.GAME_PROFILE_UPDATED, {
        game_setting_name: 'mis-hit',
        previous_game_setting: golferProfile?.misHit + '',
        new_game_setting: misHit + '',
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_MY_GAME_PROFILE,
        page_name: PAGE_NAME.ACCOUNT_MY_GAME_PROFILE_INFO,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
    }
    if (homeCourse !== golferProfile?.homeCourse) {
      await GA_logEvent(GA_EVENT_NAME.GAME_PROFILE_UPDATED, {
        game_setting_name: 'home course',
        previous_game_setting: golferProfile?.homeCourse + '',
        new_game_setting: homeCourse + '',
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_MY_GAME_PROFILE,
        page_name: PAGE_NAME.ACCOUNT_MY_GAME_PROFILE_INFO,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
      await GA_setUserProperty('home_course', homeCourse + '');
    }
    if (favoriteTeamMembers !== golferProfile?.favoriteTeamMembers) {
      await GA_logEvent(GA_EVENT_NAME.GAME_PROFILE_UPDATED, {
        game_setting_name: 'followed player',
        previous_game_setting: golferProfile?.favoriteTeamMembers + '',
        new_game_setting: favoriteTeamMembers + '',
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_MY_GAME_PROFILE,
        page_name: PAGE_NAME.ACCOUNT_MY_GAME_PROFILE_INFO,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
    }
  };

  const handleAction = async () => {
    if (!changed) {
      navigation.goBack();
      return;
    }
    setLoading(true);
    try {
      const updatedUser = await updateUser({
        timePlayingGolf: timePlayingGolf ? `${timePlayingGolf?.trim()}` : null,
        rpm: roundsPerMonth,
        handicapPreference: handicap ? 0 : 1,
        userInputHandicap: handicap,
        targetScore: targetScore,
        maximumDriverDistance: getDriveLength(),
        strongestArea: strongestArea,
        weakestArea: weakestArea,
        ballStrike: ballStrike,
        misHit: misHit,
        strokesGainedBaseline: strokesGainedBaseline,
        shotShape: shotShape,
        avoidShot: avoidShot,
        mostScaredShot: mostScaredShot,
        homeCourse: homeCourse,
        iGolfCourseId: iGolfCourseId,
        favoriteTeamMembers: favoriteTeamMembers,
        onboardingCompleteSteps: {
          timePlayingGolf: true,
          rpmComplete: true,
          targetScoreComplete: true,
          maximumDriverDistance: true,
          strongestArea: true,
          weakestArea: true,
          shotShape: true,
          ballStrike: true,
          misHit: true,
          avoidShot: true,
          mostScaredShot: true,
          homeCourse: true,
          favoriteTeamMembers: true,
        },
      });
      await GA_logEventChangeGameProfile();
      // Update user in redux
      addCurrentUser(updatedUser);
      // Stop loading state and navigate to next screen
      setLoading(false);
      showToast({
        type: 'success',
        message: t('game_profile.action.success'),
      });
      navigation.goBack();
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: t('game_profile.action.error'),
      });
    }
  };

  return (
    <View style={[appStyles.flex, appStyles.whiteBg]}>
      <View
        style={[
          appStyles.row,
          appStyles.hCenter,
          {marginLeft: 16, marginTop: Platform.OS === 'android' ? '5%' : '11%'},
        ]}
      >
        <TouchableOpacity
          style={{marginRight: 10}}
          onPress={() => {
            navigation.goBack();
          }}
        >
          <IconBack />
        </TouchableOpacity>
        <View>
          <Text
            size={22}
            black
            Din79Font
            weight={'800'}
            style={{letterSpacing: 1.1}}
          >
            {t('home.toupdate.supporting_copy.my_game_profile')}
          </Text>
        </View>
      </View>
      <View style={[appStyles.flex, {marginTop: 20}]}>
        <FormItemProfile
          label={t('game_profile.years_playing')}
          icon="chevron-right"
          onPress={() =>
            navigation.navigate('IntroQuiz', {
              screen: 'UserQuiz',
              params: {
                origin: 'Setting',
                quizType: QUIZ_TYPE.YEARS,
                timePlayingGolf: `${timePlayingGolf?.trim()}` || '',
                setTimePlayingGolf,
              },
            })
          }
          topBorder
          bottomBorder
          borderBottomWidth={1}
          borderBottomColor={'#00000080'}
          value={timePlayingGolf}
          disabled={loading}
          labelStyle={Platform.OS == 'android' ? appStyles.black : {}}
          style={styles.viewItem}
        />
        <FormItemProfile
          label={t('game_profile.rounds_month')}
          icon="chevron-right"
          value={
            roundsPerMonth && roundsPerMonth !== 0
              ? roundsPerMonth?.toString()
              : ''
          }
          onPress={() =>
            navigation.navigate('IntroQuiz', {
              screen: 'UserQuiz',
              params: {
                origin: 'Setting',
                roundsPerMonth: roundsPerMonth || 1,
                setRoundsPerMonth,
                quizType: QUIZ_TYPE.ROUNDS,
              },
            })
          }
          bottomBorder
          backgroundColor={'transparent'}
          borderBottomWidth={1}
          borderBottomColor={'#00000080'}
          disabled={loading}
          labelStyle={Platform.OS == 'android' ? appStyles.black : {}}
          style={styles.viewItem}
        />
        <FormItemProfile
          label={t('game_profile.weakest_area')}
          value={weakestArea}
          icon="chevron-right"
          onPress={() =>
            navigation.navigate('IntroQuiz', {
              screen: 'UserQuiz',
              params: {
                origin: 'Setting',
                weakestArea,
                setWeakestArea,
                quizType: QUIZ_TYPE.WEAKEST,
              },
            })
          }
          bottomBorder
          backgroundColor={'transparent'}
          borderBottomWidth={1}
          borderBottomColor={'#00000080'}
          disabled={loading}
          labelStyle={Platform.OS == 'android' ? appStyles.black : {}}
          style={styles.viewItem}
        />
        <FormItemProfile
          label={t('game_profile.mis_hit')}
          value={misHit}
          icon="chevron-right"
          onPress={() =>
            navigation.navigate('IntroQuiz', {
              screen: 'UserQuiz',
              params: {
                origin: 'Setting',
                misHit: misHit || '',
                setMisHit,
                quizType: QUIZ_TYPE.MISHIT,
              },
            })
          }
          bottomBorder
          backgroundColor={'transparent'}
          borderBottomWidth={1}
          borderBottomColor={'#00000080'}
          disabled={loading}
          labelStyle={Platform.OS == 'android' ? appStyles.black : {}}
          style={styles.viewItem}
        />
        <FormItemProfile
          label={t('game_profile.home_course')}
          value={homeCourse}
          icon="chevron-right"
          onPress={() =>
            navigation.navigate('IntroQuiz', {
              screen: 'UserQuiz',
              params: {
                origin: 'Setting',
                homeCourse: homeCourse,
                setHomeCourse,
                setIGolfCourseId,
                quizType: QUIZ_TYPE.HOMECOURSE,
              },
            })
          }
          bottomBorder
          backgroundColor={'transparent'}
          borderBottomWidth={1}
          borderBottomColor={'#00000080'}
          disabled={loading}
          labelStyle={Platform.OS == 'android' ? appStyles.black : {}}
          style={styles.viewItem}
        />
        <FormItemProfile
          isFollowPlayer={true}
          label={t('game_profile.followed_players')}
          value={favoriteTeamMembers}
          icon="chevron-right"
          onPress={() =>
            navigation.navigate('IntroQuiz', {
              screen: 'UserQuiz',
              params: {
                origin: 'Setting',
                favoriteTeamMembers: favoriteTeamMembers,
                setFavoriteTeamMembers,
                quizType: QUIZ_TYPE.FOLLOWPLAYER,
              },
            })
          }
          bottomBorder
          borderBottomWidth={1}
          borderBottomColor={'#00000080'}
          disabled={loading}
          labelStyle={Platform.OS == 'android' ? appStyles.black : {}}
          style={styles.viewItem}
        />
      </View>
      {changed && (
        <View
          style={[
            appStyles.pBMd,
            appStyles.pTSm,
            {backgroundColor: 'transparent'},
            appStyles.pHSm,
          ]}
        >
          <Button
            text={t('game_profile.save_settings')}
            textColor="black"
            backgroundColor={'transparent'}
            loadingMode={'undark'}
            textSize={'mds'}
            centered
            DINbold
            disabled={loading}
            loading={loading}
            borderColor={'black'}
            onPress={handleAction}
            // leftIcon={'checkmark'}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  viewPlaying: {
    paddingTop: 20,
    paddingBottom: 20,
  },
  viewItem: {
    paddingTop: Platform.OS === 'android' || smallScreenIphone ? 14 : 18,
    paddingBottom: Platform.OS === 'android' || smallScreenIphone ? 12 : 14,
    paddingHorizontal: 16,
  },
  viewText: {
    marginHorizontal: 16,
  },
});

const mapDispatchToProps = {addCurrentUser};

export default connect(null, mapDispatchToProps)(MyGameProfile);
