import React, {useState} from 'react';
import {View, StatusBar, Keyboard} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {isEmpty, isStrongPassword} from 'validator';
import * as Animatable from 'react-native-animatable';

import Text from 'components/Text';
import TextInput from 'components/TextInput';
import Button from 'components/Button';

import {changePassword} from 'requests/accounts';

import appStyles from 'styles/global';
import {GREY} from 'config';
import {showToast} from 'utils/toast';
import {t} from 'i18next';
import { GA_logEvent } from 'utils/googleAnalytics';
import { GA_EVENT_NAME, PAGE_NAME, PAGE_CATEGORY, SCREEN_TYPES } from 'utils/constant';

const ResetPassword = ({navigation}) => {
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const validated =
    !isEmpty(oldPassword || '') &&
    !isEmpty(newPassword || '') &&
    !isEmpty(confirmPassword || '') &&
    newPassword === confirmPassword;

  const resetPassword = async () => {
    Keyboard.dismiss();

    // Validate password
    if (
      !isStrongPassword(newPassword, {
        minLength: 8,
        minLowercase: 1,
        minUppercase: 1,
        minNumbers: 1,
        minSymbols: 0,
      })
    ) {
      return showToast({
        type: 'error',
        message: t('Invalid_new_password'),
        subText: t('Minimum_length_8,_at_least_one_uppercase_and_number'),
      });
    }

    setLoading(true);
    try {
      await changePassword(oldPassword, newPassword);
      GA_logEvent(GA_EVENT_NAME.ACCOUNT_SETTINGS_CHANGED, {
        setting_name: 'reset password',
        page_type: SCREEN_TYPES.ACCOUNT,
        page_category: PAGE_CATEGORY.ACCOUNT_GENDER,
        page_name: PAGE_NAME.ACCOUNT_GENDER_SETTING,
        screen_type: SCREEN_TYPES.ACCOUNT,
      });
      navigation.goBack();
      setLoading(false);
      showToast({
        type: 'success',
        message: t('Successfully_changed_password'),
      });
    } catch (error) {
      setLoading(false);
      showToast({
        type: 'error',
        message: t('An_error_occurred_changing_password'),
      });
    }
  };

  return (
    <SafeAreaView style={[appStyles.flex, appStyles.mBSm]}>
      <StatusBar barStyle="light-content" />
      <View style={[appStyles.flex, appStyles.mTSm, appStyles.pHMd]}>
        <Animatable.View animation="fadeInUp">
          <Text style={[appStyles.white, appStyles.textCenter, appStyles.mBMd]}>
            settings.headline.reset_password
          </Text>
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={100}>
          <TextInput
            style={[appStyles.mBSm]}
            placeholder={t('reset_password.old_password')}
            onChangeText={setOldPassword}
            secureTextEntry={true}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={200}>
          <TextInput
            style={[appStyles.mBSm]}
            placeholder={t('reset_password.new_password')}
            onChangeText={setNewPassword}
            secureTextEntry={true}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={300}>
          <TextInput
            style={[appStyles.mBSm]}
            placeholder={t('reset_password.confirm_password')}
            onChangeText={setConfirmPassword}
            secureTextEntry={true}
          />
        </Animatable.View>
        <Animatable.View animation="fadeInUp" delay={400}>
          <Button
            text="reset_password.reset"
            backgroundColor={validated ? 'white' : GREY}
            disabled={!validated || loading}
            loading={loading}
            onPress={resetPassword}
            centered
            DINbold
          />
        </Animatable.View>
      </View>
    </SafeAreaView>
  );
};

export default ResetPassword;
