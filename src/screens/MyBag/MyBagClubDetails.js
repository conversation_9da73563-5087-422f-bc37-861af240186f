/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect} from 'react';
import {View} from 'react-native';
import DetailInMyBag from 'components/DetailInMyBag/DetailInMyBag';
import appStyles from 'styles/global';
import {connect, useSelector} from 'react-redux';
import {NOT_APPLICABLE, PAGE_CATEGORY, PAGE_NAME, SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';
import analytics from '@react-native-firebase/analytics';
import {GA_logScreenView, GA_logScreenViewV2} from 'utils/googleAnalytics';
import useMembershipLevel from 'hooks/useMembershipLevel';

const MyBagClubDetails = ({navigation, route, bagList}) => {
  const categoryId = route.params?.categoryId;
  const routeScreen = route;
  const [clubDetail, setClubDetail] = useState(route.params);
  const {membershipLevel} = useMembershipLevel();
  useEffect(() => {
    if (bagList?.length > 0) {
      const club = bagList.find(value => value?.id === route?.params?.id);
      if (club) {
        const data = {
          id: club.id,
          brandId: club?.manufacturer?.id,
          modelId: club?.modelName?.id,
          manufacturer: club?.manufacturer?.name || '',
          modelName: club?.modelName?.name || '',
          image: club.imageLarge || null,
          clubFamily: club.clubFamily || null,
        };
        setClubDetail({...clubDetail, ...data});
        GA_logScreenViewV2(PAGE_NAME.ACCOUNT_WITB_CLUB_DETAIL_INFO, PAGE_CATEGORY.ACCOUNT_WITB_CLUB_DETAIL, SCREEN_TYPES.ACCOUNT, SCREEN_TYPES.ACCOUNT);
      }
    }
  }, [bagList]);

  return (
    <View style={appStyles.flex}>
      <DetailInMyBag
        navigation={navigation}
        route={routeScreen}
        clubDetail={clubDetail}
      />
    </View>
  );
};
const mapStateToProps = state => ({
  bagList: Array.isArray(state.myBag?.bagList) ? state.myBag?.bagList : [],
});
export default connect(mapStateToProps)(MyBagClubDetails);
