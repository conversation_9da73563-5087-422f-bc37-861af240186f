import React, {useEffect, useState, useRef, useMemo} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  Dimensions,
  ActivityIndicator,
  ImageBackground,
  StatusBar,
  Image,
} from 'react-native';
import Text from 'components/Text/Text';
import appStyles from 'styles/global';
import {moderateScale} from 'react-native-size-matters';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {t} from 'i18next';
import Scorecard from './components/Scorecard';
import HandicapBG from 'assets/imgs/average_background.png';
import {get} from 'lodash';
import moment from 'moment';
import ScoreDetailAdvanced from 'screens/Scores/ScoreDetailAdvanced';
import {getRoundAdvancedStats, getRoundStat} from 'utils/singleRoundStat';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import DistanceRangeChart from 'screens/Stats/components/ClubStats/DistanceRangeChart';
import {
  filterValidHoles,
  getParFromHoles,
  getScoreFromHoles,
  validateGhinRoundScore,
  validateRound,
} from 'utils/validate';
import {GA_logCtaEvent} from 'utils/googleAnalytics';
import {isSmallScreenIphone, SCREEN_CLASS, SCREEN_TYPES} from 'utils/constant';
import {useSelector} from 'react-redux';
import {renderWinnerCompleteBoard} from 'utils/renderWinnerMultiPlayer';
import {checkCanPostRoundToUSGA, checkConnectedWHS} from 'utils/user';
import RoundOverviewStats from 'screens/Scores/components/RoundOverviewStats';
import {getTotalScore, MODE_ROUND} from './DataSubmitDefault';
import {getModePlayed} from 'utils/commonVariable';
import RoundOverviewHeader from 'screens/Scores/components/RoundOverviewHeader';
import Icon from 'react-native-vector-icons/AntDesign';
import {GHIN_MAIN_COLOR, GRAY_BACKGROUND, GOLF_CANADA_COLOR} from 'config';
import * as Animatable from 'react-native-animatable';
import {getDataCourseCache} from 'utils/home';
import ScoreInputModal from './components/ScoreInputModal';
import {getQuickStats} from './DataEditDefault';
import ShareRoundPreview from 'screens/Play/components/ShareRoundPreview';
import ShareRoundIcon from 'assets/imgs/playScore/share-round-icon.png';
import {endRound} from 'requests/add-round';
import BottomSheetSubmitGhinConfirm from 'components/BottomSheetSubmitGhinConfirm';
import ConfirmModal from 'components/Modal/ConfirmModal';
import {
  isConnectedNetwork,
  showToastErrorInternet,
} from 'utils/queueAndNetwork';
import {convertDistanceFromYards} from 'utils/convert';

//SUMMARY SCREEN - Single Player
const RoundSummaryPopup = ({
  visible,
  onCompleteRound,
  onClosePopup,
  scoreDetail,
  isAdvancedMode = false,
  isBasicMode,
  teeSelected,
  scoreCardDefault,
  dateSelected,
  listClub,
  navigation,
  roundId,
  isMultiplayerMode,
  isPracticeMode,
  onReloadListUSGA,
  setScoreDetail,
  listHoleEdited,
  setListHoleEdited,
  reloadListData,
  isEndingRound,
  isFromMenu,
  courseDetail,
}) => {
  const [loading, setLoading] = useState(false);
  const [roundStat, setRoundStat] = useState();
  const isClassicMode = getModePlayed() === MODE_ROUND.CLASSIC;
  const isClassOrBasicMode = isClassicMode || isBasicMode;
  const parNumber =
    scoreCardDefault?.parTotal?.toString() ||
    (scoreCardDefault?.parIn || scoreCardDefault?.parOut
      ? `${
          get(scoreCardDefault, 'parIn', 0) + get(scoreCardDefault, 'parOut', 0)
        }`
      : '00');
  const submitGhinSheetRef = useRef(null);
  const user = useSelector(state => state.user);
  const showHideFeatures = useSelector(state => state.app?.showHideFeatures);
  const canPostToUSGA = checkCanPostRoundToUSGA(showHideFeatures, user);
  const whs = useSelector(state => state.whs);
  const userGolfNetId = checkConnectedWHS(whs);
  const insets = useSafeAreaInsets();
  const refInsets = useRef(
    Platform.OS === 'android' ? 20 : insets?.top,
  ).current;
  const numberOfHolesPlayed = scoreDetail?.holes?.filter(
    item => item.score,
  )?.length;
  const [selectedHole, setSelectedHole] = useState(0);
  const [roundDataSubmit, setRoundDataSubmit] = useState(null);
  const [isShareRoundVisible, setShareRoundVisible] = useState(false);
  const [buttonPanelHeight, setButtonPanelHeight] = useState(0);
  const [isShowModalNoGhinConfirm, setShowModalNoGhinConfirm] = useState(false);
  const [selectGhinCourseDone, setSelectGhinCourseDone] = useState(false);
  const [ghinCourseId, setGhinCourseId] = useState(courseDetail?.ghinId);
  const [isModalShowSubmit, setModalShowSubmit] = useState(false);
  const [currentRoundInfo, setCurrentRoundInfo] = useState();
  const userDistanceUnit =
    user?.measurementUnits?.toLowerCase?.() === 'meters' ? 'meters' : 'yards';

  useEffect(() => {
    const stat = isAdvancedMode
      ? getRoundAdvancedStats(scoreDetail?.holes, listClub)
      : getRoundStat(scoreDetail?.holes);
    if (stat) {
      setRoundStat(stat);
    }
  }, [listClub, scoreDetail]);

  useEffect(() => {
    if (selectGhinCourseDone && ghinCourseId && ghinCourseId !== 0) {
      setTimeout(() => {
        setModalShowSubmit(true);
        submitGhinSheetRef?.current?.snapTo(0);
      }, 300);
    }
  }, [ghinCourseId, selectGhinCourseDone]);

  const quickStats = useMemo(() => {
    const {score, par3avg, par4avg, par5avg, parTotal} =
      getQuickStats(scoreDetail);
    const scoreToPar = score - parTotal;
    return {score, par3avg, par4avg, par5avg, scoreToPar};
  }, [scoreDetail]);
  const {score, scoreToPar, par3avg, par4avg, par5avg} = quickStats;
  const teeDistanceObj = convertDistanceFromYards({
    distanceInYards: teeSelected?.ydsTotal,
    userUnit: userDistanceUnit,
  });
  const renderHeader = isLightText => {
    return (
      <View
        style={[
          styles.viewHeader,
          {paddingTop: refInsets + (Platform.OS === 'android' ? 10 : 0)},
        ]}
      >
        <Text
          black
          DINbold
          size={26}
          style={[
            {
              marginHorizontal: 60,
              textAlign: 'center',
              marginTop: moderateScale(20),
            },
            isLightText && appStyles.white,
          ]}
        >
          {scoreDetail?.course_name?.toLocaleUpperCase()}
        </Text>
        <View style={[appStyles.row, {marginVertical: 10}]}>
          <View style={styles.viewHole}>
            <Text black size={8} style={isLightText && appStyles.white}>
              Holes
            </Text>
            <Text
              black
              style={[styles.textNumber, isLightText && appStyles.white]}
            >
              {scoreDetail?.holes?.length}
            </Text>
          </View>
          <View style={styles.viewHole}>
            <Text black size={8} style={isLightText && appStyles.white}>
              Par
            </Text>
            <Text
              black
              style={[styles.textNumber, isLightText && appStyles.white]}
            >
              {parNumber}
            </Text>
          </View>
          <View style={styles.viewHole}>
            <Text black size={8} style={isLightText && appStyles.white}>
              {teeSelected?.teeName}
              {' Tee'}
            </Text>
            <Text
              black
              style={[styles.textNumber, isLightText && appStyles.white]}
            >
              {teeDistanceObj.value}
            </Text>
          </View>
        </View>
        <View style={[appStyles.row, {alignItems: 'flex-end'}]}>
          <Text
            black
            size={13}
            style={[
              {marginBottom: Platform.OS === 'android' ? -6 : -2},
              isLightText && appStyles.white,
            ]}
          >
            {moment(dateSelected).format('LL')}{' '}
          </Text>
        </View>
      </View>
    );
  };

  const renderStatView = () => {
    return (
      <View>
        <View style={[appStyles.vCenter, appStyles.row, appStyles.hCenter]}>
          <Text
            DINbold
            style={[appStyles.white, {fontSize: 100, fontWeight: '500'}]}
          >
            {parseInt(score || 0, 10)}
            {!isNaN(scoreToPar) && scoreToPar !== 0 && (
              <>
                <View style={{width: 10}} />
                <Text style={{fontSize: 16, paddingLeft: 20}}>
                  {`${scoreToPar > 0 ? '+' + scoreToPar : scoreToPar}`}
                </Text>
              </>
            )}
          </Text>
        </View>
        <View style={[appStyles.row, appStyles.pHXSm, appStyles.spaceBetween]}>
          <View style={styles.buttonStyle}>
            <Text style={[appStyles.white, {fontSize: moderateScale(13)}]}>
              {`${t('stats.par_3')} Avg`}
            </Text>
            <Text
              DINbold
              style={[appStyles.white, appStyles.pTMd, styles.textAvgScore]}
            >
              {par3avg ? parseFloat(par3avg).toFixed(2) : '--'}
            </Text>
          </View>
          <View style={styles.buttonStyle}>
            <Text
              style={[
                appStyles.xs,
                appStyles.white,
                {fontSize: moderateScale(13)},
              ]}
            >
              {`${t('stats.par_4')} Avg`}
            </Text>
            <Text
              DINbold
              style={[appStyles.white, appStyles.pTMd, styles.textAvgScore]}
            >
              {par4avg ? parseFloat(par4avg).toFixed(2) : '--'}
            </Text>
          </View>
          <View style={styles.buttonStyle}>
            <Text
              style={[
                appStyles.xs,
                appStyles.white,
                {fontSize: moderateScale(13)},
              ]}
            >
              {`${t('stats.par_5')} Avg`}
            </Text>
            <Text
              DINbold
              style={[appStyles.white, appStyles.pTMd, styles.textAvgScore]}
            >
              {par5avg ? parseFloat(par5avg).toFixed(2) : '--'}
            </Text>
          </View>
        </View>
      </View>
    );
  };
  const onPressEditRound = () => {
    if (isEndingRound) {
      navigation?.navigate?.('App', {screen: 'Play'});
    } else {
      setLoading(true);
      onClosePopup();
      setTimeout(() => {
        setLoading(false);
      }, 3000);
    }
  };

  const onPostScoreToUSGA = async () => {
    try {
      // const {score} = quickStats;
      const validateRs = validateGhinRoundScore(
        parseInt(score || 0, 10),
        scoreDetail?.holes?.length,
        numberOfHolesPlayed,
        scoreDetail?.holes,
      );
      if (validateRs.success && (canPostToUSGA || userGolfNetId)) {
        const checkNetwork = isConnectedNetwork();
        if (!checkNetwork) {
          showToastErrorInternet();
          return;
        }
        GA_logCtaEvent(
          'usga_submit_round',
          t('ghin.score.cta.post_score_to_usga'),
          'post round summary',
          SCREEN_CLASS.PLAY,
          SCREEN_TYPES.PLAY,
        );
        if (!currentRoundInfo?.id) {
          setLoading(true);
          const saveResponse = await onCompleteRound({isSubmitingToGhin: true});
          setCurrentRoundInfo(saveResponse);
          setLoading(false);
        }
        if (userGolfNetId) {
          navigation.navigate('ScoresStats', {
            screen: 'ScoreAddCourse',
            params: {
              roundId,
              igolfRoundInfo: {
                generatedBy: scoreDetail?.generated_by,
                playedOn: moment(scoreDetail?.played_on).toISOString(),
                userTimeZone: scoreDetail?.user_timezone,
              },
              onReloadListUSGA,
            },
          });
        } else {
          if (ghinCourseId && ghinCourseId !== '0') {
            setModalShowSubmit(true);
            setTimeout(() => {
              submitGhinSheetRef?.current?.snapTo(0);
            }, 300);
          } else {
            setShowModalNoGhinConfirm(true);
          }
        }
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const onPressSaveToMyTM = async () => {
    if (!loading) {
      try {
        setLoading(true);
        await onCompleteRound();
        setLoading(false);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    }
  };
  const onPressEditScore = async holeSelected => {
    setRoundDataSubmit({...scoreDetail});
    setSelectedHole(holeSelected);
  };

  const updateRoundDataSubmit = (newData, holeNumber) => {
    setScoreDetail?.(newData);
    setRoundDataSubmit(newData);
    //update listHoleEdited for MenuPlayRound and CourseMap screen
    const hole = newData.holes[holeNumber - 1];
    const index = listHoleEdited?.findIndex?.(
      _val => _val.number === hole.number,
    );
    if (index > -1) {
      const newList = [...listHoleEdited];
      newList[index] = hole;
      setListHoleEdited([...newList]);
    } else {
      setListHoleEdited([...(listHoleEdited || []), hole]);
    }
  };

  const onOpenShareRound = () => {
    setShareRoundVisible(true);
  };

  const saveScore = async roundData => {
    setLoading(true);
    roundData.total_score = getTotalScore(roundData.holes);
    const holeUpdate = roundData.holes.filter(
      _item => _item.number === selectedHole,
    );
    let rs = await endRound(roundId, {
      ...roundData,
      holes: holeUpdate,
    });
    reloadListData?.(rs);
    setLoading(false);
  };

  const dataShare = useMemo(() => {
    const validHolesForShareRound = filterValidHoles(scoreDetail?.holes);
    const scoreForShareRound = getScoreFromHoles(validHolesForShareRound);
    const parForShareRound = getParFromHoles(validHolesForShareRound);
    return {
      scoreCard: {
        scoreDetail: scoreDetail,
        teeSelected: teeSelected,
        scoreCardDefault: scoreCardDefault,
      },
      roundDetail: {
        holes: scoreDetail?.holes?.length,
        par: parNumber,
        teeName: teeSelected?.teeName,
        teeDistance: teeDistanceObj.value,
        distanceUnit: teeDistanceObj.unit,
        datePlayed: dateSelected,
        totalScore: parseInt(scoreForShareRound || score || 0, 10),
        scoreToPar: scoreForShareRound - parForShareRound,
        courseName: scoreDetail?.course_name,
      },
      overallStatsData: getRoundStat(validHolesForShareRound)?.roundStats,
      isBasicMode: isBasicMode,
    };
  }, [
    dateSelected,
    isBasicMode,
    parNumber,
    score,
    scoreCardDefault,
    scoreDetail,
    teeSelected,
  ]);

  const onPressContinueNoGHIN = () => {
    setSelectGhinCourseDone(false);
    navigation.navigate('ScoresStats', {
      screen: 'ScoreAddCourse',
      params: {
        isSelectingGhinCourse: true,
        setSelectedGhinCourseId: setGhinCourseId,
        setSelectGhinCourseDone: setSelectGhinCourseDone,
      },
    });
  };

  const renderModalNoGhinConfirm = () => {
    return (
      <ConfirmModal
        visible={isShowModalNoGhinConfirm}
        title={t('score.confirm_no_ghin')}
        textCancel={'Cancel'}
        textConfirm={'Continue'}
        backdropPress={() => setShowModalNoGhinConfirm(false)}
        onPressConfirm={onPressContinueNoGHIN}
        isNoPadding={true}
      />
    );
  };

  const validateGhinRoundResult = useMemo(() => {
    const numberOfHolesPlayed = scoreDetail?.holes?.filter?.(
      item => item.completeHole,
    )?.length;
    const rs = validateGhinRoundScore(
      scoreDetail?.total_score,
      scoreDetail?.holes?.length,
      scoreDetail?.numberOfHolesPlayed || numberOfHolesPlayed,
      scoreDetail?.holes,
      false,
    );
    if (rs?.success || rs?.error?.type !== 'holes') {
      return true;
    } else {
      return false;
    }
  }, [scoreDetail]);

  const parValueInSubmitUSGA = useMemo(() => {
    if (canPostToUSGA) {
      const numberOfHolesPlayed = scoreDetail?.holes?.filter?.(
        item => item.completeHole,
      )?.length;
      // Check if only all front 9 holes (holes 1–9) are completed
      const onlyFrontNineCompleted =
        scoreDetail?.holes.filter(holeItem => holeItem.completeHole).length ===
          9 &&
        scoreDetail?.holes.slice(0, 9).every(holeItem => holeItem.completeHole);

      // Check if only all back 9 holes (holes 10–18) are completed
      const onlyBackNineCompleted =
        scoreDetail?.holes.filter(holeItem => holeItem.completeHole).length ===
          9 &&
        scoreDetail?.holes
          .slice(9, 18)
          .every(holeItem => holeItem.completeHole);
      if (numberOfHolesPlayed === 9) {
        if (onlyFrontNineCompleted) {
          return scoreCardDefault?.parOut;
        }
        if (onlyBackNineCompleted) {
          return scoreCardDefault?.parIn;
        }
      } else {
        return null;
      }
    } else {
      return null;
    }
  }, [canPostToUSGA, scoreDetail, scoreCardDefault]);

  if (!visible) return null;
  const shouldShowShareRound = validateRound(scoreDetail?.holes);
  const shouldShowPostToGhin =
    validateGhinRoundResult && canPostToUSGA && !isPracticeMode;
  const shouldShowPostToWHS = userGolfNetId;
  const shouldShowButtonPanel =
    isFromMenu ||
    shouldShowPostToGhin ||
    shouldShowPostToWHS ||
    shouldShowShareRound;
  const shouldShowNotEnoughHoleForUSGA =
    !validateGhinRoundResult && canPostToUSGA && !isFromMenu;

  return (
    <Animatable.View
      backdropOpacity={0.6}
      style={{
        zIndex: 10,
        position: 'absolute',
        backgroundColor: '#F5F5F5',
      }}
      deviceWidth={wp(100)}
      animation={'slideInUp'}
      duration={700}
    >
      <View
        style={[
          styles.container,
          {
            height:
              Dimensions.get('window').height -
              (Platform.OS === 'android' &&
              Number.isInteger(StatusBar.currentHeight)
                ? StatusBar.currentHeight
                : 0),
          },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.touchButtonHole,
            {top: refInsets + (Platform.OS === 'android' ? -3 : 5)},
          ]}
          onPress={onPressEditRound}
        >
          <Icon name="close" size={20} color={'#111111'} />
        </TouchableOpacity>
        <ScrollView
          style={styles.scrollView}
          bounces={false}
          contentContainerStyle={{paddingBottom: buttonPanelHeight}}
        >
          {isClassOrBasicMode ? (
            <RoundOverviewHeader
              holes={scoreDetail?.holes?.length}
              par={parNumber}
              teeName={teeSelected?.teeName}
              teeDistance={teeDistanceObj.value}
              distanceUnit={teeDistanceObj.unit}
              datePlayed={dateSelected}
              totalScore={parseInt(score || 0, 10)}
              scoreToPar={scoreToPar}
              courseName={scoreDetail?.course_name}
              parAvgStats={{
                averageScoreForParThree: par3avg,
                averageScoreForParFour: par4avg,
                averageScoreForParFive: par5avg,
              }}
            />
          ) : (
            <View
              style={[appStyles.viewShadow, {marginBottom: moderateScale(10)}]}
            >
              <ImageBackground source={HandicapBG} resizeMode={'cover'}>
                <View style={{flex: 1, paddingBottom: moderateScale(20)}}>
                  {renderHeader(true)}
                  {renderStatView()}
                </View>
              </ImageBackground>
            </View>
          )}

          {isMultiplayerMode && renderWinnerCompleteBoard(scoreDetail)}
          <Scorecard
            scoreDetail={scoreDetail}
            teeSelected={teeSelected}
            scoreCardDefault={scoreCardDefault}
            onPressEditScore={onPressEditScore}
          />
          {!isBasicMode && !isMultiplayerMode && roundStat && (
            <View style={[appStyles.pHXs, {marginTop: 16}]}>
              {isAdvancedMode ? (
                <ScoreDetailAdvanced scoreDetail={roundStat} isSummary={true} />
              ) : (
                <RoundOverviewStats
                  overallStatsData={roundStat?.roundStats}
                  courseHolesLength={scoreDetail?.holes?.length}
                />
              )}
            </View>
          )}
          {isAdvancedMode && roundStat && (
            <View>
              <View
                style={[
                  {
                    backgroundColor: GRAY_BACKGROUND,
                    marginVertical: moderateScale(30),
                  },
                ]}
              >
                <View style={[appStyles.pTXs]}>
                  <Text
                    DINbold
                    style={[
                      appStyles.mdms,
                      appStyles.bold,
                      appStyles.pHMd,
                      appStyles.black,
                      {fontSize: moderateScale(26)},
                    ]}
                  >
                    play.complete_round.distance_by_club
                  </Text>
                </View>
              </View>
              <View style={[appStyles.whiteBg, styles.viewDistanceByClub]}>
                <DistanceRangeChart stats={roundStat} />
              </View>
            </View>
          )}
        </ScrollView>
        {shouldShowButtonPanel && (
          <View
            style={[
              appStyles.viewShadowTop,
              styles.buttonsContainer,
              {
                paddingBottom: Platform.OS === 'ios' ? 40 : 30,
              },
            ]}
            onLayout={event => {
              setButtonPanelHeight(event.nativeEvent.layout.height);
            }}
          >
            <View style={{flexDirection: 'row'}}>
              {isFromMenu && (
                <View style={{flex: 1}}>
                  <TouchableOpacity
                    onPress={onPressSaveToMyTM}
                    style={[
                      styles.confirmRoundButton,
                      {
                        marginRight:
                          shouldShowPostToGhin || shouldShowPostToWHS ? 8 : 0,
                      },
                    ]}
                  >
                    <Text
                      style={styles.confirmRoundText}
                      Din79Font
                      size={12}
                      white
                      numberOfLines={1}
                    >
                      roundOverview.btn.confirm_round
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
              {shouldShowShareRound && !isFromMenu && (
                <View style={{flex: 1}}>
                  <TouchableOpacity
                    onPress={onOpenShareRound}
                    style={[
                      styles.confirmRoundButton,
                      {
                        marginRight:
                          shouldShowPostToGhin || shouldShowPostToWHS ? 8 : 0,
                      },
                    ]}
                  >
                    <Image
                      source={ShareRoundIcon}
                      style={{width: 16, height: 16, marginRight: 12}}
                    />
                    <Text
                      style={styles.confirmRoundText}
                      Din79Font
                      size={12}
                      white
                      numberOfLines={1}
                    >
                      play.share_round
                    </Text>
                  </TouchableOpacity>
                  {shouldShowNotEnoughHoleForUSGA && (
                    <Text
                      size={12}
                      style={{flex: 1, marginTop: 12, alignSelf: 'center'}}
                    >
                      {t(
                        'roundOverview.text.not_enough_holes_for_USGA_posting',
                      )}
                    </Text>
                  )}
                </View>
              )}
              {(shouldShowPostToGhin || shouldShowPostToWHS) && (
                <View style={{flex: 1}}>
                  <TouchableOpacity
                    onPress={onPostScoreToUSGA}
                    style={[
                      styles.buttonPostToUSGA,
                      {
                        backgroundColor: shouldShowPostToWHS
                          ? GOLF_CANADA_COLOR
                          : GHIN_MAIN_COLOR,
                        borderColor: shouldShowPostToWHS
                          ? GOLF_CANADA_COLOR
                          : GHIN_MAIN_COLOR,
                      },
                    ]}
                  >
                    <Text
                      style={styles.postToUSGAText}
                      Din79Font
                      size={12}
                      white
                      numberOfLines={1}
                    >
                      {shouldShowPostToWHS
                        ? t('roundOverview.btn.submit_round_to_Golf_Canada')
                        : t('play.btn.post_to_usga')}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
            {shouldShowShareRound && isFromMenu && (
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  paddingTop: 10,
                  marginHorizontal: 16,
                  marginTop: 14,
                  justifyContent: 'center',
                }}
                onPress={onOpenShareRound}
              >
                <Text
                  black
                  size={16}
                  style={{fontWeight: '400', marginRight: 8}}
                >
                  play.share_round
                </Text>
                <Text black size={16} style={{fontWeight: '400'}}>
                  􀈂
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
      {roundDataSubmit && selectedHole ? (
        <ScoreInputModal
          isScoreInputVisible={selectedHole !== 0}
          selectHole={selectedHole}
          teeSelected={teeSelected}
          setScoreInputVisible={() => setSelectedHole(0)}
          setRoundData={updateRoundDataSubmit}
          roundData={roundDataSubmit}
          onCompleteHole={isEndingRound ? saveScore : undefined}
        />
      ) : null}
      <ShareRoundPreview
        isShareRoundVisible={isShareRoundVisible}
        setShareRoundVisible={setShareRoundVisible}
        dataShare={dataShare}
      />
      {loading && (
        <ActivityIndicator
          style={StyleSheet.absoluteFill}
          size="large"
          color={'black'}
        />
      )}
      {(canPostToUSGA || userGolfNetId) && (
        <BottomSheetSubmitGhinConfirm
          currentRoundInfo={{
            ...scoreDetail,
            playedOn: scoreDetail?.playedOnUtc || scoreDetail?.playedOn,
            totalScore: scoreDetail?.total_score,
            roundMode: currentRoundInfo?.roundMode,
            courseName: currentRoundInfo?.courseName,
            coursePar: parValueInSubmitUSGA
              ? parValueInSubmitUSGA
              : currentRoundInfo?.coursePar,
            id: currentRoundInfo?.id,
          }}
          isPostWithRound
          teeValueDisplay={teeDistanceObj?.value}
          ref={submitGhinSheetRef}
          navigation={navigation}
          origin={'RoundSummaryPopup'}
          isModalShow={isModalShowSubmit}
          onCloseModal={() => {
            setModalShowSubmit(false);
            //reset ghin course Id
            if (selectGhinCourseDone) {
              setGhinCourseId(null);
            }
          }}
          onReloadListUSGA={reloadListData}
          ghinCourseId={ghinCourseId}
        />
      )}
      {isShowModalNoGhinConfirm && renderModalNoGhinConfirm()}
    </Animatable.View>
  );
};

const styles = StyleSheet.create({
  viewHeader: {
    alignItems: 'center',
    paddingBottom: 15,
    marginBottom: 15,
  },
  viewHole: {
    alignItems: 'center',
    marginHorizontal: 10,
  },
  textNumber: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  textAvgScore: {
    fontSize: moderateScale(26),
    marginTop: Platform.OS === 'ios' ? moderateScale(8) : 0,
    marginBottom: 15,
  },
  container: {
    backgroundColor: '#EBEBEB',
    overflow: 'hidden',
    width: wp(100),
  },
  scrollView: {
    backgroundColor: '#EBEBEB',
  },
  buttonStyle: {
    ...appStyles.hCenter,
    paddingTop: moderateScale(8),
    width: wp(27),
    aspectRatio: 4 / 3,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  viewDistanceByClub: {
    paddingBottom: moderateScale(20),
    paddingLeft: moderateScale(25),
  },
  touchButtonHole: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    position: 'absolute',
    zIndex: 2,
    right: 22,
  },
  buttonsContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    position: 'absolute',
    bottom: 0,
    paddingTop: 8,
    backgroundColor: 'rgba(229, 229, 229, 1)',
    width: '100%',
  },
  confirmRoundButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 24,
    backgroundColor: 'black',
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  confirmRoundText: {
    letterSpacing: 1.62,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  buttonPostToUSGA: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 24,
    backgroundColor: GHIN_MAIN_COLOR,
    borderColor: GHIN_MAIN_COLOR,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  postToUSGAText: {
    letterSpacing: 1.62,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
});

export default RoundSummaryPopup;
