import React, {useState, useEffect} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ScrollView,
} from 'react-native';

import appStyles from 'styles/global';
import Text from 'components/Text';
import ButtonScorePlusMinus from './ButtonScorePlusMinus';
import {t} from 'i18next';
import ClassicInput from './ClassicInput';
import {
  formatScore,
  formatRoundScore,
  MODE_ROUND,
  TYPE_FAIRWAY,
  TYPE_GREEN,
} from '../DataSubmitDefault';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
  Extrapolation,
  runOnJS,
} from 'react-native-reanimated';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
import {GA_logScreenViewV2} from 'utils/googleAnalytics';
import {PAGE_CATEGORY, PAGE_NAME, SCREEN_CLASS} from 'utils/constant';

const widthBasic = 161;
const widthClassic = wp(100) - 16;
const moveX = wp(23);

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

const ScoreInputByModeSection = ({
  selectHole,
  roundData,
  setScoreInputData,
  mode,
  setMode,
  isInputForUSGA,
}) => {
  const [scoreInput, setScoreInput] = useState({
    score:
      roundData?.holes?.[selectHole - 1]?.score > 0
        ? roundData?.holes?.[selectHole - 1]?.score
        : roundData?.holes?.[selectHole - 1]?.par,
    put: 2,
    fwStats: TYPE_FAIRWAY.HIT,
    grStats: TYPE_GREEN.HIT,
    bunkerHit: false,
    penaltyHit: false,
    roundScore: formatRoundScore(roundData?.holes),
    par: roundData?.holes?.[selectHole - 1]?.par,
  });
  const [showFooterClassic, setShowFooterClassic] = useState(
    mode === MODE_ROUND.CLASSIC,
  );

  useEffect(() => {
    if (scoreInput) {
      setScoreInputData(scoreInput);
    }
  }, [scoreInput, setScoreInputData]);

  useEffect(() => {
    if (mode) {
      if (mode === MODE_ROUND.CLASSIC) {
        setShowFooterClassic(true);
        offset.value = withTiming(0, {duration: 400});
      }
      if (mode === MODE_ROUND.BASIC) {
        offset.value = withTiming(moveX, {duration: 400}, () => {
          runOnJS(setShowFooterClassic)(false);
        });
      }
    }
  }, [mode]);

  useEffect(() => {
    try {
      const hole = roundData?.holes?.[selectHole - 1];
      const isUpdate = hole.score > 0;
      setScoreInput({
        score: isUpdate ? hole.score : roundData?.holes?.[selectHole - 1]?.par,
        put: isUpdate ? hole.putts_number : 2,
        fwStats: isUpdate ? hole.fw_stats : TYPE_FAIRWAY.HIT,
        grStats: isUpdate ? hole.gr_stats : TYPE_GREEN.HIT,
        bunkerHit: isUpdate ? hole.bunker_hit : false,
        penaltyHit: isUpdate ? hole.penalty_hit : false,
        roundScore: formatRoundScore(roundData?.holes),
        par: roundData?.holes?.[selectHole - 1]?.par,
      });
    } catch (error) {}
  }, [selectHole, roundData?.holes]);

  const offset = useSharedValue(mode === MODE_ROUND.CLASSIC ? 0 : moveX);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      transform: [{translateX: offset.value}],
    };
  });

  const animatedClassicStyles = useAnimatedStyle(() => {
    const opacity = interpolate(offset.value, [0, moveX], [1, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      opacity,
      zIndex: opacity === 0 ? 0 : 1,
    };
  });

  const animatedBasicStyles = useAnimatedStyle(() => {
    const opacity = interpolate(offset.value, [0, moveX], [0, 1], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      opacity,
      zIndex: opacity === 0 ? 0 : 1,
    };
  });

  const animatedHeightStyles = useAnimatedStyle(() => {
    const height = interpolate(offset.value, [0, moveX], [191, 132], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      height,
    };
  });

  const animatedButtonStyles = useAnimatedStyle(() => {
    const paddingVertical = interpolate(offset.value, [0, moveX], [5, 15], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const marginTop = interpolate(offset.value, [0, moveX], [0, 15], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const width = interpolate(
      offset.value,
      [0, moveX],
      [widthBasic, widthClassic],
      {
        extrapolateRight: Extrapolation.CLAMP,
      },
    );

    return {
      paddingVertical,
      width,
      marginTop,
    };
  });

  const animatedMovetStyles = useAnimatedStyle(() => {
    const translateX = interpolate(offset.value, [0, moveX], [widthBasic, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const translateY = interpolate(offset.value, [0, moveX], [30, 0], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    const opacity = interpolate(offset.value, [0, moveX], [0, 1], {
      extrapolateRight: Extrapolation.CLAMP,
    });
    return {
      transform: [{translateX}, {translateY}],
      opacity,
    };
  });

  const onPlusPut = () => {
    let put = scoreInput.put;
    if (scoreInput.put < 11) {
      put = put + 1;
      let score = scoreInput.score;
      if (scoreInput.score < 12 && score === put) {
        const holeStr = JSON.stringify(roundData?.holes);
        const holes = JSON.parse(holeStr);
        score = score + 1;
        holes[selectHole - 1].score = score;
        holes[selectHole - 1].completeHole = true;
        const roundScore = formatRoundScore(holes);
        setScoreInput({...scoreInput, score, put, roundScore});
      } else {
        setScoreInput({...scoreInput, score, put});
      }
    }
  };

  const onPlusScore = () => {
    if (scoreInput.score < 12) {
      const holeStr = JSON.stringify(roundData?.holes);
      const holes = JSON.parse(holeStr);
      const score = scoreInput.score + 1;
      holes[selectHole - 1].score = score;
      holes[selectHole - 1].completeHole = true;
      const roundScore = formatRoundScore(holes);
      setScoreInput({...scoreInput, score, roundScore});
    }
  };
  const onMinusScore = () => {
    if (scoreInput.score > 1) {
      const holeStr = JSON.stringify(roundData?.holes);
      const holes = JSON.parse(holeStr);
      const score = scoreInput.score - 1;
      holes[selectHole - 1].score = score;
      holes[selectHole - 1].completeHole = true;
      const roundScore = formatRoundScore(holes);
      if (score <= scoreInput.put) {
        setScoreInput({
          ...scoreInput,
          score,
          put: scoreInput.put - 1,
          roundScore,
        });
      } else {
        setScoreInput({...scoreInput, score, roundScore});
      }
    }
  };

  const onPressTrackScore = () => {
    if (mode === MODE_ROUND.CLASSIC) {
      GA_logScreenViewV2(
        PAGE_NAME.PLAY_TRACK_SCORE_ONLY,
        PAGE_CATEGORY.PLAY_ROUND,
        SCREEN_CLASS.PLAY,
        SCREEN_CLASS.PLAY,
      );
      setMode(MODE_ROUND.BASIC);
    } else {
      GA_logScreenViewV2(
        PAGE_NAME.PLAY_ADD_SCORE_CLASS,
        PAGE_CATEGORY.PLAY_ROUND,
        SCREEN_CLASS.PLAY,
        SCREEN_CLASS.PLAY,
      );
      setMode(MODE_ROUND.CLASSIC);
    }
  };

  const onMinusPut = () => {
    if (scoreInput.put > 0) {
      const put = scoreInput.put - 1;
      setScoreInput({...scoreInput, put});
    }
  };

  const renderScoreAndPut = (onMinus, onPlus, score) => {
    return (
      <Animated.View
        style={[appStyles.hCenter, appStyles.flex, animatedClassicStyles]}
      >
        <ButtonScorePlusMinus onPress={onPlus} buttonStyle={{elevation: 0}} />
        <Text
          Din79Font
          size={34}
          black
          style={[appStyles.textCenter, styles.textScoreBasic]}
        >
          {score}
        </Text>
        <ButtonScorePlusMinus
          isMinus
          onPress={onMinus}
          buttonStyle={{elevation: 0}}
        />
      </Animated.View>
    );
  };

  const renderBasicModeText = () => {
    if (hasInputtedScore()) {
      return null;
    }
    return (
      <Animated.View style={[styles.viewContainerBasic, animatedMovetStyles]}>
        <Text size={16} black style={styles.textTrackDescription}>
          {t('play.track_your_fairways_hit')}
        </Text>
        <Text
          size={12}
          black
          style={[
            styles.textTrackDescription,
            {marginTop: Platform.OS === 'android' ? 2 : 10},
          ]}
        >
          play.if_you_choose
        </Text>
      </Animated.View>
    );
  };
  const hasInputtedScore = () => {
    return formatScore(roundData?.holes) !== '-';
  };

  const renderTrackModeChange = () => {
    if (hasInputtedScore()) {
      return null;
    }
    return (
      <AnimatedTouchableOpacity
        style={[
          styles.touchTrackClassic,
          styles.viewShadow,
          {
            backgroundColor:
              mode === MODE_ROUND.CLASSIC ? '#ffffff' : '#00a3ff',
          },
          animatedButtonStyles,
        ]}
        onPress={onPressTrackScore}
      >
        <Text
          Din79Font
          size={12}
          style={{
            color: mode === MODE_ROUND.CLASSIC ? '#808080' : '#ffffff',
            letterSpacing: 1,
            fontWeight: Platform.OS === 'android' ? 'bold' : '700',
          }}
        >
          {mode === MODE_ROUND.CLASSIC
            ? 'play.track_score_only'
            : 'play.track_classic_stats'}
        </Text>
      </AnimatedTouchableOpacity>
    );
  };

  return (
    <ScrollView
      contentContainerStyle={{flex: mode === MODE_ROUND.CLASSIC ? 0 : 1}}
    >
      <Animated.View
        style={[styles.viewContainerHoleScore, animatedHeightStyles]}
      >
        <View
          style={[
            appStyles.row,
            {
              paddingHorizontal: 8,
            },
          ]}
        >
          <Animated.View style={[appStyles.flex, animatedStyles]}>
            <Text
              black
              size={16}
              style={{marginVertical: 5, textAlign: 'center'}}
            >
              play.score_input.hole_score
            </Text>
          </Animated.View>
          <Animated.View style={[appStyles.flex, animatedClassicStyles]}>
            <Text
              black
              size={16}
              style={{
                marginVertical: 5,
                textAlign: 'center',
              }}
            >
              play.input_score.putts
            </Text>
          </Animated.View>
        </View>
        <Animated.View style={[appStyles.flex, {alignItems: 'center'}]}>
          <Animated.View
            style={[
              styles.viewHoleScoreBasic,
              {
                position: 'absolute',
                marginTop: Platform.OS === 'android' ? -10 : -5,
              },
              animatedBasicStyles,
            ]}
          >
            <View style={[appStyles.row, appStyles.hCenter]}>
              <ButtonScorePlusMinus
                isMinus
                onPress={onMinusScore}
                buttonStyle={{elevation: 0}}
              />
              <View
                style={{
                  minWidth: 150,
                  justifyContent: 'center',
                  top: 7,
                }}
              >
                {/* Circle when score is less than par */}
                {scoreInput.score < scoreInput.par && isInputForUSGA && (
                  <View style={styles.firstCircle} />
                )}

                {/* Second circle when score is 2 or more below par */}
                {scoreInput.score <= scoreInput.par - 2 && isInputForUSGA && (
                  <View style={styles.secondCircle} />
                )}

                {/* Square when score is greater than par */}
                {scoreInput.score > scoreInput.par && isInputForUSGA && (
                  <View style={styles.firstSquare} />
                )}

                {/* Second square when score is 2 or more above par */}
                {scoreInput.score >= scoreInput.par + 2 && isInputForUSGA && (
                  <View style={styles.secondSquare} />
                )}

                <Text
                  size={54}
                  black
                  Din79Font
                  style={[appStyles.textCenter, styles.textScoreBasic]}
                >
                  {scoreInput.score}
                </Text>
              </View>
              <ButtonScorePlusMinus
                onPress={onPlusScore}
                buttonStyle={{elevation: 0}}
              />
            </View>
          </Animated.View>
          <View style={[appStyles.row, styles.viewInputClassic]}>
            {renderScoreAndPut(onMinusScore, onPlusScore, scoreInput.score)}
            {renderScoreAndPut(onMinusPut, onPlusPut, scoreInput.put)}
          </View>
        </Animated.View>
      </Animated.View>
      {renderTrackModeChange()}
      <View style={appStyles.flex}>
        {renderBasicModeText()}
        {showFooterClassic ? (
          <Animated.View style={animatedClassicStyles}>
            <ClassicInput
              setScoreInput={setScoreInput}
              scoreInput={scoreInput}
              isHideFairway={roundData?.holes?.[selectHole - 1]?.par === 3}
            />
          </Animated.View>
        ) : null}
      </View>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  viewHoleScoreBasic: {
    paddingVertical: 8,
    alignItems: 'center',
  },
  textScoreBasic: {
    marginBottom: 5,
    fontWeight: 'bold',
    minWidth: 100,
    marginLeft: Platform.OS === 'ios' ? -2 : 0,
    marginTop: Platform.OS === 'ios' ? 0 : -5,
  },
  touchTrackClassic: {
    alignSelf: 'center',
    marginBottom: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 24,
  },
  viewShadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.23,
    shadowRadius: 1.62,
    elevation: 2,
  },
  textTrackDescription: {
    textAlign: 'center',
  },
  viewContainerHoleScore: {
    marginHorizontal: 8,
    marginBottom: 8,
    paddingVertical: 8,
    borderRadius: 24,
    backgroundColor: '#ffffff',
  },
  viewContainerBasic: {
    alignItems: 'center',
    marginVertical: 10,
    position: 'absolute',
    alignSelf: 'center',
    paddingHorizontal: 24,
  },
  viewInputClassic: {
    position: 'absolute',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: Platform.OS === 'ios' ? 8 : 0,
  },
  firstCircle: {
    width: 56,
    height: 56,
    position: 'absolute',
    borderRadius: 28,
    borderWidth: 1,
    borderColor: '#0000004D',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  secondCircle: {
    width: 68,
    height: 68,
    position: 'absolute',
    borderRadius: 34,
    borderWidth: 1,
    borderColor: '#0000004D',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  firstSquare: {
    width: 56,
    height: 56,
    position: 'absolute',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0000004D',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  secondSquare: {
    width: 68,
    height: 68,
    position: 'absolute',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0000004D',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
});
export default ScoreInputByModeSection;
