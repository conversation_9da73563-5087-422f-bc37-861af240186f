import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import UserQuiz from 'screens/IntroQuiz/UserQuiz';
import CountryQuiz from 'screens/IntroQuiz/CountryQuiz';

const IntroStack = createStackNavigator();

export default ({navigation}) => {
  const {navigate, replace} = navigation;
  return (
    <IntroStack.Navigator>
      <IntroStack.Screen
        name="UserQuiz"
        component={UserQuiz}
        options={{
          headerShown: false,
        }}
      />
      <IntroStack.Screen
        name="CountryQuiz"
        component={CountryQuiz}
        options={{
          headerShown: false,
        }}
      />
    </IntroStack.Navigator>
  );
};
