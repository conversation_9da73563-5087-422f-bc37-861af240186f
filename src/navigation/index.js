import React, {useEffect, useRef, useState} from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {NavigationContainer} from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import analytics from '@react-native-firebase/analytics';
import {useSelector} from 'react-redux';

import Initial from '../screens/Initial';
import OnLaunch from '../screens/OnLaunch';
import SignUp from './signUp';
import VerifyOtp from './verifyOtp';
import ResetPassword from './resetPassword';
import Quiz from './quiz';
import App from './app';
import Profile from './profile';
import MyBag from './myBag';
import WebView from './webView';
import FeedArticle from './feedArticle';
import ScoresStats from './scoresStats';
import NativeCoach from './nativeCoach';
import Fittings from './fittings';
import Insights from './insights';
import Video from '../screens/Video';
import Shop from './shop';
import ClubRecommender from './clubRecommender';
import EntertainmentStack from './entertainment';
import DrillsStack from './drills';
import GearStack from './gear';

import {
  horizontalLeftToRightScreenAnimation,
  horizontalScreenAnimation,
  noLoadScreenAnimation,
  verticalScreenAnimation,
} from '../utils/animations';
import {
  TYPE_OF_OTP,
  SCREEN_TYPES,
  SCREEN_CLASS,
  CLUB_NAME,
  NOT_APPLICABLE,
  PAGE_NAME,
  PAGE_CATEGORY,
} from 'utils/constant';
import nativeCoachHorizontal from './nativeCoachHorizontal';
import {GA_logScreenViewV2} from 'utils/googleAnalytics';
import useMembershipLevel from 'hooks/useMembershipLevel';
import Handicap from './handicap';
import introQuiz from './introQuiz';
import Introduce from 'screens/Intro/Introduce';
import TourorStories from 'screens/Clubhouse/TourorStories';
import verifyBirthDay from './verifyBirthDay';
import {useInAppReviewReadyAfterLaunches} from 'hooks/useInAppReviewReadyAfterLaunches';
import {useInAppReview} from 'hooks/useInAppReview';
import golferQuestionnaire from './golferQuestionnaire';

const Root = createStackNavigator();

export default function Routes() {
  const routeNameRef = useRef(null);
  const routeInfoRef = useRef(null);
  const navigationRef = useRef(null);
  const user = useSelector(state => state?.user);
  const {membershipLevel} = useMembershipLevel();
  const [currentRouteState, setCurrentRouteState] = useState(null);
  const {triggerInAppReview} = useInAppReview();
  const readyToReview = useInAppReviewReadyAfterLaunches();

  const hasTriggered = useRef(false);
  useEffect(() => {
    if (!readyToReview || hasTriggered.current) return;

    const blockList = ['CourseMap', 'MenuPlayRound', 'ScoreAddEditDetails'];
    const currentScreen = currentRouteState?.name;

    const shouldTrigger = currentScreen && !blockList.includes(currentScreen);

    if (shouldTrigger) {
      const timeout = setTimeout(() => {
        triggerInAppReview();
        hasTriggered.current = true;
      }, 1000);

      return () => clearTimeout(timeout);
    }
  }, [readyToReview, currentRouteState?.name]);

  const trackGoogleAnalyticsScreen = async (
    currentRoute,
    prevRoute,
    currentHeaderTitle,
  ) => {
    let trackingAvailable = true;
    const currentRouteName = currentRoute?.name;
    const currentParams = currentRoute?.params;
    let screenName = currentRouteName;
    let screenClass = currentRouteName;
    let screenType = currentRouteName;
    let pageTitle = currentHeaderTitle;

    // console.log('currentRouteName', currentRouteName, currentParams?.origin, prevRoute?.name);

    switch (currentRouteName) {
      case 'WebView':
        pageTitle = currentParams?.title || '';
        switch (currentParams?.origin) {
          case 'PreviouslyTried':
            screenName = 'shop - ttb - FAQs webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.SHOP;
            break;
          case 'TrialStatus':
            screenName = 'shop - ttb - track your delivery webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.SHOP;
            break;
          case 'PROFILE_FAQ':
            screenName = 'user profile - faq webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.USER_PROFILE;
            break;
          case 'PROFILE_ORDER':
            screenName = 'user profile - orders webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.USER_PROFILE;
            break;
          case 'SUPPORT':
            screenName = PAGE_NAME.ACCOUNT_SP_INFO;
            screenType = SCREEN_TYPES.ACCOUNT;
            screenClass = PAGE_CATEGORY.ACCOUNT_SP;
            break;
          case 'DELETE_SUPPORT':
            screenName = PAGE_NAME.ACCOUNT_DELETE_CONTACT;
            screenType = SCREEN_TYPES.ACCOUNT;
            screenClass = PAGE_CATEGORY.ACCOUNT_DELETE_CONTACT;
            break;
          case 'TERMSERVICE':
            screenName = 'web page - term of service';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.OTHER;
            break;
          case 'POLICY':
            screenName = PAGE_NAME.ACCOUNT_PRIVACY;
            screenType = SCREEN_TYPES.ACCOUNT;
            screenClass = PAGE_CATEGORY.ACCOUNT_PRIVACY;
            break;
          case 'TERMCONDITION':
            screenName = PAGE_NAME.ACCOUNT_TERMS;
            screenType = SCREEN_TYPES.ACCOUNT;
            screenClass = PAGE_CATEGORY.ACCOUNT_TERMS;
            break;
          case 'SUPPLIERTRANSPARENCY':
            screenName = 'web page - supplier transparency';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.OTHER;
            break;
          case 'PROFILE_ORDERS':
            screenName = PAGE_NAME.ACCOUNT_ORDER_INFO;
            screenType = SCREEN_TYPES.ACCOUNT;
            screenClass = PAGE_CATEGORY.ACCOUNT_ORDER;
            break;
          case 'FittingHistory':
            screenName = 'fitting_history_fitting_events_webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.FITTINGS;
            break;
          case 'FittingUpcoming':
            screenName = 'upcoming_fittings_fitting_events_webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.FITTINGS;
            break;
          case 'Perks':
            screenName = 'rewards_perks_webview';
            screenType = SCREEN_TYPES.REWARDS;
            screenClass = PAGE_CATEGORY.PERK_PARTNER;
            break;
          case 'Play-FindTeeTime':
            screenName = 'play - tee times webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.PLAY;
            break;
          case 'HandicapUSGA':
            screenName = PAGE_NAME.USGA_SIGN_UP;
            screenType = SCREEN_TYPES.ACCOUNT;
            screenClass = PAGE_CATEGORY.ACCOUNT_USGA_HI;
            break;
          case 'BookNow':
            screenName = PAGE_NAME.BOOK_FITTING_INFO;
            screenType = SCREEN_TYPES.ACCOUNT;
            screenClass = PAGE_CATEGORY.BOOK_FITTING;
            break;
          case 'FittingOptions':
            screenName = 'fitting options - Find fitting webview';
            screenType = SCREEN_TYPES.FITTINGS;
            screenClass = SCREEN_CLASS.FITTINGS;
            break;
          case 'HomeDaily':
            screenName = PAGE_NAME.HOME_DAILY_INFO;
            screenType = SCREEN_TYPES.HOME;
            screenClass = PAGE_CATEGORY.HOME_DAILY;
            break;
          case 'JustInWeb':
            screenName = PAGE_NAME.JUST_IN_WEB;
            screenType = SCREEN_TYPES.HOME;
            screenClass = PAGE_CATEGORY.HOME;
            break;
          case 'ShopMainKeyWeb':
            screenName = PAGE_NAME.SHOP_MAIN_KEY_WEB;
            screenType = SCREEN_TYPES.SHOP;
            screenClass = PAGE_CATEGORY.SHOP_MAIN;
            break;
          case 'ShopTileKeyWeb':
            screenName = PAGE_NAME.SHOP_TILE_KEY_WEB;
            screenType = SCREEN_TYPES.SHOP;
            screenClass = PAGE_CATEGORY.SHOP_MAIN;
            break;
          case 'ShopProductCarousel':
            screenName = PAGE_NAME.SHOP_PRODUCT_CAROUSEL;
            screenType = SCREEN_TYPES.SHOP;
            screenClass = PAGE_CATEGORY.SHOP_PRODUCT_CAROUSEL;
            break;
          case 'PromotionWeb':
            screenName = PAGE_NAME.SHOP_PROMOTION_WEB;
            screenType = SCREEN_TYPES.SHOP;
            screenClass = PAGE_CATEGORY.SHOP_PROMOTION;
            break;
          case 'MainstaysWeb':
            screenName = PAGE_NAME.SHOP_MAINSTAYS_WEB;
            screenType = SCREEN_TYPES.SHOP;
            screenClass = PAGE_CATEGORY.SHOP_MAINSTAYS;
            break;
          case 'DiscountWeb':
            screenName = PAGE_NAME.SHOP_DISCOUNT_ITEM_WEB;
            screenType = SCREEN_TYPES.SHOP;
            screenClass = PAGE_CATEGORY.SHOP_DISCOUNT_ITEM;
            break;
          case 'TourTrashTermWeb':
            screenName = PAGE_NAME.TOUR_TRASH_TERM_CONDITION;
            screenType = SCREEN_TYPES.REWARDS;
            screenClass = PAGE_CATEGORY.TOUR_TRASH;
            break;
          case 'RewardProductWeb':
            screenName = PAGE_NAME.REWARD_PRODUCT_WEB;
            screenType = SCREEN_TYPES.REWARDS;
            screenClass = PAGE_CATEGORY.REWARD_PRODUCT;
            break;
          case 'PLACE_ORDER':
            screenName = PAGE_NAME.REWARD_PLACE_ORDER_WEB;
            screenType = SCREEN_TYPES.REWARDS;
            screenClass = PAGE_CATEGORY.REWARD_EARN_POINTS;
            break;
          case 'UPLOAD_PURCHASE':
            screenName = PAGE_NAME.REWARD_PURCHASE_WEB;
            screenType = SCREEN_TYPES.REWARDS;
            screenClass = PAGE_CATEGORY.REWARD_EARN_POINTS;
            break;
          case 'REGISTER_YOUR_CLUB':
            screenName = PAGE_NAME.REWARD_REGISTER_CLUB_WEB;
            screenType = SCREEN_TYPES.REWARDS;
            screenClass = PAGE_CATEGORY.REWARD_EARN_POINTS;
            break;
          case 'TRADE_IN_CLUB':
            screenName = PAGE_NAME.REWARD_TRADE_IN_WEB;
            screenType = SCREEN_TYPES.REWARDS;
            screenClass = PAGE_CATEGORY.REWARD_EARN_POINTS;
            break;
          default:
            trackingAvailable = false;
            break;
        }
        switch (prevRoute?.name) {
          case 'FittingDetails':
            screenName = 'upcoming fittings - fitting details webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.FITTINGS;
            break;
          case 'ShopDrops':
            screenName = 'shop - drops - promotion webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.PLP;
            break;
          case 'ShopTourTrash':
            screenName = 'shop - tour trash - promotion webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.SHOP;
            break;
          case 'ShopBasket':
            screenName = 'shop - ttb - cart check out webview';
            screenType = SCREEN_TYPES.WEB;
            screenClass = SCREEN_CLASS.SHOP;
            break;
          default:
            break;
        }
        break;
      //#region PLAY VIDEO
      case 'Video':
        screenName = 'play_video';
        pageTitle = currentParams?.video?.title || NOT_APPLICABLE;
        switch (prevRoute?.name) {
          case 'DrillsCollections':
            screenName = `drills collections ${prevRoute?.params?.tags}`;
            screenType = SCREEN_TYPES.DRILLS;
            screenClass = SCREEN_CLASS.DRILLS;
            break;
          case 'Home':
            screenName = 'home_play_video';
            screenType = SCREEN_TYPES.HOME;
            screenClass = SCREEN_CLASS.HOME;
            break;
          case 'Drills':
            screenName = 'drill_play_video';
            screenClass = PAGE_CATEGORY.DRILLS;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'DrillsByCategory':
            screenName = 'drill_category_play_video';
            screenClass = PAGE_CATEGORY.DRILLS;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'CoachDetail':
            screenName = 'drill_coach_play_video';
            screenClass = PAGE_CATEGORY.DRILLS;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'Entertainment':
            screenName = 'entertainment_play_video';
            screenClass = PAGE_CATEGORY.ENTERTAINMENT;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'ListArticlesByPlayer':
            screenName = 'entertainment_player_play_video';
            screenClass = PAGE_CATEGORY.ENTERTAINMENT;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'GearDeepDives':
            screenName = 'entertainment_gear_deep_dives_play_video';
            screenClass = PAGE_CATEGORY.ENTERTAINMENT;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'AllArticle':
            screenName = 'home_play_video';
            screenType = SCREEN_TYPES.HOME;
            screenClass = SCREEN_CLASS.HOME;
            break;
          case 'ShopTourTrash':
            screenName = 'shop - tour trash - play video';
            screenType = SCREEN_TYPES.TOURTRASH;
            screenClass = SCREEN_CLASS.SHOP;
            break;
          case 'DrillsSaved':
            screenName = 'drills - saved - play video';
            screenType = SCREEN_TYPES.DRILLS;
            screenClass = SCREEN_CLASS.DRILLS;
            break;
          case 'DrillsForYou':
            screenName = 'drills - for you - play video';
            screenType = SCREEN_TYPES.DRILLS;
            screenClass = SCREEN_CLASS.DRILLS;
            break;
          // case 'InsightDetail':
          //   screenType = 'insight detail';
          //   screenType = SCREEN_TYPES.DRILLS;
          //   screenClass = SCREEN_CLASS.DRILLS;
          //   break;
          case 'PlanAnnotations':
            switch (prevRoute?.params?.origin) {
              case 'LessonShortGameDetail':
                screenName =
                  'coach - single lessons - detail - plan annotations - play video';
                pageTitle = prevRoute?.params?.lessonTitle || '';
                screenType = SCREEN_TYPES.COACH;
                screenClass = SCREEN_CLASS.COACH;
                break;
              case 'PlanRoadMap':
                screenName = 'coach - roadmap - plan annotations - play video';
                pageTitle = prevRoute?.params?.lessonTitle || '';
                screenType = SCREEN_TYPES.COACH;
                screenClass = SCREEN_CLASS.COACH;
                break;
              case 'PutterImprove':
                screenName =
                  'coach - improve putting - detail - plan annotations - play video';
                pageTitle = prevRoute?.params?.lessonTitle || '';
                screenClass = SCREEN_CLASS.COACH;
                screenType = SCREEN_TYPES.COACH;
                break;
            }
            break;
          case 'PlanRoadMap':
            screenName = 'coach - roadmap - play video';
            screenType = SCREEN_TYPES.COACH;
            screenClass = SCREEN_CLASS.COACH;
            break;
          case 'TourTrashPDP':
            screenName = 'shop - tour trash - product - play video';
            screenType = SCREEN_TYPES.TOURTRASH;
            screenClass = SCREEN_CLASS.SHOP;
            break;
          case 'SwingMaintenanceDetail':
            screenName = 'coach - single lessons - detail - play video';
            screenType = SCREEN_TYPES.COACH;
            screenClass = SCREEN_CLASS.COACH;
            break;
          case 'LessonShortGameDetail':
            screenName = 'coach - single lessons - detail - play video';
            screenType = SCREEN_TYPES.COACH;
            screenClass = SCREEN_CLASS.COACH;
            break;
          case 'PlanRelevantLessons':
            screenName = 'coach - lessons - play video';
            screenType = SCREEN_TYPES.COACH;
            screenClass = SCREEN_CLASS.COACH;
            break;
          case 'ShopDrops':
            screenName = 'shop - drops - product - play video';
            screenType = SCREEN_TYPES.MEMBERSHOP;
            screenClass = SCREEN_CLASS.PDP;
            break;
          case 'PlanStarted':
          case 'PlanGoals':
            screenName = 'coach - learn more video';
            screenType = SCREEN_TYPES.COACH;
            screenClass = SCREEN_CLASS.COACH;
            break;
          case 'PutterImprove':
            screenName = 'coach - improve putting - detail - play video';
            screenClass = SCREEN_CLASS.COACH;
            screenType = SCREEN_TYPES.COACH;
            break;
          default:
            break;
        }
        break;
      //#region NATIVE COACH
      case 'PlanTabs':
        trackingAvailable = false;
        break;
      case 'PlanStarted':
        if (prevRoute?.name === 'PlanTabs') {
          trackingAvailable = false;
          break;
        }
        screenName = 'coach - first use';
        pageTitle = `Get Started`;
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'PlanGoals':
        screenName = 'coach - goals';
        pageTitle = `Goals`;
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'PlanSwingProfile':
        screenName = 'coach - roadmap - swing index details';
        pageTitle = `Swing Index Details`;
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'PlanRoadMap':
        screenName = 'coach - roadmap';
        pageTitle = 'Roadmap';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'PlanRelevantLessons':
        screenName = 'coach - lessons';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'CoachQuickFix':
        screenName = 'coach - single lessons';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        pageTitle = 'Single Lessons Upload';
        break;
      case 'PutterImprove':
        screenName = 'coach - improve putting - detail';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        pageTitle = 'Putting Challenge';
        break;
      case 'SwingMaintenanceDetail':
        screenName = 'coach - single lessons - detail';
        pageTitle = 'Swing Analysis';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'CameraView':
        screenName = 'coach - camera view';
        pageTitle = 'Camera View';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'VideoPreview':
        screenName = 'coach - video preview';
        screenClass = SCREEN_CLASS.COACH;
        pageTitle = 'Record Driver Face Video';
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'SelectVideoMenu':
        screenName = 'coach - select video';
        pageTitle = 'Choose A Face On Video Of Your Driver';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'DescribeShot':
        screenName = 'coach - video upload description';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'ShortPutInformation':
        screenName = 'coach - video upload description - putt return';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'LongPuttInformation':
        screenName = 'coach - video upload description - putt';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      case 'PlanAnnotations':
        switch (currentRoute?.params?.origin) {
          case 'PlanRoadMap':
            screenName = 'coach - roadmap - plan annotations';
            pageTitle = currentParams?.lessonTitle || '';
            screenClass = SCREEN_CLASS.COACH;
            screenType = SCREEN_TYPES.COACH;
            break;
          case 'LessonShortGameDetail':
            screenName = 'coach - single lessons - detail - plan annotations';
            pageTitle = currentParams?.lessonTitle || '';
            screenClass = SCREEN_CLASS.COACH;
            screenType = SCREEN_TYPES.COACH;
            break;
          case 'PutterImprove':
            screenName = 'coach - improve putting - detail - plan annotations';
            pageTitle = currentParams?.lessonTitle || '';
            screenClass = SCREEN_CLASS.COACH;
            screenType = SCREEN_TYPES.COACH;
            break;
        }
        break;
      case 'LessonShortGameDetail':
        screenName = 'coach - single lessons - detail';
        pageTitle = 'Swing Analysis';
        screenClass = SCREEN_CLASS.COACH;
        screenType = SCREEN_TYPES.COACH;
        break;
      //#endregion
      //#region DRILLS
      case 'DrillsBrowse':
        screenName = 'drills - browse';
        pageTitle = 'Browse';
        screenType = SCREEN_TYPES.DRILLS;
        screenClass = SCREEN_CLASS.DRILLS;
        break;
      case 'DrillsSaved':
        screenName = 'drills - saved';
        pageTitle = 'Saved';
        screenType = SCREEN_TYPES.DRILLS;
        screenClass = SCREEN_CLASS.DRILLS;
        break;
      case 'DrillsForYou':
        screenName = 'drills - for you';
        pageTitle = 'Your Practice Drills';
        screenType = SCREEN_TYPES.DRILLS;
        screenClass = SCREEN_CLASS.DRILLS;
        break;
      case 'DrillsCollections':
        screenName = 'drills collections';
        switch (prevRoute?.name) {
          case 'DrillsBrowse':
            screenName = `drills - collections - ${currentParams?.tags || ''}`;
            break;
          case 'DrillsSaved':
            screenName = 'drills collections saved';
            break;
          case 'DrillsForYou':
            screenName = 'drills collections for you';
            break;
        }
        screenType = SCREEN_TYPES.DRILLS;
        screenClass = SCREEN_CLASS.DRILLS;
        break;
      //#endregion
      //#region ONBOARDING
      case 'Onboarding':
        screenName = 'onboarding';
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.ON_BOARDING;
        break;
      case 'SignUp':
        screenName = 'onboarding_create_credentials';
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      case 'QuizWelcome':
        screenName = `onboarding - you're in`;
        pageTitle = `Welcome, you're in`;
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      case 'QuizRegion':
        screenName = `onboarding - country`;
        pageTitle = `Please Select Your Country`;
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      case 'QuizName':
        screenName = 'onboarding - first last name';
        pageTitle = `What's your Name?`;
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      case 'QuizGender':
        screenName = 'onboarding - gender';
        pageTitle = 'What gender do you identify as?';
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      case 'QuizBirthday':
        screenName = 'onboarding - dob';
        pageTitle = 'What is your birthday?';
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      case 'QuizHandicap':
        screenName = 'onboarding - current handicap/average score';
        pageTitle = `What's your handicap or Average Score?`;
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      case 'QuizScoreTarget':
        screenName = 'target score';
        pageTitle = `What's your target 18-hole score?`;
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        break;
      case 'QuizGolfCourse':
        screenName = 'onboarding - home course';
        pageTitle = 'Do you have a home course?';
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      case 'QuizYearsOfExperience':
        screenName = 'onboarding - golf frequency';
        pageTitle = 'How long have you been playing golf?';
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      case 'QuizTargetHandicapOrAvg':
        screenName = 'target handicap/average score';
        pageTitle = `What's your goal for handicap or Average Score?`;
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        break;
      case 'QuizCommonMisHit':
        screenName = 'mis-hit';
        pageTitle = `What's your most common mis-hit?`;
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        break;
      case 'QuizPuttingMisHit':
        screenName = 'putting mis-hit';
        pageTitle = `What area needs help with putting?`;
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        break;
      case 'Membership':
        screenName = 'select plan';
        pageTitle = 'Upgrade to champion';
        screenType = SCREEN_TYPES.ONBOARDING;
        screenClass = SCREEN_CLASS.SIGNUP;
        break;
      //#endregion
      //#region PLAY
      case 'Play':
        screenName = PAGE_NAME.PLAY_TRACK;
        screenClass = PAGE_CATEGORY.PLAY_TRACK;
        screenType = SCREEN_TYPES.PLAY;
        break;
      case 'HANDICAP':
        screenName = PAGE_NAME.PLAY_TRACK_HANDICAP;
        screenClass = PAGE_CATEGORY.PLAY_TRACK;
        screenType = SCREEN_TYPES.PLAY;
        switch (prevRoute?.name) {
          case 'MYTM':
            screenName = PAGE_NAME.PLAY_TAB_USGA_SUBMITTED;
            screenClass = PAGE_CATEGORY.PLAY_ROUND;
            screenType = SCREEN_TYPES.PLAY;
            break;
        }
        break;
      case 'ScoreAddCourse':
        screenName = PAGE_NAME.PLAY_FIND_COURSE;
        screenClass = PAGE_CATEGORY.PLAY_FIND_COURSE;
        screenType = SCREEN_TYPES.PLAY;
        break;
      case 'Nearby':
      case 'Recent':
        screenName = 'post score - location';
        pageTitle = 'select course';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.PLAY;
        break;
      case 'ScoreAddEditDetails':
        screenName = 'post_score_details';
        screenClass = PAGE_CATEGORY.PLAY_POST_A_SCORE;
        screenType = SCREEN_TYPES.PLAY;
        break;
      case 'Scores':
        screenName = 'scores - rounds';
        pageTitle = 'Scores';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Stats':
        screenName = 'stats';
        pageTitle = 'Stats';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'ScoreDetail':
        screenName = 'round_stats_detail';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      //#endregion
      //#region STATS
      case 'FilterOptions':
        switch (currentParams?.type) {
          case 'M':
            screenName = 'stats - filter - roundMode';
            pageTitle = 'Filter by Round Mode';
            break;
          case 'D':
            screenName = 'stats - filter - date';
            pageTitle = 'Filter by Date Range';
            break;
          case 'C':
            screenName = 'stats - filter - course';
            pageTitle = 'Filter by Course';
            break;
          case 'R':
            screenName = 'stats - filter - round';
            pageTitle = 'Filter by Round';
            break;
          default:
            break;
        }
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'StrokesGainedBaseline':
        screenName = 'stats - baseline';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'ApproachStats':
        screenName = 'round stats detail - classic - approach';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'PuttingStats':
        screenName = 'stats - putting';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Driving-All':
        screenName = 'stats - driving - all';
        pageTitle = 'Driving Stats - All';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Driving-Driver':
        screenName = 'stats - driving - driver';
        pageTitle = 'Driving Stats - Driver';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Driving-3FW':
        screenName = 'stats - driving -3fw';
        pageTitle = 'Driving Stats - 3FW';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Driving-Other':
        screenName = 'stats - driving - other';
        pageTitle = 'Driving Stats - Other';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Approach-100-150':
        screenName = 'stats - approach - 100150';
        pageTitle = 'Approach Stats - 100-150';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Approach-150-200':
        screenName = 'stats - approach - 150200';
        pageTitle = 'Approach Stats - 150-200';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Approach-200-250':
        screenName = 'stats - approach - 200250';
        pageTitle = 'Approach Stats - 200-250';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Approach-250-1000':
        screenName = 'stats - approach - 250';
        pageTitle = 'Approach Stats - 250';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Short-0-25':
        screenName = 'stats - short - 25';
        pageTitle = 'Short Stats - 25';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Short-25-50':
        screenName = 'stats - short - 2550';
        pageTitle = 'Short Stats - 25-50';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Short-50-75':
        screenName = 'stats - short - 5075';
        pageTitle = 'Short Stats - 50-75';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      case 'Short-75-100':
        screenName = 'stats - short - 75100';
        pageTitle = 'Short Stats - 75-100';
        screenType = SCREEN_TYPES.PLAY;
        screenClass = SCREEN_CLASS.STATS;
        break;
      //#endregion
      //#region HOME
      case 'Home':
        screenName = PAGE_NAME.HOME_MAIN_INFO;
        screenType = SCREEN_TYPES.HOME;
        screenClass = SCREEN_CLASS.HOME_MAIN;
        break;
      case 'AllArticle':
        screenName = 'home_list_article';
        screenType = SCREEN_TYPES.HOME;
        screenClass = SCREEN_CLASS.HOME;
        break;
      case 'FeedArticle':
        screenName = 'article_detail';
        screenType = SCREEN_TYPES.CONTENT;
        screenClass = PAGE_CATEGORY.CONTENT;
        switch (prevRoute?.name) {
          case 'Home':
            screenName = 'home_article_detail';
            screenType = SCREEN_TYPES.HOME;
            screenClass = SCREEN_CLASS.HOME;
            break;
          case 'Drills':
            screenName = 'drill_article_detail';
            screenClass = PAGE_CATEGORY.DRILLS;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'CoachDetail':
            screenName = 'drill_coach_article_detail';
            screenClass = PAGE_CATEGORY.DRILLS;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'Entertainment':
            screenName = 'entertainment_article_detail';
            screenClass = PAGE_CATEGORY.ENTERTAINMENT;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'ListArticlesByPlayer':
            screenName = 'entertainment_player_article_detail';
            screenClass = PAGE_CATEGORY.ENTERTAINMENT;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          case 'GearDeepDives':
            screenName = 'entertainment_gear_deep_dives_article_detail';
            screenClass = PAGE_CATEGORY.ENTERTAINMENT;
            screenType = SCREEN_TYPES.CLUBHOUSE;
            break;
          default:
            break;
        }
        break;
      case 'FeedStories':
        screenName = 'home - feed stories';
        screenType = SCREEN_TYPES.HOME;
        screenClass = SCREEN_CLASS.HOME;
        break;
      //#endregion
      //#region FITTINGS
      case 'ClubWelcome':
        screenName = 'club recommender - intro';
        pageTitle = 'Built For Your Game';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubGender':
        screenName = 'club recommender - club type';
        pageTitle = 'What type of clubs do you play?';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubBirthday':
        screenName = 'club recommender - club birthday';
        pageTitle = 'What is your birthday?';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubHanded':
        screenName = 'club recommender - club handed';
        pageTitle = 'Are you left or right handed?';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubHandicap':
        screenName = 'club recommender - club handicap';
        pageTitle = `What's your handicap?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubHeight':
        screenName = 'club recommender - club height';
        pageTitle = `What is your height?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubDriverData':
        screenName = 'club recommender - club driver data';
        pageTitle = `Do you know your driver shot data?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubBallSpeed':
        screenName = 'club recommender - yes - club ball speed';
        pageTitle = `What's your ball speed?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubLaunchAngle':
        screenName = 'club recommender - yes - club launch angle';
        pageTitle = `What is your launch angle?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubBackSpin':
        screenName = 'club recommender - yes - club backspin';
        pageTitle = `What is your backspin?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubSideSpin':
        screenName = 'club recommender - yes - club sidespin';
        pageTitle = `What is your sidespin?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubDeviationAngle':
        screenName = 'club recommender - yes - club deviation angle';
        pageTitle = `What is your deviation angle?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubPeakHeight':
        screenName = 'club recommender - yes - club peak height';
        pageTitle = `What is your peak height?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubHeadSpeed':
        screenName = 'club recommender - yes - club head speed';
        pageTitle = `What is your club head speed?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubAngleOfAttack':
        screenName = 'club recommender - yes - club angle of attack';
        pageTitle = `What is your angle of attack?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubPath':
        screenName = 'club recommender - yes - club path';
        pageTitle = `What is your club path?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubLie':
        screenName = 'club recommender - yes - club lie';
        pageTitle = `What is your club lie at impact?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubLoft':
        screenName = 'club recommender - yes - club loft';
        pageTitle = `What is your club loft at impact?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubFaceAngleTargetLine':
        screenName = 'club recommender - yes - club face angle target line';
        pageTitle = `What is your face angle to target line?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubFaceAnglePath':
        screenName = 'club recommender - yes - club face angle path';
        pageTitle = `What is your face angle relative to club path?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubToeHeel':
        screenName = 'club recommender - yes - club toe heel';
        pageTitle = `What is your toe/heel impact location?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubHighLow':
        screenName = 'club recommender - yes - club high low';
        pageTitle = `What is your high/low impact location?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubIron':
        screenName = 'club recommender - no - club 7 irons';
        pageTitle = `How far do you carry your 7 iron?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubDriverBallFlight':
        screenName = 'club recommender - no - club driver ball flight';
        pageTitle = `What is your driver ball flight?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubDriverShotShape':
        screenName = 'club recommender - no - club driver shot shape';
        pageTitle = `What is your typical driver shot shape?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubDesiredShotShape':
        screenName = 'club recommender - no - club desired shot shape';
        pageTitle = `What is your desired driver shot shape?`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubResults':
        screenName = 'club recommender - club results';
        pageTitle = `Results`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'ClubDetails':
        screenName = 'club recommender - club details';
        pageTitle = `Club Stats`;
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.CLUB_RECOMMENDER;
        break;
      case 'FittingOptions':
        screenName = 'fitting options';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.FITTINGS;
        break;
      case 'FittingHistory':
        screenName = 'fitting history';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.FITTINGS;
        break;
      case 'FittingUpcoming':
        screenName = 'fitting upcoming';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.FITTINGS;
        break;
      case 'FittingRecommendations':
        screenName = 'fitting recommendations';
        pageTitle = 'Fitting Recommendation';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.FITTINGS;
        break;
      case 'FittingDetails':
        screenName = 'fitting details';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.FITTINGS;
        break;
      case 'FittingShotData':
        screenName = 'fitting shot data';
        screenType = SCREEN_TYPES.FITTINGS;
        screenClass = SCREEN_CLASS.FITTINGS;
        break;
      //#endregion
      //#region WITB
      case 'ActiveClubs':
        screenName = PAGE_NAME.ACCOUNT_WITB_ACTIVE_LIST;
        screenClass = PAGE_CATEGORY.ACCOUNT_WITB_ACTIVE;
        screenType = SCREEN_TYPES.ACCOUNT;
        break;
      case 'InactiveClubs':
        screenName = PAGE_NAME.ACCOUNT_WITB_INACTIVE_LIST;
        screenClass = PAGE_CATEGORY.ACCOUNT_WITB_INACTIVE;
        screenType = SCREEN_TYPES.ACCOUNT;
        break;
      case 'CompareClubs':
        screenName = 'bag_compare_clubs';
        screenClass = SCREEN_CLASS.BAG;
        screenType = SCREEN_TYPES.WITB;
        break;
      case 'MyBagAdd':
        screenName = 'bag_add_club';
        screenClass = SCREEN_CLASS.BAG;
        screenType = SCREEN_TYPES.WITB;
        break;
      case 'MyBagAddLoft':
        screenName = 'bag_add_loft';
        screenClass = SCREEN_CLASS.BAG;
        screenType = SCREEN_TYPES.WITB;
        break;
      case 'MyBagAddBrand':
        screenName = 'bag_add_details_select_brand';
        screenClass = SCREEN_CLASS.BAG;
        screenType = SCREEN_TYPES.WITB;
        break;
      case 'MyBagAddModel':
        screenName = 'bag_add_details_select_model';
        screenClass = SCREEN_CLASS.BAG;
        screenType = SCREEN_TYPES.WITB;
        break;
      case 'MyBagAddShaftFlex':
        screenName = 'bag_add_details_select_shaft_flex';
        screenClass = SCREEN_CLASS.BAG;
        screenType = SCREEN_TYPES.WITB;
        break;
      case 'MyBagAddSpecs':
        screenName = 'bag_select_specs';
        screenClass = SCREEN_CLASS.BAG;
        screenType = SCREEN_TYPES.WITB;
        break;
      case 'MyBagClubDetails':
        //handle inside screen
        trackingAvailable = false;
        break;
      case 'MyBagScreens':
        //handle inside screen in case of no club
        trackingAvailable = false;
        break;
      case 'BagCompareDetail':
        switch (currentParams?.itemFirst?.clubFamily?.name) {
          case CLUB_NAME.PUTTER:
            screenName = 'bag - club comparison - putter';
            break;
          default:
            screenName = 'bag_club_comparison';
            break;
        }
        // pageTitle = 'Club Comparison';
        screenClass = SCREEN_CLASS.BAG;
        screenType = SCREEN_TYPES.WITB;
        break;
      //#endregion
      //#region USER PROFILE
      case 'Profile':
        screenName = PAGE_NAME.ACCOUNT_MAIN_INFO;
        screenClass = PAGE_CATEGORY.ACCOUNT;
        screenType = SCREEN_TYPES.ACCOUNT;
        break;
      case 'Settings':
        screenName = PAGE_NAME.ACCOUNT_SETTING_INFO;
        screenClass = PAGE_CATEGORY.ACCOUNT;
        screenType = SCREEN_TYPES.ACCOUNT;
        break;
      case 'ResetPassword':
        screenName = PAGE_NAME.ACCOUNT_RESET_PASS;
        screenClass = PAGE_CATEGORY.ACCOUNT_RESET_PASS;
        screenType = SCREEN_TYPES.ACCOUNT;
        break;
      case 'MyGameProfile':
        screenName = PAGE_NAME.ACCOUNT_MY_GAME_PROFILE_INFO;
        screenClass = PAGE_CATEGORY.ACCOUNT_MY_GAME_PROFILE;
        screenType = SCREEN_TYPES.ACCOUNT;
        break;
      case 'UniqueCode':
        screenName = 'user profile - settings - unique code';
        pageTitle = 'Discounts & Promotions';
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.SETTINGS;
        break;
      case 'Notifications':
        screenName = PAGE_NAME.ACCOUNT_INBOX_INFO;
        screenClass = PAGE_CATEGORY.ACCOUNT_INBOX;
        screenType = SCREEN_TYPES.ACCOUNT;
        break;
      //#region GAME PROFILE
      case 'QuizRounds':
        screenName = 'user profile - game profile - rounds';
        pageTitle = 'How many rounds do you play a month?';
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'EditQuizYearsOfExperience':
        screenName = 'onboarding - golf frequency';
        pageTitle = 'How long have you been playing golf?';
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'EditQuizHandicap':
        screenName = 'onboarding - club handicap';
        pageTitle = `What's your current handicap?`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizDriveLength':
        screenName = 'user profile - game profile - drive distance';
        pageTitle = `What's your max distance on a well-hit drive?`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizStrength':
        screenName = 'user profile - game profile - strongest area';
        pageTitle = `What is the strongest part of your game? Select all that apply.`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizWeakness':
        screenName = 'user profile - game profile - weakest area';
        pageTitle = `What is the weakest part of your game? Select all that apply.`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizShotShape':
        screenName = 'user profile - game profile - shot shape';
        pageTitle = `What's your most common shot shape?`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizBallStrike':
        screenName = 'user profile - game profile - ball strike';
        pageTitle = `What's your most common ball strike?`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizBallMiss':
        screenName = 'mis-hit';
        pageTitle = `What's your most common mis-hit?`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizAvoid':
        screenName = 'user profile - game profile - eliminate';
        pageTitle = `What shot type do you want to avoid/eliminate from your game?`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizFear':
        screenName = 'user profile - game profile - scariest shot';
        pageTitle = `What shot scares you the most? Select all that apply.`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizPro':
        screenName = 'user profile - game profile - favorite players';
        pageTitle = `Who are your favorite members of Team Taylormade?`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      case 'QuizHanded':
        screenName = 'on boarding - club handed';
        pageTitle = `Are you left or right handed?`;
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.ONBOARDING;
        break;
      //#endregion
      //#endregion
      //#region SHOP
      case 'ShopDrops':
        screenName = 'shop - drops';
        pageTitle = 'Exclusive Product Drops';
        screenClass = SCREEN_CLASS.PLP;
        screenType = SCREEN_TYPES.MEMBERSHOP;
        break;
      case 'ShopPDP':
        screenName = 'shop - drops - product';
        pageTitle = currentRoute?.params?.productName;
        screenClass = SCREEN_CLASS.PDP;
        screenType = SCREEN_TYPES.MEMBERSHOP;
        break;
      case 'ShopTTB':
        screenName = 'shop - ttb';
        pageTitle =
          'Customize your club, try it on your course and on your time.';
        screenClass = SCREEN_CLASS.SHOP;
        screenType = SCREEN_TYPES.TTB;
        break;
      case 'ShopBasket':
        screenName = 'cart';
        pageTitle = 'Shopping Cart';
        screenClass = SCREEN_CLASS.SHOP;
        screenType = SCREEN_TYPES.MEMBERSHOP;
        break;
      case 'ShopTourTrash':
        screenName = 'shop - tour trash';
        pageTitle =
          'Enter for a chance to win clubs used by TaylorMade Tour Players.';
        screenClass = SCREEN_CLASS.SHOP;
        screenType = SCREEN_TYPES.TOURTRASH;
        break;
      case 'TourTrashPDP':
        screenName = PAGE_NAME.TOUR_TRASH_INFO;
        screenClass = PAGE_CATEGORY.TOUR_TRASH;
        screenType = SCREEN_TYPES.REWARDS;
        break;
      case 'TryThenBuyPDP':
        if (currentParams?.charge) {
          screenName = 'shop - ttb - keep this club';
          pageTitle =
            'Your journey to better golf begins now. Enjoy the grind!';
          screenClass = SCREEN_CLASS.SHOP;
        } else {
          screenName = 'shop - ttb - previous trials club';
          pageTitle = '';
          screenClass = SCREEN_CLASS.PDP;
        }
        screenType = SCREEN_TYPES.TTB;
        break;
      case 'TryThenBuyReturn':
        screenName = 'shop - ttb - return club survey';
        pageTitle = 'Mind if we ask why?';
        screenClass = SCREEN_CLASS.SHOP;
        screenType = SCREEN_TYPES.TTB;
        break;
      case 'MemberBenefit':
        screenName = 'shop - perks';
        pageTitle = '';
        screenClass = SCREEN_CLASS.SHOP;
        screenType = SCREEN_TYPES.PERKS;
        break;
      case 'BenefitPDP':
        screenName = PAGE_NAME.PERK_PARTNER_INFO;
        screenClass = PAGE_CATEGORY.PERK_PARTNER;
        screenType = SCREEN_TYPES.REWARDS;
        break;
      //#endregion
      //#region GHIN
      case 'HandicapUSGA':
        //handle inside screen
        trackingAvailable = false;
        break;
      case 'GhinConfirmCourse':
        screenName = 'post score - map USGA course';
        pageTitle = 'Confirm Course Location';
        screenClass = SCREEN_CLASS.PLAY;
        screenType = SCREEN_TYPES.PLAY;
        break;
      case 'GhinConfirmTee':
        screenName = 'post score - map USGA tee';
        pageTitle = 'Confirm Course Location';
        screenClass = SCREEN_CLASS.PLAY;
        screenType = SCREEN_TYPES.PLAY;
        break;
      //#endregion
      case 'ReferralInputEmail':
        screenName = 'input friends email';
        pageTitle = 'Enter a friend’s email address';
        screenClass = SCREEN_CLASS.REFERRAL;
        screenType = SCREEN_TYPES.SETTINGS;
        break;
      case 'ShippingAddress':
        screenName = 'input address';
        pageTitle = 'Enter Your Shipping Address';
        screenClass = SCREEN_CLASS.USER_PROFILE;
        screenType = SCREEN_TYPES.SETTINGS;
        break;
      case 'Drills':
        screenName = PAGE_NAME.CLUBHOUSE_DRILL_INFO;
        screenClass = PAGE_CATEGORY.CLUBHOUSE_DRILL;
        screenType = SCREEN_TYPES.CLUBHOUSE;
        break;
      case 'Entertainment':
        screenName = PAGE_NAME.CLUBHOUSE_ENTERTAINMENT_INFO;
        screenClass = PAGE_CATEGORY.CLUBHOUSE_ENTERTAINMENT;
        screenType = SCREEN_TYPES.CLUBHOUSE;
        break;
      case 'TourorStories':
        screenName = PAGE_NAME.CLUBHOUSE_TOURSTORY;
        screenClass = PAGE_CATEGORY.CLUBHOUSE;
        screenType = SCREEN_TYPES.CLUBHOUSE;
        break;
      case 'DrillsByCategory':
        screenName = PAGE_NAME.CLUBHOUSE_DRILL_CATEGORY;
        screenClass = PAGE_CATEGORY.CLUBHOUSE;
        screenType = SCREEN_TYPES.CLUBHOUSE;
        break;
      case 'CoachDetail':
        screenName = PAGE_NAME.CLUBHOUSE_DRILL_COACH;
        screenClass = PAGE_CATEGORY.CLUBHOUSE;
        screenType = SCREEN_TYPES.CLUBHOUSE;
        break;
      case 'GearDeepDives':
        screenName = PAGE_NAME.CLUBHOUSE_GEAR_DEEP_DIVE;
        screenClass = PAGE_CATEGORY.CLUBHOUSE;
        screenType = SCREEN_TYPES.CLUBHOUSE;
        break;
      case 'ListArticlesByPlayer':
        screenName = PAGE_NAME.CLUBHOUSE_ARTICLE_PLAYER;
        screenClass = PAGE_CATEGORY.CLUBHOUSE;
        screenType = SCREEN_TYPES.CLUBHOUSE;
        break;
      case 'ShopTabScreen':
        screenName = PAGE_NAME.SHOP_MAIN_INFO;
        screenClass = PAGE_CATEGORY.SHOP_MAIN;
        screenType = SCREEN_TYPES.SHOP;
        break;
      case 'Rewards':
        screenName = PAGE_NAME.REWARDS_MAIN_INFO;
        screenClass = PAGE_CATEGORY.REWARDS_MAIN;
        screenType = SCREEN_TYPES.REWARDS;
        break;
      case 'FindACourse':
        screenName = PAGE_NAME.PLAY_FIND_COURSE;
        screenClass = PAGE_CATEGORY.PLAY_FIND_COURSE;
        screenType = SCREEN_TYPES.PLAY;
        break;
      case 'CourseMap':
        screenName = PAGE_NAME.PLAY_COURSE_MAP;
        screenClass = PAGE_CATEGORY.PLAY_TRACK;
        screenType = SCREEN_TYPES.PLAY;
        break;
      case 'MenuPlayRound':
        screenName = PAGE_NAME.PLAY_ON_COURSE_MENU;
        screenClass = PAGE_CATEGORY.PLAY_ON_COURSE;
        screenType = SCREEN_TYPES.PLAY;
        break;
      case 'RoundOverview':
        screenName = PAGE_NAME.PLAY_ROUND_OVERVIEW;
        screenClass = PAGE_CATEGORY.PLAY_ROUND;
        screenType = SCREEN_TYPES.PLAY;
        switch (prevRoute?.name) {
          case 'HANDICAP':
            screenName = PAGE_NAME.PLAY_ROUND_OVERVIEW_USGA;
            screenClass = PAGE_CATEGORY.PLAY_ROUND;
            screenType = SCREEN_TYPES.PLAY;
            break;
        }
        break;
      case 'UserQuiz':
        trackingAvailable = false;
        break;
      case 'Introduce':
        trackingAvailable = false;
        break;
      case 'MYTM':
        trackingAvailable = false;
        switch (prevRoute?.name) {
          case 'HANDICAP':
            screenName = PAGE_NAME.PLAY_TAB_TRACKED_ROUNDS;
            screenClass = PAGE_CATEGORY.PLAY_ROUND;
            screenType = SCREEN_TYPES.PLAY;
            trackingAvailable = true;
            break;
        }
        break;
    }

    if (!trackingAvailable) {
      return;
    }
    GA_logScreenViewV2(screenName, screenClass, screenType, screenType);
  };

  return (
    <NavigationContainer
      ref={navigationRef}
      onReady={() => {
        routeNameRef.current = navigationRef.current.getCurrentRoute().name;
        routeInfoRef.current = navigationRef.current.getCurrentRoute();
      }}
      onStateChange={() => {
        const previousRouteName = routeNameRef.current;
        const previousRouteInfo = routeInfoRef.current;
        const currentRouteName = navigationRef.current.getCurrentRoute().name;
        const currentRoute = navigationRef.current.getCurrentRoute();
        const currentOptions = navigationRef.current.getCurrentOptions();
        const screenTitle = currentOptions?.title
          ? currentOptions?.title
          : currentOptions?.headerTitle || currentOptions?.tabBarLabel || '';
        if (previousRouteName !== currentRouteName) {
          trackGoogleAnalyticsScreen(
            currentRoute,
            previousRouteInfo,
            screenTitle,
          );
        }
        routeNameRef.current = currentRouteName;
        routeInfoRef.current = currentRoute;
        setCurrentRouteState(currentRoute);
      }}
    >
      <Root.Navigator initialRouteName="Initial" mode="modal" headerMode="none">
        <Root.Screen
          name="Initial"
          component={Initial}
          options={horizontalScreenAnimation}
        />
        <Root.Screen
          name="Onboarding"
          component={OnLaunch}
          options={horizontalScreenAnimation}
        />
        <Root.Screen
          name="SignUp"
          component={SignUp}
          options={noLoadScreenAnimation}
        />
        <Root.Screen
          name="VerifyOtp"
          component={VerifyOtp}
          options={noLoadScreenAnimation}
        />
        <Root.Screen
          name="ResetPassword"
          component={ResetPassword}
          options={horizontalScreenAnimation}
        />
        <Root.Screen
          name="Quiz"
          component={Quiz}
          options={horizontalScreenAnimation}
        />
        <Root.Screen
          name="IntroQuiz"
          component={introQuiz}
          options={horizontalScreenAnimation}
        />
        <Root.Screen
          name="App"
          component={App}
          options={horizontalScreenAnimation}
        />
        <Root.Screen
          name="Profile"
          component={Profile}
          options={verticalScreenAnimation}
        />
        <Root.Screen
          name="MyBag"
          component={MyBag}
          options={{...horizontalScreenAnimation, gestureEnabled: false}}
        />
        <Root.Screen
          name="WebView"
          component={WebView}
          options={verticalScreenAnimation}
        />
        <Root.Screen
          name="FeedArticle"
          component={FeedArticle}
          options={horizontalScreenAnimation}
        />
        <Root.Screen
          name="Fittings"
          component={Fittings}
          options={{...verticalScreenAnimation, gestureEnabled: false}}
        />
        <Root.Screen
          name="ScoresStats"
          component={ScoresStats}
          options={{...verticalScreenAnimation, gestureEnabled: false}}
        />
        <Root.Screen
          name="Insights"
          component={Insights}
          options={horizontalScreenAnimation}
        />
        <Root.Screen
          name="Video"
          component={Video}
          options={verticalScreenAnimation}
        />
        <Root.Screen
          name="Shop"
          component={Shop}
          options={verticalScreenAnimation}
        />
        <Root.Screen
          name="ClubRecommender"
          component={ClubRecommender}
          options={verticalScreenAnimation}
        />
        <Root.Screen
          name="NativeCoach"
          component={NativeCoach}
          options={verticalScreenAnimation}
        />
        <Root.Screen
          name="NativeCoachHorizontal"
          component={nativeCoachHorizontal}
          options={horizontalScreenAnimation}
        />
        <Root.Screen
          name="HandicapHorizontal"
          component={Handicap}
          options={horizontalLeftToRightScreenAnimation}
        />
        <Root.Screen
          name="TourorStories"
          component={TourorStories}
          options={{
            ...horizontalScreenAnimation,
            gestureEnabled: false,
            headerShown: false,
          }}
        />
        <Root.Screen
          name="EntertainmentStack"
          component={EntertainmentStack}
          options={{
            ...verticalScreenAnimation,
            gestureEnabled: false,
          }}
        />
        <Root.Screen
          name="DrillsStack"
          component={DrillsStack}
          options={{
            ...horizontalScreenAnimation,
            gestureEnabled: true,
          }}
        />
        <Root.Screen
          name="GearStack"
          component={GearStack}
          options={{
            ...horizontalScreenAnimation,
            gestureEnabled: true,
          }}
        />
        <Root.Screen
          name="Introduce"
          component={Introduce}
          options={{horizontalScreenAnimation, gestureEnabled: false}}
        />
        <Root.Screen
          name="VerifyBirthDay"
          component={verifyBirthDay}
          options={{...noLoadScreenAnimation, gestureEnabled: false}}
        />
        <Root.Screen
          name="GolferQuestionnaire"
          component={golferQuestionnaire}
          options={horizontalScreenAnimation}
        />
      </Root.Navigator>
      <Toast ref={ref => Toast.setRef(ref)} />
    </NavigationContainer>
  );
}
