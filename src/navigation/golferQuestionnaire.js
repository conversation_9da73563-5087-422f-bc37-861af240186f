import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import GolferQuestionnaire from 'screens/Rewards/component/GolferQuestionnaire/GolferQuestionnaire';

const QuestionnaireStack = createStackNavigator();

export default ({navigation}) => {
  const {navigate, replace} = navigation;
  return (
    <QuestionnaireStack.Navigator>
      <QuestionnaireStack.Screen
        name="GolferQuestionnaire"
        component={GolferQuestionnaire}
        options={{
          headerShown: false,
        }}
      />
    </QuestionnaireStack.Navigator>
  );
};
