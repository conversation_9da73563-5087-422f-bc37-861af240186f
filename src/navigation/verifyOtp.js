import React from 'react';
import {View} from 'react-native';
import {createStackNavigator} from '@react-navigation/stack';
import VerifyOtp from '../screens/VerifyOtp';

const VerifyOtpStack = createStackNavigator();

export default ({navigation: {navigate}}) => (
  <VerifyOtpStack.Navigator
    screenOptions={{
      cardStyle: {
        backgroundColor: '#e5e5e5',
      },
      headerTitle: '',
      headerStyle: {
        backgroundColor: '#e5e5e5',
        shadowColor: 'transparent',
      },
      headerTitleStyle: {
        alignSelf: 'center',
      },
    }}
  >
    <VerifyOtpStack.Screen
      name='VerifyOtp'
      component={VerifyOtp}
      options={{
        headerLeft: () => <View />,
        headerRight: () => <View />,
        headerShown: false,
      }}
    />
  </VerifyOtpStack.Navigator>
);
