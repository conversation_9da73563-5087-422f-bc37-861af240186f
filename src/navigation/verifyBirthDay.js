import React from 'react';
import {View} from 'react-native';
import {createStackNavigator} from '@react-navigation/stack';
import BirthdayVerificationScreen from 'screens/BirthdayVerificationScreen';

const VerifyBirthDayStack = createStackNavigator();

export default ({navigation: {navigate}}) => (
  <VerifyBirthDayStack.Navigator
    screenOptions={{
      cardStyle: {
        backgroundColor: '#e5e5e5',
      },
      headerTitle: '',
      headerStyle: {
        backgroundColor: '#e5e5e5',
        shadowColor: 'transparent',
      },
      headerTitleStyle: {
        alignSelf: 'center',
      },
    }}
  >
    <VerifyBirthDayStack.Screen
      name="BirthdayVerificationScreen"
      component={BirthdayVerificationScreen}
      options={{
        headerLeft: () => <View />,
        headerRight: () => <View />,
        headerShown: false,
      }}
    />
  </VerifyBirthDayStack.Navigator>
);
