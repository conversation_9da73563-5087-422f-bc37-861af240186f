import React, { useEffect, useRef } from 'react';
import {View, Text, Animated, StyleSheet, TouchableOpacity, Platform} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export const CustomToast = ({ message, duration = 2000, onHide }) => {
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.sequence([
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.delay(duration),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onHide();
    });
  }, []);

  return (
    <Animated.View style={[styles.container, { opacity, top: Platform.OS === 'android' ? 5 : 45 }]}>
      <View style={styles.leftBorder} />
      <View style={styles.contentContainer}>
        <View style={styles.iconContainer}>
          <Icon name="time-outline" size={24} color="#F87018" />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.text}>{message}</Text>
        </View>
      </View>
      <TouchableOpacity style={styles.closeButton} onPress={onHide}>
        <Icon name="close" size={20} color="#000" />
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 8,
    right: 8,
    backgroundColor: 'white',
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1000,
    overflow: 'hidden',
  },
  leftBorder: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 4,
    backgroundColor: '#F87018'
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    padding: 16,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContainer: {},
  text: {
    fontSize: 12,
    color: '#000',
  },
  closeButton: {
    padding: 6,
  },
});
