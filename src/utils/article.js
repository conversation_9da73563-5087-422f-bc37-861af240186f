import {GA_EVENT_NAME, NOT_APPLICABLE} from './constant';
import {GA_getCommonParamsByClickLocation, GA_logEvent} from './googleAnalytics';

export const GA_logSelectContentEvent = item => {
  let {contentName, contentType, contentCategory, clickLocation} = item;
  let {screenType, pageCategory, pageName, contentCategoryByLocation} =
    GA_getCommonParamsByClickLocation(clickLocation);
  GA_logEvent(GA_EVENT_NAME.SELECT_CONTENT, {
    content_type: contentType || NOT_APPLICABLE,
    content_category:
      contentCategory || contentCategoryByLocation || NOT_APPLICABLE,
    content_name: contentName || NOT_APPLICABLE,
    click_location: clickLocation || NOT_APPLICABLE,
    screen_type: screenType,
    page_name: pageName,
    page_type: screenType,
    page_category: pageCategory,
  });
};

export const articlePressed = async (navigate, item, clickLocation) => {
  try {
    if (isVideoArticle(item)) {
      GA_logSelectContentEvent({
        contentName: item?.title,
        contentType: 'video',
        clickLocation,
      });
      navigate('Video', {
        video: {
          id: item?.urlId,
          title: item?.title,
          host: item?.videoType,
          contentId: item?.id,
          clickLocation,
          contentName: item?.title,
        },
      });
    } else {
      GA_logSelectContentEvent({
        contentName: item?.title,
        contentType: 'article',
        clickLocation,
      });
      navigate('FeedArticle', {
        screen: 'FeedArticle',
        params: {
          item: item,
          clickLocation,
        },
      });
    }
  } catch (error) {}
};

export const isVideoArticle = item => {
  return (
    item?.entry_type === 'partnerVideos' ||
    (!!item?.video_id && !!item?.video_type) ||
    (item?.entry_type === 'videos' && !!item?.urlId)
  );
};

export const getThumbnail = item => {
  let thumbnail = item?.primaryImage;
  if (!thumbnail && item?.urlId) {
    thumbnail = `https://img.youtube.com/vi/${item?.urlId}/hqdefault.jpg`;
  }
  return thumbnail;
};

export const drillPressed = (
  navigate,
  videoData,
  origin,
  clickLocation,
  contentCategory,
) => {
  GA_logSelectContentEvent({
    contentName: videoData?.title,
    contentType: 'video',
    clickLocation,
    contentCategory,
  });
  navigate('Video', {
    video: {
      id: videoData?.video_id,
      title: videoData?.title,
      host: videoData?.video_type,
      contentId: videoData?.id,
      origin: origin,
      videoUrl: videoData?.video_url,
      videoProvider: videoData?.dataSource,
      clickLocation,
      contentCategory,
      contentName: videoData?.title,
    },
  });
};
