import AsyncStorage from '@react-native-async-storage/async-storage';
import { writeLogToDB } from 'requests/logging';
import {LOGGING_EVENT, STORAGE_KEY} from './constant';

//Function save log to AsyncStorage
export const saveLogToStorage = async log => {
  try {
    // Save lastest object log to AsyncStorage
    await AsyncStorage.setItem(
      STORAGE_KEY.FORCE_LOGOUT_LOGGING,
      JSON.stringify(log),
    );
    console.log('Log saved to AsyncStorage as an object', log);
  } catch (error) {
    console.error('Error saving log to AsyncStorage:', error);
  }
};

// Hàm đ<PERSON>y dữ li<PERSON>u lên server
export const uploadLogsToServer = async () => {
  try {
    const storedLogs = await AsyncStorage.getItem(
      STORAGE_KEY.FORCE_LOGOUT_LOGGING,
    );
    console.log('storedLogs', storedLogs);
    if (storedLogs) {
      const savingLog = JSON.parse(storedLogs);

      // call API to upload the log
      const response = await writeLogToDB(savingLog);
      console.log('response log', response);
      if (response) {
        console.log('Logs uploaded successfully');
        // Remove the log after a successful upload
        await AsyncStorage.removeItem(STORAGE_KEY.FORCE_LOGOUT_LOGGING);
      } else {
        console.error('Failed to upload logs:', response.status);
      }
    }
  } catch (error) {
    console.error('Error uploading logs to server:', error);
  }
};
