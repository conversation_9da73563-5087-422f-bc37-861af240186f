import FastImage from 'react-native-fast-image/src';

export const preloadImages = (
  images,
  index = 0,
  callbackCompleteOnce,
  callbackCompleteAll,
) => {
  // Determine the number of images to load this time
  const batchSize = 5;
  const nextIndex = index + batchSize;
  //   console.log('index', index);
  // Take the next 5 photos (or less if not enough)
  const imagesToLoad = images.slice(index, nextIndex);
  // Call the preload function with the selected images
  FastImage.preload(
    imagesToLoad,
    (loaded, total) => {
      //   console.log('loaded', loaded);
      //   console.log('total', total);
    },
    (loaded, skipped) => {
      //   console.log('loaded', loaded);
      //   console.log('skipped', skipped);
      // If there are still images to load, continue recursively
      if (nextIndex < images.length) {
        callbackCompleteOnce?.();
        // Call the preloadImages function for the next batch
        preloadImages(images, nextIndex);
      } else {
        callbackCompleteAll?.();
      }
    },
  );
};

/**
 * Preload images and wait for the process to complete.
 * @param {Array} images - Array of image URIs to preload.
 * @returns {Promise<void>} Resolves when all images are preloaded.
 */
export const preloadImagesAsync = async images => {
  let startTime = Date.now();
  return new Promise((resolve, reject) => {
    try {
      FastImage.preload(
        images,
        (loaded, total) => {
          // console.log('loaded', loaded);
          // console.log('total', total);
          let endTime = Date.now();
          let executeTime = endTime - startTime;
          console.log('executeTime', executeTime);
          console.log('images', images[0].uri);
        },
        (loaded, skipped) => {
          resolve();
        },
      );
    } catch (error) {
      reject(error); // Reject the Promise if something goes wrong
    }
  });
};
