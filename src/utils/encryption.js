import * as Keychain from 'react-native-keychain';
import { generateSecureRandom } from 'react-native-securerandom';
import { <PERSON><PERSON><PERSON> } from 'buffer';
const CryptoJS = require('crypto-js');

// Unique non-sensitive ID which we use to save the store password
const encryptionKey = 'MYTM_ENCRYPTION';

/**
 * Get or generate a secure encryption key stored in Keychain.
 */
export const getEncryptionKey = async () => {
  try {
    // Check for existing credentials
    const existingCredentials = await Keychain.getGenericPassword();
    if (existingCredentials) {
      return { isFresh: false, key: existingCredentials.password };
    }

    // Generate new credentials based on random string
    const randomBytes = await generateSecureRandom(32);
    if (!randomBytes) {
      throw new Error('Error generating a secure random key buffer');
    }

    // Convert Uint8Array -> Base64 using Buffer
    const randomBytesString = Buffer.from(randomBytes).toString('base64');
    if (!randomBytesString) {
      throw new Error('Error converting secure random key buffer');
    }

    // Save to Keychain
    const hasSetCredentials = await Keychain.setGenericPassword(
      encryptionKey,
      randomBytesString
    );
    if (hasSetCredentials) {
      return { isFresh: true, key: randomBytesString };
    }
    throw new Error('Error setting the generic password on Keychain');
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * Encrypt data using AES with SHA256-derived key and IV.
 */
export const encryptionData = (data, key) => {
  const Sha256 = CryptoJS.SHA256;
  const Hex = CryptoJS.enc.Hex;
  const Utf8 = CryptoJS.enc.Utf8;
  const Base64 = CryptoJS.enc.Base64;
  const AES = CryptoJS.AES;

  const secret_key = key + '-key';
  const secret_iv = key + '-iv';

  const keySHA = Sha256(secret_key).toString(Hex).substr(0, 32);
  const iv = Sha256(secret_iv).toString(Hex).substr(0, 16);

  // AES encryption
  const output = AES.encrypt(data, Utf8.parse(keySHA), {
    iv: Utf8.parse(iv),
  })?.toString();

  // Convert output -> UTF8 -> Base64
  const outputB64 = Utf8.parse(output).toString(Base64);

  // Replace '=' with '-' for storage
  return outputB64.replace(/=/g, '-');
};

/**
 * Decrypt data encrypted with encryptionData().
 */
export const decryptionData = (data, key) => {
  const Sha256 = CryptoJS.SHA256;
  const Hex = CryptoJS.enc.Hex;
  const Utf8 = CryptoJS.enc.Utf8;
  const AES = CryptoJS.AES;
  const Base64 = CryptoJS.enc.Base64;

  // Restore original Base64 string
  const replaceChars = data.replace(/-/g, '=');

  // Convert Base64 -> UTF8
  const outputUTF8 = Base64.parse(replaceChars).toString(Utf8);

  const secret_key = key + '-key';
  const secret_iv = key + '-iv';

  const keySHA = Sha256(secret_key).toString(Hex).substr(0, 32);
  const iv = Sha256(secret_iv).toString(Hex).substr(0, 16);

  // AES decryption
  const decrypted = AES.decrypt(outputUTF8, Utf8.parse(keySHA), {
    iv: Utf8.parse(iv),
  }).toString(Utf8);

  return decrypted;
};
