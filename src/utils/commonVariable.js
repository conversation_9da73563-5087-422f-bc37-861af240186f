import moment from 'moment';
import {MODE_ROUND} from 'screens/PlayCourseMap/DataSubmitDefault';
import {COUNTRY_CODE, SYNC_ROUND_TYPE} from './constant';

let myBagTab = 'ActiveClubs';

let initStartDeeperInsightScreen = 'ActiveClubs';
let avgScoreCache = 0;
let modePlayed = MODE_ROUND.CLASSIC;
let isFakeGps = false;
let isCourseOutline = false;
let inprogressHoleSubmitArray = [];
let country = COUNTRY_CODE.USA;
let language = 'en';
let currency = 'USD';
let currencySymbol = '$';
let syncRound3rdParty = SYNC_ROUND_TYPE.USGA;
let isNewUser = false;
let askPermission = false;
let userLocation = {};
let nearbyCourses = [];
let deviceCountry = 'Unknown';

export const getInprogressHoleSubmitArray = () => {
  return inprogressHoleSubmitArray;
};
export const setInprogressHoleSubmitArray = data => {
  inprogressHoleSubmitArray = data;
};

export const getMyBagTab = () => {
  return myBagTab;
};
export const setMyBagTab = name => {
  myBagTab = name;
};

export const setStartDeeperInsightScreen = name => {
  initStartDeeperInsightScreen = name;
};

export const getStartDeeperInsightScreen = () => {
  return initStartDeeperInsightScreen;
};

// Swing authentication token

let swingAuthenToken = '';
let swingShotURL = '';
export const setSwingShotData = data => {
  swingAuthenToken = data?.token;
  swingShotURL = data?.endpoint;
};

export const setSwingAuthenToken = token => {
  swingAuthenToken = token;
};

export const getSwingAuthenToken = () => {
  return swingAuthenToken;
};

export const getSwingShotURL = () => {
  return swingShotURL;
};

export const resetSwingShotData = () => {
  swingAuthenToken = '';
  swingShotURL = '';
};

let reloadScreen = false;

export const setReloadScreen = value => {
  reloadScreen = value;
};

export const getReloadScreen = () => {
  return reloadScreen;
};

let firstBaselineData = {};

export const setFirstBaselineData = data => {
  firstBaselineData = data;
};

export const getFirstBaselineData = () => {
  return firstBaselineData;
};

export const getAvgScore = () => {
  return avgScoreCache;
};
export const setAvgScore = score => {
  avgScoreCache = score;
};

export const getModePlayed = () => {
  return modePlayed;
};

export const setModePlayed = mode => {
  modePlayed = mode;
};
export const getModeFakeGps = () => {
  return isFakeGps;
};

export const setModeFakeGps = mode => {
  isFakeGps = mode;
};

export const getCourseOutlineSetting = () => {
  return isCourseOutline;
};

export const setCourseOutlineSetting = isOutline => {
  isCourseOutline = isOutline;
};

export const setCountry = rg => {
  country = rg;
  if (rg === COUNTRY_CODE.CAN) {
    moment.locale('en-ca');
    currency = 'CAD';
    currencySymbol = 'C$';
    syncRound3rdParty = SYNC_ROUND_TYPE.WHS;
  } else if (rg === COUNTRY_CODE.USA) {
    moment.locale('en');
    currency = 'USD';
    currencySymbol = '$';
    syncRound3rdParty = SYNC_ROUND_TYPE.USGA;
  }
};

export const getCountry = () => {
  return country;
};

export const setLanguage = lang => {
  language = lang;
};

export const getLanguage = () => {
  return language || 'en';
};

export const isCanadaMarket = () => {
  return country === COUNTRY_CODE.CAN;
};

export const getCurrency = () => {
  return currency || 'USD';
};

export const getCurrencySymbol = cur => {
  if (cur) {
    switch (cur) {
      case 'CAD':
        return 'C$';
      default:
        return '$';
    }
  }
  return currencySymbol || '$';
};

export const getSyncRound3rdParty = () => {
  return syncRound3rdParty || SYNC_ROUND_TYPE.USGA;
};

export const setNewUser = isNew => {
  isNewUser = isNew;
};

export const getNewUser = () => {
  return isNewUser;
};

export const setAskPermissions = isNew => {
  askPermission = isNew;
};

export const getAskPermissions = () => {
  return askPermission;
};

export const getUserLocation = () => {
  return userLocation;
};

export const setUserLocation = location => {
  userLocation = location;
};

export const getNearbyCourses = () => {
  return nearbyCourses;
};

export const setNearbyCourses = courses => {
  nearbyCourses = courses;
};

export const getDeviceCountry = () => {
  return deviceCountry;
};

export const setDeviceCountry = countryCode => {
  deviceCountry = countryCode;
};
