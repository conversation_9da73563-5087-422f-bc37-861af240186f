{"name": "myTaylorMade", "version": "0.0.1", "private": true, "scripts": {"ios": "react-native run-ios --scheme Debug", "ios-ipad": "react-native run-ios --scheme Debug --simulator='iPad Pro (12.9-inch) (5th generation)'", "ios-se": "react-native run-ios --scheme Debug --simulator='iPhone SE (2nd generation)'", "android": "react-native run-android --variant=devDebug", "postinstall": "patch-package", "start": "react-native start", "test": "jest", "lint": "eslint .", "atest": "cd android/ && fastlane android atest", "testf": "cd ios/ && fastlane ios stage", "refresh": "yarn install && cd ios/ && pod install && cd ..", "android:dev": "cd android && ./gradlew installDevDebug", "android:prod": "cd android && ./gradlew installProductionDebug"}, "dependencies": {"@babylonjs/core": "^5.53.0", "@babylonjs/loaders": "^5.53.0", "@babylonjs/react-native": "^1.5.1", "@babylonjs/react-native-iosandroid-0-70": "^1.5.1", "@react-native-async-storage/async-storage": "^1.14.1", "@react-native-community/art": "^1.2.0", "@react-native-community/blur": "^4.3.2", "@react-native-community/checkbox": "^0.5.9", "@react-native-community/netinfo": "^9.3.0", "@react-native-community/slider": "^3.0.3", "@react-native-firebase/analytics": "^13.1.0", "@react-native-firebase/app": "^13.1.0", "@react-native-firebase/messaging": "^13.1.0", "@react-native-masked-view/masked-view": "^0.2.9", "@react-native-picker/picker": "^2.4.9", "@react-navigation/bottom-tabs": "^5.11.8", "@react-navigation/material-top-tabs": "^5.3.15", "@react-navigation/native": "^5.9.3", "@react-navigation/stack": "^5.14.3", "@reduxjs/toolkit": "^1.5.1", "@turf/turf": "^6.5.0", "appcenter": "4.4.5", "appcenter-analytics": "4.4.5", "appcenter-crashes": "4.4.5", "axios": "^0.21.1", "babylonjs": "^5.53.0", "base-64": "^1.0.0", "crypto-js": "^4.1.1", "date-fns": "^2.30.0", "deprecated-react-native-prop-types": "^2.3.0", "events": "^3.3.0", "expo-screen-orientation": "3.3.0", "geolib": "^3.3.1", "html-entities": "^2.5.2", "i18next": "^21.6.9", "iso-3166-1": "^2.1.1", "lodash": "^4.17.21", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "patch-package": "^6.4.7", "postinstall-postinstall": "^2.1.0", "promise-reflect": "^1.1.0", "prop-types": "^15.7.2", "query-string": "^8.1.0", "react": "19.1.0", "react-i18next": "^11.17.3", "react-native": "0.80.1", "react-native-animatable": "^1.3.3", "react-native-animated-wheel-picker": "^1.1.3", "react-native-appsflyer": "^6.9.4", "react-native-auth0": "2.13.3", "react-native-branch": "^5.0.0", "react-native-camera": "^4.2.1", "react-native-chart-kit": "^6.12.0", "react-native-charts-wrapper": "^0.5.10", "react-native-code-push": "^7.0.5", "react-native-collapsible": "^1.6.1", "react-native-config": "^1.4.8", "react-native-contacts": "^7.0.5", "react-native-create-thumbnail": "^1.5.1", "react-native-dashed-line": "^1.1.0", "react-native-date-picker": "^3.2.10", "react-native-device-info": "^10.2.0", "react-native-draggable-flatlist": "^3.1.2", "react-native-extra-dimensions-android": "^1.2.5", "react-native-fast-image": "https://github.com/nicomontanari/react-native-fast-image", "react-native-geolocation-service": "^5.3.0-beta.1", "react-native-gesture-handler": "^2.8.0", "react-native-htmlview": "^0.16.0", "react-native-image-picker": "^4.8.2", "react-native-in-app-review": "^4.3.5", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keychain": "^7.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^2.1.7", "react-native-mail": "^6.1.1", "react-native-maps": "^1.3.1", "react-native-modal": "^13.0.0", "react-native-orientation-locker": "^1.5.0", "react-native-pager-view": "^5.4.11", "react-native-permissions": "^3.0.3", "react-native-progress": "^4.1.2", "react-native-progress-bar-animated": "^1.0.6", "react-native-reanimated": "^3.18.0", "react-native-reanimated-carousel": "^3.5.1", "react-native-responsive-image-view": "^2.1.0", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^3.2.0", "react-native-screens": "^4.13.1", "react-native-securerandom": "^1.0.0", "react-native-select-contact": "^1.6.3", "react-native-share": "^7.3.7", "react-native-shimmer-placeholder": "^2.0.8", "react-native-size-matters": "^0.4.0", "react-native-snap-carousel": "^3.9.1", "react-native-splash-screen": "^3.2.0", "react-native-svg": "^12.1.1", "react-native-swipe-gestures": "^1.0.5", "react-native-switch": "^1.5.1", "react-native-tab-view": "^2.16.0", "react-native-testfairy": "2.60.0", "react-native-toast-message": "^1.4.9", "react-native-uuid": "^2.0.1", "react-native-vector-icons": "^9.2.0", "react-native-version-check": "^3.4.3", "react-native-video": "^5.2.0", "react-native-view-shot": "^3.1.2", "react-native-volume-manager": "^2.0.8", "react-native-watch-connectivity": "^1.0.11", "react-native-webview": "^11.23.1", "react-native-wheel-picker-android": "^2.0.6", "react-redux": "^7.2.3", "reactotron-react-native": "^5.0.1", "realm": "11.0.0-rc.2", "reanimated-bottom-sheet": "^1.0.0-alpha.22", "redux": "^4.0.5", "redux-persist": "^6.0.0", "redux-persist-transform-encrypt": "^3.0.1", "stream": "^0.0.2", "timers": "^0.1.1", "validator": "^13.5.2", "xml2js": "^0.4.23"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native-community/eslint-config": "^2.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native": "^0.67.9", "@types/react-test-renderer": "^19.1.0", "babel-jest": "^26.6.3", "babel-plugin-inline-import": "^3.0.0", "babel-plugin-module-resolver": "^4.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "^0.68.0", "prettier": "2.8.8", "react-native-svg-transformer": "^0.14.3", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "resolutions": {"@gregfrench/react-native-wheel-picker/@react-native-picker/picker": "^2.4.9"}}