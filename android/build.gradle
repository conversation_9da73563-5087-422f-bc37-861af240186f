// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "30.0.3"
        minSdkVersion = 25
        compileSdkVersion = 31
        targetSdkVersion = 31
        ndkVersion = "21.4.7075529"
        androidXCore = "1.8.0"
        kotlinVersion = '1.6.0'
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.google.gms:google-services:4.3.8'
        classpath("com.android.tools.build:gradle:7.1.2")
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.6.0"
        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.38.1'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

def REACT_NATIVE_VERSION = new File(['node', '--print',"JSON.parse(require('fs').readFileSync(require.resolve('react-native/package.json'), 'utf-8')).version"].execute(null, rootDir).text.trim())
allprojects {
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }
        google()
        jcenter()
    }
    configurations.all {
        resolutionStrategy {
            force "com.facebook.react:react-native:" + REACT_NATIVE_VERSION
        }
        resolutionStrategy.eachDependency { DependencyResolveDetails details ->
            if (details.requested.group == 'net.minidev' && details.requested.name == 'json-smart') {
                details.useVersion '2.3'
            }
        }
    }
}
