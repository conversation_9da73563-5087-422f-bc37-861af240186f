apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'

project.ext.envConfigFiles = [
    debug  : ".env",
    release: ".env",
    staging: ".env",
]

apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"
apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"

import com.android.build.OutputFile
apply from: "../../node_modules/react-native-code-push/android/codepush.gradle"
 

react {
    // enableHermes = false
    // bundleInStagingRelease = true
    // devDisabledInStagingRelease = true
    // debuggableVariants = ["devDebug", "productionDebug"]
    // autolinkLibrariesWithApp()
}
/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false
 
/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = io.github.react-native-community:jsc-android-intl:2026004.+`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'io.github.react-native-community:jsc-android:2026004.+'
def enableSeparateBuildPerCPUArchitecture = false

/**
 * Whether to enable the Hermes VM.
 *
 * This should be set on project.ext.react and that value will be read here. If it is not set
 * on project.ext.react, JavaScript will not be compiled to Hermes Bytecode
 * and the benefits of using Hermes will therefore be sharply reduced.
 */
def enableHermes = false;

/**
 * Architectures to build native code for in debug.
 */
def nativeArchitectures = project.getProperties().get("reactNativeDebugArchitectures")

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion
    
    namespace "com.taylormadegolf.mytaylormadeplus.android"

    defaultConfig {
        applicationId "com.taylormadegolf.mytaylormadeplus.android"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 727
        versionName "3.0.0"
        missingDimensionStrategy 'store', 'play'
        missingDimensionStrategy 'react-native-camera', 'general'
        manifestPlaceholders = [auth0Domain: "auth-stg.taylormadegolf.com", auth0Scheme: "${applicationId}"]
    }
    splits {
        abi {
            reset()
            enable enableSeparateBuildPerCPUArchitecture
            universalApk false  // If true, also generate a universal APK
            include "armeabi-v7a", "x86", "arm64-v8a", "x86_64"
        }
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            if (project.hasProperty('MYAPP_UPLOAD_STORE_FILE')) {
                storeFile file(MYAPP_UPLOAD_STORE_FILE)
                storePassword MYAPP_UPLOAD_STORE_PASSWORD
                keyAlias MYAPP_UPLOAD_KEY_ALIAS
                keyPassword MYAPP_UPLOAD_KEY_PASSWORD
            }
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            if (nativeArchitectures) {
                ndk {
                    abiFilters nativeArchitectures.split(',')
                }
            }
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            resValue "string", "CodePushDeploymentKey", '""'
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            resValue "string", "CodePushDeploymentKey", '"cnr1d0L27eNtvcrjpocIqPTvMjeGVcaAztu13"'
        }
        stagingRelease {
            signingConfig signingConfigs.debug
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            resValue "string", "CodePushDeploymentKey", '"5q7ecqywyVydq_2UCgfxlSHUKyTl0PeXNmPxt"'
            // Note: It's a good idea to provide matchingFallbacks for the new buildType you create to prevent build issues
            // Add the following line if not already there
            matchingFallbacks = ['release']
        }
    }
    packagingOptions {
        jniLibs {
            pickFirsts += ['lib/x86/libc++_shared.so', 'lib/x86_64/libc++_shared.so', 'lib/armeabi-v7a/libc++_shared.so', 'lib/arm64-v8a/libc++_shared.so', 'lib/arm64-v8a/libfbjni.so', 'lib/armeabi-v7a/libfbjni.so', 'lib/x86/libfbjni.so', 'lib/x86_64/libfbjni.so']
        }
    }


}
static def versionCodeDate() {
    return new Date().format("yyyyMMdd0").toInteger() + 1
}

dependencies {
    implementation "com.facebook.react:react-android"
    implementation platform('com.google.firebase:firebase-bom:28.0.1')
    implementation 'com.google.firebase:firebase-analytics'
    implementation fileTree(dir: "libs", include: ["*.jar"])
    //noinspection GradleDynamicVersion
    implementation "com.facebook.react:react-native:+"
    implementation 'com.google.android.gms:play-services-wearable:18.0.0'// From node_modules
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.0-RC"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.6.0-RC"
    // wearApp project(':wearable')
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
    implementation "com.github.klaviyo.klaviyo-android-sdk:analytics:1.3.4"
    implementation "com.github.klaviyo.klaviyo-android-sdk:push-fcm:1.3.4"
    implementation 'org.jetbrains:annotations:16.0.2'

    if (enableHermes) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

// Run this once to be able to run the application with BUCK
// puts all compile dependencies into folder libs for BUCK to use
task copyDownloadableDepsToLibs(type: Copy) {
    from configurations.implementation
    into 'libs'
}
