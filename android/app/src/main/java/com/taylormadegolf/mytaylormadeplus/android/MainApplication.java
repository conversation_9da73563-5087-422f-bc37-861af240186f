package com.taylormadegolf.mytaylormadeplus.android;
import com.klaviyo.analytics.Klaviyo;
import com.taylormadegolf.mytaylormadeplus.android.generated.BasePackageList;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;

import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.microsoft.codepush.react.CodePush;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.soloader.SoLoader;

import java.util.List;
import java.util.Arrays;
import io.branch.rnbranch.RNBranchModule;

import org.jetbrains.annotations.Nullable;
import org.unimodules.adapters.react.ModuleRegistryAdapter;
import org.unimodules.adapters.react.ReactModuleRegistryProvider;
import com.facebook.react.bridge.JSIModulePackage;
import com.swmansion.reanimated.ReanimatedJSIModulePackage;

public class MainApplication extends Application implements ReactApplication {
  private final ReactModuleRegistryProvider mModuleRegistryProvider = new ReactModuleRegistryProvider(new BasePackageList().getPackageList(), null);

  private final ReactNativeHost mReactNativeHost =
      new ReactNativeHost(this) {
        @Override
        public boolean getUseDeveloperSupport() {
          return BuildConfig.DEBUG;
        }

        @Override
        protected String getJSBundleFile(){
          return CodePush.getJSBundleFile();
        }

        @Override
        protected List<ReactPackage> getPackages() {
          @SuppressWarnings("UnnecessaryLocalVariable")
          List<ReactPackage> packages = new PackageList(this).getPackages();
          // Packages that cannot be autolinked yet can be added manually here, for example:
          packages.add(new OpenNotificationSettingsPackage());
          packages.add(new OpenLocationSettingsPackage());
          packages.add(new VideoPlayerPackage());
          packages.add(new WearOSPackage());
          packages.add(new KlaviyoPackage());

          // Add unimodules
          List<ReactPackage> unimodules = Arrays.<ReactPackage>asList(
            new ModuleRegistryAdapter(mModuleRegistryProvider)
          );
          packages.addAll(unimodules);
          return packages;
        }

        @Override
        protected String getJSMainModuleName() {
          return "index";
        }

        @Override protected JSIModulePackage getJSIModulePackage() { return new ReanimatedJSIModulePackage(); }
      };

  @Override
  public ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

    @Override
    public Intent registerReceiver(@Nullable BroadcastReceiver receiver, IntentFilter filter) {
        if (Build.VERSION.SDK_INT >= 34 && getApplicationInfo().targetSdkVersion >= 34) {
            return super.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED);
        } else {
            return super.registerReceiver(receiver, filter);
        }
    }

  @Override
  public void onCreate() {
    super.onCreate();
    SoLoader.init(this, /* native exopackage */ false);
    RNBranchModule.getAutoInstance(this);
      String keyDev = getResources().getString(R.string.klaviyo_public_key_dev);
      String keyProd = getResources().getString(R.string.klaviyo_public_key_prod);
      Klaviyo.INSTANCE.initialize(BuildConfig.APPLICATION_ID.equals("com.taylormadegolf.mytaylormadeplus.app.android") ? keyProd: keyDev, this);

      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
          registerActivityLifecycleCallbacks(Klaviyo.INSTANCE.getLifecycleCallbacks());
      }
  }
}
