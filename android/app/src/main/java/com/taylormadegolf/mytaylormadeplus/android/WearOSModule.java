package com.taylormadegolf.mytaylormadeplus.android;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.wearable.DataMap;
import com.google.android.gms.wearable.Wearable;

public class WearOSModule extends ReactContextBaseJavaModule {
    private String MESSAGE_PATH = "/mytaylormadeplus_message_path";
    private String MESSAGE_DATA_USER_ID = "MESSAGE_DATA_USER_ID";
    private String MESSAGE_DATA_USER_TOKEN = "MESSAGE_DATA_USER_TOKEN";
    private String MESSAGE_DATA_YARD_UNIT = "MESSAGE_DATA_YARD_UNIT";
    public static ReactApplicationContext reactContext;
    public WearOSModule(@Nullable ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @NonNull
    @Override
    public String getName() {
        return "WearOSModule";
    }
    @ReactMethod
    public void checkLogin(String nodeId, ReadableMap data) {
        sendMessage(nodeId, data);

    }
    private void sendMessage(String nodeId, ReadableMap data){
        if (!data.getString("userId").equals("")) {
            DataMap dataMap = new DataMap();
            dataMap.putString(MESSAGE_DATA_USER_ID, data.getString("userId"));
            dataMap.putString(MESSAGE_DATA_USER_TOKEN, data.getString("userToken"));
            dataMap.putBoolean(MESSAGE_DATA_YARD_UNIT, data.getBoolean("isUnitYard"));
                Task<Integer> sendTask =  Wearable.getMessageClient(getReactApplicationContext())
                        .sendMessage(
                                nodeId,
                                MESSAGE_PATH,
                                dataMap.toByteArray()
                        );
                sendTask.addOnSuccessListener(new OnSuccessListener<Integer>() {
                    @Override
                    public void onSuccess(Integer integer) {
                    }
                });
                sendTask.addOnFailureListener(new OnFailureListener() {
                    @Override
                    public void onFailure(@NonNull Exception e) {
                    }
                });
        }
    }
}
