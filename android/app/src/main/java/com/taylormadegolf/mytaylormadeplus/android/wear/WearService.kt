package com.taylormadegolf.mytaylormadeplus.android.wear

import android.content.Intent
import android.os.CountDownTimer
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactContext
import com.facebook.react.bridge.WritableMap
import com.facebook.react.modules.core.DeviceEventManagerModule.RCTDeviceEventEmitter
import com.google.android.gms.wearable.MessageEvent
import com.google.android.gms.wearable.WearableListenerService
import com.taylormadegolf.mytaylormadeplus.android.MainActivity
import com.taylormadegolf.mytaylormadeplus.android.WearOSModule
import kotlinx.coroutines.*


class WearService : WearableListenerService() {

    private val MESSAGE_PATH = "/mytaylormadeplus_message_path"
    var nodeId: String = ""

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)

    override fun onDestroy() {
        scope.cancel()
        super.onDestroy()
    }

    override fun onMessageReceived(messageEvent: MessageEvent) {
        scope.launch {
            handleMessageEvent(messageEvent)
        }
    }

    private suspend fun handleMessageEvent(messageEvent: MessageEvent) {
        when (messageEvent.path) {
            MESSAGE_PATH -> {
                getAuthenticateInfo(messageEvent.sourceNodeId)
            }
        }
    }

    private fun getAuthenticateInfo(nodeId: String) {
        this.nodeId = nodeId
        if(WearOSModule.reactContext != null){
            sendMessageToApp()
        }else{
            startActivity(Intent(baseContext, MainActivity::class.java).apply {
                flags =
                    Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            })
            timer.start()
        }

//        if (AppPreferences.isLoggedIn()) {
//            val userID = AppPreferences.getUserId().toString()
//            val userToken = AppPreferences.getToken()
//            val isUnitYard = AppPreferences.userPrefersYards()
//            sendMessage(nodeId, DataMap().apply {
//                putString(MESSAGE_DATA_USER_ID, userID)
//                putString(MESSAGE_DATA_USER_TOKEN, userToken)
//                putBoolean(MESSAGE_DATA_YARD_UNIT, isUnitYard)
//            })
//            startActivity(Intent(baseContext, MainActivity::class.java).apply {
//                putExtra(Keys.OPEN_COMMAND_EXTRA, true)
//                putExtra(Keys.OPEN_TAB_INDEX, FooterTabsIndex.MY_GAME)
//                flags =
//                    Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
//            })
//        } else {
//            sendMessage(nodeId, DataMap())
//            startActivity(Intent(baseContext, SplashActivity::class.java).apply {
//                flags =
//                    Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
//            })
//        }
    }

    private fun sendEvent(
        reactContext: ReactContext,
        eventName: String,
        params: WritableMap?
    ) {
        reactContext
            .getJSModule(RCTDeviceEventEmitter::class.java)
            .emit(eventName, params)
    }
    private fun sendMessageToApp(){
        if(WearOSModule.reactContext != null) {
            timer.cancel()
            val params = Arguments.createMap().apply {
                putString("nodeId", nodeId)
            }
            sendEvent(WearOSModule.reactContext, "getAuthFromWearOS", params)
        }
    }

    val timer = object: CountDownTimer(20000, 1000) {
        override fun onTick(millisUntilFinished: Long) {
        }

        override fun onFinish() {
            sendMessageToApp()
        }
    }

//    private suspend fun sendMessage(nodeId: String, dataMap: DataMap) {
//        try {
//            Wearable.getMessageClient(applicationContext)
//                .sendMessage(
//                    nodeId,
//                    MESSAGE_PATH,
//                    dataMap.toByteArray()
//                ).await()
//        } catch (throwable: Exception) {
////            Timber.i("Send message failed")
//        }
//    }

}
