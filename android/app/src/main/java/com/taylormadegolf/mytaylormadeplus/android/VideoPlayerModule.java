package com.taylormadegolf.mytaylormadeplus.android;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Environment;

import androidx.core.content.FileProvider;

import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

import java.io.File;
import java.io.IOException;
import java.net.URI;

public class VideoPlayerModule extends ReactContextBaseJavaModule implements ActivityEventListener {

    public final int VIDEO_CODE = 1;

    public VideoPlayerModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "VideoPlayerManager";
    }

    @ReactMethod
    public void showVideoPlayer(String url) {
        Activity currentActivity = getCurrentActivity();
        if (currentActivity != null) {
            Intent videoIntent = new Intent(Intent.ACTION_VIEW);

            Uri videoUri = Uri.parse(url);
            if(url.contains("file://")){
                File file = new File(url.replace("file://",""));
                if(file.isFile()){
                    videoUri = FileProvider.getUriForFile(this.getReactApplicationContext() , this.getReactApplicationContext().getPackageName() + ".provider", new File(url.replace("file://","")));
                }
            }
            videoIntent.setDataAndType(videoUri, "video/*");
            videoIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            currentActivity.startActivityForResult(videoIntent, VIDEO_CODE);
        }
    }

    @Override
    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        if (requestCode == VIDEO_CODE) {
            getCurrentActivity().finish();
        }
    }

    @Override
    public void onNewIntent(Intent intent) {
    }
}
