package com.taylormadegolf.mytaylormadeplus.android;


import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.klaviyo.analytics.Klaviyo;

public class KlaviyoModule extends ReactContextBaseJavaModule {

	KlaviyoModule(ReactApplicationContext reactContext) {
		super(reactContext);
	}

	@Override
	public String getName() {
		return "KlaviyoModule";
	}

	@ReactMethod
	public void setUserEmail(String email) {
		Klaviyo.INSTANCE.resetProfile();
		Klaviyo.INSTANCE.setEmail(email);
	}

	@ReactMethod
	public void addPushDeviceToken(String token) {
		Klaviyo.INSTANCE.setPushToken(token);
	}
}
