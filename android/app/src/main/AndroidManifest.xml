<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools"
  package="com.taylormadegolf.mytaylormadeplus.android" tools:node="replace">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- Include this only if you are planning to use the camera roll -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.READ_PROFILE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>

    <!-- Include this only if you are planning to use the microphone for video recording -->
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:allowBackup="false"
      android:requestLegacyExternalStorage="true"
      android:usesCleartextTraffic="false"
      android:hardwareAccelerated="true"
      android:theme="@style/AppTheme">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true"
        >
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>

        <!-- Deep Linking Configuration -->
        <intent-filter>
          <data android:scheme="mytaylormadeplus" />
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
        </intent-filter>
        <!-- End -->

        <!-- Branch App Links (optional) -->
        <intent-filter android:autoVerify="true">
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
          <data android:scheme="https" android:host="3sshf.app.link" />
          <data android:scheme="https" android:host="3sshf.test-app.link" />
        </intent-filter>
      </activity>

      <activity android:name="com.auth0.react.RedirectActivity" tools:node="replace" android:exported="true">
        <intent-filter android:autoVerify="true" tools:targetApi="m">
          <action android:name="android.intent.action.VIEW" />
  
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
  
          <data android:host="auth.taylormadegolf.com" 
                android:pathPrefix="/android/${applicationId}/callback" 
                android:scheme="${applicationId}" />
          <data android:host="auth-stage.taylormadegolf.com" 
                android:pathPrefix="/android/${applicationId}/callback" 
                android:scheme="${applicationId}" />
        </intent-filter>
      </activity>
        <meta-data android:name="com.google.ar.core" android:value="optional" />
        <meta-data tools:replace="android:value" android:name="com.google.ar.core.min_apk_version" android:value="21" />
      <meta-data android:name="io.branch.sdk.BranchKey" android:value="key_live_cbWCeUhHIFw0Fx1rATLqWgfmsuchDvPJ"/>
      <meta-data android:name="io.branch.sdk.BranchKey.test" android:value="key_test_goWAlMlQUquZss1qsM9kaoapwEgkFqH1"/>
      <meta-data android:name="google_analytics_automatic_screen_reporting_enabled" android:value="false" />
      <meta-data android:name="io.branch.sdk.TestMode" android:value="true" />     <!-- Set to true to use Branch_Test_Key (useful when simulating installs and/or switching between debug and production flavors) -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_key"/>
      <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <service
            android:name=".wear.WearService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.android.gms.wearable.MESSAGE_RECEIVED" />
                <data
                    android:host="*"
                    android:pathPrefix="/"
                    android:scheme="wear" />
            </intent-filter>
        </service>
        <service android:name="com.klaviyo.pushFcm.KlaviyoPushService" android:exported="false" android:stopWithTask="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <meta-data android:name="com.klaviyo.push.default_notification_icon"
            android:resource="@drawable/notification_icon" />
    </application>
    <!-- Also add this for Android 11+ support -->
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <data android:mimeType="vnd.android.cursor.dir/contact" />
        </intent>
    </queries>
</manifest>
