# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

#https://github.com/DylanVann/react-native-fast-image
-keep public class com.dylanvann.fastimage.* {*;}
-keep public class com.dylanvann.fastimage.** {*;}
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
#https://www.npmjs.com/package/react-native-config
-keep class com.taylormadegolf.mytaylormadeplus.android.BuildConfig { *; }

#https://github.com/oblador/react-native-keychain
-keep class com.facebook.crypto.** {
   *;
}
#Branch https://help.branch.io/developers-hub/docs/react-native#section-install-branch
-dontwarn com.crashlytics.android.answers.shim.**
-dontwarn com.google.firebase.appindexing.**

#https://github.com/henninghall/react-native-date-picker
-keep public class net.time4j.android.ApplicationStarter
-keep public class net.time4j.PrettyTime

#https://github.com/react-native-device-info/react-native-device-info
-keepclassmembers class com.android.installreferrer.api.** {
  *;
}
-keep class com.google.android.gms.common.** {*;}
#https://github.com/realm/realm-js/issues/1960
-keep class io.realm.react.util.SSLHelper

#https://github.com/facebook/react-native/issues/9043#issuecomment-298832659
-keep class com.facebook.react.devsupport.** { *; }
-dontwarn com.facebook.react.devsupport.**

#https://github.com/software-mansion/react-native-svg/issues/1061
-keep public class com.horcrux.svg.** {*;}