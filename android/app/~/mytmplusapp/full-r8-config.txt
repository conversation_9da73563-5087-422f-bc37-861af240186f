# The proguard configuration file for the following section is /Users/<USER>/mytmplusapp/android/app/build/intermediates/default_proguard_files/global/proguard-android.txt-7.1.2
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimization is turned off by default. <PERSON> does not like code run
# through the ProGuard optimize steps (and performs some
# of these optimizations on its own).
# Note that if you want to enable optimization, you cannot just
# include optimization flags in your own project configuration file;
# instead you will need to point to the
# "proguard-android-optimize.txt" file instead of this one from your
# project.properties file.
-dontoptimize

-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Preserve some attributes that may be required for reflection.
-keepattributes Annota<PERSON>Default,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# This class is deprecated, but remains for backward compatibility.
-dontwarn android.util.FloatMath

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep
-keep class androidx.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from /Users/<USER>/mytmplusapp/android/app/build/intermediates/default_proguard_files/global/proguard-android.txt-7.1.2
# The proguard configuration file for the following section is /Users/<USER>/mytmplusapp/android/app/proguard-rules.pro
# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# Disabling obfuscation is useful if you collect stack traces from production crashes
# (unless you are using a system that supports de-obfuscate the stack traces).
# -dontobfuscate

# React Native

# Keep our interfaces so they can be used by other ProGuard rules.
# See http://sourceforge.net/p/proguard/bugs/466/
-keep,allowobfuscation @interface com.facebook.proguard.annotations.DoNotStrip
-keep,allowobfuscation @interface com.facebook.proguard.annotations.KeepGettersAndSetters

# Do not strip any method/class that is annotated with @DoNotStrip
-keep @com.facebook.proguard.annotations.DoNotStrip class *
-keepclassmembers class * {
    @com.facebook.proguard.annotations.DoNotStrip *;
}

-keep @com.facebook.proguard.annotations.DoNotStripAny class * {
    *;
}

-keepclassmembers @com.facebook.proguard.annotations.KeepGettersAndSetters class * {
  void set*(***);
  *** get*();
}

-keep class * implements com.facebook.react.bridge.JavaScriptModule { *; }
-keep class * implements com.facebook.react.bridge.NativeModule { *; }
-keepclassmembers,includedescriptorclasses class * { native <methods>; }
-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactProp <methods>; }
-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactPropGroup <methods>; }

-dontwarn com.facebook.react.**
-keep,includedescriptorclasses class com.facebook.react.bridge.** { *; }
-keep,includedescriptorclasses class com.facebook.react.turbomodule.core.** { *; }

-dontwarn com.huawei.hms.ads.installreferrer.api.InstallReferrerClient$Builder
-dontwarn com.huawei.hms.ads.installreferrer.api.InstallReferrerClient
-dontwarn com.huawei.hms.ads.installreferrer.api.InstallReferrerStateListener

# hermes
-keep class com.facebook.jni.** { *; }


# okio

-keep class sun.misc.Unsafe { *; }
-dontwarn java.nio.file.*
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn okio.**


-keep class com.facebook.react.uimanager.NativeViewHierarchyManager { *; }
-keep class com.facebook.react.uimanager.**
-keep class android.content.pm.PackageManager.**
-keep class com.huawei.hms.ads.installreferrer.api.InstallReferrerStateListener  {*;}
-keep class io.branch.referral.StoreReferrerHuaweiAppGallery {*;}

-keepnames class * extends com.facebook.react.bridge.JavaScriptModule { *; }
-keepnames class * extends com.facebook.react.bridge.CxxModuleWrapper {*; }
-keepclassmembers class * extends com.facebook.react.bridge.NativeModule {
    @com.facebook.react.bridge.ReactMethod *;
    public <init>(...);
}

-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactProp <methods>; }
-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactPropGroup <methods>; }
-keepnames class * extends com.facebook.react.uimanager.ViewManager
-keepnames class * extends com.facebook.react.uimanager.NativeViewHierarchyManager
-keepnames class * extends com.facebook.react.uimanager.ReactShadowNode
-keepnames class * extends io.branch.referral.StoreReferrerHuaweiAppGallery
-keepnames class * extends com.huawei.hms.ads.installreferrer.api.InstallReferrerStateListener
-keep class **$$PropsSetter
-keep class **$$ReactModuleInfoProvider
-keep class com.facebook.react.bridge.ReadableType { *; }

-keepnames class com.facebook.quicklog.QuickPerformanceLogger {
  void markerAnnotate(int,int,java.lang.String,java.lang.String);
  void markerTag(int,int,java.lang.String);
}

## Putting this here is kind of a hack.  I don't want to modify the OSS bridge.
## TODO mhorowitz: add @DoNotStrip to the interface directly.

-keepclassmembers class com.facebook.react.bridge.queue.MessageQueueThread {
  public boolean isOnThread();
  public void assertIsOnThread();
}

-keep class com.facebook.react.cxxbridge.ModuleRegistryHolder { *; }
-keep class com.facebook.react.cxxbridge.CatalystInstanceImpl { *; }
-keep class com.facebook.react.cxxbridge.JavaScriptExecutor { *; }
-keep class com.facebook.react.bridge.queue.NativeRunnable { *; }
-keep class com.facebook.react.bridge.ExecutorToken { *; }
-keep class com.facebook.react.bridge.ReadableType { *; }

-printusage ~/mytmplusapp/usage.txt
-printconfiguration ~/mytmplusapp/full-r8-config.txt
# End of content from /Users/<USER>/mytmplusapp/android/app/proguard-rules.pro
# The proguard configuration file for the following section is /Users/<USER>/mytmplusapp/android/app/build/intermediates/aapt_proguard_file/release/aapt_rules.txt
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.core.content.FileProvider { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class cl.json.RNShareFileProvider { <init>(); }
-keep class com.android.billingclient.api.ProxyBillingActivity { <init>(); }
-keep class com.auth0.react.AuthenticationActivity { <init>(); }
-keep class com.auth0.react.RedirectActivity { <init>(); }
-keep class com.facebook.react.devsupport.DevSettingsActivity { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.RevocationBoundService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.internal.SignInHubActivity { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementJobService { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementReceiver { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementService { <init>(); }
-keep class com.google.android.play.core.assetpacks.AssetPackExtractionService { <init>(); }
-keep class com.google.android.play.core.common.PlayCoreDialogWrapperActivity { <init>(); }
-keep class com.google.android.play.core.missingsplits.PlayCoreMissingSplitsActivity { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.iid.FirebaseInstanceIdReceiver { <init>(); }
-keep class com.google.firebase.messaging.FirebaseMessagingService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.google.mlkit.common.internal.MlKitComponentDiscoveryService { <init>(); }
-keep class com.google.mlkit.common.internal.MlKitInitProvider { <init>(); }
-keep class com.imagepicker.ImagePickerProvider { <init>(); }
-keep class com.reactnativecommunity.webview.RNCWebViewFileProvider { <init>(); }
-keep class com.taylormadegolf.mytaylormadeplus.app.android.MainActivity { <init>(); }
-keep class com.taylormadegolf.mytaylormadeplus.app.android.MainApplication { <init>(); }
-keep class com.testfairy.activities.ProvideFeedbackActivity { <init>(); }
-keep class expo.modules.filesystem.FileSystemFileProvider { <init>(); }
-keep class io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider { <init>(); }
-keep class io.invertase.firebase.messaging.ReactNativeFirebaseMessagingHeadlessService { <init>(); }
-keep class io.invertase.firebase.messaging.ReactNativeFirebaseMessagingReceiver { <init>(); }
-keep class io.invertase.firebase.messaging.ReactNativeFirebaseMessagingService { <init>(); }
-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.coordinatorlayout.widget.CoordinatorLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.MaterialToolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.chip.Chip { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.chip.ChipGroup { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.datepicker.MaterialCalendarGridView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.BaselineLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.CheckableImageButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.Snackbar$SnackbarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.SnackbarContentLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputEditText { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textview.MaterialTextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.maps.android.ui.RotationLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.henninghall.date_picker.pickers.AndroidNative { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.henninghall.date_picker.pickers.IosClone { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.reactnativecommunity.picker.CheckedTextViewImpl { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.reactnativecommunity.picker.TextViewImpl { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from /Users/<USER>/mytmplusapp/android/app/build/intermediates/aapt_proguard_file/release/aapt_rules.txt
# The proguard configuration file for the following section is /Users/<USER>/mytmplusapp/node_modules/react-native-code-push/android/app/build/intermediates/consumer_proguard_dir/release/lib0/proguard.txt
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Invoked via reflection, when setting js bundle.
-keepclassmembers class com.facebook.react.ReactInstanceManager {
    private final ** mBundleLoader;
}

# Can't find referenced class org.bouncycastle.**
-dontwarn com.nimbusds.jose.**

# End of content from /Users/<USER>/mytmplusapp/node_modules/react-native-code-push/android/app/build/intermediates/consumer_proguard_dir/release/lib0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/2457186f51368e6c3fe468e8a8f60fc9/transformed/jetified-react-native-0.70.2-release/proguard.txt
# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# Disabling obfuscation is useful if you collect stack traces from production crashes
# (unless you are using a system that supports de-obfuscate the stack traces).
# -dontobfuscate

# React Native

# Keep our interfaces so they can be used by other ProGuard rules.
# See http://sourceforge.net/p/proguard/bugs/466/
-keep,allowobfuscation @interface com.facebook.proguard.annotations.DoNotStrip
-keep,allowobfuscation @interface com.facebook.proguard.annotations.KeepGettersAndSetters

# Do not strip any method/class that is annotated with @DoNotStrip
-keep @com.facebook.proguard.annotations.DoNotStrip class *
-keepclassmembers class * {
    @com.facebook.proguard.annotations.DoNotStrip *;
}

-keep @com.facebook.proguard.annotations.DoNotStripAny class * {
    *;
}

-keepclassmembers @com.facebook.proguard.annotations.KeepGettersAndSetters class * {
  void set*(***);
  *** get*();
}

-keep class * implements com.facebook.react.bridge.JavaScriptModule { *; }
-keep class * implements com.facebook.react.bridge.NativeModule { *; }
-keepclassmembers,includedescriptorclasses class * { native <methods>; }
-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactProp <methods>; }
-keepclassmembers class *  { @com.facebook.react.uimanager.annotations.ReactPropGroup <methods>; }

-dontwarn com.facebook.react.**
-keep,includedescriptorclasses class com.facebook.react.bridge.** { *; }
-keep,includedescriptorclasses class com.facebook.react.turbomodule.core.** { *; }

# hermes
-keep class com.facebook.jni.** { *; }


# okio

-keep class sun.misc.Unsafe { *; }
-dontwarn java.nio.file.*
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn okio.**

# End of content from /Users/<USER>/.gradle/caches/transforms-3/2457186f51368e6c3fe468e8a8f60fc9/transformed/jetified-react-native-0.70.2-release/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/b02af0252155c47e36c0651bc0a01e87/transformed/jetified-play-services-auth-base-18.0.0/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.auth.zzeq {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/b02af0252155c47e36c0651bc0a01e87/transformed/jetified-play-services-auth-base-18.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/0c295bd1d59f7dd316af9f3845446359/transformed/jetified-firebase-appindexing-20.0.0/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.icing.zzda {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/0c295bd1d59f7dd316af9f3845446359/transformed/jetified-firebase-appindexing-20.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/0f4d19f67ab32c1bd81030029682771c/transformed/jetified-common-17.2.0/proguard.txt
# Annotations are implemented as attributes, so we have to explicitly keep them.
# Catch all which encompasses attributes like RuntimeVisibleParameterAnnotations
# and RuntimeVisibleTypeAnnotations
-keepattributes RuntimeVisible*Annotation*

# JNI is an entry point that's hard to keep track of, so there's
# an annotation to mark fields and methods used by native code.

# Keep the annotations that proguard needs to process.
-keep class com.google.android.apps.common.proguard.UsedBy*

# Just because native code accesses members of a class, does not mean that the
# class itself needs to be annotated - only annotate classes that are
# referenced themselves in native code.
-keep @com.google.android.apps.common.proguard.UsedBy* class * {
  <init>();
}
-keepclassmembers class * {
    @com.google.android.apps.common.proguard.UsedBy* *;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/0f4d19f67ab32c1bd81030029682771c/transformed/jetified-common-17.2.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/502467e5b478eb44e8e520e927ee2f80/transformed/jetified-play-services-base-18.1.0/proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from /Users/<USER>/.gradle/caches/transforms-3/502467e5b478eb44e8e520e927ee2f80/transformed/jetified-play-services-base-18.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/0614b8d9f71f5f49ff66173e3407622e/transformed/jetified-play-services-measurement-api-20.0.1/proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjx {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/0614b8d9f71f5f49ff66173e3407622e/transformed/jetified-play-services-measurement-api-20.0.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/82336660599100035579ec0ea8ca9a0d/transformed/jetified-firebase-messaging-23.0.0/proguard.txt
# Analytics library is optional.
# Access to this class is protected by try/catch(NoClassDefFoundError e)
# b/35686744 Don't fail during proguard if the class is missing from the APK.
-dontwarn com.google.android.gms.measurement.AppMeasurement*

# End of content from /Users/<USER>/.gradle/caches/transforms-3/82336660599100035579ec0ea8ca9a0d/transformed/jetified-firebase-messaging-23.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e99c05974e97b8933433a478f2040d00/transformed/jetified-firebase-common-20.0.0/proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/e99c05974e97b8933433a478f2040d00/transformed/jetified-firebase-common-20.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/1be8fc7634caf19b01b15c531305a905/transformed/jetified-play-services-tasks-18.0.2/proguard.txt


# End of content from /Users/<USER>/.gradle/caches/transforms-3/1be8fc7634caf19b01b15c531305a905/transformed/jetified-play-services-tasks-18.0.2/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/intermediates/consumer_proguard_dir/release/lib0/proguard.txt

-keepclassmembers class * {
  @org.unimodules.core.interfaces.ExpoProp *;
}

-keepclassmembers class * {
  @org.unimodules.core.interfaces.ExpoMethod *;
}

-keep @org.unimodules.core.interfaces.DoNotStrip class *
-keepclassmembers class * {
  @org.unimodules.core.interfaces.DoNotStrip *;
}

# End of content from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/intermediates/consumer_proguard_dir/release/lib0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/29b1bb955bf0f9f1e84448119e12aec3/transformed/jetified-transport-backend-cct-3.0.0/proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/29b1bb955bf0f9f1e84448119e12aec3/transformed/jetified-transport-backend-cct-3.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/3c14d81971522e4e91196c6347055b70/transformed/jetified-firebase-encoders-json-18.0.0/proguard.txt

# End of content from /Users/<USER>/.gradle/caches/transforms-3/3c14d81971522e4e91196c6347055b70/transformed/jetified-firebase-encoders-json-18.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/fe19d6236f1764cf768eb0bec06db4b7/transformed/material-1.1.0/proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior
-keepattributes RuntimeVisible*Annotation*

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# MaterialComponentsViewInflater inflates Material Components rather than their AppCompat counterparts.
-keep class com.google.android.material.theme.MaterialComponentsViewInflater { *; }

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# An inner class of RecyclerView is part of MaterialPickerDialogFragment.java 
-keep class androidx.recyclerview.widget.RecyclerView { *; }


# End of content from /Users/<USER>/.gradle/caches/transforms-3/fe19d6236f1764cf768eb0bec06db4b7/transformed/material-1.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/bf12c99a0e55c208b29c12e4b55daeed/transformed/appcompat-1.4.1/proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# aapt is not able to read app::actionViewClass and app:actionProviderClass to produce proguard
# keep rules. Add a commonly used SearchView to the keep list until b/109831488 is resolved.
-keep class androidx.appcompat.widget.SearchView { <init>(...); }

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/bf12c99a0e55c208b29c12e4b55daeed/transformed/appcompat-1.4.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/a4f45a06fc8a6415194a46c1964a19a2/transformed/rules/lib/META-INF/proguard/okhttp3.pro
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform

# End of content from /Users/<USER>/.gradle/caches/transforms-3/a4f45a06fc8a6415194a46c1964a19a2/transformed/rules/lib/META-INF/proguard/okhttp3.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/1e09e0e766a3f7257a574a79ccdba922/transformed/rules/lib/META-INF/proguard/okio.pro
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# End of content from /Users/<USER>/.gradle/caches/transforms-3/1e09e0e766a3f7257a574a79ccdba922/transformed/rules/lib/META-INF/proguard/okio.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/1bdc6b1de9552061169c3f42b3b0ccd2/transformed/jetified-transport-runtime-3.0.0/proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/1bdc6b1de9552061169c3f42b3b0ccd2/transformed/jetified-transport-runtime-3.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/c67d7972be31b3088d3f92d847878a21/transformed/jetified-af-android-sdk-6.9.1/proguard.txt



-dontwarn com.android.installreferrer, com.appsflyer.**




# https://support.appsflyer.com/hc/en-us/articles/207032126-AppsFlyer-SDK-Integration-Android#11-known-issues
# Also will make it easier to investigate stack traces coming from clients
-keep class com.appsflyer.** {
    <fields>;    <methods>;
}

-keep public class com.android.installreferrer.** {
    <fields>;    <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/c67d7972be31b3088d3f92d847878a21/transformed/jetified-af-android-sdk-6.9.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/b8766c37180d77a089d71f7810f5ff99/transformed/coordinatorlayout-1.1.0/proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# End of content from /Users/<USER>/.gradle/caches/transforms-3/b8766c37180d77a089d71f7810f5ff99/transformed/coordinatorlayout-1.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e76e58f77f24c8456cf5314bbc9652da/transformed/jetified-play-services-measurement-20.0.1/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjx {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/e76e58f77f24c8456cf5314bbc9652da/transformed/jetified-play-services-measurement-20.0.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/d166cecae539227e1588f253e15b5904/transformed/jetified-play-services-measurement-sdk-20.0.1/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjx {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/d166cecae539227e1588f253e15b5904/transformed/jetified-play-services-measurement-sdk-20.0.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/4e5b08ba297a08a2294e195026b4b4d9/transformed/jetified-glide-4.12.0/proguard.txt
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
  *** rewind();
}

# Uncomment for DexGuard only
#-keepresourcexmlelements manifest/application/meta-data@value=GlideModule

# End of content from /Users/<USER>/.gradle/caches/transforms-3/4e5b08ba297a08a2294e195026b4b4d9/transformed/jetified-glide-4.12.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/2ed39b644c13f27350a99eb2d1d11815/transformed/vectordrawable-animated-1.1.0/proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/2ed39b644c13f27350a99eb2d1d11815/transformed/vectordrawable-animated-1.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/6586c300b8dd217568eff5edc13e62c9/transformed/work-runtime-2.7.1/proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker (also marked with @Keep)
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from /Users/<USER>/.gradle/caches/transforms-3/6586c300b8dd217568eff5edc13e62c9/transformed/work-runtime-2.7.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/43ba66e14704102508c4e7fc20cf47fa/transformed/webkit-1.4.0/proguard.txt
# Copyright 2018 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# We need to avoid obfuscating the support library boundary interface because
# this API is shared with the Android Support Library.
# Note that we only 'keep' the package org.chromium.support_lib_boundary itself,
# any sub-packages of that package can still be obfuscated.
-keep public class org.chromium.support_lib_boundary.* { public *; }

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent WebViewClientCompat from being renamed, since chromium depends on this name.
-keepnames public class androidx.webkit.WebViewClientCompat


# End of content from /Users/<USER>/.gradle/caches/transforms-3/43ba66e14704102508c4e7fc20cf47fa/transformed/webkit-1.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/1879998fcb019d93003bc5f25db16b38/transformed/jetified-play-services-measurement-impl-20.0.1/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjx {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/1879998fcb019d93003bc5f25db16b38/transformed/jetified-play-services-measurement-impl-20.0.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/fd9cb28f8e85c40ec90da07215ef53ac/transformed/jetified-play-services-measurement-sdk-api-20.0.1/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjx {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/fd9cb28f8e85c40ec90da07215ef53ac/transformed/jetified-play-services-measurement-sdk-api-20.0.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/cf48850598a427c6ad547a0ebcf1179a/transformed/jetified-play-services-measurement-base-20.0.1/proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzjx {
  <fields>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/cf48850598a427c6ad547a0ebcf1179a/transformed/jetified-play-services-measurement-base-20.0.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/c55bc791bc6b78e34802f53ec279007b/transformed/jetified-play-services-basement-18.1.0/proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Internal Google annotations for generating Proguard keep rules.
-dontwarn com.google.android.apps.common.proguard.UsedBy*

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from /Users/<USER>/.gradle/caches/transforms-3/c55bc791bc6b78e34802f53ec279007b/transformed/jetified-play-services-basement-18.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e05017ba0c0eda9a6f4198caeceebbdb/transformed/fragment-1.3.6/proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/e05017ba0c0eda9a6f4198caeceebbdb/transformed/fragment-1.3.6/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/11db9379715c678ffc61f6bf888f8347/transformed/media-1.0.0/proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/11db9379715c678ffc61f6bf888f8347/transformed/media-1.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f0429e3e5c0fe28c6d0f3f4fdf2d813b/transformed/recyclerview-1.1.0/proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

-keepclassmembers class androidx.recyclerview.widget.RecyclerView {
    public void suppressLayout(boolean);
    public boolean isLayoutSuppressed();
}
# End of content from /Users/<USER>/.gradle/caches/transforms-3/f0429e3e5c0fe28c6d0f3f4fdf2d813b/transformed/recyclerview-1.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/c327aab2b2c4584b1d0c9384e4c2dbea/transformed/transition-1.2.0/proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/c327aab2b2c4584b1d0c9384e4c2dbea/transformed/transition-1.2.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/d147476b2078e09bbaca9b48b6099771/transformed/core-1.7.0/proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/d147476b2078e09bbaca9b48b6099771/transformed/core-1.7.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/08c62118acd98556dce9a0ae74905b2e/transformed/versionedparcelable-1.1.1/proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from /Users/<USER>/.gradle/caches/transforms-3/08c62118acd98556dce9a0ae74905b2e/transformed/versionedparcelable-1.1.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/c561efb41340c135bc9c4857b5a13980/transformed/jetified-lifecycle-viewmodel-savedstate-2.3.1/proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/c561efb41340c135bc9c4857b5a13980/transformed/jetified-lifecycle-viewmodel-savedstate-2.3.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/c2a23acd8728e334db9383c05fd3eae8/transformed/lifecycle-viewmodel-2.3.1/proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/c2a23acd8728e334db9383c05fd3eae8/transformed/lifecycle-viewmodel-2.3.1/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/11dead055066d5fb3aadfec300986820/transformed/jetified-savedstate-1.1.0/proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/11dead055066d5fb3aadfec300986820/transformed/jetified-savedstate-1.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f58130c3c47345d77a8b02b20d984567/transformed/jetified-lifecycle-process-2.4.0/proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from /Users/<USER>/.gradle/caches/transforms-3/f58130c3c47345d77a8b02b20d984567/transformed/jetified-lifecycle-process-2.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e01ced8166a89a9621ab6c19a9f9b90c/transformed/jetified-startup-runtime-1.0.0/proguard.txt
# This Proguard rule ensures that ComponentInitializers are are neither shrunk nor obfuscated.
# This is because they are discovered and instantiated during application initialization.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger

# End of content from /Users/<USER>/.gradle/caches/transforms-3/e01ced8166a89a9621ab6c19a9f9b90c/transformed/jetified-startup-runtime-1.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/7af83a5b092a74a3eedc797ce933f28b/transformed/room-runtime-2.2.5/proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# End of content from /Users/<USER>/.gradle/caches/transforms-3/7af83a5b092a74a3eedc797ce933f28b/transformed/room-runtime-2.2.5/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/e97261dd9d430edfcade384b9d785ef4/transformed/jetified-firebase-components-17.0.0/proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar

# End of content from /Users/<USER>/.gradle/caches/transforms-3/e97261dd9d430edfcade384b9d785ef4/transformed/jetified-firebase-components-17.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/ef65d3340d1ff5d8dc37fe9a77d8cad6/transformed/lifecycle-runtime-2.4.0/proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep !interface * implements androidx.lifecycle.LifecycleObserver {
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }
# End of content from /Users/<USER>/.gradle/caches/transforms-3/ef65d3340d1ff5d8dc37fe9a77d8cad6/transformed/lifecycle-runtime-2.4.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f3583d770217df2b3b6d6aa64d1a2853/transformed/jetified-transport-api-3.0.0/proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from /Users/<USER>/.gradle/caches/transforms-3/f3583d770217df2b3b6d6aa64d1a2853/transformed/jetified-transport-api-3.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/f728a57207ed40a8f666154c5ce0ceab/transformed/rules/lib/META-INF/proguard/androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/f728a57207ed40a8f666154c5ce0ceab/transformed/rules/lib/META-INF/proguard/androidx-annotations.pro
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/ce43f2fff809ae79faded6649cf8bd20/transformed/jetified-fbcore-2.5.0/proguard.txt
# Keep our interfaces so they can be used by other ProGuard rules.
# See http://sourceforge.net/p/proguard/bugs/466/
-keep,allowobfuscation @interface com.facebook.common.internal.DoNotStrip
-keep,allowobfuscation @interface com.facebook.soloader.DoNotOptimize

# Do not strip any method/class that is annotated with @DoNotStrip
-keep @com.facebook.common.internal.DoNotStrip class *
-keepclassmembers class * {
    @com.facebook.common.internal.DoNotStrip *;
}

# Do not strip any method/class that is annotated with @DoNotOptimize
-keep @com.facebook.soloader.DoNotOptimize class *
-keepclassmembers class * {
    @com.facebook.soloader.DoNotOptimize *;
}

# Keep native methods
-keepclassmembers class com.facebook.** {
    native <methods>;
}

# Do not strip SoLoader class and init method
-keep public class com.facebook.soloader.SoLoader {
    public static void init(android.content.Context, int);
}

-dontwarn okio.**
-dontwarn com.squareup.okhttp.**
-dontwarn okhttp3.**
-dontwarn javax.annotation.**
-dontwarn com.android.volley.toolbox.**
-dontwarn com.facebook.infer.**

# End of content from /Users/<USER>/.gradle/caches/transforms-3/ce43f2fff809ae79faded6649cf8bd20/transformed/jetified-fbcore-2.5.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/6af3602bf7d4d56e2388c8c1b872d766/transformed/jetified-appcenter-react-native-4.4.5/proguard.txt
# The following options are set by default.
# Make sure they are always set, even if the default proguard config changes.
-dontskipnonpubliclibraryclasses
-verbose
# End of content from /Users/<USER>/.gradle/caches/transforms-3/6af3602bf7d4d56e2388c8c1b872d766/transformed/jetified-appcenter-react-native-4.4.5/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/b9faf52aa34e2ae69ae80baee166f5df/transformed/jetified-appcenter-analytics-4.4.5/proguard.txt
# The following options are set by default.
# Make sure they are always set, even if the default proguard config changes.
-dontskipnonpubliclibraryclasses
-verbose
# End of content from /Users/<USER>/.gradle/caches/transforms-3/b9faf52aa34e2ae69ae80baee166f5df/transformed/jetified-appcenter-analytics-4.4.5/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/9b4904bb806584e17ac221937460745b/transformed/jetified-appcenter-crashes-4.4.5/proguard.txt
# The following options are set by default.
# Make sure they are always set, even if the default proguard config changes.
-dontskipnonpubliclibraryclasses
-verbose
# End of content from /Users/<USER>/.gradle/caches/transforms-3/9b4904bb806584e17ac221937460745b/transformed/jetified-appcenter-crashes-4.4.5/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/32c527fb8cdb1064eb317fc9a5f5e4d8/transformed/jetified-appcenter-4.4.5/proguard.txt
# The following options are set by default.
# Make sure they are always set, even if the default proguard config changes.
-dontskipnonpubliclibraryclasses
-verbose

-keepclasseswithmembers class * implements com.microsoft.appcenter.AppCenterService {
    public static ** getInstance();
}

-keepclassmembers class * implements javax.net.ssl.SSLSocketFactory {
    private final javax.net.ssl.SSLSocketFactory delegate;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/32c527fb8cdb1064eb317fc9a5f5e4d8/transformed/jetified-appcenter-4.4.5/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/487b47c3004a25164bd8fa9091d024f5/transformed/jetified-time4j-android-4.8-2021a/proguard.txt
-useuniqueclassmembernames

-keep class net.time4j.android.spi.AndroidResourceLoader

-keepnames class net.time4j.tz.olson.AFRICA
-keepnames class net.time4j.tz.olson.AMERICA
-keepnames class net.time4j.tz.olson.AMERICA$ARGENTINA
-keepnames class net.time4j.tz.olson.AMERICA$INDIANA
-keepnames class net.time4j.tz.olson.AMERICA$KENTUCKY
-keepnames class net.time4j.tz.olson.AMERICA$NORTH_DAKOTA
-keepnames class net.time4j.tz.olson.ANTARCTICA
-keepnames class net.time4j.tz.olson.ASIA
-keepnames class net.time4j.tz.olson.ATLANTIC
-keepnames class net.time4j.tz.olson.AUSTRALIA
-keepnames class net.time4j.tz.olson.EUROPE
-keepnames class net.time4j.tz.olson.INDIAN
-keepnames class net.time4j.tz.olson.PACIFIC

-keep class net.time4j.SPX
-keep class net.time4j.tz.SPX
-keep class net.time4j.tz.model.SPX

-keepnames class net.time4j.calendar.ChineseCalendar$SPX
-keepnames class net.time4j.calendar.CopticCalendar$SPX
-keepnames class net.time4j.calendar.EthiopianCalendar$SPX
-keepnames class net.time4j.calendar.EthiopianTime$SPX
-keepnames class net.time4j.calendar.HebrewCalendar$SPX
-keepnames class net.time4j.calendar.HebrewTime$SPX
-keepnames class net.time4j.calendar.HijriCalendar$SPX
-keepnames class net.time4j.calendar.HistoricCalendar$SPX
-keepnames class net.time4j.calendar.IndianCalendar$SPX
-keepnames class net.time4j.calendar.JapaneseCalendar$SPX
-keepnames class net.time4j.calendar.JucheCalendar$SPX
-keepnames class net.time4j.calendar.JulianCalendar$SPX
-keepnames class net.time4j.calendar.KoreanCalendar$SPX
-keepnames class net.time4j.calendar.MinguoCalendar$SPX
-keepnames class net.time4j.calendar.PersianCalendar$SPX
-keepnames class net.time4j.calendar.ThaiSolarCalendar$SPX
-keepnames class net.time4j.calendar.VietnameseCalendar$SPX
-keepnames class net.time4j.calendar.bahai.SPX
-keepnames class net.time4j.calendar.frenchrev.SPX
-keepnames class net.time4j.calendar.hindu.SPX
-keepnames class net.time4j.history.SPX

-keepclassmembers class net.time4j.** implements java.io.Serializable {
    static final long serialVersionUID;
    !static !transient <fields>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
-keepclassmembers class **.SPX implements java.io.Externalizable {
    static final long serialVersionUID;
    <init>();
    public void writeExternal(java.io.ObjectOutput);
    public void readExternal(java.io.ObjectInput);
    java.lang.Object readResolve();
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/487b47c3004a25164bd8fa9091d024f5/transformed/jetified-time4j-android-4.8-2021a/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/0ee9195eb5ebe90eca481b307686b798/transformed/jetified-billing-4.0.0/proguard.txt
# Keep the AIDL interface
-keep class com.android.vending.billing.** { *; }

-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
# End of content from /Users/<USER>/.gradle/caches/transforms-3/0ee9195eb5ebe90eca481b307686b798/transformed/jetified-billing-4.0.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/b2b7888f407367e72c764fcbd9e1d1d4/transformed/jetified-core-1.9.0/proguard.txt
-keepnames class com.google.android.play.core.review.ReviewInfo

# End of content from /Users/<USER>/.gradle/caches/transforms-3/b2b7888f407367e72c764fcbd9e1d1d4/transformed/jetified-core-1.9.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/0991df8c6bef75956123ccba04b6d301/transformed/biometric-1.1.0/proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.AuthenticationCallbackProvider$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.BiometricFragment$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.BiometricManager$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.CancellationSignalProvider$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.CryptoObjectUtils$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.FingerprintDialogFragment$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.KeyguardUtils$Api* {
    <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking
        class androidx.biometric.PackageUtils$Api* {
    <methods>;
}

# End of content from /Users/<USER>/.gradle/caches/transforms-3/0991df8c6bef75956123ccba04b6d301/transformed/biometric-1.1.0/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/a093700bdf2b257b2cbd069df7abbbfd/transformed/jetified-testfairy-android-sdk-1.12.27/proguard.txt
-keep class com.testfairy.** { *; }
-dontwarn com.testfairy.**
-keepattributes Exceptions, Signature, LineNumberTable
-dontusemixedcaseclassnames
# End of content from /Users/<USER>/.gradle/caches/transforms-3/a093700bdf2b257b2cbd069df7abbbfd/transformed/jetified-testfairy-android-sdk-1.12.27/proguard.txt
# The proguard configuration file for the following section is /Users/<USER>/.gradle/caches/transforms-3/8d62b23c2800dcbcdd4125a291270500/transformed/jetified-annotation-experimental-1.1.0/proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Ignore missing Kotlin meta-annotations so that this library can be used
# without adding a compileOnly dependency on the Kotlin standard library.
-dontwarn kotlin.Deprecated
-dontwarn kotlin.Metadata
-dontwarn kotlin.ReplaceWith
-dontwarn kotlin.annotation.AnnotationRetention
-dontwarn kotlin.annotation.AnnotationTarget
-dontwarn kotlin.annotation.Retention
-dontwarn kotlin.annotation.Target

# End of content from /Users/<USER>/.gradle/caches/transforms-3/8d62b23c2800dcbcdd4125a291270500/transformed/jetified-annotation-experimental-1.1.0/proguard.txt
# The proguard configuration file for the following section is <unknown>
-ignorewarnings
# End of content from <unknown>