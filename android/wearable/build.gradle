plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-android-extensions'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
}

android {
    compileSdk 33

    defaultConfig {
        applicationId "com.taylormadegolf.mytaylormadeplus.android"
        minSdk 25
        targetSdk 33
        versionName "2.9.3"
        versionCode versionCodeDate()
    }

    signingConfigs {
        release {
            storeFile file("${rootDir}/app/my-release-key-staging.keystore")
            storePassword "123456"
            keyAlias "my-key-alias"
            keyPassword "123456"
        }
        debug {
            storeFile file("${rootDir}/app/debug.keystore")
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        stagingRelease {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
    }

    buildFeatures {
        viewBinding true
    }

    dataBinding {
        enabled = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8.toString()
    }

    androidExtensions {
        experimental = true
    }
    flavorDimensions 'app'
    productFlavors {
        production {
            dimension 'app'
        }
        dev {
            dimension 'app'
            versionName "dev 1.0.0"
        }
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
}

kapt {
    correctErrorTypes = true
}

static def versionCodeDate() {
    return new Date().format("yyyyMMdd0").toInteger() + 3
}

dependencies {

    implementation 'androidx.core:core-ktx:1.7.0'
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.0-RC"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.6.0-RC"
    implementation 'com.google.android.gms:play-services-wearable:17.1.0'
    implementation 'com.google.android.gms:play-services-location:19.0.0'
    implementation 'androidx.percentlayout:percentlayout:1.0.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.appcompat:appcompat:1.4.0'
    implementation 'androidx.wear:wear:1.2.0'
    implementation 'io.reactivex:rxandroid:1.2.1'
    implementation "androidx.core:core-splashscreen:1.0.0-alpha02"

    //On going activity
    implementation "androidx.wear:wear-ongoing:1.0.0"
     // Includes LocusIdCompat and new Notification categories for Ongoing Activity.
    implementation "androidx.core:core:1.6.0"

    // Jetpack
    implementation "androidx.recyclerview:recyclerview:1.2.1"
    implementation 'androidx.activity:activity-ktx:1.4.0'
    kapt "com.android.databinding:compiler:3.1.4"

    // Hilt
    implementation("com.google.dagger:hilt-android:2.38.1")
    kapt("com.google.dagger:hilt-android-compiler:2.38.1")

    // Network
    implementation "com.squareup.retrofit2:retrofit:2.9.0"
    implementation "com.squareup.retrofit2:adapter-rxjava2:2.9.0"
    implementation "com.squareup.retrofit2:converter-gson:2.9.0"
    implementation "com.squareup.okhttp3:logging-interceptor:4.9.0"
    implementation "com.google.code.gson:gson:2.8.9"

    // RX
    implementation "com.jakewharton.rxbinding3:rxbinding-core:3.0.0"
    implementation "com.jakewharton.rxbinding3:rxbinding-appcompat:3.0.0"
    implementation "com.jakewharton.rxbinding3:rxbinding-recyclerview:3.0.0"
    implementation "io.reactivex.rxjava2:rxjava:2.2.18"
    implementation "io.reactivex.rxjava2:rxandroid:2.1.1"
    implementation 'com.jakewharton.rxrelay2:rxrelay:2.1.1'
    implementation 'com.jakewharton.rxbinding4:rxbinding:4.0.0'

    // Progress Bar
    implementation 'com.mikhaellopez:circularprogressbar:3.1.0'
    // Replaced deprecated ch.halcyon:squareprogressbar with alternative
    implementation 'com.github.mrwonderman:android-square-progressbar:1.6.0'
}