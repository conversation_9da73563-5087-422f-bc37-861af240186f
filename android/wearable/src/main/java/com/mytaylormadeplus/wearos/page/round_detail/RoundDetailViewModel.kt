package com.mytaylormadeplus.wearos.page.round_detail

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.model.Course
import com.mytaylormadeplus.wearos.data.model.Round
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.data.preferences.UserSession
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.utils.LiveDataWrapper
import com.mytaylormadeplus.wearos.utils.extension.toUnitMeters
import org.json.JSONObject
import java.util.*
import javax.inject.Inject

class RoundDetailViewModel @Inject constructor(
    private val appPreferences: AppPreferences,
    private val userSession: UserSession,
    private val playRoundManager: PlayRoundManager
) : BaseViewModel<RoundDetailNavigator>() {

    private val _roundData = MutableLiveData<Round>()
    val roundData: LiveData<Round>
        get() = _roundData

    val selectedTee = LiveDataWrapper<String>()

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }

    fun setData(course: Course) {
        playRoundManager.initRound(userSession.userID!!, course)
        _roundData.postValue(playRoundManager.round)
        selectedTee.postValue(convertUnitYds(playRoundManager.round.tee.teeName.toString(), playRoundManager.round.tee.totalYards))
    }

    fun getAllTeeNames(): ArrayList<String> {
        return playRoundManager.getAllTeeNames()
    }

    fun getAllRoundTypes(): ArrayList<String> {
        return playRoundManager.getAllRoundTypes()
    }

    fun getAllRoundModes(): ArrayList<String> {
        return playRoundManager.getAllRoundModes()
    }

    fun onTeeSelected(selectedIndex: Int) {
        playRoundManager.onTeeSelected(selectedIndex).let { round ->
            _roundData.postValue(round)
            selectedTee.postValue(convertUnitYds(round.tee.teeName.toString(), round.tee.totalYards))
        }
    }

    fun onTypeSelected(selectedIndex: Int) {
        playRoundManager.onTypeSelected(selectedIndex).let { round ->
            _roundData.postValue(round)
        }
    }

    fun onModeSelected(selectedIndex: Int) {
        playRoundManager.onModeSelected(selectedIndex).let { round ->
            if (round.roundMode.value == "Advanced" && !userSession.isShowAdvance!!) {
                navigator.goToShowAdvance()
            } else {
                _roundData.postValue(round)
            }
        }
    }

    fun playRound() {
        playRoundManager.playRound()
        navigator.goToPlayRound()
    }

    private fun convertUnitYds(teeName: String, totalYard: Long): String {
        if (teeName == "N/A" || totalYard == 0L) {
            return teeName
        }
        if (userSession.isUnitYard) {
            return "$teeName $totalYard YDS"
        }
        val total: String = (totalYard * 0.9144).toUnitMeters()
        return "$teeName $total M"
    }
}