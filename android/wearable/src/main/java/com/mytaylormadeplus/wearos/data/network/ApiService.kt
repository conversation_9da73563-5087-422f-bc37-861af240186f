package com.mytaylormadeplus.wearos.data.network

import com.mytaylormadeplus.wearos.data.network.request.SubmitRoundParam
import com.mytaylormadeplus.wearos.data.network.response.CourseListResponse
import io.reactivex.Observable
import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.*

interface ApiService {

    @POST("mrp/add-round")
    fun createRound(@Body body: SubmitRoundParam): Observable<Response<ResponseBody>>

    @PUT("mrp/update-round/{roundID}")
    fun editRound(@Path("roundID") roundID: String, @Body body: SubmitRoundParam): Observable<Response<ResponseBody>>

    @DELETE("play/round/{round_id}")
    fun deleteRound(@Path("round_id") roundId: String): Observable<Response<ResponseBody>>

    @GET("play/courses")
    fun iGolfSearchCourse(@Query("referenceLatitude") referenceLatitude: Double, @Query("referenceLongitude") referenceLongitude: Double, @Query("radius") radius: Int, @Query("resultsPerPage") resultsPerPage: Int, @Query("active") active: Int): Observable<CourseListResponse>

    @GET("play/courses/details/{courseID}")
    fun iGolfCourseDetail(@Path("courseID") courseID: String): Observable<Response<ResponseBody>>

    @GET("play/courses/details/{courseID}/tee")
    fun iGolfTee(@Path("courseID") courseID: String): Observable<Response<ResponseBody>>

    @GET("play/courses/details/{courseID}/scorecard")
    fun iGolfScorecard(@Path("courseID") courseID: String): Observable<Response<ResponseBody>>

    @GET("play/courses/details/{courseID}/gps")
    fun iGolfGPS(@Path("courseID") courseID: String): Observable<Response<ResponseBody>>

    @GET("play/courses/details/{courseID}/gps_vector")
    fun iGolfGPSVector(@Path("courseID") courseID: String): Observable<Response<ResponseBody>>

    @GET("play/clubs/active")
    fun getActiveClubs(@Query("take") take: Int): Observable<Response<ResponseBody>>

    @GET("play/show-advance")
    fun getShowAdvance(): Observable<Response<ResponseBody>>

//    @GET("api/users/{userID}/stats/club")
//    fun getClubStatsWithParameters(@Path("userID") userID: String, @Query("units") units: String): Observable<Response<ResponseBody>>
}