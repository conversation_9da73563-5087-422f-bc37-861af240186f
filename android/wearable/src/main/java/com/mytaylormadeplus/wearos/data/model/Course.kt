package com.mytaylormadeplus.wearos.data.model

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.mytaylormadeplus.wearos.utils.extension.toDistanceString
import kotlinx.android.parcel.Parcelize
import com.google.gson.annotations.SerializedName

@Parcelize
data class Course(
    @Expose
    val sdf: String? = null,
    @Expose
    @SerializedName("idCourse")
    val idCourse: String = "",
    @Expose
    @SerializedName("active")
    val active: Int? = null,
    @Expose
    @SerializedName("address1")
    val address1: String? = null,
    @Expose
    @SerializedName("address2")
    val address2: String? = null,
    @Expose
    @SerializedName("city")
    val city: String? = null,
    @Expose
    @SerializedName("countryFull")
    val countryFull: String? = null,
    @Expose
    @SerializedName("countryShort")
    val countryShort: String? = null,
    @Expose
    @SerializedName("courseName")
    val courseName: String? = null,
    @Expose
    @SerializedName("email")
    val email: String? = null,
    @Expose
    @SerializedName("gpsAvailable")
    val gpsAvailable: Int? = null,
    @Expose
    @SerializedName("layoutHoles")
    val layoutHoles: Int? = null,
    @Expose
    @SerializedName("layoutTotalHoles")
    val layoutTotalHoles: Int? = null,
    @Expose
    @SerializedName("layoutName")
    val layoutName: String? = null,
    @Expose
    @SerializedName("otherState")
    val otherState: String? = null,
    @Expose
    @SerializedName("stateShort")
    val stateShort: String? = null,
    @Expose
    @SerializedName("zipCode")
    val zipCode: String? = null,
    @Expose
    @SerializedName("scorecardAvailable")
    val scorecardAvailable: Int? = null,
    @Expose
    @SerializedName("syncOutputAvailable")
    val syncOutputAvailable: Int? = null,
    @Expose
    @SerializedName("vectorAvailable")
    val vectorAvailable: Int? = null,
    @Expose
    @SerializedName("conditionRating")
    val conditionRating: Double? = null,
    @Expose
    @SerializedName("recommendRating")
    val recommendRating: Double? = null,
    @Expose
    @SerializedName("distance")
    var distance: Double? = null,
    @Expose
    @SerializedName("latitude")
    val latitude: Double? = null,
    @Expose
    @SerializedName("longitude")
    val longitude: Double? = null,
    @Expose
    @SerializedName("teeTimesAvailable")
    val teeTimesAvailable: Int? = null,
    @Expose
    @SerializedName("classification")
    val classification: String? = null,
    @Expose
    @SerializedName("favoriteCourse")
    val favoriteCourse: Int? = null,

    var tees: List<Tee> = emptyList(),
    var holdNumber: Int = 0,
    var isUnitYds: String = "",
) : Parcelable {
    fun getDisplayDistance(): String {
        return (distance ?: 0.0).toDistanceString()
    }

    fun getFullAddress(): String {
        val address = address1 ?: address2
        val city = if (city.isNullOrEmpty()) "" else ", $city"
        val state = if (stateShort.isNullOrEmpty()) {
            if (otherState.isNullOrEmpty()) "" else ", $otherState"
        } else {
            ", $stateShort"
        }
        return address + city + state
    }

    fun isValidCourse(): Boolean {
        return scorecardAvailable == 1 && gpsAvailable == 1
    }

}