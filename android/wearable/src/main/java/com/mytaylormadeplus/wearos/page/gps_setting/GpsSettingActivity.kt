package com.mytaylormadeplus.wearos.page.gps_setting

import android.content.Intent
import android.net.Uri
import android.provider.Settings
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivityGpsSettingBinding
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class GpsSettingActivity : BaseActivity<GpsSettingNavigator, GpsSettingViewModel, ActivityGpsSettingBinding>(),
    GpsSettingNavigator {

    override fun setupViewDataBinding() {
        binding = ActivityGpsSettingBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
    }

    override fun setup() {
    }

    override fun gotoGpsSettings() {
        startActivity(Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", packageName, null)
        })
        finish()
    }
}