package com.mytaylormadeplus.wearos.manager

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import javax.inject.Inject

class NetworkService @Inject constructor(val context: Context, private val playRoundManager: PlayRoundManager) {

    private val connectivityManager = context.getSystemService(ConnectivityManager::class.java);
    private var isConnecting = false
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        // network is available for use
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            if(!isConnecting){
                isConnecting = true
                playRoundManager.endRoundWhenOnline()
            }
        }

        // Network capabilities have changed for the network
        override fun onCapabilitiesChanged(
            network: Network,
            networkCapabilities: NetworkCapabilities
        ) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            val hasCellular = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
            val hasWifi = networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        }

        // lost network connection
        override fun onLost(network: Network) {
            super.onLost(network)
            isConnecting = false
        }
    }
    fun start (){
        try {
            connectivityManager.unregisterNetworkCallback(networkCallback)
        } catch (e: Exception) {
        } finally {
            connect()
        }

    }
    private fun connect (){
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
            .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
            .addTransportType(NetworkCapabilities.TRANSPORT_BLUETOOTH)
            .build()
        connectivityManager.requestNetwork(networkRequest, networkCallback)

    }
}