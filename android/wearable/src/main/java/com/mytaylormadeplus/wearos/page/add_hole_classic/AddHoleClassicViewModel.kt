package com.mytaylormadeplus.wearos.page.add_hole_basic

import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.model.RoundHole
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import javax.inject.Inject

class AddHoleClassicViewModel @Inject constructor(val playRoundManager: PlayRoundManager) :
    BaseViewModel<AddHoleClassicNavigator>() {

    override fun setup() {

    }

    fun finishHoleClassic(roundHole: RoundHole) {
        playRoundManager.finishClassicHole(roundHole)
        navigator.finishHole()
    }

    fun finishHoleBasic(holeScore: Int) {
        playRoundManager.finishBasicHole(holeScore)
        navigator.finishHole()

    }

    fun isCompleteHole(): Boolean {
       return playRoundManager.checkCompleteHole()
    }

    fun isClassicRound(): <PERSON><PERSON><PERSON> {
        return playRoundManager.isClassicRound()
    }
}