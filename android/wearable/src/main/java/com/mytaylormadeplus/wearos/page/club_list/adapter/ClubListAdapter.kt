package com.mytaylormadeplus.wearos.page.club_list.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.mytaylormadeplus.wearos.R

class ClubListAdapter(
    private val rows: List<IRow>,
    private val clickItemClub: OnClickItemClub,
    private val clickBtnPutter: OnClickPutter,
    private val clickBtnEndHole: OnClickEndHole
): RecyclerView.Adapter<RecyclerView.ViewHolder>()  {

    private var arrayPutterPoint: ArrayList<PutterArray> = ArrayList()

    interface IRow
    class HeaderRow : IRow
    class ClubRow(val clubType: String, val clubName: String, val clubFamily: String) : IRow
    class PutterRow(val clubType: String, val clubName: String) : IRow
    class EndRow : IRow

    class HeaderViewHolder(itemView: View): RecyclerView.ViewHolder(itemView)

    inner class ClubViewHolder(itemView: View): RecyclerView.ViewHolder(itemView), View.OnClickListener {
        private var view: View = itemView
        val clubType: TextView = view.findViewById(R.id.lbClubType)
        val clubName: TextView = view.findViewById(R.id.lbClubName)
        val viewClubType: LinearLayout = view.findViewById(R.id.viewClubType)

        init {
            view.setOnClickListener(this)
        }

        override fun onClick(v: View?) {
            val position: Int = adapterPosition
            if (position != RecyclerView.NO_POSITION) {
                clickItemClub.onClickItemClub(position)
            }
        }
    }
    interface OnClickItemClub {
        fun onClickItemClub(position: Int)
    }

    inner class PutterViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        private var view: View = itemView
        private val btnPutter: LinearLayout = view.findViewById(R.id.btnAddPutter)
        private val btnMinus: LinearLayout = view.findViewById(R.id.btnMinus)
        private val btnPlus: LinearLayout = view.findViewById(R.id.btnPlus)
        private val lbPutterPoint: TextView = view.findViewById(R.id.lbPutterPoint)
        private var numberPoint: Int = 0
        init {
            btnMinus.setOnClickListener {
                val position: Int = adapterPosition
                if (numberPoint > 0) {
                    numberPoint = numberPoint.minus(1)
                    lbPutterPoint.text = numberPoint.toString()
                    addPutterToPosition(position, numberPoint)
                }
            }
            btnPlus.setOnClickListener {
                val position: Int = adapterPosition
                numberPoint = numberPoint.plus(1)
                lbPutterPoint.text = numberPoint.toString()
                addPutterToPosition(position, numberPoint)
            }
            btnPutter.setOnClickListener {
                val position: Int = adapterPosition
                clickBtnPutter.onClickPutter(numberPoint, position)
            }
        }
    }
    interface OnClickPutter {
        fun onClickPutter(number: Int, position: Int)
    }

    inner class EndViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        private var view: View = itemView
        private val btnEndHole: Button = view.findViewById(R.id.btnEndHole)
        init {
            btnEndHole.setOnClickListener {
                if (arrayPutterPoint.size > 0) {
                    clickBtnEndHole.onClickEndHole(arrayPutterPoint[0].number, arrayPutterPoint[0].position)
                } else {
                    clickBtnEndHole.onClickEndHole(0, 0)
                }
            }
        }
    }
    interface OnClickEndHole {
        fun onClickEndHole(number: Int, position: Int)
    }

    companion object {
        private const val TYPE_HEADER = 0
        private const val TYPE_CLUB = 1
        private const val TYPE_PUTTER = 2
        private const val TYPE_END = 3
    }

    override fun getItemViewType(position: Int): Int = when (rows[position]) {
        is HeaderRow -> TYPE_HEADER
        is ClubRow -> TYPE_CLUB
        is PutterRow -> TYPE_PUTTER
        is EndRow -> TYPE_END
        else -> throw IllegalArgumentException()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = when (viewType) {
        TYPE_HEADER -> HeaderViewHolder(LayoutInflater.from(parent.context)
            .inflate(R.layout.widget_club_list_header, parent, false))
        TYPE_CLUB -> ClubViewHolder(LayoutInflater.from(parent.context)
            .inflate(R.layout.widget_club_list_item, parent, false))
        TYPE_PUTTER -> PutterViewHolder(LayoutInflater.from(parent.context)
            .inflate(R.layout.widet_club_list_putter, parent, false))
        TYPE_END -> EndViewHolder(LayoutInflater.from(parent.context)
            .inflate(R.layout.widget_club_list_end, parent, false))
        else -> throw IllegalArgumentException()
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) = when (holder.itemViewType) {
        TYPE_HEADER -> onBindHeader()
        TYPE_CLUB -> onBindClub(holder, rows[position] as ClubRow)
        TYPE_PUTTER -> onBindPutter()
        TYPE_END -> onBindEnd()
        else -> throw IllegalArgumentException()
    }

    override fun getItemCount() = rows.count()

    private fun onBindHeader() { }

    private fun onBindClub(holder: RecyclerView.ViewHolder, row: ClubRow) {
        val clubRow = holder as ClubViewHolder
        if (row.clubType == "Driver") {
            clubRow.clubType.text = "D"
        } else if (row.clubFamily.lowercase() == "hybrid") {
            clubRow.clubType.text = row.clubType.replaceFirst("-", "").uppercase()
        } else {
            clubRow.clubType.text = row.clubType.replaceFirst("-", "")
        }
        clubRow.clubName.text = row.clubName
        if (row.clubName == "PENALTY") {
            clubRow.viewClubType.setBackgroundResource(R.drawable.bg_red_orange)
        } else {
            clubRow.viewClubType.setBackgroundResource(R.drawable.bg_black)
        }
    }

    private fun onBindEnd() {}

    private fun onBindPutter() { }

    private fun addPutterToPosition(position: Int, number: Int) {
        val index: Int = arrayPutterPoint.indexOfFirst { it.position == position }
        if (index < 0) {
            arrayPutterPoint.add(PutterArray(number, position))
        } else {
            if (number == 0) {
                arrayPutterPoint.removeAt(index)
            } else {
                arrayPutterPoint[index].number = number
            }
        }
        Log.i("info", "arrayPutterPoint: $index")
        Log.i("info", "arrayPutterPoint: $arrayPutterPoint")
    }
}

data class PutterArray(
    var number: Int,
    val position: Int
)