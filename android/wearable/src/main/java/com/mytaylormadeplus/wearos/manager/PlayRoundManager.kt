package com.mytaylormadeplus.wearos.manager

import android.location.Location
import com.google.gson.Gson
import com.mytaylormadeplus.wearos.base.BaseNavigator
import com.mytaylormadeplus.wearos.constant.RoundConfig
import com.mytaylormadeplus.wearos.data.model.*
import com.mytaylormadeplus.wearos.data.network.helper.easyCompose
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.data.preferences.UserSession
import com.mytaylormadeplus.wearos.data.repository.ClubsRepository
import com.mytaylormadeplus.wearos.data.repository.RoundRepository
import com.mytaylormadeplus.wearos.utils.*
import com.mytaylormadeplus.wearos.utils.extension.deepCopy
import com.mytaylormadeplus.wearos.utils.extension.toUnitMeters
import io.reactivex.disposables.CompositeDisposable
import org.json.JSONObject
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.collections.ArrayList
import kotlin.concurrent.timerTask
import kotlin.math.min
import kotlin.math.round

@Singleton
open class PlayRoundManager @Inject constructor(
    private val userSession: UserSession,
    private val appPreferences: AppPreferences,
    private val roundRepository: RoundRepository,
    private val compositeDisposable: CompositeDisposable,
    private val clubsRepository: ClubsRepository
) {
    lateinit var course: Course
    lateinit var round: Round
    lateinit var selectedHole: RoundHole
    val selectedHoleLiveData = LiveDataWrapper<RoundHole>()
    val selectedScoreLiveData = LiveDataWrapper<String>()
    val buttonAddShowLabel = LiveDataWrapper<String>()
    private var submittedRoundOffline = false

    var playRoundListener: PlayRoundListener? = null

    abstract class PlayRoundListener {
        open fun onRoundSubmitted() {}
        open fun onRoundEnded() {}
        open fun onNextHole() {}
        open fun onCanceled() {}
    }

    /************************************************************
     * Setup Round
     ************************************************************/

    fun initRound(userID: String, course: Course): Round {
        this.course = course
        this.round = Round()
        this.round.userID = userID
        this.round.courseID = course.idCourse
        this.round.courseName = course.courseName ?: ""
        this.round.courseAddress = course.getFullAddress()
        this.round.generatedBy = SystemUtils.getDeviceName()+" Watch"
        this.round.completed = false
        this.round.inprogress = true

        onTeeSelected(0)
        onTypeSelected(0)
        onModeSelected(0)

        return this.round
    }

    fun getAllTeeNames(): ArrayList<String> {
        val arrayData: ArrayList<String> = ArrayList()
        course.tees.map {
            arrayData.add(
                convertUnitYds(
                    it.teeName.toString(),
                    it.totalYards
                )
            )
        } as ArrayList<String>
        return arrayData
    }

    fun getAllRoundTypes(): ArrayList<String> {
        return RoundType.all.map { it.value } as ArrayList<String>
    }

    fun getAllRoundModes(): ArrayList<String> {
        return RoundMode.all.map { it.value } as ArrayList<String>
    }

    fun onTeeSelected(selectedIndex: Int): Round {
        round.tee = course.tees[selectedIndex]
        parseCourseGPSVectorByTeeName(round)
        round.teeName = round.tee.teeName ?: ""
        round.numberOfHoles = round.tee.numberOfHoles
        round.par = round.tee.totalPar
        round.tee.holes?.let { teeHoles ->
            round.holes = teeHoles.map {
                RoundHole(
                    holeNumber = it.holeNumber,
                    par = it.par,
                    yards = it.yards,
                    handicap = it.handicap,
                    pinLocation = it.greenMiddle,
                    greenFront = it.greenFrontList,
                    greenBack = it.greenBackList,
                    features = it.features
                )
            } as ArrayList<RoundHole>
        }
        return round
    }

    fun onTypeSelected(selectedIndex: Int): Round {
        round.roundType = RoundType.all[selectedIndex]
        return round
    }

    fun onModeSelected(selectedIndex: Int): Round {
        round.roundMode = RoundMode.all[selectedIndex]
        return round
    }

    /************************************************************
     * Play Round
     ************************************************************/

    fun playRound() {
        onHoleSelected(round.holes.first().holeNumber)
        this.round.playedOn = DateUtils.getCurrentDateString()
        cacheRoundData()
        submitRound()
    }

    fun getListActiveClubs(
        completeRound: Boolean = false,
        navigator: BaseNavigator? = null,
    ) {
        clubsRepository.getActiveClubs().easyCompose({ response ->
            response.body()?.string()?.let { responseData ->
                userSession.listActiveClubs = responseData
            }
        }, navigator = navigator, disposable = compositeDisposable, showProgress = completeRound)
    }

//    fun getClubStatsWithParameters(
//        completeRound: Boolean = false,
//        navigator: BaseNavigator? = null
//    ) {
//        clubsRepository.getClubStatsWithParameters().easyCompose({ response ->
//            response.body()?.string().let { responseData ->
//                userSession.distanceClub = responseData
//            }
//        }, navigator = navigator, disposable = compositeDisposable, showProgress = completeRound)
//    }

    fun getShowAdvance(
        completeRound: Boolean = false,
        navigator: BaseNavigator? = null,
    ) {
        userSession.isShowAdvance = false
        roundRepository.getShowAdvance().easyCompose({ response ->
            response.body()?.string().let { responseData ->
                val responseObject = JSONObject(responseData)
                userSession.isShowAdvance = responseObject.getBoolean("show")
            }
        }, navigator = navigator, disposable = compositeDisposable, showProgress = completeRound)
    }

    fun registerPlayRoundListener(playRoundViewModel: PlayRoundListener) {
        this.playRoundListener = playRoundViewModel
    }

    fun isAdvancedRound(): Boolean {
        return round.roundMode == RoundMode.Advanced
    }

    fun onHoleSelected(holeNumber: Int) {
        selectedHole = round.holes.first { it.holeNumber == holeNumber }
        round.selectedHoleNumber = holeNumber
        setupNextShot(selectedHole.holeScore.plus(1))
        selectedHoleLiveData.postValue(selectedHole)
        checkTotalScore()
    }

    private fun checkTotalScore(){
        if (round.roundMode == RoundMode.Advanced){
            selectedScoreLiveData.postValue(selectedHole.holeScore.toString())
        }else {
            val dataHole: ArrayList<RoundHole> =
                round.holes.filter { it.holeScore > 0 } as ArrayList<RoundHole>
            if(dataHole.size == 0){
                selectedScoreLiveData.postValue("--")
            }else {
                var totalPar = dataHole.map { it.par }.reduce { totalPar, par -> totalPar + par }
                when {
                    round.totalScore < totalPar -> {
                        selectedScoreLiveData.postValue("-${totalPar - round.totalScore}")
                    }
                    round.totalScore > totalPar -> {
                        selectedScoreLiveData.postValue("+${round.totalScore - totalPar}")
                    }
                    round.totalScore == totalPar -> {
                        selectedScoreLiveData.postValue("E")
                    }
                }
            }
        }
    }

    private fun selectNextHole() {
        if (selectedHole.holeNumber < round.numberOfHoles) {
            onHoleSelected(selectedHole.holeNumber + 1)
            playRoundListener?.onNextHole()
        } else {
            playRoundListener?.onRoundEnded()
        }
    }

    fun finishBasicHole(holeScore: Int) {
        if (selectedHole.holeNumber == 0) {
            selectedHole.updateStartedOn()
        }
        selectedHole.holeScore = holeScore
        selectedHole.lastSyncDate = System.currentTimeMillis()
        round.roundMode = RoundMode.Basic
        round.totalScore = round.holes.map { it.holeScore }
            .reduce { totalScore, holeScore -> totalScore + holeScore }
        selectedHoleLiveData.postValue(selectedHole)
        setupNextShot(selectedHole.holeScore.plus(1))
        cacheRoundData()
        submitRound()
        checkTotalScore()
        appPreferences.setCompleteHole(true)
        Timer().schedule(timerTask {
            selectNextHole()
        }, 1000)
    }

    fun finishClassicHole(roundHole: RoundHole) {
        if (selectedHole.holeNumber == 0) {
            selectedHole.updateStartedOn()
        }
        selectedHole = roundHole;
        selectedHole.finishHole = true
        selectedHole.lastSyncDate = System.currentTimeMillis()
        round.roundMode = RoundMode.Classic
        updateStatusCompleteHole()
        round.totalScore = round.holes.map { it.holeScore }
            .reduce { totalScore, holeScore -> totalScore + holeScore }
        selectedHoleLiveData.postValue(selectedHole)
        setupNextShot(selectedHole.holeScore.plus(1))
        cacheRoundData()
        submitRound()
        checkTotalScore()
        appPreferences.setCompleteHole(true)
        Timer().schedule(timerTask {
            selectNextHole()
        }, 1000)
    }


    fun finishAdvancedHole() {
        if (selectedHole.strokes.size > 0) {
            selectedHole.finishHole = true
        }
        updateStatusCompleteHole()
        submitRound()
        Timer().schedule(timerTask {
            selectNextHole()
        }, 1000)
    }

    fun updateStatusCompleteHole(){
        val holesUpdate: ArrayList<RoundHole> = ArrayList()
        for (hole in round.holes) {
            if (hole.holeNumber == selectedHole.holeNumber) {
                holesUpdate.add(selectedHole)
            } else {
                holesUpdate.add(hole)
            }
        }
        round.holes = holesUpdate
    }

    fun addShot(
        clubID: String?,
        clubName: String?,
        clubType: String?,
        location: Location?,
        distanceToPin: Long,
        isPutter: Boolean = false,
        putterCount: Int = 1,
        isPenalty: Boolean = false
    ) {
        if (selectedHole.strokes.isNullOrEmpty()) {
            selectedHole.updateStartedOn()
        }
        selectedHole.lastShotName = setupLastShotName(clubName, clubType, isPenalty, isPutter)
        if (isPutter) {
            for (i in 1..putterCount) {
                selectedHole.strokes.add(
                    createShot(
                        clubID = clubID,
                        location = location,
                        distanceToPin = distanceToPin,
                        isPutter = isPutter,
                        isPenalty = isPenalty
                    )
                )
            }
        } else {
            selectedHole.strokes.add(
                createShot(
                    clubID = clubID,
                    location = location,
                    distanceToPin = distanceToPin,
                    isPutter = isPutter,
                    isPenalty = isPenalty
                )
            )
        }
        if(selectedHole.strokes.size > 0){
            for (i in selectedHole.strokes.indices) {
                val roundStroke: RoundStroke = selectedHole.strokes.get(i)
                roundStroke.shotNumber = i + 1
                if(roundStroke.coords != null) {
                    val fromLocation = Location("Location")
                    fromLocation.latitude = roundStroke.coords!!.get(0)
                    fromLocation.longitude = roundStroke.coords!!.get(1)
                    roundStroke.shotDistance = getDistancePreviousShot(fromLocation, i)
                }
            }
        }

        selectedHole.holeScore = selectedHole.strokes.size
        selectedHole.lastSyncDate = System.currentTimeMillis()
        round.totalScore =
            round.holes.map { it.strokes.size }
                .reduce { totalScore, holeScore -> totalScore + holeScore }
        selectedHoleLiveData.postValue(selectedHole)
        setupNextShot(selectedHole.holeScore.plus(1))
        checkTotalScore()
        cacheRoundData()
        if (isPutter) {
            finishAdvancedHole()
        }
    }

    private fun createShot(
        clubID: String?,
        location: Location?,
        distanceToPin: Long,
        isPutter: Boolean = false,
        isPenalty: Boolean = false
    ): RoundStroke {
        return RoundStroke(
            clubID = clubID,
            ordinal = selectedHole.strokes.size + 1,
            startingLie = if (isPutter) "green" else "",
            timestamp = DateUtils.getCurrentDateString(),
            penalty = isPenalty,
            manuallyAdded = false,
            coords = if (isPutter || location == null) listOf(0.0, 0.0) else listOf(
                location.latitude,
                location.longitude
            ),
            shotDistance = if(location == null) 0L else getDistanceToPin(location, false),
            distanceToPin = distanceToPin,
            shotNumber = selectedHole.strokes.size + 1
        )
    }

    fun getTotalPutters(): Int {
        return round.holes.sumOf { hole -> hole.strokes.count { stroke -> stroke.startingLie == "green" } }
    }

    private fun setupNextShot(nextShot: Int) {
        if (isAdvancedRound()) {
            buttonAddShowLabel.postValue("ADD $nextShot${getSubHole(nextShot)} SHOT")
        } else {
            if (selectedHole.holeScore > 0) {
                buttonAddShowLabel.postValue("EDIT HOLE SCORE")
            } else {
                buttonAddShowLabel.postValue("ADD HOLE SCORE")

            }
        }
    }

    private fun setupLastShotName(
        clubName: String?,
        clubType: String?,
        isPenalty: Boolean = false,
        isPutter: Boolean = false
    ): String {
        if (isPutter) {
            return "P-"
        }
        if (isPenalty) {
            return "$clubName-"
        }
        if (clubType == "Driver") {
            return "D-"
        }
        return "${clubType?.replaceFirst("-", "")}-"
    }

    fun getPreviousShot(currentLocation: Location): String {
        val arrayStroke: ArrayList<RoundStroke> = selectedHole.strokes
        if (arrayStroke.size > 0) {
            val coordinates: List<Double>? = arrayStroke[arrayStroke.size - 1].coords
            val distance: Long = getDistanceToLastShot(
                coordinates = coordinates,
                currentLocation = currentLocation
            )
            return "${selectedHole.lastShotName}${distance}"
        }
        return ""
    }

   private fun getDistancePreviousShot(currentLocation: Location?, i: Int): Long {
       if(currentLocation != null){
            val arrayStroke: ArrayList<RoundStroke> = selectedHole.strokes
           if(arrayStroke.size > 0) {
               val coordinates: List<Double>? = if(i == arrayStroke.size - 1) selectedHole.pinLocation else arrayStroke[i + 1].coords
               return getDistanceToLastShot(
                   coordinates = coordinates,
                   currentLocation = currentLocation
               )
           }
       }
       return 0L
    }

    private fun getSubHole(holeNumber: Int): String {
        if (holeNumber == 1) {
            return "st"
        }
        if (holeNumber == 2) {
            return "nd"
        }
        if (holeNumber == 3) {
            return "rd"
        }
        return "th"
    }

    /************************************************************
     * Round Distance
     ************************************************************/

    fun getDistanceToPin(currentLocation: Location, useUnitYards: Boolean = false): Long {
        return getDistance(selectedHole.pinLocation, currentLocation, useUnitYards)
    }

    fun getDistanceToFront(currentLocation: Location): Long {
        return getDistance(selectedHole.greenFront, currentLocation)
    }

    fun getDistanceToBack(currentLocation: Location): Long {
        return getDistance(selectedHole.greenBack, currentLocation)
    }

    private fun getDistanceToLastShot(coordinates: List<Double>?, currentLocation: Location): Long {
        return getDistance(coordinates, currentLocation)
    }

    private fun getDistance(
        fromLocationCoords: List<Double>?,
        toLocation: Location,
        useUnitYards: Boolean = false
    ): Long {
        if (fromLocationCoords.isNullOrEmpty()) {
            return 0L
        }
        val fromLocation = Location("Location")
        fromLocation.latitude = fromLocationCoords[0]
        fromLocation.longitude = fromLocationCoords[1]
        var distance = fromLocation.distanceTo(toLocation) // Meters
        if (userSession.isUnitYard || useUnitYards) {
            distance = DistanceConverter.fromMetersToYards(distance) // Yards
        }
        return min(round(distance).toLong(), RoundConfig.ROUND_MAX_DISTANCE)
    }

    /************************************************************
     * Pause, Resume Round
     ************************************************************/

    private fun cacheRoundData() {
        val roundData = Gson().toJson(round).toString()
        appPreferences.saveRoundData(roundData)
    }

    private fun getRoundDataFromCache(): Round? {
        try {
            val roundData = appPreferences.getRoundData()
            if (!roundData.isNullOrEmpty()) {
                return Gson().fromJson(roundData, Round::class.java)
            }
        } catch (e: java.lang.Exception) {
        }
        return null
    }

    fun isClassicRound(): Boolean {
        return round.roundMode.equals(RoundMode.Classic)
    }

    fun checkCompleteHole(): Boolean {
        return appPreferences.isCompleteHole()
    }

    fun clearRoundDataCache() {
        appPreferences.saveRoundData("")
        appPreferences.setCompleteHole(false)
    }

    fun resumeRoundFromCache(): Boolean {
        getRoundDataFromCache()?.let {
            this.round = it
            onHoleSelected(round.selectedHoleNumber)
            return true
        }
        return false
    }

    /************************************************************
     * Submit Round
     ************************************************************/

    private var isRoundSubmitting = false

    fun submitRound(
        completeRound: Boolean = false,
        navigator: BaseNavigator? = null,
        playRoundListener: PlayRoundListener? = null
    ) {
        if (!completeRound && isRoundSubmitting) {
            return
        }
        isRoundSubmitting = true
        round.completed = completeRound
        round.inprogress = !completeRound
        val submitRound = round.deepCopy()
        val list = round.holes.filter { it.holeScore > 0 }

        if(!isNewRound()) {
            submitRound.holes.removeIf { it.isSynced() }
        }

        if (isNewRound()) {
            roundRepository.createRound(submitRound).easyCompose(
                { response ->
                    try {
                        val responseJson = JSONObject(response.body()?.string() ?: "{}")
                        this.round.roundID = responseJson.getString("roundId")
                    } catch (e: Exception) {
                    }
                    submitRound.holes.removeIf { it.isSynced() }
                    onRoundSubmitted(submitRound, false)
                    playRoundListener?.onRoundSubmitted()
                    isRoundSubmitting = false
                },
                error = {
                    isRoundSubmitting = false
                },
                navigator = navigator,
                disposable = compositeDisposable,
                showProgress = completeRound
            )
        } else if (list.isEmpty()) {
            roundRepository.deleteRound(submitRound.roundID).easyCompose(
                { response ->
                    onRoundSubmitted(submitRound, true)
                    playRoundListener?.onCanceled()
                    isRoundSubmitting = false
                },
                error = {
                    isRoundSubmitting = false
                },
                navigator = navigator,
                disposable = compositeDisposable,
                showProgress = completeRound
            )
        } else {
            roundRepository.editRound(round.roundID, submitRound).easyCompose(
                { response ->
                    onRoundSubmitted(submitRound, completeRound)
                    playRoundListener?.onRoundSubmitted()
                    isRoundSubmitting = false
                },
                error = {
                    isRoundSubmitting = false
                },
                navigator = navigator,
                disposable = compositeDisposable,
                showProgress = completeRound
            )
        }
    }

    fun cancelRound(
        navigator: BaseNavigator? = null,
        playRoundListener: PlayRoundListener? = null
    ) {
        isRoundSubmitting = true
        val submitRound = round.deepCopy()
        if (submitRound.roundID != null) {
            submitRound.holes.removeIf { it.isSynced() }
            roundRepository.deleteRound(submitRound.roundID).easyCompose(
                { response ->
                    onRoundSubmitted(submitRound, true)
                    playRoundListener?.onCanceled()
                    isRoundSubmitting = false
                },
                error = {
                    isRoundSubmitting = false
                },

                navigator = navigator,
                disposable = compositeDisposable,
                showProgress = true
            )
        } else {
            playRoundListener?.onCanceled()
        }
    }

    private fun onRoundSubmitted(submittedRound: Round, completeRound: Boolean) {
        submittedRound.holes.forEach { submittedHole ->
            round.holes.first { it.holeNumber == submittedHole.holeNumber }.let { hole ->
                hole.lastSyncDate = submittedHole.lastModifiedDate
            }
        }
        if (completeRound) {
            clearRoundDataCache()
        } else {
            cacheRoundData()
        }
    }
    fun endRoundWhenOnline() {
        try {
            if(!submittedRoundOffline) {
                getRoundDataFromCache()?.let {
                    submittedRoundOffline = true
                    this.round = it
                    val submitRound = round.deepCopy()
                    submitRound.holes.removeIf { it.isSynced() }
                    if (submitRound.holes.size > 0) {
                        roundRepository.editRound(round.roundID, submitRound).easyCompose({ response ->
                            submittedRoundOffline = false
                            onRoundSubmitted(submitRound, false)
                        }, error = {
                            submittedRoundOffline = false
                        }, navigator = null, disposable = compositeDisposable, showProgress = false)
                    } else {
                        submittedRoundOffline = false
                    }
                }
            }

        } catch (ex: Exception) {
            submittedRoundOffline = false
        }
    }

    private fun isNewRound(): Boolean {
        return round.roundID.isNullOrEmpty()
    }

    private fun convertUnitYds(teeName: String, totalYard: Long): String {
        if (teeName == "N/A" || totalYard == 0L) {
            return teeName
        }
        if (userSession.isUnitYard) {
            return "$teeName $totalYard YDS"
        }
        val total: String = (totalYard * 0.9144).toUnitMeters()
        return "$teeName $total M"
    }

    private fun parseCourseGPSVectorByTeeName(round: Round) {
        val responseJson = JSONObject(appPreferences.getIGolfGPSVector() ?: "{}")
        val vectorGPSObject = responseJson.getJSONObject("vectorGpsObject")
        val listHoleDataJson = vectorGPSObject.getJSONObject("holes").getJSONArray("hole")
        for (i in 0 until listHoleDataJson.length()) {
            val holeData = listHoleDataJson.getJSONObject(i)
            val holeNumber = holeData.getInt("holeNumber")
            val selectedHole: Hole =
                round.tee.holes?.firstOrNull { it.holeNumber == holeNumber } ?: continue
            getHoleData(selectedHole, holeData)
        }
    }

    private fun getHoleData(selectedHole: Hole, holeData: JSONObject) {
        val featureCollection = FeatureCollection()
        featureCollection.type = "FeatureCollection"
        val listFeatures: ArrayList<Feature> = ArrayList()
        featureCollection.features = listFeatures
        selectedHole.features = featureCollection

        createNewFeature(
            listFeatures, holeData,
            "teebox",
            FeaturePropertyType.TEE,
            selectedHole.holeNumber,
            3,
            "Tee"
        )
        createNewFeature(
            listFeatures,
            holeData,
            "fairway",
            FeaturePropertyType.FAIRWAY,
            selectedHole.holeNumber,
            4,
            "Fairway"
        )
        createNewFeature(
            listFeatures,
            holeData,
            "bunker",
            FeaturePropertyType.BUNKER,
            selectedHole.holeNumber,
            0,
            "Bunker"
        )
        createNewFeature(
            listFeatures,
            holeData,
            "sand",
            FeaturePropertyType.BUNKER,
            selectedHole.holeNumber,
            0,
            "Bunker"
        )
        createNewFeature(
            listFeatures,
            holeData,
            "green",
            FeaturePropertyType.GREEN,
            selectedHole.holeNumber,
            1,
            "Green"
        )
    }

    private fun createNewFeature(
        listFeatures: ArrayList<Feature>,
        holeData: JSONObject,
        dataKey: String,
        featureProperyType: String,
        holeNumber: Int,
        position: Int,
        label: String
    ) {
        if (!holeData.has(dataKey)) {
            return
        }
        val shapeJsonArray =
            holeData.getJSONObject(dataKey).getJSONObject("shapes").getJSONArray("shape")
        for (index in 0 until shapeJsonArray.length()) {
            val feature = Feature()
            feature.type = "Feature"
            val properties = FeatureProperty()
            properties.type = featureProperyType
            properties.holeNumber = holeNumber
            properties.kind = "polygon"
            properties.label = label
            properties.position = position
            feature.properties = properties
            val geometry = GeometryShape()
            geometry.type = "Polygon"
            val coordinateItem: MutableList<List<Double>> = ArrayList()
            val pointsBoundary = shapeJsonArray.getJSONObject(index).getString("points")
            val listPoints = pointsBoundary.split(",").toTypedArray()
            for (pointsString in listPoints) {
                val points = pointsString.split(" ").toTypedArray()
                val pointItem: MutableList<Double> = ArrayList()
                pointItem.add(java.lang.Double.valueOf(points[0]))
                pointItem.add(java.lang.Double.valueOf(points[1]))
                coordinateItem.add(pointItem)
            }
            if (coordinateItem.isNotEmpty()) {
                val coordinates: MutableList<List<List<Double>>> = ArrayList()
                coordinates.add(coordinateItem)
                geometry.coordinates = coordinates
                feature.geometry = geometry
                listFeatures.add(feature)
            }
        }
    }
    fun unitIsYard(): Boolean {
        return userSession.isUnitYard
    }
}
