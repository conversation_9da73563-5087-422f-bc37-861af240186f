package com.mytaylormadeplus.wearos.page.score_card

import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.model.RoundHole
import com.mytaylormadeplus.wearos.data.model.RoundMode
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.page.score_card.adapter.ScoreCardAdapter
import javax.inject.Inject

class ScoreCardViewModel @Inject constructor(
    val playRoundManager: PlayRoundManager,
    private val appPreferences: AppPreferences
): BaseViewModel<ScoreCardNavigator>() {

    private var totalScore: Int = 0
    private val rows = mutableListOf<ScoreCardAdapter.IRow>()

    fun getScoreCard(): MutableList<ScoreCardAdapter.IRow> {
        rows.add(ScoreCardAdapter.HeaderRow())
        val roundHoles: ArrayList<RoundHole> = playRoundManager.round.holes
        var puttsScore = 0
        roundHoles.forEach { puttsScore += it.puttsNumber }
        if (roundHoles.size <= 9) {
            roundHoles.map { rows.add(ScoreCardAdapter.ScoreRow(it.holeNumber, getSubHole(it.holeNumber), getHoleScore(it.holeScore), it.par)) }
        } else {
            val first9Hole: ArrayList<RoundHole> = roundHoles.filter{ it.holeNumber < 10 } as ArrayList<RoundHole>
            print(first9Hole)
            first9Hole.map { rows.add(ScoreCardAdapter.ScoreRow(it.holeNumber, getSubHole(it.holeNumber), getHoleScore(it.holeScore), it.par)) }
            rows.add(ScoreCardAdapter.TotalRow("OUT", getTotalScore(first9Hole)))
            val last9Hole: ArrayList<RoundHole> = roundHoles.filter{ it.holeNumber > 9 } as ArrayList<RoundHole>
            print(last9Hole)
            last9Hole.map { rows.add(ScoreCardAdapter.ScoreRow(it.holeNumber, getSubHole(it.holeNumber), getHoleScore(it.holeScore), it.par)) }
            rows.add(ScoreCardAdapter.TotalRow("IN", getTotalScore(last9Hole)))
        }
        rows.add(ScoreCardAdapter.TotalRow("TOTAL", getTotalScore(roundHoles)))
        rows.add(ScoreCardAdapter.FooterRow(puttsScore, playRoundManager.round.roundMode.equals(RoundMode.Basic)||playRoundManager.round.roundMode.equals(RoundMode.Advanced)))
        return rows
    }

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }

    private fun getSubHole(holeNumber: Int): String {
        if (holeNumber == 1) { return "st" }
        if (holeNumber == 2) { return "nd" }
        if (holeNumber == 3) { return "rd" }
        return "th"
    }

    private fun getHoleScore(holeScore: Int): Int {
        return holeScore
    }

    private fun getTotalScore(roundHoles: ArrayList<RoundHole>): Int {
        totalScore = roundHoles.map { it.holeScore }.reduce { totalScore, holeScore -> totalScore + holeScore }
        return  totalScore
    }

    fun goToPlayRound(numberHole: Int) {
        val selectHole: ScoreCardAdapter.ScoreRow = rows[numberHole] as ScoreCardAdapter.ScoreRow
        playRoundManager.onHoleSelected(selectHole.numberHole)
    }
}