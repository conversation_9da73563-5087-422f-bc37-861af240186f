package com.mytaylormadeplus.wearos.page.nearby_courses

import android.location.Location
import android.util.Log
import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.constant.RoundConfig
import com.mytaylormadeplus.wearos.data.model.*
import com.mytaylormadeplus.wearos.data.network.helper.easyCompose
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.data.preferences.UserSession
import com.mytaylormadeplus.wearos.data.repository.CourseRepository
import com.mytaylormadeplus.wearos.manager.LocationProvider
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.utils.LiveDataWrapper
import com.mytaylormadeplus.wearos.utils.extension.toDistanceString
import org.json.JSONArray
import org.json.JSONObject
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers
import rx.subscriptions.CompositeSubscription
import java.util.*
import javax.inject.Inject
import kotlin.collections.ArrayList
import kotlin.math.min
import kotlin.math.round

class NearbyCoursesViewModel @Inject constructor(
    private val playRoundManager: PlayRoundManager,
    private val userSession: UserSession,
    private val locationProvider: LocationProvider,
    private val appPreferences: AppPreferences
) : BaseViewModel<NearbyCoursesNavigator>() {

    @Inject
    lateinit var courseRepository: CourseRepository

    val courseData = LiveDataWrapper<List<Course>>()

    private val compositeSubscription = CompositeSubscription()

    override fun setup() {
        super.setup()
        setupLocationListener()
    }

    override fun onDestroy() {
        super.onDestroy()
        compositeSubscription.clear()
        locationProvider.stop()
    }

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }

    private fun setupLocationListener() {
        compositeSubscription.add(locationProvider.getLocationObservable()
            .subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { location ->
                location?.let {
                    val distance = handleDistanceToMeters(it.latitude, it.longitude)
                    Log.i("info", "distance: $distance")
                    if (distance > 800) {
                        userSession.currentLong = it.longitude.toString()
                        userSession.currentLat = it.latitude.toString()
                        Log.i("info", "distance: $distance")
                        getCourseList(it.latitude, it.longitude)
                    }
                }
            })
    }

    private fun getCourseList(lat: Double, lng: Double) {
        val radius = 40
        val resultsPerPage = 10
        navigator.showLoadingView()
        courseRepository.iGolfSearchCourse(
            radius = radius,
            lat = lat,
            lng = lng,
            resultsPerPage = resultsPerPage
        )
            .easyCompose({ response ->
                response.courseList?.filter { it.isValidCourse() }?.let {
                    val data = ArrayList<Course>()
                    data.addAll(it)
                    setData(data)
                }
            }, navigator = navigator, disposable = compositeDisposable, showProgress = false)
        navigator.hideLoadingView()
    }

    fun setData(courseList: ArrayList<Course>?) {
        if (userSession.isUnitYard) {
            courseList?.forEach { it.isUnitYds = "mi" }
        } else {
            courseList?.forEach {
                it.distance = handleDistance(it).toDouble()
            }
            courseList?.forEach { it.isUnitYds = "km" }
        }
        courseList?.let {
            courseData.postValue(it)
        }
    }

    fun getCourseDetails(course: Course) {
        navigator.showLoadingView()
        courseRepository.iGolfTee(courseID = course.idCourse).easyCompose({ response ->
            response.body()?.string()?.let { responseData ->
                parseCourseTees(course, responseData)
            }
            getCourseScoreCard(course)
            playRoundManager.getListActiveClubs()
            //playRoundManager.getClubStatsWithParameters()
            playRoundManager.getShowAdvance()
        }, navigator = navigator, disposable = compositeDisposable, showProgress = false)
    }

    private fun getCourseScoreCard(course: Course) {
        courseRepository.iGolfScorecard(courseID = course.idCourse).easyCompose({ response ->
            response.body()?.string()?.let { responseData ->
                parseCourseScorecard(course, responseData)
            }
            getCourseGPS(course)
        }, navigator = navigator, disposable = compositeDisposable, showProgress = false)
    }

    private fun getCourseGPS(course: Course) {
        courseRepository.iGolfGPS(courseID = course.idCourse).easyCompose({ response ->
            response.body()?.string()?.let { responseData ->
                parseCourseGPS(course, responseData)
            }
            getCourseGPSVector(course)
        }, navigator = navigator, disposable = compositeDisposable, showProgress = false)
    }

    private fun getCourseGPSVector(course: Course) {
        courseRepository.iGolfGPSVector(courseID = course.idCourse).easyCompose({ response ->
            response.body()?.string()?.let { responseData ->
                val responseJson = JSONObject(responseData)
                if(responseJson.has("errorDetails")) {
                    val errorMessage = "This course is missing essential data. Please try again with another course"
                    navigator.showError(errorMessage)
                    navigator.hideLoadingView()
                } else {
                    if(course.tees[0].teeName == "N/A") {
                        val errorMessage = "This course is missing data. You can still track your round but some features may be unavailable."
                        navigator.showError(errorMessage)
                    }
                    appPreferences.saveIGolfGPSVector(responseData)
                    navigator.hideLoadingView()
                    navigator.gotoRoundDetails(course)
                }
            }
        }, navigator = navigator, disposable = compositeDisposable, showProgress = false)
    }

    private fun parseCourseGPS(course: Course, response: String) {
        val responseJson = JSONObject(response)
        if(responseJson.has("errorDetails")) {
            val errorMessage = "This course is missing essential data. Please try again with another course"
            navigator.showError(errorMessage)
            return
        }
        val gpsList: JSONArray = responseJson.getJSONArray("gpsList")
        val listHolesMap: MutableMap<Int, JSONObject> = HashMap()
        for (i in 0 until gpsList.length()) {
            val holeJson = gpsList.getJSONObject(i)
            val holeNumber = holeJson.getInt("holeNumber")
            listHolesMap[holeNumber] = holeJson
        }
        for (tee in course.tees) {
            for (hole in tee.holes ?: emptyList()) {
                val holeJson = listHolesMap[hole.holeNumber] ?: continue

                try {
                    // Front
                    val greenFront: MutableList<Double> = mutableListOf()
                    if (!holeJson.isNull("frontLat") && !holeJson.isNull("frontLon")) {
                        greenFront.add(holeJson.optDouble("frontLat", 0.0))
                        greenFront.add(holeJson.optDouble("frontLon", 0.0))
                    }
                    hole.greenFrontList = greenFront

                    // Middle
                    val greenMiddle: MutableList<Double> = mutableListOf()
                    if (!holeJson.isNull("centerLat") && !holeJson.isNull("centerLon")) {
                        greenMiddle.add(holeJson.optDouble("centerLat", 0.0))
                        greenMiddle.add(holeJson.optDouble("centerLon", 0.0))
                    }
                    hole.greenMiddle = greenMiddle

                    // Back
                    val greenBack: MutableList<Double> = mutableListOf()
                    if (!holeJson.isNull("backLat") && !holeJson.isNull("backLon")) {
                        greenBack.add(holeJson.optDouble("backLat", 0.0))
                        greenBack.add(holeJson.optDouble("backLon", 0.0))
                    }
                    hole.greenBackList = greenBack

                } catch (e: Exception) {
                    Log.e("HoleDataParsing", "Error parsing hole ${hole.holeNumber}: ${e.message}")
                    continue
                }
            }
        }
    }

    private fun parseCourseScorecard(course: Course, response: String) {
        val responseJson = JSONObject(response)
        if(responseJson.has("errorDetails")) {
            val errorMessage = "This course is missing essential data. Please try again with another course"
            navigator.showError(errorMessage)
            return
        }
        val menScorecardList = responseJson.getJSONArray("menScorecardList")
        val wmnScorecardList = responseJson.getJSONArray("wmnScorecardList")
        for (tee in course.tees) {
            var pars: JSONArray
            var hcps: JSONArray
            var totalPars = 0
            if (tee.isMenTee) {
                pars = menScorecardList.getJSONObject(0).getJSONArray("parHole")
                hcps = menScorecardList.getJSONObject(0).getJSONArray("hcpHole")
            } else {
                pars = wmnScorecardList.getJSONObject(0).getJSONArray("parHole")
                hcps = wmnScorecardList.getJSONObject(0).getJSONArray("hcpHole")
            }
            for (hole in tee.holes!!) {
                hole.par = pars.getInt(hole.holeNumber - 1)
                hole.handicap = hcps.getInt(hole.holeNumber - 1)
                totalPars += hole.par
            }
            tee.totalPar = totalPars
        }
    }

    private fun parseCourseTees(course: Course, response: String) {
        try {
            val responseJson = JSONObject(response)

            // Check if response contains errorDetails
            if (responseJson.has("errorDetails")) {
                // Set a tee N/A for the List Tee
                val listTees: MutableList<Tee> = ArrayList<Tee>()
                val tee = Tee()
                val teeName = "N/A"
                tee.teeName = teeName
                tee.gender = "men"
                tee.rating = 0.0f
                tee.slope = 0.0f
                val courseNumHoles = course.layoutHoles ?: 0
                tee.numberOfHoles = courseNumHoles
                tee.totalYards = 0L
                val listHoles: MutableList<Hole> = ArrayList<Hole>()
                for (index in 1..courseNumHoles) {
                    val hole = Hole()
                    hole.holeNumber = index
                    hole.yards = 0L
                    listHoles.add(hole)
                }

                tee.holes = listHoles
                listTees.add(tee)
                course.tees = listTees
                course.holdNumber = courseNumHoles
                return
            }

            val teesListJson = responseJson.getJSONArray("teesList")
            val courseNumHoles = responseJson.getInt("courseNumHoles")
            val listTees: MutableList<Tee> = ArrayList<Tee>()

            for (i in 0 until teesListJson.length()) {
                val teeData = teesListJson.getJSONObject(i)
                val tee = Tee()
                val teeName = teeData.getString("teeName")
                tee.teeName = teeName
                tee.gender = teeData.getString("gender")

                if (tee.isMenTee) {
                    tee.rating = teeData.getDouble("ratingMen").toFloat()
                    tee.slope = teeData.getDouble("slopeMen").toFloat()
                } else {
                    tee.rating = teeData.getDouble("ratingWomen").toFloat()
                    tee.slope = teeData.getDouble("slopeWomen").toFloat()
                }

                tee.numberOfHoles = courseNumHoles

                tee.totalYards = if (!teeData.isNull("ydsTotal")) {
                    teeData.getLong("ydsTotal")
                } else {
                    0L
                }

                val listHoles: MutableList<Hole> = ArrayList<Hole>()

                if (!teeData.isNull("ydsHole")) {
                    val ydsHole = teeData.getJSONArray("ydsHole")

                    for (index in 1..courseNumHoles) {
                        val hole = Hole()
                        hole.holeNumber = index

                        hole.yards = if (index - 1 < ydsHole.length() && !ydsHole.isNull(index - 1)) {
                            ydsHole.getLong(index - 1)
                        } else {
                            0L
                        }

                        listHoles.add(hole)
                    }
                } else {
                    for (index in 1..courseNumHoles) {
                        val hole = Hole()
                        hole.holeNumber = index
                        hole.yards = 0L
                        listHoles.add(hole)
                    }
                }

                tee.holes = listHoles
                listTees.add(tee)
            }

            course.holdNumber = courseNumHoles
            course.tees = listTees
        } catch (e: Exception) {
            navigator.showError("${e.message}")
            course.holdNumber = course.layoutHoles ?: 0
            course.tees = emptyList()
        }
    }

    private fun handleDistance(res: Course): String {
        val fromLocation = Location("Location")
        fromLocation.latitude = res.latitude!!
        fromLocation.longitude = res.longitude!!
        val currentLocation: Location = Location("Location")
        currentLocation.latitude = userSession.currentLat?.toDouble() ?: fromLocation.latitude
        currentLocation.longitude = userSession.currentLong?.toDouble() ?: fromLocation.longitude
        Log.i("info", "diss: ${"%.1f".format(currentLocation.distanceTo(fromLocation) * 0.001)}")
        return (currentLocation.distanceTo(fromLocation) * 0.001).toDistanceString()
    }

    private fun handleDistanceToMeters(latitude: Double, longitude: Double): Long {
        val fromLocation = Location("Location")
        fromLocation.latitude = latitude
        fromLocation.longitude = longitude
        val currentLocation: Location = Location("Location")
        currentLocation.latitude = userSession.currentLat?.toDouble() ?: fromLocation.latitude
        currentLocation.longitude = userSession.currentLong?.toDouble() ?: fromLocation.longitude
        val distance = fromLocation.distanceTo(currentLocation) // Meters
        return min(round(distance).toLong(), RoundConfig.ROUND_MAX_DISTANCE)
    }
}