package com.mytaylormadeplus.wearos.data.model

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Round(

    @Expose
    @SerializedName("user_id")
    var userID: String = "",

    @Expose
    @SerializedName("igolf_course_id")
    var courseID: String = "",

    @Expose
    @SerializedName("course_id")
    var mprCourseID: String = "0",

    @Expose
    @SerializedName("map_id")
    var mapID: String = "iGolf",

    @Expose
    @SerializedName("course_name")
    var courseName: String = "",

    @Expose
    @SerializedName("generated_by")
    var generatedBy: String = "Samsung Galaxy Watch 4",

    @Expose
    @SerializedName("round_mode")
    var roundMode: RoundMode = RoundMode.all.first(),

    @Expose
    @SerializedName("round_type")
    var roundType: RoundType = RoundType.all.first(),

    @Expose
    @SerializedName("holes")
    var holes: ArrayList<RoundHole> = ArrayList(),

    @Expose
    @SerializedName("completed")
    var completed: Boolean = false,

    @Expose
    @SerializedName("inprogress")
    var inprogress: Boolean = true,

    @Expose
    @SerializedName("played_on")
    var playedOn: String = "",

    @Expose
    @SerializedName("tee_name")
    var teeName: String? = "",

    @Expose
    @SerializedName("total_score")
    var totalScore: Int = 0,

    var roundID: String = "",

    var courseAddress: String = "",

    var numberOfHoles: Int = 0,

    var selectedHoleNumber: Int = 1,

    var tee: Tee = Tee(),

    var par: Int = 0

) : Parcelable