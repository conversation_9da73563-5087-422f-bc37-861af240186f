package com.mytaylormadeplus.wearos.data.repository

import com.mytaylormadeplus.wearos.data.network.ApiService
import com.mytaylormadeplus.wearos.data.network.request.CourseDetailParam
import com.mytaylormadeplus.wearos.data.network.request.CourseListParam
import com.mytaylormadeplus.wearos.data.network.response.CourseListResponse
import io.reactivex.Observable
import okhttp3.ResponseBody
import retrofit2.Response
import javax.inject.Inject

class CourseRepository @Inject constructor(private var apiService: ApiService) {

    fun iGolfSearchCourse(radius: Int, lat: Double, lng: Double, resultsPerPage: Int): Observable<CourseListResponse> {
//        val params = CourseListParam(
//            active = 1,
//            radius = radius,
//            referenceLatitude = lat,
//            referenceLongitude = lng,
//            resultsPerPage = resultsPerPage
//        )
        return apiService.iGolfSearchCourse(active = 1,
            radius = radius,
            referenceLatitude = lat,
            referenceLongitude = lng,
            resultsPerPage = resultsPerPage)
    }

    fun iGolfCourseDetail(courseID: String): Observable<Response<ResponseBody>> {
        return apiService.iGolfCourseDetail(courseID = courseID)
    }

    fun iGolfTee(courseID: String): Observable<Response<ResponseBody>> {
        return apiService.iGolfTee(courseID = courseID)
    }

    fun iGolfScorecard(courseID: String): Observable<Response<ResponseBody>> {
        return apiService.iGolfScorecard(courseID = courseID)
    }

    fun iGolfGPS(courseID: String): Observable<Response<ResponseBody>> {
        return apiService.iGolfGPS(courseID = courseID)
    }

    fun iGolfGPSVector(courseID: String): Observable<Response<ResponseBody>> {
        return apiService.iGolfGPSVector(courseID)
    }
}