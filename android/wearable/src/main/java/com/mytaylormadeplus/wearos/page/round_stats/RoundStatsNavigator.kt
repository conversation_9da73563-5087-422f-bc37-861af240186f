package com.mytaylormadeplus.wearos.page.round_stats

import com.mytaylormadeplus.wearos.base.BaseNavigator

interface RoundStatsNavigator : BaseNavigator {
    fun endRound()
    fun cancelRound()
    fun setValueGraph(
        fairwayHit: Float,
        fairwayLeft: Float,
        fairwayRight: Float,
        greenIn: Float,
        sandSaves: Float,
        puttPerRound: Float
    )
    fun showDash()
    fun checkCancelRound(isCancelRound: Boolean): String

}