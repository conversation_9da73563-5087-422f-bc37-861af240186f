package com.mytaylormadeplus.wearos.data.model

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

object FeaturePropertyType {
    const val HOLE_BOUNDARIES = "hole-boundary"
    const val TEE = "tee"
    const val TEE_BOUNDARY = "tee-boundary"
    const val FAIRWAY = "fairway"
    const val GREEN = "green"
    const val TREES = "trees"
    const val BUNKER = "bunker"
    const val HAZARD = "hazard"
}

object Label {
    const val TEE = "Tee"
    const val FAIRWAY = "Fairway"
    const val BUNKER = "Bunker"
    const val HAZARD = "Hazard"
    const val HAZARD_LINE = "Hazard Line"
    const val ROUGH = "Rough"
    const val GREEN = "Green"
    const val TREES = "Trees"
    const val OFF_COURSE = "Off course"
}

@Parcelize
data class FeatureProperty(
    var type: String? = null,
    var holeNumber: Int = 0,
    var mapperV1Id: Long = 0,
    var kind: String? = null,
    var label: String? = null,
    var position: Int = 0,
) : Parcelable