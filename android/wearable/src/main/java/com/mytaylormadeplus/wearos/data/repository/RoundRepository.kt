package com.mytaylormadeplus.wearos.data.repository

import com.mytaylormadeplus.wearos.data.model.Round
import com.mytaylormadeplus.wearos.data.network.ApiService
import com.mytaylormadeplus.wearos.data.network.request.SubmitRoundParam
import com.mytaylormadeplus.wearos.data.preferences.UserSession
import com.mytaylormadeplus.wearos.utils.extension.deepCopy
import io.reactivex.Observable
import okhttp3.ResponseBody
import retrofit2.Response
import javax.inject.Inject

class RoundRepository @Inject constructor(
    private var apiService: ApiService,
    private var userSession: UserSession
) {

    fun createRound(round: Round): Observable<Response<ResponseBody>> {
        val submitRound = round.deepCopy()
        if(submitRound.teeName == "N/A") {
            submitRound.teeName = null
        }
        val params = SubmitRoundParam(
            round = submitRound,
            roundSubmit = true,
        )
        return apiService.createRound(body = params)
    }

    fun editRound(roundID: String, round: Round): Observable<Response<ResponseBody>> {
        val submitRound = round.deepCopy()
        if(submitRound.teeName == "N/A") {
            submitRound.teeName = null
        }
        val params = SubmitRoundParam(
            round = submitRound,
            roundSubmit = true,
        )
        return apiService.editRound(roundID = roundID, body = params)
    }

    fun deleteRound(roundID: String): Observable<Response<ResponseBody>> {
        return apiService.deleteRound(roundID)
    }

    fun getShowAdvance(): Observable<Response<ResponseBody>> {
        return apiService.getShowAdvance()
    }
}