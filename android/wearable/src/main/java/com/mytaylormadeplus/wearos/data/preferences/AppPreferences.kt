package com.mytaylormadeplus.wearos.data.preferences

import android.content.Context
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
open class AppPreferences @Inject constructor(
    context: Context,
    private val userSession: UserSession
) : BasePreferences(context, context.packageName) {

    companion object {
        const val KEY_BUILD_ENVIRONMENT = "KEY_BUILD_ENVIRONMENT"
        const val KEY_ROUND_DATA = "KEY_ROUND_DATA"
        const val KEY_OPEN_APP_FIRST_TIME = "KEY_OPEN_APP_FIRST_TIME"
        const val KEY_I_GOLF_GPS_VECTOR = "KEY_I_GOLF_GPS_VECTOR"
        const val KEY_PLAT_FORM = "KEY_FLAT_FORM"
        const val KEY_COMPLETE_HOLE = "KEY_COMPLETE_HOLE"
    }

    /***********************************************************************************************
     *
     * BUILD ENVIRONMENT
     *
     ***********************************************************************************************/

    open fun getServerEnvironment(default: Int): Int {
        return getInt(KEY_BUILD_ENVIRONMENT, default)
    }

    open fun setServerEnvironment(env: Int) {
        putInt(KEY_BUILD_ENVIRONMENT, env)
    }

    open fun isFirstTimeOpenApp(): Boolean {
        return getBoolean(KEY_OPEN_APP_FIRST_TIME, true)
    }

    open fun setFirstTimeOpenApp(value: Boolean) {
        putBoolean(KEY_OPEN_APP_FIRST_TIME, value)
    }

    open fun getRoundData(): String? {
        return getString("$KEY_ROUND_DATA-${userSession.userID!!}")
    }

    open fun saveRoundData(data: String) {
        putString("$KEY_ROUND_DATA-${userSession.userID!!}", data)
    }

    open fun getIGolfGPSVector(): String? {
        return getString("$KEY_I_GOLF_GPS_VECTOR-${userSession.userID!!}")
    }

    open fun saveIGolfGPSVector(data: String) {
        putString("$KEY_I_GOLF_GPS_VECTOR-${userSession.userID!!}", data)
    }

    open fun setFlatForm(data: String) {
        putString(KEY_PLAT_FORM, data)
    }

    open fun getFlatForm(): String? {
        return getString(KEY_PLAT_FORM)
    }

    open fun isCompleteHole(): Boolean {
        return getBoolean(KEY_COMPLETE_HOLE, false)
    }

    open fun setCompleteHole(value: Boolean) {
        putBoolean(KEY_COMPLETE_HOLE, value)
    }
}