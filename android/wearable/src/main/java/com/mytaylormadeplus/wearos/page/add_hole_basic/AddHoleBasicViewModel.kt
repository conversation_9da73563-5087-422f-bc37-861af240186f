package com.mytaylormadeplus.wearos.page.add_hole_basic

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import javax.inject.Inject

class AddHoleBasicViewModel @Inject constructor(val playRoundManager: PlayRoundManager) :
    BaseViewModel<AddHoleBasicNavigator>() {

    var holeScore: Int = 0

    private val _holeScoreData = MutableLiveData<Int>()
    val holeScoreData: LiveData<Int>
        get() = _holeScoreData


    override fun setup() {
        val score: Int = playRoundManager.selectedHole.holeScore
        if (score > 0) {
            holeScore = score
            _holeScoreData.postValue(holeScore)
        } else {
            holeScore = playRoundManager.selectedHole.par
            _holeScoreData.postValue(holeScore)
        }
    }

    fun increaseScore() {
        if (holeScore < 12) {
            holeScore = holeScore.plus(1)
            _holeScoreData.postValue(holeScore)
        }
    }

    fun decreaseScore() {
        if (holeScore > 1) {
            holeScore = holeScore.minus(1)
            _holeScoreData.postValue(holeScore)
        }
    }

    fun finishHole() {
        playRoundManager.finishBasicHole(holeScore)
        navigator.finishHole()
    }

}