package com.mytaylormadeplus.wearos.page.connection_square

import com.mytaylormadeplus.wearos.base.BaseNavigator
import com.mytaylormadeplus.wearos.data.model.Course
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.manager.WearService
import java.util.ArrayList

interface ConnectionSquareNavigator: BaseNavigator {
    fun gotoCourseListScreen(coursesList: ArrayList<Course>)
    fun openConnectionError(error: WearService.WearConnectionError)
    fun openResumeRound()
    fun startNetworkService(playRoundManager: PlayRoundManager)
}