package com.mytaylormadeplus.wearos.page.splash

import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.data.preferences.UserSession
import javax.inject.Inject

class SplashViewModel @Inject constructor(
    private val appPreferences: AppPreferences,
    private val userSession: UserSession,
) : BaseViewModel<SplashNavigator>() {

    override fun setup() {
        resetUserSession()
        if (!appPreferences.isFirstTimeOpenApp()) {
            navigator.gotoConnection()
        }
    }

    fun setFlatForm(data: String) {
        appPreferences.setFlatForm(data)
    }

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }

    private fun resetUserSession() {
        userSession.logout()
    }

    fun onConfirm() {
        appPreferences.setFirstTimeOpenApp(false)
        navigator.gotoConnection()
    }

}