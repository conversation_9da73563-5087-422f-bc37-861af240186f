package com.mytaylormadeplus.wearos.page.show_advance_notification

import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivityShowAdvanceNotificationBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ShowAdvanceActivity: BaseActivity<ShowAdvanceNavigator, ShowAdvanceViewModel, ActivityShowAdvanceNotificationBinding>(),
    ShowAdvanceNavigator {
    override fun setupViewDataBinding() {
        binding = ActivityShowAdvanceNotificationBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
        binding.scrollView.requestFocus()
    }

    override fun setup() {
    }

    override fun cancel() {
        finish()
    }
}