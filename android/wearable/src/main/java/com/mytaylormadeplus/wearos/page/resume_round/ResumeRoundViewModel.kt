package com.mytaylormadeplus.wearos.page.resume_round

import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.utils.DateUtils
import javax.inject.Inject

class ResumeRoundViewModel @Inject constructor(
    private val playRoundManager: PlayRoundManager,
    private val appPreferences: AppPreferences
) :
    BaseViewModel<ResumeRoundNavigator>() {

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }

    fun resumeRound() {
        navigator.openPlayRound()
    }

    fun cancelRound() {
        playRoundManager.cancelRound(
            navigator = navigator,
            playRoundListener = object : PlayRoundManager.PlayRoundListener() {
                override fun onCanceled() {
                    navigator.openConnection()
                }
            })
    }

    fun getRoundPlayOnDate(): String {
        return DateUtils.parseDateStringFrom(playRoundManager.round.playedOn, DateUtils.DATE_FORMAT)
    }

    fun getRoundName(): String {
        return playRoundManager.round.courseName
    }
}