package com.mytaylormadeplus.wearos.data.model

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Tee(
    var teeName: String? = null,
    var slope: Float = 0f,
    var rating: Float = 0f,
    var gender: String? = null,
    var totalPar: Int = 0,
    var totalYards: Long = 0,
    var numberOfHoles: Int = 0,
    var holes: List<Hole>? = null
) : Parcelable {

    val isMenTee: Boolean
        get() = "men" == gender

}