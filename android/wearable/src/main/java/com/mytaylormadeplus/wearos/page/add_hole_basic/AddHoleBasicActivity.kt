package com.mytaylormadeplus.wearos.page.add_hole_basic

import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivityAddHoleBasicBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddHoleBasicActivity : BaseActivity<AddHoleBasicNavigator, AddHoleBasicViewModel, ActivityAddHoleBasicBinding>(),
    AddHoleBasicNavigator {

    override fun setupViewDataBinding() {
        binding = ActivityAddHoleBasicBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
        binding.playRound = viewModel.playRoundManager
    }

    override fun setup() {
        setData()
        setupView()
        setupAction()
    }

    private fun setData() {
    }

    private fun setupView() {
    }

    private fun setupAction() {
        binding.btnHole.setOnClickListener {

        }
    }

    override fun finishHole() {
        finish()
    }
}