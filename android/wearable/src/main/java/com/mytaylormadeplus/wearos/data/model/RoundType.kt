package com.mytaylormadeplus.wearos.data.model

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
enum class RoundType(var value: String) : Parcelable {

    Practice("Practice"),
    Tournament("Tournament");

    override fun toString(): String {
        return value
    }

    companion object {
        val all: ArrayList<RoundType>
            get() = arrayListOf(Practice, Tournament)
    }
}