package com.mytaylormadeplus.wearos.page.select_list

import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import javax.inject.Inject

class SelectListViewModel @Inject constructor(
    private val appPreferences: AppPreferences
): BaseViewModel<SelectListNavigator>() {
    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }
}