package com.mytaylormadeplus.wearos.base

import androidx.lifecycle.ViewModel
import com.mytaylormadeplus.wearos.utils.SchedulerProvider
import io.reactivex.disposables.CompositeDisposable
import javax.inject.Inject

open class BaseViewModel<Navigator : BaseNavigator> @Inject constructor() : ViewModel() {

    lateinit var navigator: Navigator

    @Inject
    lateinit var schedulerProvider: SchedulerProvider

    @Inject
    lateinit var compositeDisposable: CompositeDisposable

    open fun setup() {}

    open fun onDestroy() {
        compositeDisposable.dispose()
    }
    open fun onResume() {
    }
    open fun onStop() {
    }
    open fun onPause() {
    }
}
