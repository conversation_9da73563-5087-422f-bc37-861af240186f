package com.mytaylormadeplus.wearos.page.play_round

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.CountDownTimer
import android.view.MotionEvent
import android.view.WindowManager
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import androidx.wear.ongoing.OngoingActivity
import androidx.wear.ongoing.Status
import com.mytaylormadeplus.wearos.R
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.data.model.RoundMode
import com.mytaylormadeplus.wearos.databinding.ActivityPlayRoundBinding
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.page.add_hole_basic.AddHoleClassicActivity
import com.mytaylormadeplus.wearos.page.club_list.ClubListActivity
import com.mytaylormadeplus.wearos.page.round_stats.RoundStatsActivity
import com.mytaylormadeplus.wearos.page.score_card.ScoreCardActivity
import com.mytaylormadeplus.wearos.page.summary.SummaryActivity
import dagger.hilt.android.AndroidEntryPoint
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers
import rx.subscriptions.CompositeSubscription

@AndroidEntryPoint
class PlayRoundActivity : BaseActivity<PlayRoundNavigator, PlayRoundViewModel, ActivityPlayRoundBinding>(),
    PlayRoundNavigator {

    private var isAdvancedRound: Boolean = false
    private var numBright: Float = 0.9f
    private var CHANNEL_ID: String = "Ongoing Activity"
    private var mainText: String = "status"
    private var NOTIFICATION_ID: Int = 12345678
    private lateinit var notificationManager: NotificationManager

    private val compositeSubscription = CompositeSubscription()

    override fun setupViewDataBinding() {
        binding = ActivityPlayRoundBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
        binding.playRound = viewModel.playRoundManager
        binding.scrollView.requestFocus()
        numBright = getBrightness()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun generateNotification(mainText: String): Notification {
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val importance = NotificationManager.IMPORTANCE_LOW
        val mChannel = NotificationChannel(CHANNEL_ID, CHANNEL_ID, importance)
        notificationManager.createNotificationChannel(mChannel)
        var notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setOngoing(true).setVisibility(NotificationCompat.VISIBILITY_PUBLIC)

        val ongoingActivityStatus = Status.Builder()
            // Sets the text used across various surfaces.
            .addTemplate(mainText)
            .build()

        val launchActivityIntent = Intent(this, this::class.java)
        launchActivityIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK

        val activityPendingIntent = PendingIntent.getActivity(
            this,
            0,
            launchActivityIntent,
            PendingIntent.FLAG_MUTABLE,
        )

        val ongoingActivity =
            OngoingActivity.Builder(
                applicationContext, NOTIFICATION_ID, notificationBuilder
            ).setAnimatedIcon(R.drawable.ic_notification).setStaticIcon(R.drawable.ic_notification).setStatus(ongoingActivityStatus)
                .setCategory(NotificationCompat.CATEGORY_LOCATION_SHARING)
                .setTouchIntent(activityPendingIntent)
                .build()
        ongoingActivity.apply(applicationContext)
        notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build())
        return notificationBuilder.build()
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun setupOngoingActivity() {
        compositeSubscription.add(viewModel.locationProvider.getLocationObservable().subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { location ->
                location?.let {
                    mainText = viewModel.playRoundManager.getDistanceToPin(it).toString()
                    generateNotification(mainText + "(m) to hole " + viewModel.playRoundManager.round.selectedHoleNumber)
                }
            })
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun setup() {
        setData()
        setupView()
        setupOngoingActivity()
        setupAction()
        handlerUISquare()
        timer.start()
    }

    private fun handlerUISquare() {
        if (viewModel.getFlatForm() == "square") {
            binding.viewTwoButton.setPadding(25,0,25,0)
        }
    }

    private fun setData() {
        isAdvancedRound = viewModel.isAdvancedRound()
    }

    private fun setupView() {
    }

    private fun setupAction() {
        binding.btnAddShot.setOnClickListener {
            if (isAdvancedRound) {
                openClub()
            } else {
                openAddBasicHoleScore()
            }
        }
        binding.btnSumary.setOnClickListener {
            openSummary()
        }
        binding.btnHole.setOnClickListener {
            openScorecard()
        }
        binding.llContainer.setOnTouchListener { view, motionEvent -> onTouchEvent(motionEvent) }
        viewModel.playRoundManager.registerPlayRoundListener(object : PlayRoundManager.PlayRoundListener() {
            @RequiresApi(Build.VERSION_CODES.O)
            override fun onNextHole() {
                generateNotification(mainText + "(m) to hole " + viewModel.playRoundManager.round.selectedHoleNumber)
            }
        })
    }

    override fun scrollScreenToTop() {
        binding.scrollView.smoothScrollTo(0, 0)
    }

    override fun openSummary() {
        if(viewModel.playRoundManager.round.roundMode.equals(RoundMode.Classic)) {
            val intent = Intent(this, RoundStatsActivity::class.java)
            startActivity(intent)
        }else{
            val intent = Intent(this, SummaryActivity::class.java)
            startActivity(intent)
        }
    }

    private fun openAddBasicHoleScore() {
//        val intent = Intent(this, AddHoleBasicActivity::class.java)
//        startActivity(intent)
        val intent = Intent(this, AddHoleClassicActivity::class.java)
        startActivity(intent)
    }

    private fun openClub() {
        val intent = Intent(this, ClubListActivity::class.java)
        intent.putExtra("isGreen", viewModel.getIsGreen())
        intent.putExtra("distanceGreen", viewModel.getDistanceGreen())
        startActivity(intent)
    }

    private fun openScorecard() {
        val intent = Intent(this, ScoreCardActivity::class.java)
        startActivity(intent)
    }
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when(event.action){
            MotionEvent.ACTION_DOWN->{
                setBrightness(numBright)
                timer.cancel()
            }
            MotionEvent.ACTION_CANCEL->{
                timer.start()
            }
        }
        return true
    }

    override fun onDestroy() {
        super.onDestroy()
        timer.cancel()
        compositeSubscription.clear()
        notificationManager.cancel(NOTIFICATION_ID)
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onPause() {
        super.onPause()
        timer.cancel()
        setBrightness(numBright)
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onResume() {
        super.onResume()
        if(numBright > 0) {
            setBrightness(numBright)
        }
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        timer.cancel()
        timer.start()
    }

    val timer = object: CountDownTimer(20000, 1000) {
        override fun onTick(millisUntilFinished: Long) {

        }

        override fun onFinish() {
            setBrightness(0.02f)
        }
    }

    fun setBrightness(num: Float){
        val layout = window.attributes
        layout.screenBrightness = num
        window.attributes = layout
    }
    fun getBrightness(): Float{
        val layout = window.attributes
        return layout.screenBrightness
    }

}