package com.mytaylormadeplus.wearos.page.play_round

import android.location.Location
import android.util.Log
import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.model.Feature
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.manager.LocationProvider
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.utils.LiveDataWrapper
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers
import rx.subscriptions.CompositeSubscription
import javax.inject.Inject

class PlayRoundViewModel @Inject constructor(
    val playRoundManager: PlayRoundManager,
    val locationProvider: LocationProvider,
    private val appPreferences: AppPreferences
) :
    BaseViewModel<PlayRoundNavigator>() {

    private val compositeSubscription = CompositeSubscription()

    val distanceToPin = LiveDataWrapper<Long>()
    val distanceToFront = LiveDataWrapper<Long>()
    val distanceToBack = LiveDataWrapper<Long>()
    val previousShot = LiveDataWrapper<String>()
    private lateinit var realmTimeLocation: Location

    override fun setup() {
        super.setup()
        setupLocationListener()
        playRoundManager.registerPlayRoundListener(object : PlayRoundManager.PlayRoundListener() {
            override fun onRoundEnded() {
                navigator.openSummary()
            }

            override fun onNextHole() {
                navigator.scrollScreenToTop()
            }
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        compositeSubscription.clear()
        locationProvider.stop()
    }

    override fun onResume() {
        super.onResume()
        locationProvider.resume();
    }

    override fun onStop() {
        super.onStop()
        locationProvider.pause();
    }

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }

    fun isAdvancedRound(): Boolean {
        return playRoundManager.isAdvancedRound()
    }

    private fun setupLocationListener() {
        compositeSubscription.add(locationProvider.getLocationObservable().subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { location ->
                location?.let {
                    onLocationChanged(it)
                    realmTimeLocation = it
                }
            })
    }

    private fun onLocationChanged(currentLocation: Location) {
        distanceToPin.postValue(playRoundManager.getDistanceToPin(currentLocation))
        distanceToFront.postValue(playRoundManager.getDistanceToFront(currentLocation))
        distanceToBack.postValue(playRoundManager.getDistanceToBack(currentLocation))
        previousShot.postValue(playRoundManager.getPreviousShot(currentLocation))
    }

    // club suggestion

    fun getDistanceGreen(): Long {
        return playRoundManager.getDistanceToPin(realmTimeLocation)
    }

    fun getIsGreen(): Boolean {
        return getLieForCoordinate().lowercase() == "green"
    }

    private fun getLieForCoordinate(): String {
        var containedInFeature: Feature? = null
        val featrues: ArrayList<Feature> = ArrayList()
        for (feature in playRoundManager.selectedHole.features?.features ?: emptyList()) {
            if(getOddNodes(realmTimeLocation, getPathForFeature(feature))) {
                if (containedInFeature != null) {
                    val containedInFeatureZIndex: Int = getZIndexFromFeature(containedInFeature)
                    val featureZIndex: Int = getZIndexFromFeature(feature)
                    if (featureZIndex >= containedInFeatureZIndex) {
                        containedInFeature = feature;
                    }
                } else {
                    containedInFeature = feature
                }
                featrues.add(feature)
            }
        }
        if (containedInFeature?.properties?.label == "Trees") {
            val indexOfFeature: Int = featrues.indexOf(containedInFeature)
            var featureUnderneath: Feature?
            if (indexOfFeature > 0) {
                featureUnderneath = featrues.elementAt(indexOfFeature - 1)
            } else {
                featureUnderneath = featrues.elementAt(0)
            }
            return featureUnderneath.properties?.label ?: ""
        }
        return containedInFeature?.properties?.label ?: ""
    }

    private fun getOddNodes(currentLocation: Location, path: ArrayList<List<Double>>): Boolean {
        val x: Double = currentLocation.latitude
        val y: Double = currentLocation.longitude
        var oddNodes: Boolean = false
        var j: Int = path.size-1
        for (i in 0 until path.size) {
            val prevLocation = path[i]
            val nextLocation = path[j]
            if ((prevLocation[0] < y && nextLocation[0] >= y) || (nextLocation[0] < y && prevLocation[0] >= y)) {
                if (prevLocation[1] + (y - prevLocation[0])/(nextLocation[0] - prevLocation[0])*(nextLocation[1] - prevLocation[1]) < x ) {
                    oddNodes = true
                }
            }
            j = i
        }

        return oddNodes
    }

    private fun getPathForFeature(feature: Feature): ArrayList<List<Double>> {
        val path: ArrayList<List<Double>> = ArrayList()
        val arrayCoordinates: List<List<Double>> = feature.geometry?.coordinates?.get(0) ?: ArrayList()
        arrayCoordinates.map { path.add(it) }
        return path
    }

    private fun getZIndexFromFeature(feature: Feature): Int {
        return when (feature.properties?.label) {
            "course-boundary" -> 0
            "hole-boundary" -> 5
            "fairway" -> 10
            "tee-boundary" -> 15
            "bunker" -> 15
            "green" -> 15
            "tee" -> 15
            "hazard" -> 20
            "trees" -> 20
            "rough" -> 5
            else -> 0
        }
    }
}