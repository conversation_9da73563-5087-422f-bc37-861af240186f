package com.mytaylormadeplus.wearos.page.end_round_notification

import android.content.Intent
import android.os.Bundle
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivityEndRoundNotificationBinding
import com.mytaylormadeplus.wearos.page.connection.ConnectionActivity
import com.mytaylormadeplus.wearos.page.connection_square.ConnectionSquareActivity
import dagger.hilt.android.AndroidEntryPoint
import java.util.*
import kotlin.concurrent.timerTask

@AndroidEntryPoint
class EndRoundNotificationActivity :
    BaseActivity<EndRoundNotificationNavigator, EndRoundNotificationViewModel, ActivityEndRoundNotificationBinding>() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEndRoundNotificationBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupViewDataBinding() {
        binding = ActivityEndRoundNotificationBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
    }

    override fun setup() {
        Timer().schedule(timerTask {
            if (viewModel.getFlatForm() == "square") {
                val intent = Intent(this@EndRoundNotificationActivity, ConnectionSquareActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(intent)
            } else {
                val intent = Intent(this@EndRoundNotificationActivity, ConnectionActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(intent)
            }
            finish()
        }, 3000)
    }

}