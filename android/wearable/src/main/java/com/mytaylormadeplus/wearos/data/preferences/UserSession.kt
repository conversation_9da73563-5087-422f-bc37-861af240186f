package com.mytaylormadeplus.wearos.data.preferences

import android.content.Context
import com.google.gson.Gson
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserSession @Inject constructor(context: Context, val gson: <PERSON>son) :
    BasePreferences(context, "user_session") {

    companion object {
        const val KEY_ACCESS_TOKEN = "KEY_ACCESS_TOKEN"
        const val KEY_USER_ID = "KEY_USER_ID"
        const val KEY_UNIT_YARD = "KEY_UNIT_YARD"
        const val KEY_LIST_CLUBS = "KEY_LIST_CLUBS"
        const val KEY_IS_SHOW_ADVANCE = "KEY_IS_SHOW_ADVANCED"
        const val KEY_LOCATION_LONG = "KEY_LOCATION_LONG"
        const val KEY_LOCATION_LAT = "KEY_LOCATION_LAT"
        const val KEY_DISTANCE_CLUB = "KEY_DISTANCE_CLUB"
    }

    var isLoggedIn: Boolean
        get() = !accessToken.isNullOrEmpty()
        set(value) {}

    var accessToken: String?
        get() = getString(KEY_ACCESS_TOKEN)
        set(value) = putString(KEY_ACCESS_TOKEN, value)

    var userID: String?
        get() = getString(KEY_USER_ID)
        set(value) = putString(KEY_USER_ID, value)

    var listActiveClubs: String?
        get() = getString(KEY_LIST_CLUBS)
        set(value) = putString(KEY_LIST_CLUBS, value)

    var distanceClub: String?
        get() = getString(KEY_DISTANCE_CLUB)
        set(value) = putString(KEY_DISTANCE_CLUB, value)

    var isShowAdvance: Boolean
        get() = getBoolean(KEY_IS_SHOW_ADVANCE)
        set(value) = putBoolean(KEY_IS_SHOW_ADVANCE, value)

    var isUnitYard: Boolean
        get() = getBoolean(KEY_UNIT_YARD)
        set(value) = putBoolean(KEY_UNIT_YARD, value)

    var currentLong: String?
        get() = getString(KEY_LOCATION_LONG)
        set(value) = putString(KEY_LOCATION_LONG, value)

    var currentLat: String?
        get() = getString(KEY_LOCATION_LAT)
        set(value) = putString(KEY_LOCATION_LAT, value)

    fun logout() {
        remove(KEY_USER_ID)
        remove(KEY_ACCESS_TOKEN)
    }
}