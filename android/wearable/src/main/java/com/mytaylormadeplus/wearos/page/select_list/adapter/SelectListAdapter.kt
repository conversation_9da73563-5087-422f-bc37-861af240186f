package com.mytaylormadeplus.wearos.page.select_list.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.mytaylormadeplus.wearos.R
import com.mytaylormadeplus.wearos.data.model.ListSelectMode

class SelectListAdapter(
    private val mList: List<ListSelectMode>,
    private val listener: OnItemClickListener
): RecyclerView.Adapter<SelectListAdapter.ViewHolder>() {

    // Holds the views for adding it to image and text
    inner class ViewHolder(itemView: View): RecyclerView.ViewHolder(itemView), View.OnClickListener {
        private var view: View = itemView

        val textView: TextView = view.findViewById(R.id.lbName)

        init {
            view.setOnClickListener(this)
        }

        override fun onClick(v: View?) {
            val position: Int = adapterPosition
            if (position != RecyclerView.NO_POSITION) {
                listener.onItemClick(position)
            }
        }

    }

    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.activity_select_list_item, parent, false)

        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = mList[position]

        // sets the text to the textview from our itemHolder class
        holder.textView.text = item.lbName
    }

    override fun getItemCount(): Int {
        return mList.size
    }
}