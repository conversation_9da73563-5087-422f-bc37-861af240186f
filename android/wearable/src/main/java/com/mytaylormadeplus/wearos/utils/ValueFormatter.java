package com.mytaylormadeplus.wearos.utils;


import androidx.annotation.NonNull;

import java.text.DecimalFormat;
import java.util.Locale;

public class ValueFormatter {

    public static class Formats {
        public static final String NO_DECIMAL = "%.0f";
        public static final String NO_DECIMAL_COMMAS = "%,.0f";
        public static final String ONE_DECIMAL = "%.1f";
        public static final String TWO_DECIMAL = "%.2f";
    }

    public static String format(float value, @NonNull String format) {
        return String.format(format, value, Locale.ENGLISH);
    }

    public static String formatSigned(float value, @NonNull String format) {
        if (value > 0) {
            return "+" + String.format(format, value, Locale.ENGLISH);
        } else {
            return String.format(format, value, Locale.ENGLISH);
        }
    }

    public static String formatSignedNoPrefix(float value, @NonNull String format) {
        return String.format(format, value, Locale.ENGLISH);
    }


    public static String formatScoreSigned(float value, @NonNull String format) {
        if (value > 0) {
            return String.format(format, value, Locale.ENGLISH);
        } else {
            return "E";
        }
    }

    public static String formatPercentage(float value, @NonNull String format) {
        return String.format(format, value, Locale.ENGLISH) + "%";
    }


    public static String getNumberSuffix(int number) {
        if (number >= 11 && number <= 13) {
            return "th";
        }
        switch (number % 10) {
            case 1:
                return "st";
            case 2:
                return "nd";
            case 3:
                return "rd";
            default:
                return "th";
        }
    }

    public static String getOrdinalNumberString(int number) {
        return number + getNumberSuffix(number);
    }

    public static String formatDecimalPercent(double value){
        DecimalFormat decimalFormat = new DecimalFormat("#.#");
        return decimalFormat.format(value);
    }
}
