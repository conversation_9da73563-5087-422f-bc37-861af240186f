package com.mytaylormadeplus.wearos.page.score_card

import android.content.Intent
import androidx.wear.widget.WearableLinearLayoutManager
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.data.model.RoundMode
import com.mytaylormadeplus.wearos.databinding.ActivityScoreCardBinding
import com.mytaylormadeplus.wearos.page.round_stats.RoundStatsActivity
import com.mytaylormadeplus.wearos.page.score_card.adapter.ScoreCardAdapter
import com.mytaylormadeplus.wearos.page.select_list.CustomScrollingLayoutCallbackNotScale
import com.mytaylormadeplus.wearos.page.summary.SummaryActivity
import dagger.hilt.android.AndroidEntryPoint
import java.util.*
import kotlin.concurrent.timerTask

@AndroidEntryPoint
class ScoreCardActivity :
    BaseActivity<ScoreCardNavigator, ScoreCardViewModel, ActivityScoreCardBinding>(),
    ScoreCardAdapter.OnItemClickListener {

    override fun setupViewDataBinding() {
        binding = ActivityScoreCardBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
    }

    override fun setup() {
        val recyclerview = binding.rvScoreCard
        recyclerview.apply {
            layoutManager = WearableLinearLayoutManager(this@ScoreCardActivity, CustomScrollingLayoutCallbackNotScale())
        }
        recyclerview.adapter = ScoreCardAdapter(viewModel.getScoreCard(), this)
        recyclerview.requestFocus()
        handlerUISquare()
    }

    private fun handlerUISquare() {
        if (viewModel.getFlatForm() == "square") {
            binding.rvScoreCard.setPadding(15, 0, 15, 0)
        }
    }

    override fun onItemClick(position: Int) {
        viewModel.goToPlayRound(position)
        Timer().schedule(timerTask {
            finish()
        }, 300)
    }

    override fun onEndRound() {
        if (viewModel.playRoundManager.round.roundMode.equals(RoundMode.Classic)) {
            val intent = Intent(this, RoundStatsActivity::class.java)
            startActivity(intent)
        } else {
            val intent = Intent(this, SummaryActivity::class.java)
            startActivity(intent)
        }
    }

}