package com.mytaylormadeplus.wearos.widget;


import static com.mytaylormadeplus.wearos.utils.ValueFormatter.format;
import static com.mytaylormadeplus.wearos.utils.ValueFormatter.formatPercentage;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.view.View;

import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import com.mytaylormadeplus.wearos.R;
import com.mytaylormadeplus.wearos.utils.ValueFormatter;


public class PieChartGraph extends View {

    // Percent inside
    private int textPercentColor;
    private float textPercentSize;
    private Typeface textFont;

    // small dot
    private int dotColor;
    private float dotRadius;

    // small text
    private int smallTexColor;
    private float smallTextSize;
    private Rect smallTextRect;

    // progress bar
    private float progressBarSize;

    // small circle inside
    private int smallCircleColor;

    private float padding;
    private float sizeBorder;

    private float width;
    private float height;
    private float smallWitdh;
    private float smallHeight;

    private float centerX;
    private float centerY;

    private Canvas canvas;
    private Paint mPaint;

    private float value;
    private boolean showDash = false;

    private boolean usePercentage = true;

    private boolean showLimitBar = false;
    private int limitBarValue;
    private float maxValue = 100;
    private int colorBorder;

    public PieChartGraph(Context context) {
        super(context);
        initView();
    }

    public PieChartGraph(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public PieChartGraph(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    private void initView() {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);

        padding = 48;

        // percent
        textFont = ResourcesCompat.getFont(getContext(), R.font.din_condensed_bold);
        textPercentColor = ContextCompat.getColor(getContext(), R.color.black);
        textPercentSize = 70;
        sizeBorder = 14;

        // dot
        dotColor = ContextCompat.getColor(getContext(), R.color.white);

        // small text
        smallTexColor = ContextCompat.getColor(getContext(), R.color.white);
        smallTextSize = 28;

        // small circle
        smallCircleColor = ContextCompat.getColor(getContext(), R.color.white);

    }

    public void setValue(float value) {
        this.value = value;
        this.showDash = false;
        invalidate();
    }

    public void setTextPercentSize(int value) {
        textPercentSize = value;
    }

    public void usePercentage(boolean usePercentage) {
        this.usePercentage = usePercentage;
    }

    public void showLimitBar(boolean showLimitBar) {
        this.showLimitBar = showLimitBar;
        invalidate();
    }

    public void setLimitBarValue(int limitBarValue) {
        this.limitBarValue = limitBarValue;
    }

    public void setMaxValue(float maxValue) {
        this.maxValue = maxValue;
    }

    public void setColorBorder(int colorBorder) {
        this.colorBorder = colorBorder;
    }

    private void setTestValues() {
        if (getRootView().isInEditMode()) {
            usePercentage = true;
            value = 25;
            showLimitBar = true;
            limitBarValue = 36;
        }
    }

    private void initSizes() {
        width = getWidth() - padding;
        height = getHeight() - padding;

        smallWitdh = width - sizeBorder * 2;
        smallHeight = height - sizeBorder * 2;

        centerX = getWidth() / 2f;
        centerY = getHeight() / 2f;

        progressBarSize = width - smallWitdh;
        dotRadius = progressBarSize / 2;


    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        this.canvas = canvas;

        initSizes();

//        setTestValues();

        drawBackgroundCircle();
        drawCenterCircle();
        drawValueCircle();

        if (showLimitBar) {
            drawLimitBarValue();
            drawLimitValue();
        }


        drawValueLabel();

    }

    /**
     * ************************
     * <p>
     * DRAW STATIC GRAPH METHODS
     * <p>
     * ************************
     */


    private void drawBackgroundCircle() {
        mPaint.reset();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(ContextCompat.getColor(getContext(), R.color.gray_light_2));

        canvas.drawCircle(centerX, centerY, width / 2, mPaint);
    }

    private void drawCenterCircle() {
        mPaint.reset();
        if (value == 0) {
            mPaint.setColor(ContextCompat.getColor(getContext(), R.color.gray_light_3));
            setLayerType(LAYER_TYPE_SOFTWARE, mPaint);
//            mPaint.setShadowLayer(padding / 4 + 10, 0, 15f, getResources().getColor(R.color.color_chart_gray_shadow));
        } else {
            mPaint.setColor(smallCircleColor);
        }
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setStyle(Paint.Style.FILL);
        if (value == 0) {
            canvas.drawCircle(centerX, centerY, smallWitdh / 2 + 2, mPaint);
        } else {
            canvas.drawCircle(centerX, centerY, smallWitdh / 2, mPaint);
        }
    }

    /**
     * ************************
     * <p>
     * DRAW VALUES METHODS
     * <p>
     * ************************
     */

    private void drawValueCircle() {
        mPaint.reset();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(sizeBorder);
        mPaint.setColor(this.colorBorder != 0 ? this.colorBorder : ContextCompat.getColor(getContext(), R.color.green_light_2));

        RectF oval = new RectF();
        oval.set(padding / 2 + padding / 6, padding / 2 + padding / 6, width + (padding * 3 / 9), height + (padding * 3 / 9));

        if (value >= maxValue) {
            Path path = new Path();
            path.addOval(oval, Path.Direction.CW);
            canvas.drawPath(path, mPaint);
        } else {
            Path path = new Path();
            path.arcTo(oval, 270, ((value * 360) / maxValue));
            canvas.drawPath(path, mPaint);
        }

    }

    private void drawValueLabel() {
        mPaint.reset();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setColor(textPercentColor);
        mPaint.setTextAlign(Paint.Align.CENTER);
        mPaint.setTextSize(textPercentSize);
        mPaint.setTypeface(textFont);

        float margintTop = textPercentSize / 3;
        if (showDash) {
            canvas.drawText("--", centerX, centerY + margintTop, mPaint);
        } else {
            if (usePercentage) {
                canvas.drawText(formatPercentage(value, ValueFormatter.Formats.NO_DECIMAL), centerX, centerY + margintTop, mPaint);
            } else {
                canvas.drawText(format(value, ValueFormatter.Formats.NO_DECIMAL), centerX, centerY + margintTop, mPaint);
            }
        }
    }

    private void drawLimitBarValue() {
        mPaint.reset();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setColor(dotColor);
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawCircle(padding / 2 + dotRadius / 2, centerY, dotRadius / 2, mPaint);
    }

    private void drawLimitValue() {
        mPaint.reset();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setColor(smallTexColor);
        mPaint.setTextAlign(Paint.Align.RIGHT);
        mPaint.setTextSize(smallTextSize);
        mPaint.setTypeface(textFont);

        String smallText = format(limitBarValue, ValueFormatter.Formats.NO_DECIMAL);
        smallTextRect = new Rect();
        mPaint.getTextBounds(smallText, 0, smallText.length(), smallTextRect);

        canvas.drawText(smallText, padding / 2 - 4, centerY + smallTextRect.height() / 2f, mPaint);
    }

    public void showDash() {
        showDash = true;
        value = 0;
        invalidate();
    }

    private Point calculatePointOnArc(float circleCeX, float circleCeY, float circleRadius, float endAngle) {
        Point point = new Point();
        double endAngleRadian = endAngle * (Math.PI / 180);

        int pointX = (int) Math.round((circleCeX + circleRadius * Math.cos(endAngleRadian)));
        int pointY = (int) Math.round((circleCeY + circleRadius * Math.sin(endAngleRadian)));

        point.x = pointX;
        point.y = pointY;

        return point;
    }
}
