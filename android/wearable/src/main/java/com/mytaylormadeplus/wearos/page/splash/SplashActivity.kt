package com.mytaylormadeplus.wearos.page.splash

import android.content.Intent
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivitySplashBinding
import com.mytaylormadeplus.wearos.page.connection.ConnectionActivity
import com.mytaylormadeplus.wearos.page.connection_square.ConnectionSquareActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SplashActivity : BaseActivity<SplashNavigator, SplashViewModel, ActivitySplashBinding>(),
    SplashNavigator {

    override fun setupViewDataBinding() {
        binding = ActivitySplashBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
        binding.scrollView.requestFocus()
    }

    override fun setup() {
        val height = resources.displayMetrics.heightPixels
        val width = resources.displayMetrics.widthPixels
        val isRound = resources.configuration.isScreenRound
        if (isRound) {
            if (height != width) {
                viewModel.setFlatForm("round_chin")
            } else {
                viewModel.setFlatForm("round")
            }
        } else {
            viewModel.setFlatForm("square")
        }
        handlerUISquare()
    }

    private fun handlerUISquare() {
        if (viewModel.getFlatForm() == "square") {
            binding.scrollView.setPadding(15,0,15,0)
        }
    }

    override fun gotoConnection() {
        val flatForm = viewModel.getFlatForm()
        if (flatForm == "square") {
            startActivity(Intent(this, ConnectionSquareActivity::class.java))
        } else {
            startActivity(Intent(this, ConnectionActivity::class.java))
        }
        finish()
    }

}