package com.mytaylormadeplus.wearos.page.non_club_error

import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivityClubErrorBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ClubErrorActivity:
    BaseActivity<ClubErrorNavigator, ClubErrorViewModel, ActivityClubErrorBinding>(),
    ClubErrorNavigator {
    override fun setupViewDataBinding() {
        binding = ActivityClubErrorBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
    }

    override fun setup() {

    }

    override fun exitApp() {
        finish()
    }
}