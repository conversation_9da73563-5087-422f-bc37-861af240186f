package com.mytaylormadeplus.wearos.data.model

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
enum class RoundMode(var value: String): Parcelable {

    Advanced("Advanced"),
    Classic("Classic"),
    Basic("Basic");

    override fun toString(): String {
        return value
    }

    companion object {
        val all: ArrayList<RoundMode>
            get() = arrayListOf(Classic)
    }
}