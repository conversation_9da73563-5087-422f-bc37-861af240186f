package com.mytaylormadeplus.wearos.page.connection_square

import com.mytaylormadeplus.wearos.Environment
import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.model.Course
import com.mytaylormadeplus.wearos.data.network.helper.easyCompose
import com.mytaylormadeplus.wearos.data.preferences.UserSession
import com.mytaylormadeplus.wearos.data.repository.CourseRepository
import com.mytaylormadeplus.wearos.manager.LocationProvider
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.manager.WearService
import com.mytaylormadeplus.wearos.utils.LiveDataWrapper
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers
import rx.subscriptions.CompositeSubscription
import java.util.*
import javax.inject.Inject
import kotlin.concurrent.timerTask

class ConnectionSquareViewModel @Inject constructor(
    private val userSession: UserSession,
    private val courseRepository: CourseRepository,
    private val locationProvider: LocationProvider,
    private val wearService: WearService,
    private val playRoundManager: PlayRoundManager
): BaseViewModel<ConnectionSquareNavigator>(), WearService.WearServiceListener {

    private val compositeSubscription = CompositeSubscription()

    private var checkGPSConnectionTimer: Timer? = null

    val versionName = LiveDataWrapper<String>()

    open val courseList = ArrayList<Course>()

    override fun setup() {
        wearService.start()
        navigator.startNetworkService(playRoundManager)
    }

    override fun onDestroy() {
        super.onDestroy()
        wearService.stop()
        compositeSubscription.clear()
        stopCheckGPSConnection()
    }

    /************************************************************
     *  GPS Permission
     ************************************************************/

    fun setVersionName(data: String) {
        versionName.postValue("Version $data")
    }

    private fun getCurrentLocation() {
        compositeSubscription.add(locationProvider.getLocationObservable().subscribeOn(Schedulers.computation())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { location ->
                location?.let {
                    stopCheckGPSConnection()
                    userSession.currentLong = it.longitude.toString()
                    userSession.currentLat= it.latitude.toString()
                    getCourseList(lat = it.latitude, lng = it.longitude)
                    compositeSubscription.clear()
                }
            })
    }

    private fun getCourseList(lat: Double, lng: Double) {
        val radius = 40
        val resultsPerPage = 10
        courseRepository.iGolfSearchCourse(radius = radius, lat = lat, lng = lng, resultsPerPage = resultsPerPage)
            .easyCompose({ response ->
                courseList.clear()
                response.courseList?.filter { it.isValidCourse() }?.let {
                    courseList.addAll(it)
                }
                navigator.gotoCourseListScreen(courseList)
            }, navigator = navigator, disposable = compositeDisposable, showProgress = false)
    }

    /************************************************************
     *  GPS Permission
     ************************************************************/

    private fun startLocationService() {
        startGPSCheckConnection()
        locationProvider.start()
    }

    fun onGpsPermissionGranted() {
        startLocationService()
        startGetAuthInfo()
    }

    private fun onGPSConnectionError() {
        navigator.openConnectionError(WearService.WearConnectionError.GPS)
    }

    private fun startGPSCheckConnection() {
        try {
            stopCheckGPSConnection()
            checkGPSConnectionTimer = Timer()
            checkGPSConnectionTimer?.schedule(timerTask {
                onGPSConnectionError()
            }, 60 * 1000)
        } catch (e: Exception) {
        }
    }

    private fun stopCheckGPSConnection() {
        try {
            checkGPSConnectionTimer?.cancel()
            checkGPSConnectionTimer = null
        } catch (e: Exception) {
        }
    }

    /************************************************************
     *  Wear Service
     ************************************************************/

    private fun startGetAuthInfo() {
        if (Environment.MOCK_LOGIN_ENABLE) {
            userSession.userID = Environment.MOCK_LOGIN_USER_ID
            userSession.accessToken = Environment.MOCK_LOGIN_TOKEN
        }
        if (!userSession.isLoggedIn) {
            wearService.getAuthInfo(this)
        } else {
            if (!shouldResumeRound()) {
                getCurrentLocation()
            }
        }
    }

    override fun onReceiveData(userId: String, userToken: String, isUnitYard: Boolean) {
        userSession.userID = userId
        userSession.accessToken = userToken
        userSession.isUnitYard = isUnitYard
        if (!shouldResumeRound()) {
            getCurrentLocation()
        }
    }

    override fun onError(error: WearService.WearConnectionError) {
        navigator.openConnectionError(error)
    }

    /************************************************************
     *  Pause/Resume Round
     ************************************************************/

    private fun shouldResumeRound(): Boolean {
        if (playRoundManager.resumeRoundFromCache()) {
            navigator.openResumeRound()
            return true
        }
        return false
    }
}