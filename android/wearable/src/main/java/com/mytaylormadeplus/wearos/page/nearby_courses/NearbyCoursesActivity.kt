package com.mytaylormadeplus.wearos.page.nearby_courses

import android.content.Intent
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import androidx.wear.widget.WearableLinearLayoutManager
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.data.model.Course
import com.mytaylormadeplus.wearos.databinding.ActivityNearbyCoursesBinding
import com.mytaylormadeplus.wearos.page.nearby_courses.adapter.NearbyCoursesAdapter
import com.mytaylormadeplus.wearos.page.round_detail.RoundDetailActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NearbyCoursesActivity : BaseActivity<NearbyCoursesNavigator, NearbyCoursesViewModel, ActivityNearbyCoursesBinding>(),
    NearbyCoursesNavigator {

    override fun setupViewDataBinding() {
        binding = ActivityNearbyCoursesBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
    }

    override fun setup() {
        setupRecyclerView()
        val courseList = intent.getParcelableArrayListExtra<Course>("courseList")
        viewModel.setData(courseList)
    }

    private fun setupRecyclerView() {
        val adapter = NearbyCoursesAdapter(object: NearbyCoursesAdapter.ItemClickListener {
            override fun onItemClick(course: Course) {
                viewModel.getCourseDetails(course)
            }
        })
        binding.recyclerView.apply {
            isEdgeItemsCenteringEnabled = true
            layoutManager = WearableLinearLayoutManager(this@NearbyCoursesActivity, CustomScrollingLayoutCallback())
        }
        binding.recyclerView.adapter = adapter
        viewModel.courseData.observe(this, Observer {
            it.let(adapter::submitList)
        })
        binding.recyclerView.requestFocus()
        if (viewModel.getFlatForm() == "square") {
            binding.recyclerView.setPadding(0, 70, 0, 70)
        }
    }

    override fun gotoRoundDetails(course: Course) {
        startActivity(Intent(this, RoundDetailActivity::class.java).apply {
            putExtra("course", course)
        })
        finish()
    }

}

class CustomScrollingLayoutCallback : WearableLinearLayoutManager.LayoutCallback() {

    private var progressToCenter: Float = 0f

    override fun onLayoutFinished(child: View, parent: RecyclerView) {
        child.apply {
            val centerOffset = height.toFloat() / 2.0f / parent.height.toFloat()
            val yRelativeToCenterOffset = y / parent.height + centerOffset

            progressToCenter = Math.abs(0.5f - yRelativeToCenterOffset)
            progressToCenter = Math.min(progressToCenter, 0.7f)

            scaleX = 1 - progressToCenter
            scaleY = 1 - progressToCenter
        }
    }
}