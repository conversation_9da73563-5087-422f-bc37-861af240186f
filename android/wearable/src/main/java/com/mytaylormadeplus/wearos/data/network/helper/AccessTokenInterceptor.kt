package com.mytaylormadeplus.wearos.data.network.helper

import com.mytaylormadeplus.wearos.Environment
import com.mytaylormadeplus.wearos.data.preferences.UserSession
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject

class AccessTokenInterceptor @Inject constructor(
    private val environment: Environment, private val userSession: UserSession
) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val url = chain.request().url
        if (userSession.accessToken.isNullOrEmpty() || !url.toString().contains(environment.serverURL)) {
            return chain.proceed(originalRequest)
        }
        val tokenAddedRequest = originalRequest.newBuilder()
            .header("clientId", environment.serverAppClientID)
            .header("Authorization", ("Bearer " + userSession.accessToken) ?: "")
            .method(originalRequest.method, originalRequest.body)
            .build()
        return chain.proceed(tokenAddedRequest)
    }
}
