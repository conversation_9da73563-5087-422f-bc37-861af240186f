package com.mytaylormadeplus.wearos.utils

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer

class LiveDataWrapper<T> {

    private val emitData = MutableLiveData<T>()
    open val data: LiveData<T>
        get() = emitData

    fun postValue(data: T) {
        emitData.postValue(data)
    }

    fun observe(owner: LifecycleOwner, observer: Observer<in T?>) {
        data.observe(owner, observer)
    }
}