package com.mytaylormadeplus.wearos.page.end_round_notification

import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import javax.inject.Inject

class EndRoundNotificationViewModel @Inject constructor(
    private val appPreferences: AppPreferences
): BaseViewModel<EndRoundNotificationNavigator>() {

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }

}