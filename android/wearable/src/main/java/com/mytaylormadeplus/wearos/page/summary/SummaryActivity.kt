package com.mytaylormadeplus.wearos.page.summary

import android.content.Intent
import androidx.core.view.isVisible
import com.mytaylormadeplus.wearos.R
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivitySummaryBinding
import com.mytaylormadeplus.wearos.page.connection.ConnectionActivity
import com.mytaylormadeplus.wearos.page.connection_square.ConnectionSquareActivity
import com.mytaylormadeplus.wearos.page.end_round_notification.EndRoundNotificationActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SummaryActivity : BaseActivity<SummaryNavigator, SummaryViewModel, ActivitySummaryBinding>(),
    SummaryNavigator {

    override fun setupViewDataBinding() {
        binding = ActivitySummaryBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
    }

    override fun setup() {
        setData()
        setupView()
    }

    private fun setData() {
    }

    private fun setupView() {
        binding.vStats.isVisible = viewModel.isAdvancedRound()
        binding.vScroll.isVerticalScrollBarEnabled = viewModel.isAdvancedRound()
        binding.vScroll.requestFocus()
    }

    override fun endRound() {
        runOnUiThread {
            val intent = Intent(this, EndRoundNotificationActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            startActivity(intent)
            finish()
        }
    }

    override fun cancelRound() {
        runOnUiThread {
            val flatForm = viewModel.getFlatForm()
            if (flatForm == "square") {
                val intent = Intent(this, ConnectionSquareActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(intent)
            } else {
                val intent = Intent(this, ConnectionActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(intent)
            }
            finish()
        }
    }

    override fun checkCancelRound(isCancelRound: Boolean): String {
        if (isCancelRound)
            return getString(R.string.cancel_round)
        return getString(R.string.finish)

    }
}