package com.mytaylormadeplus.wearos.page.round_stats

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.model.RoundHole
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.utils.ClassicUtils
import com.mytaylormadeplus.wearos.utils.DateUtils
import com.mytaylormadeplus.wearos.utils.DateUtils.parseDateStringFrom
import javax.inject.Inject

class RoundStatsViewModel @Inject constructor(
    val playRoundManager: PlayRoundManager,
    val appPreferences: AppPreferences,
) : BaseViewModel<RoundStatsNavigator>() {
    private var total: Int = 0
    private var totalPar: Int = 0

    private val _totalScore = MutableLiveData<Int>()
    val totalScore: LiveData<Int>
        get() = _totalScore

    private val _totalParString = MutableLiveData<String>()
    val totalParString: LiveData<String>
        get() = _totalParString

    private val _courseName = MutableLiveData<String>()
    val courseNameString: LiveData<String>
        get() = _courseName

    private val _playDate = MutableLiveData<String>()
    val playDateString: LiveData<String>
        get() = _playDate

    private val _finishString = MutableLiveData<String>()
    val finishString: LiveData<String>
        get() = _finishString

    override fun setup() {
        val round = playRoundManager.round
        //add total score
        total = round.totalScore
        _totalScore.postValue(total)
        _courseName.postValue(round.courseName)
        _playDate.postValue(parseDateStringFrom(round.playedOn, DateUtils.DATE_FORMAT_US))
        //add total par
        val dataHole: ArrayList<RoundHole> =
            round.holes.filter { it.holeScore > 0 } as ArrayList<RoundHole>
        _finishString.postValue(navigator.checkCancelRound(dataHole.size == 0))
        if (dataHole.size == 0) {
            _totalParString.postValue("--")
            navigator.showDash()
        } else {
            totalPar = dataHole.map { it.par }.reduce { totalPar, par -> totalPar + par }
            when {
                total < totalPar -> {
                    _totalParString.postValue("-${totalPar - total}")
                }
                total > totalPar -> {
                    _totalParString.postValue("+${total - totalPar}")
                }
                total == totalPar -> {
                    _totalParString.postValue("E")
                }
            }
            var missLeftCount = 0f
            var missRightCount = 0f
            var fairwayHitCount = 0f
            var greenHitCount = 0f
            var sandSavesCount = 0f
            var bunkerHitCount = 0f
            var putPerRoundCount = 0f
            var parThreeCount = 0f
            var missLeftPer = -1f
            var missRightPer = -1f
            var fairwayHitPer = -1f
            var greenHitPer = -1f
            var sandSavesPer = -1f
            var putPerRoundPer = -1f
            var hadGreenIn = false;
            dataHole.forEach {

                if (it.fwStats.equals(ClassicUtils.FAIRWAY_LEFT))
                    missLeftCount++

                if (it.fwStats.equals(ClassicUtils.FAIRWAY_HIT))
                    fairwayHitCount++

                if (it.fwStats.equals(ClassicUtils.FAIRWAY_RIGHT))
                    missRightCount++

                if (it.grStats.equals(ClassicUtils.GREEN_HIT))
                    greenHitCount++

                if (it.bunkerHit) {
                    bunkerHitCount++
                    if (it.par >= it.holeScore) {
                        sandSavesCount++
                    }
                }
                if (it.puttsNumber > 0)
                    putPerRoundCount += it.puttsNumber

                if (it.par == 3)
                    parThreeCount++

                if(!it.grStats.equals(""))
                    hadGreenIn = true

            }
            var holeNotParThree: Float = dataHole.size - parThreeCount
            if (missLeftCount > 0 || missRightCount > 0 || fairwayHitCount > 0) {
                missLeftPer = (missLeftCount / holeNotParThree) * 100
                missRightPer = (missRightCount / holeNotParThree) * 100
                fairwayHitPer = (fairwayHitCount / holeNotParThree) * 100
            }
            if (greenHitCount > 0 || hadGreenIn)
                greenHitPer = (greenHitCount / dataHole.size) * 100
            if (putPerRoundCount > 0)
                putPerRoundPer = (putPerRoundCount / dataHole.size) * 18
            if (bunkerHitCount > 0)
                sandSavesPer = (sandSavesCount / bunkerHitCount) * 100

            navigator.setValueGraph(
                fairwayHitPer,
                missLeftPer,
                missRightPer,
                greenHitPer,
                sandSavesPer,
                putPerRoundPer
            )
        }

    }

    fun endRound() {
        playRoundManager.submitRound(
            completeRound = true,
            navigator = navigator,
            playRoundListener = object : PlayRoundManager.PlayRoundListener() {
                override fun onRoundSubmitted() {
                    navigator.endRound()
                }

                override fun onCanceled() {
                    navigator.cancelRound()
                }
            })
    }

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }

}