package com.mytaylormadeplus.wearos.page.connection_square

import android.Manifest
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.CountDownTimer
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import ch.halcyon.squareprogressbar.SquareProgressBar
import com.mytaylormadeplus.wearos.Environment
import com.mytaylormadeplus.wearos.R
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.data.model.Course
import com.mytaylormadeplus.wearos.databinding.ActivityConnectionSquareBinding
import com.mytaylormadeplus.wearos.manager.NetworkService
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.manager.WearService
import com.mytaylormadeplus.wearos.page.connection_error.ConnectionErrorActivity
import com.mytaylormadeplus.wearos.page.gps_setting.GpsSettingActivity
import com.mytaylormadeplus.wearos.page.nearby_courses.NearbyCoursesActivity
import com.mytaylormadeplus.wearos.page.resume_round.ResumeRoundActivity
import dagger.hilt.android.AndroidEntryPoint
import java.util.ArrayList

@AndroidEntryPoint
class ConnectionSquareActivity:
    BaseActivity<ConnectionSquareNavigator, ConnectionSquareViewModel, ActivityConnectionSquareBinding>(),
    ConnectionSquareNavigator {

    private val LOCATION_PERMISSION_REQUEST_CODE = 1001
    private val NOTIFICATION_PERMISSION_REQUEST_CODE = 1002
    private var CHANNEL_ID: String = "Ongoing Activity"
    private lateinit var notificationManager: NotificationManager
    private lateinit var squareProgressBar: SquareProgressBar

    override fun setupViewDataBinding() {
        binding = ActivityConnectionSquareBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
    }

    override fun setup() {
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPostNotificationPermission()
        } else {
            requestGpsPermission()
        }
        getVersionName()
        handleProgressBar()
    }

    private fun handleProgressBar() {
        squareProgressBar = binding.sprogressbar
        squareProgressBar.setImage(R.drawable.ic_transparent)
        squareProgressBar.setColor("#34C759")
        squareProgressBar.width = 3
        squareProgressBar.isOpacity = false
        var number: Double = 1.0
        val timer = object : CountDownTimer(2000000, 10) {
            override fun onTick(millisUntilFinished: Long) {
                number += 1.0
                if(number >= 100) {
                    squareProgressBar.progress = 1.0
                    number = 1.0
                } else {
                    squareProgressBar.progress = number
                }
            }

            override fun onFinish() {
            }
        }
        timer.start()
    }

    private fun getVersionName() {
        try {
            val versionName: String = this.packageManager.getPackageInfo(this.packageName, 0).versionName
            viewModel.setVersionName(versionName)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
    }

    private fun requestPostNotificationPermission() {
        ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.POST_NOTIFICATIONS), NOTIFICATION_PERMISSION_REQUEST_CODE)
    }

    private fun requestGpsPermission() {
        if (Environment.MOCK_GPS_ENABLE) {
            viewModel.onGpsPermissionGranted()
        } else {
            if (ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                    LOCATION_PERMISSION_REQUEST_CODE
                )
            } else {
                viewModel.onGpsPermissionGranted()
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            LOCATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    viewModel.onGpsPermissionGranted()
                } else {
                    openGpsSetting()
                }
                return
            }
            NOTIFICATION_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                    val importance = NotificationManager.IMPORTANCE_LOW
                    val mChannel = NotificationChannel(CHANNEL_ID, CHANNEL_ID, importance)
                    notificationManager.createNotificationChannel(mChannel)
                }
                requestGpsPermission()
                return
            }
        }
    }

    private fun openGpsSetting() {
        startActivity(Intent(this, GpsSettingActivity::class.java))
        finish()
    }

    override fun gotoCourseListScreen(coursesList: ArrayList<Course>) {
        startActivity(Intent(this, NearbyCoursesActivity::class.java).apply {
            putExtra("courseList", viewModel.courseList)
        })
        finish()
    }

    override fun openConnectionError(error: WearService.WearConnectionError) {
        val intent = Intent(this, ConnectionErrorActivity::class.java)
        intent.putExtra("error", error)
        startActivity(intent)
        finish()
    }

    override fun openResumeRound() {
        startActivity(Intent(this, ResumeRoundActivity::class.java))
        finish()
    }

    override fun startNetworkService(playRoundManager: PlayRoundManager) {
        NetworkService(this,playRoundManager).start()
    }
}