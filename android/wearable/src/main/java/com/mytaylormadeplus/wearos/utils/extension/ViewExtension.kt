package com.mytaylormadeplus.wearos.utils.extension

import android.annotation.SuppressLint
import android.view.View
import com.jakewharton.rxbinding3.view.clicks
import io.reactivex.android.schedulers.AndroidSchedulers
import java.util.concurrent.TimeUnit

@SuppressLint("CheckResult")
fun View.clickDelay(delayTime: Long = 1000, onClick: () -> Unit) {
    clicks().doOnError { }
        .throttleFirst(delayTime, TimeUnit.MILLISECONDS, AndroidSchedulers.mainThread())
        .subscribe { onClick.invoke() }
}

@SuppressLint("CheckResult")
fun View.clicks(onClick: () -> Unit) {
    this.clicks().subscribe { onClick.invoke() }
}
