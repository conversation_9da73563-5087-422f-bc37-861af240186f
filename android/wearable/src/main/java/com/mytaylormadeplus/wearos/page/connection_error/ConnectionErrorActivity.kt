package com.mytaylormadeplus.wearos.page.connection_error

import com.mytaylormadeplus.wearos.R
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivityConnectionErrorBinding
import com.mytaylormadeplus.wearos.manager.WearService
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ConnectionErrorActivity :
    BaseActivity<ConnectionErrorNavigator, ConnectionErrorViewModel, ActivityConnectionErrorBinding>(),
    ConnectionErrorNavigator {

    override fun setupViewDataBinding() {
        binding = ActivityConnectionErrorBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
    }

    override fun setup() {
        val error = intent.getSerializableExtra("error") as WearService.WearConnectionError?
        val errorMessage = when (error) {
            WearService.WearConnectionError.AUTH -> getString(R.string.connection_error_auth)
            WearService.WearConnectionError.CONNECTION -> getString(R.string.connection_error_pairing)
            WearService.WearConnectionError.GPS -> getString(R.string.connection_error_gps)
            else -> getString(R.string.connection_error_pairing)
        }
        binding.tvErrorMessage.text = errorMessage
    }

    override fun exitApp() {
        finish()
    }
}