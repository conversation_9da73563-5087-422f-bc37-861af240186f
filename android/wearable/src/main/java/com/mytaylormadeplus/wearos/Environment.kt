package com.mytaylormadeplus.wearos

import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import javax.inject.Inject

class Environment @Inject constructor(private val preferences: AppPreferences) {

    companion object {
        // Mock User Data
        const val MOCK_LOGIN_ENABLE = false
//        const val MOCK_LOGIN_USER_ID = "42426"
//        const val MOCK_LOGIN_TOKEN = "3acc272fd5d8baf3c9ea56eeacb0b689d9c2b459b204ce6ecd9432d6aaa2eb9e"

        // <EMAIL>
        const val MOCK_LOGIN_USER_ID = "198120"
        const val MOCK_LOGIN_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3-II35u6lQgyUGkgNtaumNOkV04muzKon_7VoL7P56s"


        // Mock GPS Data
        const val MOCK_GPS_ENABLE = false
        const val MOCK_GPS_LAT = 21.021357
        const val MOCK_GPS_LNG = 105.789230

        // Server Config
        // Prod
        const val PROD_SERVER = "https://app-prd-mytaylormade-app-001.azurewebsites.net/v1/"
        const val PROD_SERVER_TOKEN = "ff87f6ad53a64a7939469e9920e92990b7ba7dc4689f1854a4c7"
        const val PROD_SERVER_APP_CLIENT_ID = "322e230d08c4fff101792e5aa93029cae10ef307d2d395738e1f39b11cb559c8"
        // Dev
        const val DEV_SERVER = "https://mytaylormadedev.azurewebsites.net/v1/"
        const val DEV_SERVER_APP_CLIENT_ID = "cff4a6465a84e8f7a30a7f6ea6243e4768690cac9ce8a5161017dce55637e056"
        const val DEV_SERVER_TOKEN = "ff87f6ad53a64a7939469e9920e92990b7ba7dc4689f1854a4c7"
    }

    private val ENVIRONMENT_DEV = 1
    private val ENVIRONMENT_PROD = 2
    private val ENVIRONMENT_DEFAULT = ENVIRONMENT_DEV

    val isDevServer: Boolean
        get() = buildEnvironment == ENVIRONMENT_DEV

    val isProdServer: Boolean
        get() = buildEnvironment == ENVIRONMENT_PROD

    fun changeToDevServer() {
        preferences.setServerEnvironment(ENVIRONMENT_DEV)
    }

    fun changeToProdServer() {
        preferences.setServerEnvironment(ENVIRONMENT_PROD)
    }
//  run always is release.
    private val isReleaseBuild: Boolean = BuildConfig.FLAVOR == "production"

    private val buildEnvironment: Int
        get() = if (isReleaseBuild) ENVIRONMENT_PROD else preferences.getServerEnvironment(ENVIRONMENT_DEFAULT)

    val serverURL: String
        get() = if (isDevServer) DEV_SERVER else PROD_SERVER

    val serverAppClientID: String
        get() = if (isDevServer) DEV_SERVER_TOKEN else PROD_SERVER_TOKEN


}