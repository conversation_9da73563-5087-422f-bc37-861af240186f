package com.mytaylormadeplus.wearos.widget

import android.app.Dialog
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.os.CountDownTimer
import android.view.Window
import com.mytaylormadeplus.wearos.R
import kotlinx.android.synthetic.main.dialog_loading_square_view.*

class LoadingView(ctx: Context) : Dialog(ctx) {

    private val appPreferences: SharedPreferences = ctx.getSharedPreferences(ctx.packageName, Context.MODE_PRIVATE)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        window?.setBackgroundDrawableResource(android.R.color.transparent)
        val flatForm = appPreferences.getString("KEY_FLAT_FORM", null)
        if (flatForm == "square") {
            setContentView(R.layout.dialog_loading_square_view)
            handleProgressBar()
        } else {
            setContentView(R.layout.dialog_loading_view)
        }
    }

    private fun handleProgressBar() {
        sprogressbar.setImage(R.drawable.ic_transparent)
        sprogressbar.width = 3
        sprogressbar.isOpacity = false
        sprogressbar.setColor("#34C759")
        var number: Double = 1.0
        val timer = object : CountDownTimer(2000000, 10) {
            override fun onTick(millisUntilFinished: Long) {
                number += 1.0
                if(number >= 100) {
                    sprogressbar.progress = 1.0
                    number = 1.0
                } else {
                    sprogressbar.progress = number
                }
            }

            override fun onFinish() {
            }
        }
        timer.start()
    }
}