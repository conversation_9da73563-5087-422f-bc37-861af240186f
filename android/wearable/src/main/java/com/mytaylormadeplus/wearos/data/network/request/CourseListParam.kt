package com.mytaylormadeplus.wearos.data.network.request

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

data class CourseListParam(
    @Expose
    @SerializedName("active")
    val active: Int,

    @Expose
    @SerializedName("radius")
    val radius: Int,

    @Expose
    @SerializedName("referenceLatitude")
    val referenceLatitude: Double,

    @Expose
    @SerializedName("referenceLongitude")
    val referenceLongitude: Double,

    @Expose
    @SerializedName("resultsPerPage")
    val resultsPerPage: Int,
)