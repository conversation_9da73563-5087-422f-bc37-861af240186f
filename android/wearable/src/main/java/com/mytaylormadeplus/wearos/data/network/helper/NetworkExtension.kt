package com.mytaylormadeplus.wearos.data.network.helper

import com.mytaylormadeplus.wearos.base.BaseNavigator
import com.mytaylormadeplus.wearos.data.network.response.BaseResponse
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import retrofit2.Response

fun <T> Observable<T>.easyCompose(
    subscribe: (T) -> Unit,
    error: (Throwable) -> Unit = {},
    navigator: BaseNavigator?,
    disposable: CompositeDisposable,
    showProgress: Boolean = true,
): Disposable? {
    return this.subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .apply { if (showProgress) navigator?.showLoadingView() }
        .doFinally { if (showProgress) navigator?.hideLoadingView() }
        .handleResponseStatus(navigator)
        .handleException(navigator, { subscribe.invoke(it) }, error)
        .apply { disposable.add(this) }
}

fun <T> Observable<T>.handleResponseStatus(navigator: BaseNavigator? = null): Observable<T> {
    return this.doOnNext { res ->
        if (res is BaseResponse) {
            if (!res.isSuccessful) {
                navigator?.showError(res.message ?: "")
                throw Throwable(res.message)
            }
        } else if (res is Response<*>) {
            if (!res.isSuccessful) {
                navigator?.showError(res.message())
                throw Throwable(res.message())
            }
        }
    }
}

fun <T> Observable<T>.handleException(
    view: BaseNavigator?,
    subscribe: (T) -> Unit,
    error: (Throwable) -> Unit = {}
): Disposable {
    return this.subscribe({
        subscribe.invoke(it)
    }, {
        error.invoke(it)
        view?.hideLoadingView()
        view?.handleNetworkError(it)
        it.printStackTrace()
    })
}

