package com.mytaylormadeplus.wearos.manager


import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Looper
import android.util.Log
import androidx.core.content.ContextCompat
import com.google.android.gms.location.*
import com.mytaylormadeplus.wearos.Environment
import rx.Observable
import rx.subjects.BehaviorSubject
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
open class LocationProvider @Inject constructor(val context: Context) : LocationCallback() {

    private var locationRequest: LocationRequest? = null
    private var locationManager: LocationManager? = null
    private var isOnResult: Boolean = true
    var currentLocation: Location? = if (Environment.MOCK_GPS_ENABLE) Location("Location").apply {
        latitude = Environment.MOCK_GPS_LAT
        longitude = Environment.MOCK_GPS_LNG
    } else null

    private val locationPublishSubject = BehaviorSubject.create<Location>().apply {
        currentLocation?.let {
            onNext(it)
        }
    }

    private val fusedLocationClient: FusedLocationProviderClient by lazy {
        LocationServices.getFusedLocationProviderClient(context)
    }

    fun start() {
        if (ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            fusedLocationClient.removeLocationUpdates(this)
            locationRequest = LocationRequest.create()
            locationRequest?.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
            locationRequest?.interval = 1000
            locationRequest?.fastestInterval = 500
            locationRequest?.numUpdates = 1
            locationRequest?.isFastestIntervalExplicitlySet
            locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager?

            loadRequest()

        }
    }
    private fun loadRequest(){
        if (ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            fusedLocationClient.removeLocationUpdates(this)
            fusedLocationClient.requestLocationUpdates(
                locationRequest!!,
                this,
                Looper.getMainLooper()
            )
            locationManager?.requestLocationUpdates(
                LocationManager.GPS_PROVIDER, 1000L, 0f,  locationListener, Looper.myLooper());
        }
    }
    fun stop() {
        clearListener()
        locationManager?.removeUpdates(locationListener);
    }

    fun resume() {

        loadRequest()
        isOnResult = true
    }

    fun pause() {
        clearListener()
    }

    private fun clearListener(){
        try {
            locationManager?.removeUpdates(locationListener);
            fusedLocationClient.removeLocationUpdates(this)
        }catch (ex: Exception) {
        }
    }

    override fun onLocationResult(locationResult: LocationResult) {
        super.onLocationResult(locationResult)
        if(isOnResult) {
            currentLocation = locationResult.lastLocation
            currentLocation?.let {
                locationPublishSubject.onNext(it)
            }
        }
        Log.i("info", "Location Change: " + locationResult.locations.toString())
    }
    private val locationListener = LocationListener { p0 ->
        isOnResult = false
        currentLocation = p0
        currentLocation?.let {
            locationPublishSubject.onNext(it)
        }

    }

    override fun onLocationAvailability(locationAvailability: LocationAvailability) {
        super.onLocationAvailability(locationAvailability)
        Log.i("info", "Location Change: " + locationAvailability.isLocationAvailable)
    }

    open fun getLocationObservable(): Observable<Location> {
        return locationPublishSubject
    }
}