package com.mytaylormadeplus.wearos.data.model

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

@Parcelize
data class Hole(
    var holeNumber: Int = 0,
    var par: Int = 0,
    var yards: Long = 0,
    var handicap: Int = 0,
    var teeBoxBoundary: List<List<Double>>? = null,
    var greenFrontList: List<Double>? = null,
    var greenMiddle: List<Double>? = null,
    var greenBackList: List<Double>? = null,
    var features: FeatureCollection? = null
) : Parcelable