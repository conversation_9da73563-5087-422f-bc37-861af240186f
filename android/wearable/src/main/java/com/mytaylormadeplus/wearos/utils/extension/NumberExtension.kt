package com.mytaylormadeplus.wearos.utils.extension

import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.*
import kotlin.math.log10
import kotlin.math.pow

fun Double.toDistanceString(): String {
    val formatter = NumberFormat.getInstance(Locale.US) as DecimalFormat
    formatter.applyPattern("##.#")
    return formatter.format(this)
}

fun Double.toUnitMeters(): String {
    val formatter = NumberFormat.getInstance(Locale.US) as DecimalFormat
    formatter.applyPattern("####")
    return formatter.format(this)
}

fun Long.toShortString(): String {
    if (this < 1E3) return this.toString()
    val exp = (log10(this.toDouble()) / log10(1E3)).toInt()
    val number = this / 1E3.pow(exp.toDouble())
    val decimalFormat = when (log10(number).toInt()) {
        0 -> DecimalFormat("#.##")
        1 -> DecimalFormat("##.#")
        else -> DecimalFormat("###")
    }
    return decimalFormat.format(number) + "KMB"[exp - 1]
}