package com.mytaylormadeplus.wearos.page.nearby_courses.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.mytaylormadeplus.wearos.data.model.Course
import com.mytaylormadeplus.wearos.databinding.WidgetCourseItemBinding
import com.mytaylormadeplus.wearos.utils.extension.clicks

class NearbyCoursesAdapter constructor(private val listener: ItemClickListener) :
    ListAdapter<Course, NearbyCoursesAdapter.NearbyCoursesViewHolder>(Companion) {

    interface ItemClickListener {
        fun onItemClick(course: Course)
    }

    class NearbyCoursesViewHolder(val binding: WidgetCourseItemBinding) : RecyclerView.ViewHolder(binding.root)

    companion object : DiffUtil.ItemCallback<Course>() {
        override fun areItemsTheSame(oldItem: Course, newItem: Course): Boolean =
            oldItem === newItem

        override fun areContentsTheSame(oldItem: Course, newItem: Course): Boolean =
            oldItem.idCourse == newItem.idCourse
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NearbyCoursesViewHolder {
        val layoutInflater = LayoutInflater.from(parent.context)
        val binding = WidgetCourseItemBinding.inflate(layoutInflater)
        return NearbyCoursesViewHolder(binding)
    }

    override fun onBindViewHolder(holder: NearbyCoursesViewHolder, position: Int) {
        val course = getItem(position)
        holder.binding.course = course
        holder.binding.executePendingBindings()
        holder.binding.root.clicks {
            listener.onItemClick(course)
        }
    }
}