package com.mytaylormadeplus.wearos.data.model

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class RoundStroke(
    @Expose
    @SerializedName("club_id")
    var clubID: String? = null,

    @Expose
    @SerializedName("ordinal")
    var ordinal: Int = 1,

    @Expose
    @SerializedName("starting_lie")
    var startingLie: String? = null,

    @Expose
    @SerializedName("timestamp")
    var timestamp: String? = null,

    @Expose
    @SerializedName("penalty")
    var penalty: Boolean = false,

    @Expose
    @SerializedName("strokes_gained")
    var strokesGained: Float = 0f,

    @Expose
    @SerializedName("shot_number")
    var shotNumber: Int = 0,

    @Expose
    @SerializedName("shot_at")
    var shotAt: String? = null,// format 2015-09-01T21:43:14Z

    @Expose
    @SerializedName("ending_lie")
    var endingLie: String? = null,

    @Expose
    @SerializedName("coords")
    var coords: List<Double>? = null,

//    @Expose
//    @SerializedName("manually_moved")
//    var manuallyMoved: Boolean = false,
//
//    @Expose
//    @SerializedName("auto_moved")
//    var autoMoved: Boolean = false,

    @Expose
    @SerializedName("manually_added")
    var manuallyAdded: Boolean = false,

    @Expose
    @SerializedName("distance")
    var shotDistance: Long = 0,

    @Expose
    @SerializedName("distance_from_pin")
    var distanceToPin: Long = 0,

//    @Expose
//    @SerializedName("stat_distance_to_pin")
//    var statDistanceToPin: Float = 0f,


    var difficult: Boolean = false,
    var recovery: Boolean = false,
): Parcelable