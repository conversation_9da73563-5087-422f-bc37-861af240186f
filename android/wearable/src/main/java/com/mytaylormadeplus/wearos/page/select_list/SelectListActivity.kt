package com.mytaylormadeplus.wearos.page.select_list

import android.app.Activity
import android.content.Intent
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.wear.widget.WearableLinearLayoutManager
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivitySelectListBinding
import com.mytaylormadeplus.wearos.data.model.ListSelectMode
import com.mytaylormadeplus.wearos.page.select_list.adapter.SelectListAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SelectListActivity: BaseActivity<SelectListNavigator, SelectListViewModel, ActivitySelectListBinding>(), SelectListAdapter.OnItemClickListener {

    private lateinit var title: String
    private lateinit var data: ArrayList<String>


    override fun setupViewDataBinding() {
        binding = ActivitySelectListBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
        title = intent.getStringExtra("title").toString()
        data = intent.getStringArrayListExtra("data")!!
    }

    override fun setup() {
        binding.lbTitle.text = intent.getStringExtra("title").toString()
        val recyclerview = binding.rvSelectList
        recyclerview.apply {
            layoutManager = WearableLinearLayoutManager(this@SelectListActivity, CustomScrollingLayoutCallbackNotScale())
        }
        recyclerview.adapter = SelectListAdapter(getData(), this)
        recyclerview.requestFocus()
        if (viewModel.getFlatForm() == "square") {
            recyclerview.setPadding(15,0,15,0)
        }
    }

    private fun getData(): List<ListSelectMode> {
        return data.map { ListSelectMode(it) }
    }

    override fun onItemClick(position: Int) {
        val intent = Intent()
        intent.putExtra("selectedIndex", position)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

}
class CustomScrollingLayoutCallbackNotScale : WearableLinearLayoutManager.LayoutCallback() {


    override fun onLayoutFinished(child: View, parent: RecyclerView) {
        child.apply {

            scaleX = 1f
            scaleY = 1f
        }
    }
}