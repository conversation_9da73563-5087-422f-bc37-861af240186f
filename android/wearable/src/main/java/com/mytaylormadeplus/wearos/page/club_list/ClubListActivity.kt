package com.mytaylormadeplus.wearos.page.club_list

import androidx.wear.widget.WearableLinearLayoutManager
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivityClubListBinding
import com.mytaylormadeplus.wearos.page.club_list.adapter.ClubListAdapter
import com.mytaylormadeplus.wearos.page.select_list.CustomScrollingLayoutCallbackNotScale
import dagger.hilt.android.AndroidEntryPoint
import java.util.*
import kotlin.concurrent.timerTask

@AndroidEntryPoint
class ClubListActivity:
    BaseActivity<ClubListNavigator, ClubListViewModel, ActivityClubListBinding>(),
    ClubListAdapter.OnClickItemClub,
    ClubListAdapter.OnClickPutter,
    ClubListAdapter.OnClickEndHole {

    override fun setupViewDataBinding() {
        binding = ActivityClubListBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
    }

    override fun setup() {
        getDataFromIntent()
        val recyclerview = binding.rvClubList
        recyclerview.apply {
            layoutManager = WearableLinearLayoutManager(this@ClubListActivity, CustomScrollingLayoutCallbackNotScale())
        }
        recyclerview.adapter = ClubListAdapter(viewModel.getActiveClubs(), this, this, this)
        recyclerview.requestFocus()
        recyclerview.scrollToPosition(viewModel.getIndexClubs())
        handlerUISquare()
    }

    private fun handlerUISquare() {
        if (viewModel.getFlatForm() == "square") {
            binding.rvClubList.setPadding(18,0,18,0)
        }
    }

    private fun getDataFromIntent() {
        viewModel.setIsGreen(intent.getBooleanExtra("isGreen", false))
        viewModel.setDistanceGreen(intent.getLongExtra("distanceGreen", 0))
    }

    override fun onClickItemClub(position: Int) {
        viewModel.clickToClub(position)
        Timer().schedule(timerTask {
            finish()
        }, 300)
    }

    override fun onClickPutter(number: Int, position: Int) {
        viewModel.clickToPutter(number, position)
        Timer().schedule(timerTask {
            finish()
        }, 300)
    }

    override fun onClickEndHole(number: Int, position: Int) {
        viewModel.clickToEndHole(number, position)
        Timer().schedule(timerTask {
            finish()
        }, 300)
    }
}