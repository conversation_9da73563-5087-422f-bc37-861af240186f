package com.mytaylormadeplus.wearos.page.score_card.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.ViewGroup
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.mytaylormadeplus.wearos.R

class ScoreCardAdapter(
    private val rows: List<IRow>,
    private val listener: OnItemClickListener
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    interface IRow
    class HeaderRow : IRow
    class ScoreRow(
        val numberHole: Int,
        val numberSubHole: String,
        val holeScore: Int,
        val par: Int
    ) : IRow

    class TotalRow(val titleTotal: String, val totalHole: Int) : IRow

    class FooterRow(val puttScore: Int, val isHide: Boolean) : IRow
    inner class HeaderViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    inner class ScoreViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView),
        View.OnClickListener {
        private var view: View = itemView
        val holeScore: TextView = view.findViewById(R.id.lbHoleScore)
        val numberHole: TextView = view.findViewById(R.id.lbNumberHole)
        val numberSubHole: TextView = view.findViewById(R.id.lbNumberSubHole)
        val viewHoleScore: LinearLayout = view.findViewById(R.id.vHoleScore)

        init {
            view.setOnClickListener(this)
        }

        override fun onClick(v: View?) {
            val position: Int = adapterPosition
            if (position != RecyclerView.NO_POSITION) {
                listener.onItemClick(position)
            }
        }
    }

    interface OnItemClickListener {
        fun onItemClick(position: Int)

        fun onEndRound()

    }

    inner class TotalViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var view: View = itemView
        val titleTotal: TextView = view.findViewById(R.id.lbTitleTotal)
        val totalHole: TextView = view.findViewById((R.id.lbTotalHole))
        val viewTotalHole: LinearLayout = view.findViewById(R.id.vTotalHole)
    }

    inner class FooterViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private var view: View = itemView
        val tvPuttScore: TextView = view.findViewById(R.id.tvPuttScore)
        val btnEndRound: Button = view.findViewById((R.id.btnEndRound))
        val llPutts: LinearLayout = view.findViewById((R.id.llPutts))
    }

    companion object {
        private const val TYPE_HEADER = 0
        private const val TYPE_SCORE = 1
        private const val TYPE_TOTAL = 2
        private const val TYPE_FOOTER = 3
    }

    override fun getItemViewType(position: Int): Int = when (rows[position]) {
        is HeaderRow -> TYPE_HEADER
        is ScoreRow -> TYPE_SCORE
        is TotalRow -> TYPE_TOTAL
        is FooterRow -> TYPE_FOOTER
        else -> throw IllegalArgumentException()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = when (viewType) {
        TYPE_HEADER -> HeaderViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.widget_score_card_header, parent, false)
        )
        TYPE_SCORE -> ScoreViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.widget_score_card_item, parent, false)
        )
        TYPE_TOTAL -> TotalViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.widget_score_card_total, parent, false)
        )
        TYPE_FOOTER -> FooterViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.widget_score_card_footer, parent, false)
        )
        else -> throw IllegalArgumentException()
    }


    override fun getItemCount() = rows.count()

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) =
        when (holder.itemViewType) {
            TYPE_HEADER -> onBindHeader()
            TYPE_SCORE -> onBindScore(holder, rows[position] as ScoreRow)
            TYPE_TOTAL -> onBindTotal(holder, rows[position] as TotalRow)
            TYPE_FOOTER -> onBindFooter(holder, rows[position] as FooterRow)
            else -> throw IllegalArgumentException()
        }

    private fun onBindHeader() {}

    private fun onBindScore(holder: RecyclerView.ViewHolder, row: ScoreRow) {
        val scoreRow = holder as ScoreViewHolder
        scoreRow.numberHole.text = row.numberHole.toString()
        scoreRow.numberSubHole.text = row.numberSubHole
        if (row.holeScore > 0) {
            scoreRow.holeScore.text = row.holeScore.toString()
            getSymbols(scoreRow.viewHoleScore, row.holeScore, row.par)
        } else {
            scoreRow.holeScore.text = ""
            scoreRow.viewHoleScore.setBackgroundResource(R.drawable.layout_bg)
        }
    }

    private fun onBindTotal(holder: RecyclerView.ViewHolder, row: TotalRow) {
        val totalRow = holder as TotalViewHolder
        totalRow.titleTotal.text = row.titleTotal
        if (row.totalHole > 0) {
            totalRow.totalHole.text = row.totalHole.toString()
            totalRow.viewTotalHole.setBackgroundResource(R.drawable.bg_black)
        } else {
            totalRow.totalHole.text = ""
            totalRow.viewTotalHole.setBackgroundResource(R.drawable.layout_bg)
        }
    }

    private fun onBindFooter(holder: RecyclerView.ViewHolder, row: FooterRow) {
        val footerRound = holder as FooterViewHolder
        footerRound.tvPuttScore.setText(row.puttScore.toString())
        footerRound.btnEndRound.setOnClickListener { listener.onEndRound() }
        if (row.isHide) {
            footerRound.llPutts.visibility = GONE
        }
    }

    private fun getSymbols(view: LinearLayout, hole: Int, par: Int) {
        if (par > hole) {
            if (par - hole == 1) {
                view.setBackgroundResource(R.drawable.bg_bridie)
            }
            if (par - hole > 1) {
                view.setBackgroundResource(R.drawable.bg_eagle)
            }
        } else if (hole > par) {
            if (hole - par == 1) {
                view.setBackgroundResource(R.drawable.bg_bogey)
            }
            if (hole - par > 1) {
                view.setBackgroundResource(R.drawable.bg_double_bogey)
            }
        } else {
            view.setBackgroundResource(R.drawable.layout_bg)
        }
    }
}