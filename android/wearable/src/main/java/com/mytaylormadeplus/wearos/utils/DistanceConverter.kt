package com.mytaylormadeplus.wearos.utils

object DistanceConverter {
    fun fromYardsToMeter(yards: Float): Float {
        return yards / 1.09361f
    }

    fun fromMetersToYards(meters: Float): Float {
        return meters * 1.09361f
    }

    fun fromMetersToFeet(meters: Float): Float {
        return meters * 3.28084f
    }

    fun fromFeetToMeters(feet: Float): Float {
        return feet / 3.28084f
    }

    fun fromMetersToMiles(meters: Float): Float {
        return meters * 0.000621371f
    }

    fun fromMetersToKilometers(meters: Float): Float {
        return meters / 1000
    }

    fun fromYardsToFeet(yards: Float): Float {
        return yards * 3
    }
}