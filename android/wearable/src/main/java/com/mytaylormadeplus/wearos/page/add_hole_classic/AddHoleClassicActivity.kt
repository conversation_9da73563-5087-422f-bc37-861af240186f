package com.mytaylormadeplus.wearos.page.add_hole_basic

import android.os.SystemClock
import android.view.View
import android.view.View.GONE
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import com.mytaylormadeplus.wearos.R
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.data.model.RoundHole
import com.mytaylormadeplus.wearos.databinding.ActivityAddHoleClassicBinding
import com.mytaylormadeplus.wearos.utils.ClassicUtils
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AddHoleClassicActivity :
    BaseActivity<AddHoleClassicNavigator, AddHoleClassicViewModel, ActivityAddHoleClassicBinding>(),
    AddHoleClassicNavigator {
    var holeScore: Int = 0
    var putts: Int = 0
    var isBunkerShot: Boolean = false
    var isTrackScoresOnly: Boolean = false
    var hole: RoundHole? = null
    var greenHit: String? = ClassicUtils.GREEN_HIT
    var fairwayHit: String? = ClassicUtils.FAIRWAY_HIT
    private var mLastClickTime: Long = 0


    override fun setupViewDataBinding() {
        binding = ActivityAddHoleClassicBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
        binding.playRound = viewModel.playRoundManager
        binding.scrollView.requestFocus()

    }

    override fun setup() {
        setData()
        setupView()
        setupAction()
    }

    private fun setData() {
        hole = binding.playRound!!.selectedHole
        if (hole != null) {
            if (hole!!.holeScore > 0) {
                holeScore = hole!!.holeScore
            } else {
                holeScore = hole!!.par
            }
            if (hole!!.grStats != null && !hole!!.grStats.equals("")) {
                putts = hole!!.puttsNumber
                greenHit = hole!!.grStats
            } else {
                putts = 2
            }
            if (hole!!.fwStats != null && !hole!!.fwStats.equals("")) {
                fairwayHit = hole!!.fwStats
            }
            isBunkerShot = hole!!.bunkerHit

            if (hole!!.par == 3) {
                fairwayHit = "";
                binding.tvFairwayTitle.setText("Fairway N/A (Par 3)");
                binding.fairways.imgFairwayLeft.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.gray_light_3
                    )
                );
                binding.fairways.imgFairwayRight.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.gray_light_3
                    )
                );
                binding.fairways.imgFairwayHit.setBackground(
                    ContextCompat.getDrawable(
                        this,
                        R.drawable.bg_curved_gray_white
                    )
                );
                binding.fairways.imgFairwayHit.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.gray_light_3
                    )
                );
            }
        }


    }

    private fun setupView() {
        if (hole != null) {
            binding.lbHole.setText(
                "${getString(R.string.hole)} ${hole!!.holeNumber} - ${
                    getString(
                        R.string.par
                    )
                } ${hole!!.par}"
            )
            if (hole!!.holeScore > 0) {
                binding.btnComplete.setText(getString(R.string.save_score))
            }
        }
        if (viewModel.isCompleteHole()) {
            binding.viewLine.visibility = GONE
            binding.lnTrack.visibility = GONE
            if (!viewModel.isClassicRound()) {
                val layoutParams: LinearLayout.LayoutParams = binding.btnComplete.layoutParams as LinearLayout.LayoutParams
                layoutParams.topMargin = 0
                binding.lbHoleScore.visibility = GONE
                binding.btnComplete.layoutParams = layoutParams
                isTrackScoresOnly = true
                checkTrackScore()
            }
        }
        updateView()
        checkDrawable()
        checkDrawableBunker()
        if (hole!!.par != 3) {
            checkDrawableFairway()
        }
    }

    private fun setupAction() {
        binding.rlPlusScore.setOnClickListener {
            increaseScore();
            updateView();
        }
        binding.rlMinusScore.setOnClickListener {
            decreaseScore();
            updateView();
        }
        binding.rlPlusPutts.setOnClickListener {
            increasePutts();
            updateView();
        }
        binding.rlMinusPutts.setOnClickListener {
            decreasePutts();
            updateView();
        }
        if (hole!!.par != 3) {
            binding.fairways.imgFairwayLeft.setOnClickListener {
                if (!fairwayHit.equals(ClassicUtils.FAIRWAY_LEFT)) {
                    fairwayHit = ClassicUtils.FAIRWAY_LEFT;
                } else {
                    fairwayHit = ClassicUtils.FAIRWAY_HIT;
                }
                checkDrawableFairway();
            }
            binding.fairways.imgFairwayRight.setOnClickListener {
                if (!fairwayHit.equals(ClassicUtils.FAIRWAY_RIGHT)) {
                    fairwayHit = ClassicUtils.FAIRWAY_RIGHT;
                } else {
                    fairwayHit = ClassicUtils.FAIRWAY_HIT;
                }
                checkDrawableFairway();
            }
            binding.fairways.imgFairwayHit.setOnClickListener {
                fairwayHit = ClassicUtils.FAIRWAY_HIT;
                checkDrawableFairway();
            }
        }
        binding.greens.imgGreenUp.setOnClickListener {
            if (greenHit.equals(ClassicUtils.GREEN_UP))
                greenHit = ClassicUtils.GREEN_HIT;
            else greenHit = ClassicUtils.GREEN_UP;
            checkDrawable();
        }
        binding.greens.imgGreenDown.setOnClickListener {
            if (greenHit.equals(ClassicUtils.GREEN_DOWN))
                greenHit = ClassicUtils.GREEN_HIT;
            else greenHit = ClassicUtils.GREEN_DOWN;
            checkDrawable();
        }
        binding.greens.imgGreenLeft.setOnClickListener {
            if (greenHit.equals(ClassicUtils.GREEN_LEFT))
                greenHit = ClassicUtils.GREEN_HIT;
            else greenHit = ClassicUtils.GREEN_LEFT;
            checkDrawable();
        }
        binding.greens.imgGreenRight.setOnClickListener {
            if (greenHit.equals(ClassicUtils.GREEN_RIGHT))
                greenHit = ClassicUtils.GREEN_HIT;
            else greenHit = ClassicUtils.GREEN_RIGHT;
            checkDrawable();
        }
        binding.greens.imgGreenHit.setOnClickListener {
            greenHit = ClassicUtils.GREEN_HIT;
            checkDrawable();
        }
        binding.tvYes.setOnClickListener {
            isBunkerShot = true;
            checkDrawableBunker();
        }
        binding.tvNo.setOnClickListener {
            isBunkerShot = false;
            checkDrawableBunker();
        }
        binding.lnTrack.setOnClickListener {
            isTrackScoresOnly = !isTrackScoresOnly
            checkTrackScore();
        }
        binding.btnComplete.setOnClickListener {
            if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
                return@setOnClickListener
            }
            mLastClickTime = SystemClock.elapsedRealtime();
            var roundHole = hole
            if (roundHole == null) {
                roundHole = RoundHole()
            }
            if (!isTrackScoresOnly) {
                roundHole.fwStats = fairwayHit
                roundHole.grStats = greenHit
                roundHole.bunkerHit = isBunkerShot
                roundHole.puttsNumber = putts
                roundHole.holeScore = holeScore
                viewModel.finishHoleClassic(roundHole)
            } else {
                viewModel.finishHoleBasic(holeScore)
            }

        }
    }

    private fun increaseScore() {
        if (holeScore < 12) {
            holeScore = holeScore.plus(1)
        }
    }

    private fun decreaseScore() {
        if (holeScore > 1) {
            holeScore = holeScore.minus(1)
            checkPuttWithScore()
        }
    }

    private fun increasePutts() {
        if (putts < holeScore && putts < 11) {
            putts = putts.plus(1)
            checkScoreWithPutt()
        }
    }

    private fun decreasePutts() {
        if (putts > 0) {
            putts = putts.minus(1)
        }
    }

    private fun checkPuttWithScore() {
        if (putts >= holeScore) {
            putts--;
        }
    }
    private fun checkScoreWithPutt() {
        if (putts >= holeScore) {
            holeScore++;
        }
    }
    private fun updateView() {
        binding.tvHoleScore.setText(holeScore.toString())
        binding.tvPutts.setText(putts.toString())
    }

    private fun checkTrackScore() {
        if (isTrackScoresOnly) {
            binding.tvTrack.setText(R.string.track_firs_and_girs);
            binding.lnTrack.setBackground(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.custom_button_track_active
                )
            );
            binding.viewLine.setBackgroundColor(ContextCompat.getColor(this, R.color.blue_track));
            binding.llTrackFIR.setVisibility(View.GONE);
            binding.scrollView.scrollY = 30
        } else {
            binding.tvTrack.setText(R.string.track_scores_only);
            binding.lnTrack.setBackground(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.custom_button_track_inactive
                )
            );
            binding.viewLine.setBackgroundColor(ContextCompat.getColor(this, R.color.gray_light));
            binding.llTrackFIR.setVisibility(View.VISIBLE);
        }
    }

    private fun checkDrawableFairway() {
        binding.fairways.imgFairwayHit.setBackground(
            ContextCompat.getDrawable(
                this,
                R.drawable.bg_curved_gray
            )
        );
        binding.fairways.imgFairwayHit.setColorFilter(
            ContextCompat.getColor(
                this,
                R.color.gray_light_2
            )
        );
        binding.fairways.imgFairwayRight.setColorFilter(
            ContextCompat.getColor(
                this,
                R.color.gray_light_2
            )
        );
        binding.fairways.imgFairwayLeft.setColorFilter(
            ContextCompat.getColor(
                this,
                R.color.gray_light_2
            )
        );
        when (fairwayHit) {
            ClassicUtils.FAIRWAY_HIT -> {
                binding.fairways.imgFairwayHit.setBackground(
                    ContextCompat.getDrawable(
                        this,
                        R.drawable.bg_curved_green_white
                    )
                );
                binding.fairways.imgFairwayHit.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.white
                    )
                );
            }
            ClassicUtils.FAIRWAY_LEFT -> binding.fairways.imgFairwayLeft.setColorFilter(
                ContextCompat.getColor(this, R.color.redOrange)
            );
            else -> {
                binding.fairways.imgFairwayRight.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.redOrange
                    )
                );
            }
        }
    }

    private fun checkDrawable() {
        binding.greens.imgGreenHit.setBackground(
            ContextCompat.getDrawable(
                this,
                R.drawable.bg_curved_gray
            )
        )
        binding.greens.imgGreenHit.setColorFilter(
            ContextCompat.getColor(
                this,
                R.color.gray_light_2
            )
        )
        binding.greens.imgGreenUp.setBackground(
            ContextCompat.getDrawable(
                this,
                R.drawable.bg_circle_gray_green_hit
            )
        )
        binding.greens.imgGreenDown.setBackground(
            ContextCompat.getDrawable(
                this,
                R.drawable.bg_circle_gray_green_hit
            )
        )
        binding.greens.imgGreenLeft.setBackground(
            ContextCompat.getDrawable(
                this,
                R.drawable.bg_circle_gray_green_hit
            )
        )
        binding.greens.imgGreenRight.setBackground(
            ContextCompat.getDrawable(
                this,
                R.drawable.bg_circle_gray_green_hit
            )
        )
        binding.greens.imgGreenUp.setColorFilter(ContextCompat.getColor(this, R.color.gray_light_2))
        binding.greens.imgGreenDown.setColorFilter(
            ContextCompat.getColor(
                this,
                R.color.gray_light_2
            )
        )
        binding.greens.imgGreenLeft.setColorFilter(
            ContextCompat.getColor(
                this,
                R.color.gray_light_2
            )
        )
        binding.greens.imgGreenRight.setColorFilter(
            ContextCompat.getColor(
                this,
                R.color.gray_light_2
            )
        )

        when (greenHit) {
            ClassicUtils.GREEN_UP -> {
                binding.greens.imgGreenUp.setBackground(
                    ContextCompat.getDrawable(
                        this,
                        R.drawable.bg_circle_red_green_hit
                    )
                )
                binding.greens.imgGreenUp.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.white
                    )
                )
            }
            ClassicUtils.GREEN_DOWN -> {
                binding.greens.imgGreenDown.setBackground(
                    ContextCompat.getDrawable(
                        this,
                        R.drawable.bg_circle_red_green_hit
                    )
                )
                binding.greens.imgGreenDown.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.white
                    )
                )
            }
            ClassicUtils.GREEN_LEFT -> {
                binding.greens.imgGreenLeft.setBackground(
                    ContextCompat.getDrawable(
                        this,
                        R.drawable.bg_circle_red_green_hit
                    )
                )
                binding.greens.imgGreenLeft.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.white
                    )
                )
            }
            ClassicUtils.GREEN_RIGHT -> {
                binding.greens.imgGreenRight.setBackground(
                    ContextCompat.getDrawable(
                        this,
                        R.drawable.bg_circle_red_green_hit
                    )
                )
                binding.greens.imgGreenRight.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.white
                    )
                )
            }
            ClassicUtils.GREEN_HIT -> {
                binding.greens.imgGreenHit.setBackground(
                    ContextCompat.getDrawable(
                        this,
                        R.drawable.bg_curved_green_white
                    )
                )
                binding.greens.imgGreenHit.setColorFilter(
                    ContextCompat.getColor(
                        this,
                        R.color.white
                    )
                )
            }
        }
    }

    private fun checkDrawableBunker() {
        if (isBunkerShot) {
            binding.tvYes.setTextColor(ContextCompat.getColor(this, R.color.white));
            binding.tvYes.setBackground(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.custom_button_bunker_active
                )
            );
            binding.tvNo.setTextColor(ContextCompat.getColor(this, R.color.black));
            binding.tvNo.setBackground(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.custom_button_bunker_inactive
                )
            );
        } else {
            binding.tvNo.setTextColor(ContextCompat.getColor(this, R.color.white));
            binding.tvNo.setBackground(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.custom_button_bunker_active
                )
            );
            binding.tvYes.setTextColor(ContextCompat.getColor(this, R.color.black));
            binding.tvYes.setBackground(
                ContextCompat.getDrawable(
                    this,
                    R.drawable.custom_button_bunker_inactive
                )
            );
        }
    }

    override fun finishHole() {
        finish()
    }
}