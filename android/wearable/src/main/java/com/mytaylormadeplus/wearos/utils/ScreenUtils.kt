package com.mytaylormadeplus.wearos.utils

import android.content.Context
import android.util.DisplayMetrics
import android.view.WindowManager

object ScreenUtils {

    fun getScreenWidth(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        windowManager.let {
            val dm = DisplayMetrics()
            it.defaultDisplay.getMetrics(dm)
            return dm.widthPixels
        }
    }

    fun getScreenHeight(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        windowManager.let {
            val dm = DisplayMetrics()
            it.defaultDisplay.getMetrics(dm)
            return dm.heightPixels
        }
    }

    fun px2Dp(context: Context, px: Float): Float {
        return px / context.resources.displayMetrics.density
    }

    fun dp2Px(context: Context, dp: Int): Float {
        return dp * context.resources.displayMetrics.density
    }

    fun px2Sp(context: Context, px: Float): Float {
        return px / context.resources.displayMetrics.scaledDensity
    }

    fun sp2Px(context: Context, sp: Float): Float {
        return sp * context.resources.displayMetrics.scaledDensity
    }

}
