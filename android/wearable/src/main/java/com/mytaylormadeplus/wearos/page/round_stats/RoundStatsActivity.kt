package com.mytaylormadeplus.wearos.page.round_stats

import android.content.Intent
import androidx.core.content.ContextCompat
import com.mytaylormadeplus.wearos.R
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivityRoundStatsBinding
import com.mytaylormadeplus.wearos.page.connection.ConnectionActivity
import com.mytaylormadeplus.wearos.page.connection_square.ConnectionSquareActivity
import com.mytaylormadeplus.wearos.page.end_round_notification.EndRoundNotificationActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RoundStatsActivity :
    BaseActivity<RoundStatsNavigator, RoundStatsViewModel, ActivityRoundStatsBinding>(),
    RoundStatsNavigator {

    override fun setupViewDataBinding() {
        binding = ActivityRoundStatsBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
        binding.vScroll.requestFocus()
    }

    override fun setup() {
        setData()
        setupView()
    }

    private fun setData() {
    }

    private fun setupView() {
        binding.graphFairwaysLeft.setTextPercentSize(46)
        binding.graphFairwaysRight.setTextPercentSize(46)
        binding.graphFairwaysLeft.setColorBorder(ContextCompat.getColor(this, R.color.redOrange))
        binding.graphFairwaysRight.setColorBorder(ContextCompat.getColor(this, R.color.redOrange))
        binding.graphPutts.setMaxValue(48f)
        binding.graphPutts.showLimitBar(true)
        binding.graphPutts.setLimitBarValue(36)
        binding.graphPutts.usePercentage(false)
    }

    override fun endRound() {
        runOnUiThread {
            val intent = Intent(this, EndRoundNotificationActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            startActivity(intent)
            finish()
        }
    }

    override fun cancelRound() {
        runOnUiThread {
            val flatForm = viewModel.getFlatForm()
            if (flatForm == "square") {
                val intent = Intent(this, ConnectionSquareActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(intent)
            } else {
                val intent = Intent(this, ConnectionActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(intent)
            }
            finish()
        }
    }

    override fun setValueGraph(
        fairwayHit: Float,
        fairwayLeft: Float,
        fairwayRight: Float,
        greenIn: Float,
        sandSaves: Float,
        puttPerRound: Float
    ) {
        if (fairwayHit > -1)
            binding.graphFairwaysHit.setValue(fairwayHit)
        else binding.graphFairwaysHit.showDash()
        if (fairwayLeft > -1)
            binding.graphFairwaysLeft.setValue(fairwayLeft)
        else binding.graphFairwaysLeft.showDash()
        if (fairwayRight > -1)
            binding.graphFairwaysRight.setValue(fairwayRight)
        else
            binding.graphFairwaysRight.showDash()
        if (greenIn > -1)
            binding.graphGreensIn.setValue(greenIn)
        else binding.graphGreensIn.showDash()
        if (sandSaves > -1)
            binding.graphSandSaves.setValue(sandSaves)
        else binding.graphSandSaves.showDash()
        if (puttPerRound > -1) {
            binding.graphPutts.setValue(puttPerRound)
            if (puttPerRound > 36) {
                binding.graphPutts.setColorBorder(ContextCompat.getColor(this, R.color.redOrange));
            }
        } else binding.graphPutts.showDash()

    }

    override fun showDash() {
        binding.graphFairwaysHit.showDash()
        binding.graphFairwaysLeft.showDash()
        binding.graphFairwaysRight.showDash()
        binding.graphGreensIn.showDash()
        binding.graphSandSaves.showDash()
        binding.graphPutts.showDash()
    }

    override fun checkCancelRound(isCancelRound: Boolean): String {
        if (isCancelRound)
            return getString(R.string.cancel_round)
        return getString(R.string.finish)

    }
}