package com.mytaylormadeplus.wearos.utils

import android.annotation.SuppressLint
import java.text.SimpleDateFormat
import java.util.*


object DateUtils {

    const val DATE_FORMAT_FULL = "yyyy-MM-dd'T'HH:mm:ss'Z'"
    const val DATE_FORMAT = "yyyy-MM-dd"
    const val DATE_FORMAT_US = "EEE MMM dd, YYYY"

    @SuppressLint("SimpleDateFormat")
    fun getCurrentDateString(): String {
        return SimpleDateFormat(DATE_FORMAT_FULL).apply { timeZone = TimeZone.getTimeZone("UTC") }.format(Date())
    }

    fun parseDateStringFrom(date: String, format: String): String {
        val dateFormat = SimpleDateFormat(DATE_FORMAT_FULL, Locale.getDefault())
        return parseDateToString(dateFormat.parse(date), format)
    }

    fun parseStringToDate(date: String, format: String): Date {
        val dateFormat = SimpleDateFormat(format, Locale.getDefault())
        return dateFormat.parse(date) ?: Date()
    }

    fun parseDateToString(date: Date, format: String): String {
        val dateFormat = SimpleDateFormat(format, Locale.getDefault())
        return dateFormat.format(date) ?: ""
    }

}