package com.mytaylormadeplus.wearos.utils

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText


object KeyboardUtils {

    fun showKeyboard(edit: EditText, context: Context) {
        edit.isFocusable = true
        edit.isFocusableInTouchMode = true
        edit.requestFocus()
        val imm = context
            .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(edit, 0)
    }

    fun showKeyboard(view: View) {
        if (view.isFocused) {
            view.clearFocus()
        }
        view.requestFocus()
        val keyboard =
            view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        keyboard?.showSoftInput(view, InputMethodManager.SHOW_FORCED)
    }

    fun hideKeyboard(activity: Activity) {
        var view = activity.currentFocus
        if (view == null) view = View(activity)
        val imm = activity
            .getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    fun hideKeyboard(view: View) {
        val im = view.context.getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
        if (im != null && view.isFocused && view.windowToken != null) {
            im.hideSoftInputFromWindow(view.windowToken, 0)
            view.clearFocus()
        }
    }

    fun toggleKeyboard(context: Context) {
        val imm = context
            .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0)
    }

}
