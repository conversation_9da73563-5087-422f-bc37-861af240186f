package com.mytaylormadeplus.wearos.data.model

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.mytaylormadeplus.wearos.utils.DateUtils
import kotlinx.android.parcel.Parcelize

@Parcelize
data class RoundHole(
    @Expose
    @SerializedName("number")
    var holeNumber: Int = 0,

    @Expose
    @SerializedName("par")
    var par: Int = 0,

    @Expose
    @SerializedName("score")
    var holeScore: Int = 0,

    @Expose
    @SerializedName("pin_location")
    var pinLocation: List<Double>? = null,

    @Expose
    @SerializedName("strokes")
    var strokes: ArrayList<RoundStroke> = ArrayList(),

    @Expose
    @SerializedName("started_on")
    var startedOn: String? = null,

    @Expose
    @SerializedName("fw_stats")
    var fwStats: String? = null,

    @Expose
    @SerializedName("gr_stats")
    var grStats: String? = null,

    @Expose
    @SerializedName("bunker_hit")
    var bunkerHit: Boolean = false,

    @Expose
    @SerializedName("putts_number")
    var puttsNumber: Int = 0,

    @Expose
    @SerializedName("stroke_index")
    var handicap: Int = 0,

    var yards: Long = 0,

    var greenFront: List<Double>? = null,

    var greenBack: List<Double>? = null,

    var features: FeatureCollection? = null,

    var lastShotName: String = "-",

    var lastModifiedDate: Long = 0,

    var lastSyncDate: Long = 0,

    var finishHole:  Boolean = false

) : Parcelable {

    fun isSynced(): Boolean {
        return lastModifiedDate == lastSyncDate
    }

    fun updateStartedOn() {
        this.startedOn = DateUtils.getCurrentDateString()
    }
}