/*
 * Copyright 2015 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.mytaylormadeplus.wearos.manager


import android.content.Context
import android.util.Log
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.Tasks
import com.google.android.gms.wearable.*
import com.mytaylormadeplus.wearos.BuildConfig
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.concurrent.timerTask

@Singleton
open class WearService @Inject constructor(val context: Context) :
    MessageClient.OnMessageReceivedListener {

    private val CAPABILITY_PHONE_APP = "mytaylormadeplus_wear_auth"

    private val MESSAGE_PATH = "/mytaylormadeplus_message_path"
    private val MESSAGE_DATA_USER_ID = "MESSAGE_DATA_USER_ID"
    private val MESSAGE_DATA_USER_TOKEN = "MESSAGE_DATA_USER_TOKEN"
    private val MESSAGE_DATA_YARD_UNIT = "MESSAGE_DATA_YARD_UNIT"

    private var checkConnectionTimer: Timer? = null

    enum class WearConnectionError {
        AUTH, CONNECTION, GPS
    }

    interface WearServiceListener {
        fun onReceiveData(userId: String, userToken: String, isUnitYard: Boolean)
        fun onError(error: WearConnectionError)
    }

    private var wearServiceListener: WearServiceListener? = null

    open fun start() {
        Wearable.getMessageClient(context).addListener(this)
    }

    open fun stop() {
        wearServiceListener = null
        stopCheckConnection()
        Wearable.getMessageClient(context).removeListener(this)
    }

    open fun getAuthInfo(listener: WearServiceListener) {
        this.wearServiceListener = listener
        Thread {
            try {
                val capabilityInfo = Tasks.await(
                    Wearable.getCapabilityClient(context)
                        .getCapability(CAPABILITY_PHONE_APP, CapabilityClient.FILTER_REACHABLE)
                )
                val phoneNodeId = capabilityInfo.nodes.firstOrNull()?.id
                val sendMessageTask: Task<Int> =
                    Wearable.getMessageClient(context).sendMessage(phoneNodeId, MESSAGE_PATH, DataMap().toByteArray())
                Tasks.await(sendMessageTask)
                startCheckConnection()
            } catch (exception: Exception) {
                stopCheckConnection()
                wearServiceListener?.onError(WearConnectionError.CONNECTION)
            }
        }.start()
    }

    private fun startCheckConnection() {
        try {
            stopCheckConnection()
            checkConnectionTimer = Timer()
            checkConnectionTimer?.schedule(timerTask {
                wearServiceListener?.onError(WearConnectionError.CONNECTION)
            }, 60000)
        } catch (e: Exception) {
        }
    }

    private fun stopCheckConnection() {
        try {
            checkConnectionTimer?.cancel()
            checkConnectionTimer = null
        } catch (e: Exception) {
        }
    }

    override fun onMessageReceived(messageEvent: MessageEvent) {
        stopCheckConnection()
        val dataMap = DataMap.fromByteArray(messageEvent.data)
        val userId = dataMap.getString(MESSAGE_DATA_USER_ID)
        val userToken = dataMap.getString(MESSAGE_DATA_USER_TOKEN)
        val isUnitYard = dataMap.getBoolean(MESSAGE_DATA_YARD_UNIT)
        if (!userId.isNullOrEmpty() && !userToken.isNullOrEmpty()) {
            wearServiceListener?.onReceiveData(userId, userToken, isUnitYard)
        } else {
            wearServiceListener?.onError(WearConnectionError.AUTH)
        }
    }
}
