package com.mytaylormadeplus.wearos.base

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.databinding.ViewDataBinding
import com.mytaylormadeplus.wearos.R
import com.mytaylormadeplus.wearos.data.network.ErrorHandler
import com.mytaylormadeplus.wearos.widget.LoadingView
import javax.inject.Inject


abstract class BaseActivity<Navigator : BaseNavigator, ViewModel : BaseViewModel<Navigator>, Binding : ViewDataBinding>
    : AppCompatActivity(), BaseNavigator {

    @Inject
    lateinit var viewModel: ViewModel
    lateinit var binding: Binding

    private var loadingView: LoadingView? = null
    private lateinit var textToast: TextView
    private lateinit var layout: View

    override fun onCreate(savedInstanceState: Bundle?) {
        installSplashScreen()
        super.onCreate(savedInstanceState)
        viewModel.navigator = this as Navigator
        setupViewDataBinding()
        setContentView(binding.root)
        binding.lifecycleOwner = this
        setup()
        viewModel.setup()
        val inflater = layoutInflater
        layout = inflater.inflate(R.layout.toast,null)
        textToast = layout.findViewById(R.id.textToast)

    }

    abstract fun setupViewDataBinding()

    abstract fun setup()

    override fun onDestroy() {
        viewModel.onDestroy()
        super.onDestroy()
    }

    override fun onResume() {
        viewModel.onResume()
        super.onResume()
    }

    override fun onPause() {
        viewModel.onStop()
        super.onPause()
    }
    override fun onStop() {
        viewModel.onStop()
        super.onStop()
    }

    override fun showLoadingView() {
        try {
            loadingView = LoadingView(this)
            loadingView?.show()
        } catch (e: Exception) {
        }
    }

    override fun hideLoadingView() {
        try {
            loadingView?.dismiss()
        } catch (e: Exception) {
        }
    }

    override fun handleNetworkError(throwable: Throwable) {
        hideLoadingView()
        ErrorHandler.handle(this, throwable)?.let { errorMessage ->
            textToast.text = errorMessage
            val toast = Toast(this)
            toast.duration = Toast.LENGTH_LONG
            toast.setMargin(0f, 0.1f)
            toast.view = layout
            toast.show()
        }
    }

    override fun showError(message: String) {
        runOnUiThread {
            if (!TextUtils.isEmpty(message)) {
                textToast.text = message
            } else {
                textToast.text = getString(R.string.error_server_connection)
            }
            val toast = Toast(this)
            toast.duration = Toast.LENGTH_LONG
            toast.setMargin(0f, 0.1f)
            toast.view = layout
            toast.show()
        }
    }
}