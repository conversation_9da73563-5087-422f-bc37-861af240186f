package com.mytaylormadeplus.wearos.data.repository

import com.mytaylormadeplus.wearos.data.network.ApiService
import com.mytaylormadeplus.wearos.data.preferences.UserSession
import io.reactivex.Observable
import okhttp3.ResponseBody
import retrofit2.Response
import javax.inject.Inject

class ClubsRepository @Inject constructor(
    private var apiService: ApiService,
    private var userSession: UserSession
) {
    fun getActiveClubs(): Observable<Response<ResponseBody>> {
        return apiService.getActiveClubs(take = 100)
    }
    
//    fun getClubStatsWithParameters(): Observable<Response<ResponseBody>> {
//        var units: String = "yards"
//        if (!userSession.isUnitYard) {
//            units = "meters"
//        }
//        return apiService.getClubStatsWithParameters(userID = userSession.userID!!, units = units)
//    }
}