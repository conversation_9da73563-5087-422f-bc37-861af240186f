package com.mytaylormadeplus.wearos.page.club_list

import android.util.Log
import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.model.ActiveClubs
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.data.preferences.UserSession
import com.mytaylormadeplus.wearos.manager.LocationProvider
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.page.club_list.adapter.ClubListAdapter
import org.json.JSONObject
import javax.inject.Inject
import kotlin.math.abs

class ClubListViewModel @Inject constructor(
    private val userSession: UserSession,
    private val playRoundManager: PlayRoundManager,
    private val locationProvider: LocationProvider,
    private val appPreferences: AppPreferences
): BaseViewModel<ClubListNavigator>() {

    private val clubsData: ArrayList<ActiveClubs> = ArrayList()
    private val clubBetaData: ArrayList<ActiveClubs> = ArrayList()
    private val rows = mutableListOf<ClubListAdapter.IRow>()
    private var isGreen: Boolean = false
    private var distanceGreen: Long = 0

    fun setIsGreen(data: Boolean) {
        isGreen = data
    }

    fun setDistanceGreen(data: Long) {
        distanceGreen = data
    }

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }

    fun getActiveClubs(): MutableList<ClubListAdapter.IRow> {
        addClubData()
        if (clubsData.size > 0) {
            //add-header
            rows.add(ClubListAdapter.HeaderRow())

            //add-Driver
            val driverData = clubsData.filter { it.clubFamily.lowercase() == DRIVER }
            driverData.sortedWith(compareBy<ActiveClubs> { it.clubType }.thenBy { it.clubName }).map {
                rows.add(ClubListAdapter.ClubRow(it.clubType, it.clubName, it.clubFamily))
                clubBetaData.add(it)
            }

            //add-Fairways
            val fairData = clubsData.filter { it.clubFamily.lowercase() == FAIRWAY }
            fairData.sortedWith(compareBy<ActiveClubs> { it.clubType }.thenBy { it.clubName }).map {
                rows.add(ClubListAdapter.ClubRow(it.clubType, it.clubName, it.clubFamily))
                clubBetaData.add(it)
            }

            //add-Hybrid
            val hybridData = clubsData.filter { it.clubFamily.lowercase() == HYBRID }
            hybridData.sortedWith(compareBy<ActiveClubs> { it.clubType }.thenBy { it.clubName }).map {
                rows.add(ClubListAdapter.ClubRow(it.clubType, it.clubName, it.clubFamily))
                clubBetaData.add(it)
            }

            //add-Irons
            val ironData = clubsData.filter { it.clubFamily.lowercase() == IRON }
            ironData.sortedWith(compareBy<ActiveClubs> { it.clubType }.thenBy { it.clubName }).map {
                rows.add(ClubListAdapter.ClubRow(it.clubType, it.clubName, it.clubFamily))
                clubBetaData.add(it)
            }

            //add-Wedge
            val pitchingWedge = clubsData.filter { it.clubFamily.lowercase() == WEDGE && it.clubType.uppercase() == "PW" }
            pitchingWedge.sortedBy { it.clubID }.map {
                rows.add(ClubListAdapter.ClubRow(it.clubType, it.clubName, it.clubFamily))
                clubBetaData.add(it)
            }
            val approachWedge = clubsData.filter { it.clubFamily.lowercase() == WEDGE && it.clubType.uppercase() == "AW" }
            approachWedge.sortedBy { it.clubID }.map {
                rows.add(ClubListAdapter.ClubRow(it.clubType, it.clubName, it.clubFamily))
                clubBetaData.add(it)
            }
            val sandWedge = clubsData.filter { it.clubFamily.lowercase() == WEDGE && it.clubType.uppercase() == "SW" }
            sandWedge.sortedBy { it.clubID }.map {
                rows.add(ClubListAdapter.ClubRow(it.clubType, it.clubName, it.clubFamily))
                clubBetaData.add(it)
            }
            val lobWedge = clubsData.filter { it.clubFamily.lowercase() == WEDGE && it.clubType.uppercase() == "LW" }
            lobWedge.sortedBy { it.clubID }.map {
                rows.add(ClubListAdapter.ClubRow(it.clubType, it.clubName, it.clubFamily))
                clubBetaData.add(it)
            }

            //add-putter
            val putterData = clubsData.filter { it.clubFamily.lowercase() == PUTTER }
            putterData.map {
                rows.add(ClubListAdapter.PutterRow(it.clubType, it.clubName))
                clubBetaData.add(it)
            }

            //add-endHole
            rows.add(ClubListAdapter.EndRow())

            //add-penalty
            rows.add(ClubListAdapter.ClubRow("!", PENALTY, ""))
        }
        return rows
    }

    private fun addClubData() {
        val responseJson = JSONObject(userSession.listActiveClubs ?: "{}")
        val data = responseJson.getJSONArray("data")
        if (data.length() > 0) {
            for (i in 0 until data.length()) {
                val clubObject = data.getJSONObject(i)
                if (clubObject.getString("clubFamily").lowercase() != BALL) {
                    try {
                        clubsData.add(
                            ActiveClubs(
                                clubID = clubObject.getString("cdmWitbId"),
                                clubType = clubObject.getString("clubType"),
                                clubName = clubObject.getString("modelname"),
                                clubFamily = clubObject.getString("clubFamily"),
                                clubManufacturer = clubObject.getString("manufacturer"),
                                clubDistance = setDefaultDistanceByClubName(clubObject.getString("clubType"))
                            )
                        )
                    } catch (e: Exception) {
                        Log.i("info","error: $e")
                    }
                }
            }
        }
    }

    companion object {
        private const val DRIVER = "driver"
        private const val FAIRWAY = "fairway"
        private const val HYBRID= "hybrid"
        private const val IRON = "iron"
        private const val WEDGE = "wedge"
        private const val PUTTER = "putter"
        private const val PENALTY = "PENALTY"
        private const val BALL = "ball"
    }

    fun clickToEndHole(number: Int, position: Int) {
        if (number > 0) {
            clickToPutter(number, position)
        } else {
            playRoundManager.finishAdvancedHole()
        }
    }

    fun clickToClub(position: Int) {
        val item: ClubListAdapter.ClubRow = rows[position] as ClubListAdapter.ClubRow
        if (item.clubName != PENALTY) {
            val dataObj: ArrayList<ActiveClubs> = filterClub(item.clubType, item.clubName)
            Log.i("info", "clubs: ${dataObj.first()}")
            Log.i("info", "roundID: ${playRoundManager.round.roundID}")
            if (dataObj.size > 0) {
                addShot(
                    clubID = dataObj.first().clubID,
                    clubName = dataObj.first().clubName,
                    clubType = dataObj.first().clubType
                )
            }
        } else {
            addShot(clubID = null, clubName = PENALTY, clubType = null, isPenalty = true)
        }
    }

    fun clickToPutter(number: Int, position: Int) {
        val item: ClubListAdapter.PutterRow = rows[position] as ClubListAdapter.PutterRow
        val dataObj: ArrayList<ActiveClubs> = filterClub(item.clubType, item.clubName)
        Log.i("info", "clubs: ${dataObj.first()} & number putter: $number")
        if (dataObj.size > 0) {
            addShot(
                clubID = dataObj.first().clubID,
                clubName = dataObj.first().clubName,
                clubType = dataObj.first().clubType,
                isPutter = true,
                putterCount = number
            )
        }
    }

    private fun filterClub(clubType: String, clubName: String): ArrayList<ActiveClubs> {
        return clubsData.filter { it.clubType == clubType && it.clubName == clubName } as ArrayList<ActiveClubs>
    }

    private fun addShot(
        clubID: String?,
        clubName: String?,
        clubType: String?,
        isPutter: Boolean = false,
        putterCount: Int = 1,
        isPenalty: Boolean = false
    ) {
        val location = locationProvider.currentLocation
        val distanceToPin =
            if (location == null) 0L else playRoundManager.getDistanceToPin(location, useUnitYards = false)
        playRoundManager.addShot(
            clubID = clubID,
            clubName = clubName,
            clubType = clubType,
            location = location,
            distanceToPin = distanceToPin,
            isPutter = isPutter,
            putterCount = putterCount,
            isPenalty = isPenalty
        )
    }

    // club suggestion

    fun getIndexClubs(): Int {
        var clubIndex: Int = 0
        if(playRoundManager.selectedHole.strokes.size == 0 && playRoundManager.selectedHole.par != 3) {
            clubIndex = 0
        } else if (isGreen) {
            clubIndex = rows.size - 3
        } else {
            if(playRoundManager.selectedHole.par == 3) {
                clubIndex = (rows.size/2)
                val selectedClubIndex: Int = getClubIndex(clubsData, distanceGreen)
                if (selectedClubIndex != -1) {
                    clubIndex = selectedClubIndex
                }
            } else if(playRoundManager.selectedHole.par == 4 || playRoundManager.selectedHole.par == 5) {
                if (rows.size <= 3) {
                    clubIndex = 0
                } else {
                    clubIndex = (rows.size/2)
                    val selectedClubIndex: Int = getClubIndex(clubsData, distanceGreen)
                    if (selectedClubIndex != -1) {
                        clubIndex = selectedClubIndex
                    }
                }
            } else {
                clubIndex = 0
            }
        }
        return clubIndex
    }

    private fun getClubIndex(clubData: ArrayList<ActiveClubs>, distanceGreen: Long): Int {
        var selectedClubIndex = -1
        var selectedClubDistance = 9999
        for (i in 0 until clubBetaData.size) {
            val club: ActiveClubs = clubBetaData[i]
            if (club.clubDistance.toInt() != 0) {
                if (abs(club.clubDistance - distanceGreen) < selectedClubDistance) {
                    selectedClubDistance = abs(club.clubDistance - distanceGreen).toInt();
                    selectedClubIndex = i;
                }
            }
        }
        return selectedClubIndex - 1 ;
    }

    private fun setSeverDistanceByClubName(clubType: String): Long {
        val responseJson = JSONObject(userSession.distanceClub ?: "{}")
        Log.i("info", "club distance: $responseJson")
        val data = responseJson.getJSONArray("data")
        if (data.length() > 0) {
            for (i in 0 until data.length()) {
                val clubObject = data.getJSONObject(i)
                var clubTypeOne: String = clubObject.getString("club").replaceFirst("-", "").lowercase()
                var clubTypeTwo: String = clubType.replaceFirst("-", "").lowercase()
                if (clubTypeOne == clubTypeTwo) {
                    if (clubObject.has("avg") && !clubObject.isNull("avg")) {
                        var distance: Long = clubObject.getLong("avg")
                        return distance
                    }
                }
            }
        }
        return setDefaultDistanceByClubName(clubType)
    }

    private fun setDefaultDistanceByClubName(clubType: String): Long {
        return when (clubType.replaceFirst("-", "").lowercase()) {
            "2fw" -> 225
            "3fw" -> 220
            "4fw" -> 215
            "5fw" -> 205
            "7fw" -> 185
            "9fw" -> 175
            "2h"  -> 215
            "3h"  -> 205
            "4h"  -> 195
            "5h"  -> 185
            "6h"  -> 175
            "1i"  -> 195
            "2i"  -> 190
            "3i"  -> 185
            "4i"  -> 175
            "5i"  -> 165
            "6i"  -> 155
            "7i"  -> 145
            "8i"  -> 135
            "9i"  -> 125
            "pw"  -> 115
            "aw"  -> 95
            "sw"  -> 85
            "lw"  -> 75
            else -> 0
        }
    }
}