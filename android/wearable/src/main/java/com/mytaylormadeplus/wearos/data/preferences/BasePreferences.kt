package com.mytaylormadeplus.wearos.data.preferences

import android.content.Context
import android.content.SharedPreferences

open class BasePreferences constructor(context: Context, shareName: String) {

    private var appSharedPrefs: SharedPreferences = context.getSharedPreferences(shareName, Context.MODE_PRIVATE)

    fun putInt(key: String, value: Int) {
        appSharedPrefs.edit().putInt(key, value).apply()
    }

    fun getInt(key: String, defaultValue: Int = 0): Int {
        return appSharedPrefs.getInt(key, defaultValue)
    }

    fun putLong(key: String, value: Long) {
        appSharedPrefs.edit().putLong(key, value).apply()
    }

    fun getLong(key: String, defaultValue: Long = 0): Long {
        return appSharedPrefs.getLong(key, defaultValue)
    }

    fun putBoolean(key: String, value: Boolean) {
        appSharedPrefs.edit().putBoolean(key, value).apply()
    }

    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return appSharedPrefs.getBoolean(key, defaultValue)
    }

    fun putString(key: String, value: String?) {
        appSharedPrefs.edit().putString(key, value).apply()
    }

    fun getString(key: String, defaultValue: String? = null): String? {
        return appSharedPrefs.getString(key, defaultValue)
    }

    fun remove(key: String){
        appSharedPrefs.edit().remove(key).apply()
    }

    fun clearAll() {
        appSharedPrefs.edit().clear().apply()
    }
}
