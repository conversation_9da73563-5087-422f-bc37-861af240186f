package com.mytaylormadeplus.wearos.page.resume_round

import android.content.Intent
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.databinding.ActivityResumeRoundBinding
import com.mytaylormadeplus.wearos.page.connection.ConnectionActivity
import com.mytaylormadeplus.wearos.page.connection_square.ConnectionSquareActivity
import com.mytaylormadeplus.wearos.page.play_round.PlayRoundActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ResumeRoundActivity : BaseActivity<ResumeRoundNavigator, ResumeRoundViewModel, ActivityResumeRoundBinding>(),
    ResumeRoundNavigator {

    override fun setupViewDataBinding() {
        binding = ActivityResumeRoundBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
        binding.scrollView.requestFocus()
    }

    override fun setup() {
        binding.lbDate.text = viewModel.getRoundPlayOnDate()
        binding.lbCourseName.text = viewModel.getRoundName()
        binding.btnResume.setOnClickListener {
            viewModel.resumeRound()
        }
        binding.btnCancel.setOnClickListener {
            viewModel.cancelRound()
        }
        handlerUISquare()
    }

    private fun handlerUISquare() {
        if (viewModel.getFlatForm() == "square") {
            binding.viewTwoButton.setPadding(25,0,25,0)
            binding.lbCourseName.setPadding(15,0,15,0)
        } else {
            binding.lbResumeRound.setPadding(0, 23, 0, 0)
        }
    }

    override fun openPlayRound() {
        startActivity(Intent(this, PlayRoundActivity::class.java))
        finish()
    }

    override fun openConnection() {
        runOnUiThread {
            val flatForm = viewModel.getFlatForm()
            if (flatForm == "square") {
                val intent = Intent(this, ConnectionSquareActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(intent)
            } else {
                val intent = Intent(this, ConnectionActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(intent)
            }
            finish()
        }
    }

}