package com.mytaylormadeplus.wearos.page.round_detail

import android.app.Activity
import android.content.Intent
import android.view.KeyEvent
import androidx.activity.result.contract.ActivityResultContracts
import com.mytaylormadeplus.wearos.base.BaseActivity
import com.mytaylormadeplus.wearos.data.model.Course
import com.mytaylormadeplus.wearos.databinding.ActivityRoundDetailBinding
import com.mytaylormadeplus.wearos.page.connection.ConnectionActivity
import com.mytaylormadeplus.wearos.page.connection_square.ConnectionSquareActivity
import com.mytaylormadeplus.wearos.page.non_club_error.ClubErrorActivity
import com.mytaylormadeplus.wearos.page.play_round.PlayRoundActivity
import com.mytaylormadeplus.wearos.page.select_list.SelectListActivity
import com.mytaylormadeplus.wearos.page.show_advance_notification.ShowAdvanceActivity
import com.mytaylormadeplus.wearos.utils.NetworkUtils
import com.mytaylormadeplus.wearos.utils.extension.clicks
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RoundDetailActivity :
    BaseActivity<RoundDetailNavigator, RoundDetailViewModel, ActivityRoundDetailBinding>(),
    RoundDetailNavigator {

    override fun setupViewDataBinding() {
        binding = ActivityRoundDetailBinding.inflate(layoutInflater)
        binding.viewModel = viewModel
        binding.scrollView.requestFocus()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.repeatCount == 0) {
            val flatForm = viewModel.getFlatForm()
            if (flatForm == "square") {
                startActivity(Intent(this, ConnectionSquareActivity::class.java))
            } else {
                startActivity(Intent(this, ConnectionActivity::class.java))
            }
            finish()
            return true;
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun setup() {
        getCourseData()
        setupAction()
        handleUISquare()
    }

    private fun handleUISquare() {
        if (viewModel.getFlatForm() == "square") {
            binding.scrollView.setPadding(5, 0, 5, 0)
        }
    }

    private fun getCourseData() {
        intent.getParcelableExtra<Course>("course")?.let { course ->
            viewModel.setData(course)
        }
    }

    private fun setupAction() {
        binding.btnSelectTee.clicks {
            if (viewModel.getAllTeeNames().size == 1 && viewModel.getAllTeeNames().contains("N/A")) {
                return@clicks
            }
            selectTeeLauncher.launch(Intent(this, SelectListActivity::class.java).apply {
                putExtra("title", "Select Tee")
                putStringArrayListExtra("data", viewModel.getAllTeeNames())
            })
        }
        binding.btnSelectType.clicks {
            selectTypeLauncher.launch(Intent(this, SelectListActivity::class.java).apply {
                putExtra("title", "Select Type")
                putStringArrayListExtra("data", viewModel.getAllRoundTypes())
            })
        }
//        binding.btnSelectMode.clicks {
//            selectModeLauncher.launch(Intent(this, SelectListActivity::class.java).apply {
//                putExtra("title", "Select Mode")
//                putStringArrayListExtra("data", viewModel.getAllRoundModes())
//            })
//        }
    }

    private val selectTeeLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.getIntExtra("selectedIndex", 0)?.let { selectedIndex ->
                    viewModel.onTeeSelected(selectedIndex)
                }
            }
        }

    private val selectTypeLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.getIntExtra("selectedIndex", 0)?.let { selectedIndex ->
                    viewModel.onTypeSelected(selectedIndex)
                }
            }
        }

    private val selectModeLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.getIntExtra("selectedIndex", 0)?.let { selectedIndex ->
                    viewModel.onModeSelected(selectedIndex)
                }
            }
        }

    override fun goToShowAdvance() {
        Intent(this, ShowAdvanceActivity::class.java).apply {
            startActivity(this)
        }
    }

    override fun goToPlayRound() {
        if (NetworkUtils.isNetworkConnected(this)) {
            Intent(this, PlayRoundActivity::class.java).apply {
                startActivity(this)
                finish()
            }
        } else {
            this.showError("")
        }
    }

    override fun goToClubError() {
        Intent(this, ClubErrorActivity::class.java).apply {
            startActivity(this)
        }
    }
}