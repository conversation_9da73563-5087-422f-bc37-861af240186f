package com.mytaylormadeplus.wearos.page.summary

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mytaylormadeplus.wearos.base.BaseViewModel
import com.mytaylormadeplus.wearos.data.model.RoundHole
import com.mytaylormadeplus.wearos.data.model.RoundStroke
import com.mytaylormadeplus.wearos.data.preferences.AppPreferences
import com.mytaylormadeplus.wearos.manager.PlayRoundManager
import com.mytaylormadeplus.wearos.utils.DistanceConverter
import com.mytaylormadeplus.wearos.utils.ValueFormatter
import javax.inject.Inject

class SummaryViewModel @Inject constructor(
    val playRoundManager: PlayRoundManager,
    val appPreferences: AppPreferences,
) : BaseViewModel<SummaryNavigator>() {
    private var total: Int = 0
    private var totalPar: Int = 0

    private val _totalScore = MutableLiveData<Int>()
    val totalScore: LiveData<Int>
        get() = _totalScore

    private val _totalParString = MutableLiveData<String>()
    val totalParString: LiveData<String>
        get() = _totalParString

    private val _totalPutterString = MutableLiveData<String>()
    val totalPutterString: LiveData<String>
        get() = _totalPutterString

    private val _finishString = MutableLiveData<String>()
    val finishString: LiveData<String>
        get() = _finishString

    private val _greenInRegulationString = MutableLiveData<String>()
    val greenInRegulationString: LiveData<String>
        get() = _greenInRegulationString

    private val _fairwayHitString = MutableLiveData<String>()
    val fairwayHitString: LiveData<String>
        get() = _fairwayHitString

    private val _longestDistanceString = MutableLiveData<String>()
    val longestDistanceString: LiveData<String>
        get() = _longestDistanceString

    override fun setup() {
        //add total score
        total = playRoundManager.round.totalScore
        _totalScore.postValue(total)
        //add total par
        val dataHole: ArrayList<RoundHole> =
            playRoundManager.round.holes.filter { it.holeScore > 0 } as ArrayList<RoundHole>
        _finishString.postValue(navigator.checkCancelRound(dataHole.size == 0))
        if (dataHole.size == 0) {
            _totalParString.postValue("--")
        } else {
            totalPar = dataHole.map { it.par }.reduce { totalPar, par -> totalPar + par }
            when {
                total < totalPar -> {
                    _totalParString.postValue("-${totalPar - total}")
                }
                total > totalPar -> {
                    _totalParString.postValue("+${total - totalPar}")
                }
                total == totalPar -> {
                    _totalParString.postValue("E")
                }
            }
        }
        _totalPutterString.postValue(playRoundManager.getTotalPutters().toString())
        getGreenInRegulationForRound()
        getFairwayHitForRound()
        getLongestDriverForRound()
    }

    private fun getGreenInRegulationForRound() {
        var numberOfGreenInRegulation: Int = 0
        var holeScore: Int = 0
        for (hole: RoundHole in playRoundManager.round.holes) {
            if (hole.finishHole) {
                if (hole.strokes.size > 0) {
                    if (hole.strokes.size <= hole.par - 2) {
                        numberOfGreenInRegulation++
                        holeScore++
                        continue
                    }
                    for (i in hole.strokes.indices) {
                        val roundStroke: RoundStroke = hole.strokes.get(i)
                        if (roundStroke.startingLie?.lowercase().equals("green")) {
                            val strokeNumber: Int = hole.strokes.indexOf(roundStroke)
                            if (strokeNumber <= hole.par - 2) {
                                numberOfGreenInRegulation++
                                break
                            }
                        }
                    }
                    holeScore++
                }
            }
        }
        var greensInRegulation =
            if (numberOfGreenInRegulation == 0) 0.0f else ((numberOfGreenInRegulation.toFloat() / holeScore.toFloat()) * 100)

        _greenInRegulationString.postValue(
            "${
                ValueFormatter.format(
                    greensInRegulation,
                    ValueFormatter.Formats.ONE_DECIMAL
                )
            }% (${numberOfGreenInRegulation}/${holeScore})"
        )
    }

    private fun getFairwayHitForRound() {
        var holesWithParGreaterThanThree = 0
        var fairwaysHit = 0
        for (hole: RoundHole in playRoundManager.round.holes) {
            if (hole.finishHole) {
                if (hole.strokes.size > 0) {
                    if (hole.par > 3) {
                        if (hole.strokes.size > 1) {
                            val secondStroke: RoundStroke = hole.strokes.get(1)
                            if (secondStroke.startingLie?.lowercase()
                                    .equals("fairway") || secondStroke.startingLie?.lowercase()
                                    .equals("green")
                            ) {
                                fairwaysHit++
                            }
                        } else {
                            // If the first stroke have startlie = tee and endlie = fairway or green
                            // => This stroke is fairway
                            fairwaysHit++
                        }
                        holesWithParGreaterThanThree++
                    }
                }
            }
        }
        var fairwaysHitPercentage =
            if (fairwaysHit == 0) 0.0f else ((fairwaysHit.toFloat() / holesWithParGreaterThanThree.toFloat()) * 100)
        _fairwayHitString.postValue(
            "${
                ValueFormatter.format(
                    fairwaysHitPercentage,
                    ValueFormatter.Formats.ONE_DECIMAL
                )
            }% (${fairwaysHit}/${holesWithParGreaterThanThree})"
        )

    }

    private fun getLongestDriverForRound() {
        var longestDistance = 0f
        var holeNumber = 0
        for (hole: RoundHole in playRoundManager.round.holes) {
            if (hole.finishHole) {
                if (hole.par > 3) {
                    for (roundStroke: RoundStroke in hole.strokes) {
                        if (!roundStroke.penalty) {
                            if (hole.strokes.size == 1) {
                                if (roundStroke.shotDistance > longestDistance) {
                                    longestDistance = roundStroke.shotDistance.toFloat()
                                    holeNumber = hole.holeNumber
                                    break
                                }
                            }
                            var strokeIndex = hole.strokes.indexOf(roundStroke)
                            // Add new condition check first stroke (stroke.shotNumber == 1)
                            if ((strokeIndex + 1) < hole.strokes.size && roundStroke.shotNumber == 1) {
                                val nextStroke: RoundStroke = hole.strokes.get(strokeIndex + 1)
                                if (!nextStroke.penalty) {
                                    if (roundStroke.shotDistance > longestDistance) {
                                        longestDistance = roundStroke.shotDistance.toFloat()
                                        holeNumber = hole.holeNumber
                                        break
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }
        if (playRoundManager.unitIsYard()) {
            longestDistance = DistanceConverter.fromMetersToYards(longestDistance)
        }
        if (longestDistance > 999) {
            longestDistance = 999f
        }
        if (longestDistance == 0f) {
            _longestDistanceString.postValue("0")
        } else {
            _longestDistanceString.postValue(
                "${longestDistance.toLong()} / ${holeNumber}${
                    ValueFormatter.getNumberSuffix(
                        holeNumber
                    )
                }"
            )
        }
    }

    fun endRound() {
        playRoundManager.submitRound(
            completeRound = true,
            navigator = navigator,
            playRoundListener = object : PlayRoundManager.PlayRoundListener() {
                override fun onRoundSubmitted() {
                    navigator.endRound()
                }

                override fun onCanceled() {
                    navigator.cancelRound()
                }
            })
    }

    fun isAdvancedRound(): Boolean {
        return playRoundManager.isAdvancedRound()
    }

    fun getFlatForm(): String? {
        return appPreferences.getFlatForm()
    }
}