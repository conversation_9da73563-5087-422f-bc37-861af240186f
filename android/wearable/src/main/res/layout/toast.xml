<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/toast_layout_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="10dp">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="10dp"
        android:gravity="center"
        android:background="@drawable/bg_toast">

    <TextView android:id="@+id/textToast"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/sfpro"
        android:textSize="12sp"
        android:textColor="#FFF" />
    </LinearLayout>

</LinearLayout>