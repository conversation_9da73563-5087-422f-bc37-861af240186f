# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

ENV["FIREBASE_APP_DISTRIBUTION_ANDROID_APP_ID"] = "1:426726462528:android:8b282ca538d0d796163ba5";
KEYSTORE_FILE_NAME = "my-release-key-staging.keystore"
KEYSTORE_FILE_PASSWORD = "123456"
ALIAS_FILE_NAME = "my-key-alias"
ALIAS_FILE_PASSWORD = "123456"

ENV["FIREBASE_TOKEN"] = "1//0eAjd-rLmely_CgYIARAAGA4SNwF-L9IrW7vRB8FfT2VqAGV8fELk2pvtVJ27XOuwZ1V4nZUXee-6-TSgVyEw0oLYU9b29sODFBI";


def increateAppVersion()
  path = '../app/build.gradle'
    re = /versionCode\s+(\d+)/ 

    s = File.read(path)
    versionCode = s[re, 1].to_i
    s[re, 1] = (versionCode + 1).to_s

    f = File.new(path, 'w')
    f.write(s)
    f.close
    return versionCode
end

def getVersionNameForGooglePlay
  path = '../app/build.gradle'
    strVerCode = /versionCode\s+(\d+)/ 
    strVerName = /versionName\s+["|'](.+)["|']/ 

    s = File.read(path)
    versionCode = s[strVerCode, 1].to_s
    versionName = s[strVerName, 1].to_s
    return versionCode + ' (' + versionName + ')'
end

def writeChangeLogToFile(changelog)
  path = './metadata/android/en-US/changelogs/default.txt'
    s = changelog
    s = s[0, 500]
    f = File.new(path, 'w')
    f.write(s)
    f.close
end

platform :android do

  desc "Push a new beta build to TestFairy"
  lane :qa do
    # Get the last version code and increment it.
    path = '../app/build.gradle'
    re = /versionCode\s+(\d+)/ 

    s = File.read(path)
    versionCode = s[re, 1].to_i
    s[re, 1] = (versionCode + 1).to_s

    f = File.new(path, 'w')
    f.write(s)
    f.close

    gradle(task: 'clean')
    gradle(
      task: "assemble", 
      build_type: 'Release',
      flavor: 'production',
    )
    
    testfairy(
      api_key: "1edcf0a35febed6a34671acf154e12f5fd3b77a9",
      apk: "./app/build/outputs/apk/release/app-release.apk",
      comment: "Build ",
    )
  end

  desc "Push a new beta build to Firebase distribution track"
  lane :atest do
    puts("***********************************************")

    # Get the last version code and increment it.
    versionCode = increateAppVersion()

    releaseKeyFilePath = File.join(File.expand_path('../app', Dir.pwd), KEYSTORE_FILE_NAME)
    gradle(task: 'clean')
    gradle(
    # task: 'assemble', //Generates APK instead of .abb
      task: 'assemble',
      build_type: 'StagingRelease',
      flavor: 'dev',
      print_command: false,
      properties: {
        "android.injected.signing.store.file" => releaseKeyFilePath,
        "android.injected.signing.store.password" => KEYSTORE_FILE_PASSWORD,
        "android.injected.signing.key.alias" => ALIAS_FILE_NAME,
        "android.injected.signing.key.password" => ALIAS_FILE_PASSWORD,
        'versionCode' => versionCode,
      }
    )
    # credentialsFilePath = File.join(Dir.pwd, 'mytmplus-staging-174f3d4cb4c7.json')

    changelog = changelog_from_git_commits(
      commits_count: 20,
      pretty: "- %s",
      date_format: "short",
      match_lightweight_tag: true,
      merge_commit_filtering: "exclude_merges"
    )
    filtered_changelog = changelog.split("\n").reject { |line| line.include?("- update version")|| line.include?("missing commit")|| line.include?("for testing") }.join("\n")
    firebase_app_distribution(
      app: ENV["FIREBASE_APP_DISTRIBUTION_ANDROID_APP_ID"],
      groups: "hnl",
      release_notes: filtered_changelog,
      firebase_cli_token: ENV["FIREBASE_TOKEN"],
      # service_credentials_file: credentialsFilePath,
      apk_path: lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH],
      # android_artifact_type: "AAB"
      )

    #-----------------------
    # BEGIN UPLOAD TO GOOGLE PLAY
    #-----------------------
    gradle(task: 'clean')
    gradle(
      task: 'bundle',
      build_type: 'StagingRelease',
      flavor: 'dev',
      print_command: false,
      properties: {
        "android.injected.signing.store.file" => releaseKeyFilePath,
        "android.injected.signing.store.password" => KEYSTORE_FILE_PASSWORD,
        "android.injected.signing.key.alias" => ALIAS_FILE_NAME,
        "android.injected.signing.key.password" => ALIAS_FILE_PASSWORD,
        'versionCode' => versionCode,
      }
    )

    writeChangeLogToFile(filtered_changelog)

    upload_to_play_store(
      track: 'internal',
      version_code: versionCode,
      version_name: getVersionNameForGooglePlay(),
      # track_promote_to: "production",
      aab: lane_context[SharedValues::GRADLE_AAB_OUTPUT_PATH],
      skip_upload_images: true,
      skip_upload_screenshots: true,
      skip_upload_metadata: true,
      skip_upload_apk: true
    )
  end

  desc "Push a new beta build to Google Play Internal Track"
  lane :beta_testing do
    puts("***********************************************")
    # Get the last version code and increment it.
    versionCode = increateAppVersion()

    releaseKeyFilePath = File.join(File.expand_path('../app', Dir.pwd), KEYSTORE_FILE_NAME)
    gradle(task: 'clean')
    gradle(
    # task: 'assemble', //Generates APK instead of .abb
      task: 'bundle',
      flavor: 'production',
      build_type: 'Release',
      print_command: false,
      properties: {
        "android.injected.signing.store.file" => releaseKeyFilePath,
        "android.injected.signing.store.password" => KEYSTORE_FILE_PASSWORD,
        "android.injected.signing.key.alias" => ALIAS_FILE_NAME,
        "android.injected.signing.key.password" => ALIAS_FILE_PASSWORD,
        'versionCode' => versionCode,
      }
    )

    changelog = changelog_from_git_commits(
      commits_count: 8,
      pretty: "- %s",
      date_format: "short",
      match_lightweight_tag: true,
      merge_commit_filtering: "exclude_merges"
    )
    filtered_changelog = changelog.split("\n").reject { |line| line.include?("- update version")|| line.include?("missing commit") }.join("\n")
    
    writeChangeLogToFile(filtered_changelog)

    upload_to_play_store(
      track: 'internal',
      version_code: versionCode,
      version_name: getVersionNameForGooglePlay(),
      # track_promote_to: "production",
      aab: lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH],
      skip_upload_images: true,
      skip_upload_screenshots: true,
      skip_upload_metadata: true,
    )
    # https://docs.fastlane.tools/actions/upload_to_play_store/
  end

end
