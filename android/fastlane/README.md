fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## Android

### android qa

```sh
[bundle exec] fastlane android qa
```

Push a new beta build to TestFairy

### android atest

```sh
[bundle exec] fastlane android atest
```

Push a new beta build to Firebase distribution track

### android beta_testing

```sh
[bundle exec] fastlane android beta_testing
```

Push a new beta build to Google Play Internal Track

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
