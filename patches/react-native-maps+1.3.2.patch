diff --git a/node_modules/react-native-maps/.DS_Store b/node_modules/react-native-maps/.DS_Store
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-maps/android/.DS_Store b/node_modules/react-native-maps/android/.DS_Store
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-maps/android/build/.transforms/e95a0278c327029861383091c4e011c7/results.bin b/node_modules/react-native-maps/android/build/.transforms/e95a0278c327029861383091c4e011c7/results.bin
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-maps/android/build/generated/source/buildConfig/debug/com/airbnb/android/react/maps/BuildConfig.java b/node_modules/react-native-maps/android/build/generated/source/buildConfig/debug/com/airbnb/android/react/maps/BuildConfig.java
new file mode 100644
index 0000000..53e11cf
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/generated/source/buildConfig/debug/com/airbnb/android/react/maps/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.airbnb.android.react.maps;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.airbnb.android.react.maps";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/react-native-maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..f32c825
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.airbnb.android.react.maps" >
+
+    <uses-sdk
+        android:minSdkVersion="23"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..b048ca1
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.airbnb.android.react.maps",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-maps/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/react-native-maps/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/react-native-maps/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/react-native-maps/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..b3ec1ea
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/react-native-maps/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..48a2063
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/react-native-maps/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..9752a2d
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/compile_symbol_list/debug/R.txt
@@ -0,0 +1,2136 @@
+int anim abc_fade_in 0x0
+int anim abc_fade_out 0x0
+int anim abc_grow_fade_in_from_bottom 0x0
+int anim abc_popup_enter 0x0
+int anim abc_popup_exit 0x0
+int anim abc_shrink_fade_out_from_bottom 0x0
+int anim abc_slide_in_bottom 0x0
+int anim abc_slide_in_top 0x0
+int anim abc_slide_out_bottom 0x0
+int anim abc_slide_out_top 0x0
+int anim abc_tooltip_enter 0x0
+int anim abc_tooltip_exit 0x0
+int anim btn_checkbox_to_checked_box_inner_merged_animation 0x0
+int anim btn_checkbox_to_checked_box_outer_merged_animation 0x0
+int anim btn_checkbox_to_checked_icon_null_animation 0x0
+int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x0
+int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x0
+int anim btn_checkbox_to_unchecked_icon_null_animation 0x0
+int anim btn_radio_to_off_mtrl_dot_group_animation 0x0
+int anim btn_radio_to_off_mtrl_ring_outer_animation 0x0
+int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x0
+int anim btn_radio_to_on_mtrl_dot_group_animation 0x0
+int anim btn_radio_to_on_mtrl_ring_outer_animation 0x0
+int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x0
+int anim catalyst_fade_in 0x0
+int anim catalyst_fade_out 0x0
+int anim catalyst_push_up_in 0x0
+int anim catalyst_push_up_out 0x0
+int anim catalyst_slide_down 0x0
+int anim catalyst_slide_up 0x0
+int anim fragment_fast_out_extra_slow_in 0x0
+int animator fragment_close_enter 0x0
+int animator fragment_close_exit 0x0
+int animator fragment_fade_enter 0x0
+int animator fragment_fade_exit 0x0
+int animator fragment_open_enter 0x0
+int animator fragment_open_exit 0x0
+int attr actionBarDivider 0x0
+int attr actionBarItemBackground 0x0
+int attr actionBarPopupTheme 0x0
+int attr actionBarSize 0x0
+int attr actionBarSplitStyle 0x0
+int attr actionBarStyle 0x0
+int attr actionBarTabBarStyle 0x0
+int attr actionBarTabStyle 0x0
+int attr actionBarTabTextStyle 0x0
+int attr actionBarTheme 0x0
+int attr actionBarWidgetTheme 0x0
+int attr actionButtonStyle 0x0
+int attr actionDropDownStyle 0x0
+int attr actionLayout 0x0
+int attr actionMenuTextAppearance 0x0
+int attr actionMenuTextColor 0x0
+int attr actionModeBackground 0x0
+int attr actionModeCloseButtonStyle 0x0
+int attr actionModeCloseContentDescription 0x0
+int attr actionModeCloseDrawable 0x0
+int attr actionModeCopyDrawable 0x0
+int attr actionModeCutDrawable 0x0
+int attr actionModeFindDrawable 0x0
+int attr actionModePasteDrawable 0x0
+int attr actionModePopupWindowStyle 0x0
+int attr actionModeSelectAllDrawable 0x0
+int attr actionModeShareDrawable 0x0
+int attr actionModeSplitBackground 0x0
+int attr actionModeStyle 0x0
+int attr actionModeTheme 0x0
+int attr actionModeWebSearchDrawable 0x0
+int attr actionOverflowButtonStyle 0x0
+int attr actionOverflowMenuStyle 0x0
+int attr actionProviderClass 0x0
+int attr actionViewClass 0x0
+int attr activityChooserViewStyle 0x0
+int attr actualImageResource 0x0
+int attr actualImageScaleType 0x0
+int attr actualImageUri 0x0
+int attr alertDialogButtonGroupStyle 0x0
+int attr alertDialogCenterButtons 0x0
+int attr alertDialogStyle 0x0
+int attr alertDialogTheme 0x0
+int attr allowStacking 0x0
+int attr alpha 0x0
+int attr alphabeticModifiers 0x0
+int attr ambientEnabled 0x0
+int attr arrowHeadLength 0x0
+int attr arrowShaftLength 0x0
+int attr autoCompleteTextViewStyle 0x0
+int attr autoSizeMaxTextSize 0x0
+int attr autoSizeMinTextSize 0x0
+int attr autoSizePresetSizes 0x0
+int attr autoSizeStepGranularity 0x0
+int attr autoSizeTextType 0x0
+int attr autofillInlineSuggestionChip 0x0
+int attr autofillInlineSuggestionEndIconStyle 0x0
+int attr autofillInlineSuggestionStartIconStyle 0x0
+int attr autofillInlineSuggestionSubtitle 0x0
+int attr autofillInlineSuggestionTitle 0x0
+int attr background 0x0
+int attr backgroundColor 0x0
+int attr backgroundImage 0x0
+int attr backgroundSplit 0x0
+int attr backgroundStacked 0x0
+int attr backgroundTint 0x0
+int attr backgroundTintMode 0x0
+int attr barLength 0x0
+int attr borderlessButtonStyle 0x0
+int attr buttonBarButtonStyle 0x0
+int attr buttonBarNegativeButtonStyle 0x0
+int attr buttonBarNeutralButtonStyle 0x0
+int attr buttonBarPositiveButtonStyle 0x0
+int attr buttonBarStyle 0x0
+int attr buttonCompat 0x0
+int attr buttonGravity 0x0
+int attr buttonIconDimen 0x0
+int attr buttonPanelSideLayout 0x0
+int attr buttonSize 0x0
+int attr buttonStyle 0x0
+int attr buttonStyleSmall 0x0
+int attr buttonTint 0x0
+int attr buttonTintMode 0x0
+int attr cameraBearing 0x0
+int attr cameraMaxZoomPreference 0x0
+int attr cameraMinZoomPreference 0x0
+int attr cameraTargetLat 0x0
+int attr cameraTargetLng 0x0
+int attr cameraTilt 0x0
+int attr cameraZoom 0x0
+int attr checkMarkCompat 0x0
+int attr checkMarkTint 0x0
+int attr checkMarkTintMode 0x0
+int attr checkboxStyle 0x0
+int attr checkedTextViewStyle 0x0
+int attr circleCrop 0x0
+int attr closeIcon 0x0
+int attr closeItemLayout 0x0
+int attr collapseContentDescription 0x0
+int attr collapseIcon 0x0
+int attr color 0x0
+int attr colorAccent 0x0
+int attr colorBackgroundFloating 0x0
+int attr colorButtonNormal 0x0
+int attr colorControlActivated 0x0
+int attr colorControlHighlight 0x0
+int attr colorControlNormal 0x0
+int attr colorError 0x0
+int attr colorPrimary 0x0
+int attr colorPrimaryDark 0x0
+int attr colorScheme 0x0
+int attr colorSwitchThumbNormal 0x0
+int attr commitIcon 0x0
+int attr contentDescription 0x0
+int attr contentInsetEnd 0x0
+int attr contentInsetEndWithActions 0x0
+int attr contentInsetLeft 0x0
+int attr contentInsetRight 0x0
+int attr contentInsetStart 0x0
+int attr contentInsetStartWithNavigation 0x0
+int attr controlBackground 0x0
+int attr customNavigationLayout 0x0
+int attr defaultQueryHint 0x0
+int attr dialogCornerRadius 0x0
+int attr dialogPreferredPadding 0x0
+int attr dialogTheme 0x0
+int attr displayOptions 0x0
+int attr divider 0x0
+int attr dividerHorizontal 0x0
+int attr dividerPadding 0x0
+int attr dividerVertical 0x0
+int attr drawableBottomCompat 0x0
+int attr drawableEndCompat 0x0
+int attr drawableLeftCompat 0x0
+int attr drawableRightCompat 0x0
+int attr drawableSize 0x0
+int attr drawableStartCompat 0x0
+int attr drawableTint 0x0
+int attr drawableTintMode 0x0
+int attr drawableTopCompat 0x0
+int attr drawerArrowStyle 0x0
+int attr dropDownListViewStyle 0x0
+int attr dropdownListPreferredItemHeight 0x0
+int attr editTextBackground 0x0
+int attr editTextColor 0x0
+int attr editTextStyle 0x0
+int attr elevation 0x0
+int attr emojiCompatEnabled 0x0
+int attr expandActivityOverflowButtonDrawable 0x0
+int attr fadeDuration 0x0
+int attr failureImage 0x0
+int attr failureImageScaleType 0x0
+int attr firstBaselineToTopHeight 0x0
+int attr font 0x0
+int attr fontFamily 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontProviderSystemFontFamily 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr gapBetweenBars 0x0
+int attr goIcon 0x0
+int attr height 0x0
+int attr hideOnContentScroll 0x0
+int attr homeAsUpIndicator 0x0
+int attr homeLayout 0x0
+int attr icon 0x0
+int attr iconTint 0x0
+int attr iconTintMode 0x0
+int attr iconifiedByDefault 0x0
+int attr imageAspectRatio 0x0
+int attr imageAspectRatioAdjust 0x0
+int attr imageButtonStyle 0x0
+int attr indeterminateProgressStyle 0x0
+int attr initialActivityCount 0x0
+int attr isAutofillInlineSuggestionTheme 0x0
+int attr isLightTheme 0x0
+int attr itemPadding 0x0
+int attr lStar 0x0
+int attr lastBaselineToBottomHeight 0x0
+int attr latLngBoundsNorthEastLatitude 0x0
+int attr latLngBoundsNorthEastLongitude 0x0
+int attr latLngBoundsSouthWestLatitude 0x0
+int attr latLngBoundsSouthWestLongitude 0x0
+int attr layout 0x0
+int attr lineHeight 0x0
+int attr listChoiceBackgroundIndicator 0x0
+int attr listChoiceIndicatorMultipleAnimated 0x0
+int attr listChoiceIndicatorSingleAnimated 0x0
+int attr listDividerAlertDialog 0x0
+int attr listItemLayout 0x0
+int attr listLayout 0x0
+int attr listMenuViewStyle 0x0
+int attr listPopupWindowStyle 0x0
+int attr listPreferredItemHeight 0x0
+int attr listPreferredItemHeightLarge 0x0
+int attr listPreferredItemHeightSmall 0x0
+int attr listPreferredItemPaddingEnd 0x0
+int attr listPreferredItemPaddingLeft 0x0
+int attr listPreferredItemPaddingRight 0x0
+int attr listPreferredItemPaddingStart 0x0
+int attr liteMode 0x0
+int attr logo 0x0
+int attr logoDescription 0x0
+int attr mapId 0x0
+int attr mapType 0x0
+int attr maxButtonHeight 0x0
+int attr measureWithLargestChild 0x0
+int attr menu 0x0
+int attr multiChoiceItemLayout 0x0
+int attr navigationContentDescription 0x0
+int attr navigationIcon 0x0
+int attr navigationMode 0x0
+int attr nestedScrollViewStyle 0x0
+int attr numericModifiers 0x0
+int attr overlapAnchor 0x0
+int attr overlayImage 0x0
+int attr paddingBottomNoButtons 0x0
+int attr paddingEnd 0x0
+int attr paddingStart 0x0
+int attr paddingTopNoTitle 0x0
+int attr panelBackground 0x0
+int attr panelMenuListTheme 0x0
+int attr panelMenuListWidth 0x0
+int attr placeholderImage 0x0
+int attr placeholderImageScaleType 0x0
+int attr popupMenuStyle 0x0
+int attr popupTheme 0x0
+int attr popupWindowStyle 0x0
+int attr preserveIconSpacing 0x0
+int attr pressedStateOverlayImage 0x0
+int attr progressBarAutoRotateInterval 0x0
+int attr progressBarImage 0x0
+int attr progressBarImageScaleType 0x0
+int attr progressBarPadding 0x0
+int attr progressBarStyle 0x0
+int attr queryBackground 0x0
+int attr queryHint 0x0
+int attr queryPatterns 0x0
+int attr radioButtonStyle 0x0
+int attr ratingBarStyle 0x0
+int attr ratingBarStyleIndicator 0x0
+int attr ratingBarStyleSmall 0x0
+int attr retryImage 0x0
+int attr retryImageScaleType 0x0
+int attr roundAsCircle 0x0
+int attr roundBottomEnd 0x0
+int attr roundBottomLeft 0x0
+int attr roundBottomRight 0x0
+int attr roundBottomStart 0x0
+int attr roundTopEnd 0x0
+int attr roundTopLeft 0x0
+int attr roundTopRight 0x0
+int attr roundTopStart 0x0
+int attr roundWithOverlayColor 0x0
+int attr roundedCornerRadius 0x0
+int attr roundingBorderColor 0x0
+int attr roundingBorderPadding 0x0
+int attr roundingBorderWidth 0x0
+int attr scopeUris 0x0
+int attr searchHintIcon 0x0
+int attr searchIcon 0x0
+int attr searchViewStyle 0x0
+int attr seekBarStyle 0x0
+int attr selectableItemBackground 0x0
+int attr selectableItemBackgroundBorderless 0x0
+int attr shortcutMatchRequired 0x0
+int attr showAsAction 0x0
+int attr showDividers 0x0
+int attr showText 0x0
+int attr showTitle 0x0
+int attr singleChoiceItemLayout 0x0
+int attr spinBars 0x0
+int attr spinnerDropDownItemStyle 0x0
+int attr spinnerStyle 0x0
+int attr splitTrack 0x0
+int attr srcCompat 0x0
+int attr state_above_anchor 0x0
+int attr subMenuArrow 0x0
+int attr submitBackground 0x0
+int attr subtitle 0x0
+int attr subtitleTextAppearance 0x0
+int attr subtitleTextColor 0x0
+int attr subtitleTextStyle 0x0
+int attr suggestionRowLayout 0x0
+int attr switchMinWidth 0x0
+int attr switchPadding 0x0
+int attr switchStyle 0x0
+int attr switchTextAppearance 0x0
+int attr textAllCaps 0x0
+int attr textAppearanceLargePopupMenu 0x0
+int attr textAppearanceListItem 0x0
+int attr textAppearanceListItemSecondary 0x0
+int attr textAppearanceListItemSmall 0x0
+int attr textAppearancePopupMenuHeader 0x0
+int attr textAppearanceSearchResultSubtitle 0x0
+int attr textAppearanceSearchResultTitle 0x0
+int attr textAppearanceSmallPopupMenu 0x0
+int attr textColorAlertDialogListItem 0x0
+int attr textColorSearchUrl 0x0
+int attr textLocale 0x0
+int attr theme 0x0
+int attr thickness 0x0
+int attr thumbTextPadding 0x0
+int attr thumbTint 0x0
+int attr thumbTintMode 0x0
+int attr tickMark 0x0
+int attr tickMarkTint 0x0
+int attr tickMarkTintMode 0x0
+int attr tint 0x0
+int attr tintMode 0x0
+int attr title 0x0
+int attr titleMargin 0x0
+int attr titleMarginBottom 0x0
+int attr titleMarginEnd 0x0
+int attr titleMarginStart 0x0
+int attr titleMarginTop 0x0
+int attr titleMargins 0x0
+int attr titleTextAppearance 0x0
+int attr titleTextColor 0x0
+int attr titleTextStyle 0x0
+int attr toolbarNavigationButtonStyle 0x0
+int attr toolbarStyle 0x0
+int attr tooltipForegroundColor 0x0
+int attr tooltipFrameBackground 0x0
+int attr tooltipText 0x0
+int attr track 0x0
+int attr trackTint 0x0
+int attr trackTintMode 0x0
+int attr ttcIndex 0x0
+int attr uiCompass 0x0
+int attr uiMapToolbar 0x0
+int attr uiRotateGestures 0x0
+int attr uiScrollGestures 0x0
+int attr uiScrollGesturesDuringRotateOrZoom 0x0
+int attr uiTiltGestures 0x0
+int attr uiZoomControls 0x0
+int attr uiZoomGestures 0x0
+int attr useViewLifecycle 0x0
+int attr viewAspectRatio 0x0
+int attr viewInflaterClass 0x0
+int attr voiceIcon 0x0
+int attr windowActionBar 0x0
+int attr windowActionBarOverlay 0x0
+int attr windowActionModeOverlay 0x0
+int attr windowFixedHeightMajor 0x0
+int attr windowFixedHeightMinor 0x0
+int attr windowFixedWidthMajor 0x0
+int attr windowFixedWidthMinor 0x0
+int attr windowMinWidthMajor 0x0
+int attr windowMinWidthMinor 0x0
+int attr windowNoTitle 0x0
+int attr zOrderOnTop 0x0
+int bool abc_action_bar_embed_tabs 0x0
+int bool abc_config_actionMenuItemAllCaps 0x0
+int bool enable_system_alarm_service_default 0x0
+int bool enable_system_foreground_service_default 0x0
+int bool enable_system_job_service_default 0x0
+int bool workmanager_test_configuration 0x0
+int color abc_background_cache_hint_selector_material_dark 0x0
+int color abc_background_cache_hint_selector_material_light 0x0
+int color abc_btn_colored_borderless_text_material 0x0
+int color abc_btn_colored_text_material 0x0
+int color abc_color_highlight_material 0x0
+int color abc_decor_view_status_guard 0x0
+int color abc_decor_view_status_guard_light 0x0
+int color abc_hint_foreground_material_dark 0x0
+int color abc_hint_foreground_material_light 0x0
+int color abc_primary_text_disable_only_material_dark 0x0
+int color abc_primary_text_disable_only_material_light 0x0
+int color abc_primary_text_material_dark 0x0
+int color abc_primary_text_material_light 0x0
+int color abc_search_url_text 0x0
+int color abc_search_url_text_normal 0x0
+int color abc_search_url_text_pressed 0x0
+int color abc_search_url_text_selected 0x0
+int color abc_secondary_text_material_dark 0x0
+int color abc_secondary_text_material_light 0x0
+int color abc_tint_btn_checkable 0x0
+int color abc_tint_default 0x0
+int color abc_tint_edittext 0x0
+int color abc_tint_seek_thumb 0x0
+int color abc_tint_spinner 0x0
+int color abc_tint_switch_track 0x0
+int color accent_material_dark 0x0
+int color accent_material_light 0x0
+int color androidx_core_ripple_material_light 0x0
+int color androidx_core_secondary_text_default_material_light 0x0
+int color background_floating_material_dark 0x0
+int color background_floating_material_light 0x0
+int color background_material_dark 0x0
+int color background_material_light 0x0
+int color bright_foreground_disabled_material_dark 0x0
+int color bright_foreground_disabled_material_light 0x0
+int color bright_foreground_inverse_material_dark 0x0
+int color bright_foreground_inverse_material_light 0x0
+int color bright_foreground_material_dark 0x0
+int color bright_foreground_material_light 0x0
+int color button_material_dark 0x0
+int color button_material_light 0x0
+int color catalyst_logbox_background 0x0
+int color catalyst_redbox_background 0x0
+int color common_google_signin_btn_text_dark 0x0
+int color common_google_signin_btn_text_dark_default 0x0
+int color common_google_signin_btn_text_dark_disabled 0x0
+int color common_google_signin_btn_text_dark_focused 0x0
+int color common_google_signin_btn_text_dark_pressed 0x0
+int color common_google_signin_btn_text_light 0x0
+int color common_google_signin_btn_text_light_default 0x0
+int color common_google_signin_btn_text_light_disabled 0x0
+int color common_google_signin_btn_text_light_focused 0x0
+int color common_google_signin_btn_text_light_pressed 0x0
+int color common_google_signin_btn_tint 0x0
+int color dim_foreground_disabled_material_dark 0x0
+int color dim_foreground_disabled_material_light 0x0
+int color dim_foreground_material_dark 0x0
+int color dim_foreground_material_light 0x0
+int color error_color_material_dark 0x0
+int color error_color_material_light 0x0
+int color foreground_material_dark 0x0
+int color foreground_material_light 0x0
+int color highlighted_text_material_dark 0x0
+int color highlighted_text_material_light 0x0
+int color material_blue_grey_800 0x0
+int color material_blue_grey_900 0x0
+int color material_blue_grey_950 0x0
+int color material_deep_teal_200 0x0
+int color material_deep_teal_500 0x0
+int color material_grey_100 0x0
+int color material_grey_300 0x0
+int color material_grey_50 0x0
+int color material_grey_600 0x0
+int color material_grey_800 0x0
+int color material_grey_850 0x0
+int color material_grey_900 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color primary_dark_material_dark 0x0
+int color primary_dark_material_light 0x0
+int color primary_material_dark 0x0
+int color primary_material_light 0x0
+int color primary_text_default_material_dark 0x0
+int color primary_text_default_material_light 0x0
+int color primary_text_disabled_material_dark 0x0
+int color primary_text_disabled_material_light 0x0
+int color ripple_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int color secondary_text_disabled_material_dark 0x0
+int color secondary_text_disabled_material_light 0x0
+int color switch_thumb_disabled_material_dark 0x0
+int color switch_thumb_disabled_material_light 0x0
+int color switch_thumb_material_dark 0x0
+int color switch_thumb_material_light 0x0
+int color switch_thumb_normal_material_dark 0x0
+int color switch_thumb_normal_material_light 0x0
+int color tooltip_background_dark 0x0
+int color tooltip_background_light 0x0
+int dimen abc_action_bar_content_inset_material 0x0
+int dimen abc_action_bar_content_inset_with_nav 0x0
+int dimen abc_action_bar_default_height_material 0x0
+int dimen abc_action_bar_default_padding_end_material 0x0
+int dimen abc_action_bar_default_padding_start_material 0x0
+int dimen abc_action_bar_elevation_material 0x0
+int dimen abc_action_bar_icon_vertical_padding_material 0x0
+int dimen abc_action_bar_overflow_padding_end_material 0x0
+int dimen abc_action_bar_overflow_padding_start_material 0x0
+int dimen abc_action_bar_stacked_max_height 0x0
+int dimen abc_action_bar_stacked_tab_max_width 0x0
+int dimen abc_action_bar_subtitle_bottom_margin_material 0x0
+int dimen abc_action_bar_subtitle_top_margin_material 0x0
+int dimen abc_action_button_min_height_material 0x0
+int dimen abc_action_button_min_width_material 0x0
+int dimen abc_action_button_min_width_overflow_material 0x0
+int dimen abc_alert_dialog_button_bar_height 0x0
+int dimen abc_alert_dialog_button_dimen 0x0
+int dimen abc_button_inset_horizontal_material 0x0
+int dimen abc_button_inset_vertical_material 0x0
+int dimen abc_button_padding_horizontal_material 0x0
+int dimen abc_button_padding_vertical_material 0x0
+int dimen abc_cascading_menus_min_smallest_width 0x0
+int dimen abc_config_prefDialogWidth 0x0
+int dimen abc_control_corner_material 0x0
+int dimen abc_control_inset_material 0x0
+int dimen abc_control_padding_material 0x0
+int dimen abc_dialog_corner_radius_material 0x0
+int dimen abc_dialog_fixed_height_major 0x0
+int dimen abc_dialog_fixed_height_minor 0x0
+int dimen abc_dialog_fixed_width_major 0x0
+int dimen abc_dialog_fixed_width_minor 0x0
+int dimen abc_dialog_list_padding_bottom_no_buttons 0x0
+int dimen abc_dialog_list_padding_top_no_title 0x0
+int dimen abc_dialog_min_width_major 0x0
+int dimen abc_dialog_min_width_minor 0x0
+int dimen abc_dialog_padding_material 0x0
+int dimen abc_dialog_padding_top_material 0x0
+int dimen abc_dialog_title_divider_material 0x0
+int dimen abc_disabled_alpha_material_dark 0x0
+int dimen abc_disabled_alpha_material_light 0x0
+int dimen abc_dropdownitem_icon_width 0x0
+int dimen abc_dropdownitem_text_padding_left 0x0
+int dimen abc_dropdownitem_text_padding_right 0x0
+int dimen abc_edit_text_inset_bottom_material 0x0
+int dimen abc_edit_text_inset_horizontal_material 0x0
+int dimen abc_edit_text_inset_top_material 0x0
+int dimen abc_floating_window_z 0x0
+int dimen abc_list_item_height_large_material 0x0
+int dimen abc_list_item_height_material 0x0
+int dimen abc_list_item_height_small_material 0x0
+int dimen abc_list_item_padding_horizontal_material 0x0
+int dimen abc_panel_menu_list_width 0x0
+int dimen abc_progress_bar_height_material 0x0
+int dimen abc_search_view_preferred_height 0x0
+int dimen abc_search_view_preferred_width 0x0
+int dimen abc_seekbar_track_background_height_material 0x0
+int dimen abc_seekbar_track_progress_height_material 0x0
+int dimen abc_select_dialog_padding_start_material 0x0
+int dimen abc_star_big 0x0
+int dimen abc_star_medium 0x0
+int dimen abc_star_small 0x0
+int dimen abc_switch_padding 0x0
+int dimen abc_text_size_body_1_material 0x0
+int dimen abc_text_size_body_2_material 0x0
+int dimen abc_text_size_button_material 0x0
+int dimen abc_text_size_caption_material 0x0
+int dimen abc_text_size_display_1_material 0x0
+int dimen abc_text_size_display_2_material 0x0
+int dimen abc_text_size_display_3_material 0x0
+int dimen abc_text_size_display_4_material 0x0
+int dimen abc_text_size_headline_material 0x0
+int dimen abc_text_size_large_material 0x0
+int dimen abc_text_size_medium_material 0x0
+int dimen abc_text_size_menu_header_material 0x0
+int dimen abc_text_size_menu_material 0x0
+int dimen abc_text_size_small_material 0x0
+int dimen abc_text_size_subhead_material 0x0
+int dimen abc_text_size_subtitle_material_toolbar 0x0
+int dimen abc_text_size_title_material 0x0
+int dimen abc_text_size_title_material_toolbar 0x0
+int dimen autofill_inline_suggestion_icon_size 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen disabled_alpha_material_dark 0x0
+int dimen disabled_alpha_material_light 0x0
+int dimen highlight_alpha_material_colored 0x0
+int dimen highlight_alpha_material_dark 0x0
+int dimen highlight_alpha_material_light 0x0
+int dimen hint_alpha_material_dark 0x0
+int dimen hint_alpha_material_light 0x0
+int dimen hint_pressed_alpha_material_dark 0x0
+int dimen hint_pressed_alpha_material_light 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen tooltip_corner_radius 0x0
+int dimen tooltip_horizontal_padding 0x0
+int dimen tooltip_margin 0x0
+int dimen tooltip_precise_anchor_extra_offset 0x0
+int dimen tooltip_precise_anchor_threshold 0x0
+int dimen tooltip_vertical_padding 0x0
+int dimen tooltip_y_offset_non_touch 0x0
+int dimen tooltip_y_offset_touch 0x0
+int drawable abc_ab_share_pack_mtrl_alpha 0x0
+int drawable abc_action_bar_item_background_material 0x0
+int drawable abc_btn_borderless_material 0x0
+int drawable abc_btn_check_material 0x0
+int drawable abc_btn_check_material_anim 0x0
+int drawable abc_btn_check_to_on_mtrl_000 0x0
+int drawable abc_btn_check_to_on_mtrl_015 0x0
+int drawable abc_btn_colored_material 0x0
+int drawable abc_btn_default_mtrl_shape 0x0
+int drawable abc_btn_radio_material 0x0
+int drawable abc_btn_radio_material_anim 0x0
+int drawable abc_btn_radio_to_on_mtrl_000 0x0
+int drawable abc_btn_radio_to_on_mtrl_015 0x0
+int drawable abc_btn_switch_to_on_mtrl_00001 0x0
+int drawable abc_btn_switch_to_on_mtrl_00012 0x0
+int drawable abc_cab_background_internal_bg 0x0
+int drawable abc_cab_background_top_material 0x0
+int drawable abc_cab_background_top_mtrl_alpha 0x0
+int drawable abc_control_background_material 0x0
+int drawable abc_dialog_material_background 0x0
+int drawable abc_edit_text_material 0x0
+int drawable abc_ic_ab_back_material 0x0
+int drawable abc_ic_arrow_drop_right_black_24dp 0x0
+int drawable abc_ic_clear_material 0x0
+int drawable abc_ic_commit_search_api_mtrl_alpha 0x0
+int drawable abc_ic_go_search_api_material 0x0
+int drawable abc_ic_menu_copy_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_cut_mtrl_alpha 0x0
+int drawable abc_ic_menu_overflow_material 0x0
+int drawable abc_ic_menu_paste_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_selectall_mtrl_alpha 0x0
+int drawable abc_ic_menu_share_mtrl_alpha 0x0
+int drawable abc_ic_search_api_material 0x0
+int drawable abc_ic_voice_search_api_material 0x0
+int drawable abc_item_background_holo_dark 0x0
+int drawable abc_item_background_holo_light 0x0
+int drawable abc_list_divider_material 0x0
+int drawable abc_list_divider_mtrl_alpha 0x0
+int drawable abc_list_focused_holo 0x0
+int drawable abc_list_longpressed_holo 0x0
+int drawable abc_list_pressed_holo_dark 0x0
+int drawable abc_list_pressed_holo_light 0x0
+int drawable abc_list_selector_background_transition_holo_dark 0x0
+int drawable abc_list_selector_background_transition_holo_light 0x0
+int drawable abc_list_selector_disabled_holo_dark 0x0
+int drawable abc_list_selector_disabled_holo_light 0x0
+int drawable abc_list_selector_holo_dark 0x0
+int drawable abc_list_selector_holo_light 0x0
+int drawable abc_menu_hardkey_panel_mtrl_mult 0x0
+int drawable abc_popup_background_mtrl_mult 0x0
+int drawable abc_ratingbar_indicator_material 0x0
+int drawable abc_ratingbar_material 0x0
+int drawable abc_ratingbar_small_material 0x0
+int drawable abc_scrubber_control_off_mtrl_alpha 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_000 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_005 0x0
+int drawable abc_scrubber_primary_mtrl_alpha 0x0
+int drawable abc_scrubber_track_mtrl_alpha 0x0
+int drawable abc_seekbar_thumb_material 0x0
+int drawable abc_seekbar_tick_mark_material 0x0
+int drawable abc_seekbar_track_material 0x0
+int drawable abc_spinner_mtrl_am_alpha 0x0
+int drawable abc_spinner_textfield_background_material 0x0
+int drawable abc_star_black_48dp 0x0
+int drawable abc_star_half_black_48dp 0x0
+int drawable abc_switch_thumb_material 0x0
+int drawable abc_switch_track_mtrl_alpha 0x0
+int drawable abc_tab_indicator_material 0x0
+int drawable abc_tab_indicator_mtrl_alpha 0x0
+int drawable abc_text_cursor_material 0x0
+int drawable abc_text_select_handle_left_mtrl 0x0
+int drawable abc_text_select_handle_middle_mtrl 0x0
+int drawable abc_text_select_handle_right_mtrl 0x0
+int drawable abc_textfield_activated_mtrl_alpha 0x0
+int drawable abc_textfield_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_activated_mtrl_alpha 0x0
+int drawable abc_textfield_search_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_material 0x0
+int drawable abc_vector_test 0x0
+int drawable amu_bubble_mask 0x0
+int drawable amu_bubble_shadow 0x0
+int drawable autofill_inline_suggestion_chip_background 0x0
+int drawable btn_checkbox_checked_mtrl 0x0
+int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x0
+int drawable btn_checkbox_unchecked_mtrl 0x0
+int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x0
+int drawable btn_radio_off_mtrl 0x0
+int drawable btn_radio_off_to_on_mtrl_animation 0x0
+int drawable btn_radio_on_mtrl 0x0
+int drawable btn_radio_on_to_off_mtrl_animation 0x0
+int drawable common_full_open_on_phone 0x0
+int drawable common_google_signin_btn_icon_dark 0x0
+int drawable common_google_signin_btn_icon_dark_disabled 0x0
+int drawable common_google_signin_btn_icon_dark_focused 0x0
+int drawable common_google_signin_btn_icon_dark_normal 0x0
+int drawable common_google_signin_btn_icon_dark_normal_background 0x0
+int drawable common_google_signin_btn_icon_dark_pressed 0x0
+int drawable common_google_signin_btn_icon_disabled 0x0
+int drawable common_google_signin_btn_icon_light 0x0
+int drawable common_google_signin_btn_icon_light_disabled 0x0
+int drawable common_google_signin_btn_icon_light_focused 0x0
+int drawable common_google_signin_btn_icon_light_normal 0x0
+int drawable common_google_signin_btn_icon_light_normal_background 0x0
+int drawable common_google_signin_btn_icon_light_pressed 0x0
+int drawable common_google_signin_btn_text_dark 0x0
+int drawable common_google_signin_btn_text_dark_disabled 0x0
+int drawable common_google_signin_btn_text_dark_focused 0x0
+int drawable common_google_signin_btn_text_dark_normal 0x0
+int drawable common_google_signin_btn_text_dark_normal_background 0x0
+int drawable common_google_signin_btn_text_dark_pressed 0x0
+int drawable common_google_signin_btn_text_disabled 0x0
+int drawable common_google_signin_btn_text_light 0x0
+int drawable common_google_signin_btn_text_light_disabled 0x0
+int drawable common_google_signin_btn_text_light_focused 0x0
+int drawable common_google_signin_btn_text_light_normal 0x0
+int drawable common_google_signin_btn_text_light_normal_background 0x0
+int drawable common_google_signin_btn_text_light_pressed 0x0
+int drawable common_ic_googleplayservices 0x0
+int drawable googleg_disabled_color_18 0x0
+int drawable googleg_standard_color_18 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int drawable redbox_top_border_background 0x0
+int drawable test_level_drawable 0x0
+int drawable tooltip_frame_dark 0x0
+int drawable tooltip_frame_light 0x0
+int id accessibility_action_clickable_span 0x0
+int id accessibility_actions 0x0
+int id accessibility_collection 0x0
+int id accessibility_collection_item 0x0
+int id accessibility_custom_action_0 0x0
+int id accessibility_custom_action_1 0x0
+int id accessibility_custom_action_10 0x0
+int id accessibility_custom_action_11 0x0
+int id accessibility_custom_action_12 0x0
+int id accessibility_custom_action_13 0x0
+int id accessibility_custom_action_14 0x0
+int id accessibility_custom_action_15 0x0
+int id accessibility_custom_action_16 0x0
+int id accessibility_custom_action_17 0x0
+int id accessibility_custom_action_18 0x0
+int id accessibility_custom_action_19 0x0
+int id accessibility_custom_action_2 0x0
+int id accessibility_custom_action_20 0x0
+int id accessibility_custom_action_21 0x0
+int id accessibility_custom_action_22 0x0
+int id accessibility_custom_action_23 0x0
+int id accessibility_custom_action_24 0x0
+int id accessibility_custom_action_25 0x0
+int id accessibility_custom_action_26 0x0
+int id accessibility_custom_action_27 0x0
+int id accessibility_custom_action_28 0x0
+int id accessibility_custom_action_29 0x0
+int id accessibility_custom_action_3 0x0
+int id accessibility_custom_action_30 0x0
+int id accessibility_custom_action_31 0x0
+int id accessibility_custom_action_4 0x0
+int id accessibility_custom_action_5 0x0
+int id accessibility_custom_action_6 0x0
+int id accessibility_custom_action_7 0x0
+int id accessibility_custom_action_8 0x0
+int id accessibility_custom_action_9 0x0
+int id accessibility_hint 0x0
+int id accessibility_label 0x0
+int id accessibility_links 0x0
+int id accessibility_role 0x0
+int id accessibility_state 0x0
+int id accessibility_value 0x0
+int id action_bar 0x0
+int id action_bar_activity_content 0x0
+int id action_bar_container 0x0
+int id action_bar_root 0x0
+int id action_bar_spinner 0x0
+int id action_bar_subtitle 0x0
+int id action_bar_title 0x0
+int id action_container 0x0
+int id action_context_bar 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_menu_divider 0x0
+int id action_menu_presenter 0x0
+int id action_mode_bar 0x0
+int id action_mode_bar_stub 0x0
+int id action_mode_close_button 0x0
+int id action_text 0x0
+int id actions 0x0
+int id activity_chooser_view_content 0x0
+int id add 0x0
+int id adjust_height 0x0
+int id adjust_width 0x0
+int id alertTitle 0x0
+int id amu_text 0x0
+int id async 0x0
+int id auto 0x0
+int id autofill_inline_suggestion_end_icon 0x0
+int id autofill_inline_suggestion_start_icon 0x0
+int id autofill_inline_suggestion_subtitle 0x0
+int id autofill_inline_suggestion_title 0x0
+int id blocking 0x0
+int id buttonPanel 0x0
+int id catalyst_redbox_title 0x0
+int id center 0x0
+int id centerCrop 0x0
+int id centerInside 0x0
+int id checkbox 0x0
+int id checked 0x0
+int id chronometer 0x0
+int id content 0x0
+int id contentPanel 0x0
+int id custom 0x0
+int id customPanel 0x0
+int id dark 0x0
+int id decor_content_parent 0x0
+int id default_activity_button 0x0
+int id dialog_button 0x0
+int id edit_query 0x0
+int id expand_activities_button 0x0
+int id expanded_menu 0x0
+int id fitBottomStart 0x0
+int id fitCenter 0x0
+int id fitEnd 0x0
+int id fitStart 0x0
+int id fitXY 0x0
+int id focusCrop 0x0
+int id forever 0x0
+int id fps_text 0x0
+int id fragment_container_view_tag 0x0
+int id group_divider 0x0
+int id home 0x0
+int id hybrid 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id icon_only 0x0
+int id image 0x0
+int id info 0x0
+int id italic 0x0
+int id item1 0x0
+int id item2 0x0
+int id item3 0x0
+int id item4 0x0
+int id labelled_by 0x0
+int id light 0x0
+int id line1 0x0
+int id line3 0x0
+int id listMode 0x0
+int id list_item 0x0
+int id message 0x0
+int id multiply 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id off 0x0
+int id on 0x0
+int id parentPanel 0x0
+int id pointer_enter 0x0
+int id pointer_enter_capture 0x0
+int id pointer_leave 0x0
+int id pointer_leave_capture 0x0
+int id pointer_move 0x0
+int id pointer_move_capture 0x0
+int id progress_circular 0x0
+int id progress_horizontal 0x0
+int id radio 0x0
+int id react_test_id 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id rn_frame_file 0x0
+int id rn_frame_method 0x0
+int id rn_redbox_dismiss_button 0x0
+int id rn_redbox_line_separator 0x0
+int id rn_redbox_loading_indicator 0x0
+int id rn_redbox_reload_button 0x0
+int id rn_redbox_report_button 0x0
+int id rn_redbox_report_label 0x0
+int id rn_redbox_stack 0x0
+int id satellite 0x0
+int id screen 0x0
+int id scrollIndicatorDown 0x0
+int id scrollIndicatorUp 0x0
+int id scrollView 0x0
+int id search_badge 0x0
+int id search_bar 0x0
+int id search_button 0x0
+int id search_close_btn 0x0
+int id search_edit_frame 0x0
+int id search_go_btn 0x0
+int id search_mag_icon 0x0
+int id search_plate 0x0
+int id search_src_text 0x0
+int id search_voice_btn 0x0
+int id select_dialog_listview 0x0
+int id shortcut 0x0
+int id spacer 0x0
+int id special_effects_controller_view_tag 0x0
+int id split_action_bar 0x0
+int id src_atop 0x0
+int id src_in 0x0
+int id src_over 0x0
+int id standard 0x0
+int id submenuarrow 0x0
+int id submit_area 0x0
+int id tabMode 0x0
+int id tag_accessibility_actions 0x0
+int id tag_accessibility_clickable_spans 0x0
+int id tag_accessibility_heading 0x0
+int id tag_accessibility_pane_title 0x0
+int id tag_on_apply_window_listener 0x0
+int id tag_on_receive_content_listener 0x0
+int id tag_on_receive_content_mime_types 0x0
+int id tag_screen_reader_focusable 0x0
+int id tag_state_description 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id tag_window_insets_animation_callback 0x0
+int id terrain 0x0
+int id text 0x0
+int id text2 0x0
+int id textSpacerNoButtons 0x0
+int id textSpacerNoTitle 0x0
+int id time 0x0
+int id title 0x0
+int id titleDividerNoCustom 0x0
+int id title_template 0x0
+int id topPanel 0x0
+int id unchecked 0x0
+int id uniform 0x0
+int id up 0x0
+int id view_tag_instance_handle 0x0
+int id view_tag_native_id 0x0
+int id view_tree_lifecycle_owner 0x0
+int id view_tree_saved_state_registry_owner 0x0
+int id view_tree_view_model_store_owner 0x0
+int id visible_removing_fragment_view_tag 0x0
+int id webview 0x0
+int id wide 0x0
+int id window 0x0
+int id wrap_content 0x0
+int integer abc_config_activityDefaultDur 0x0
+int integer abc_config_activityShortDur 0x0
+int integer cancel_button_image_alpha 0x0
+int integer config_tooltipAnimTime 0x0
+int integer google_play_services_version 0x0
+int integer react_native_dev_server_port 0x0
+int integer react_native_inspector_proxy_port 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x0
+int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x0
+int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x0
+int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x0
+int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x0
+int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x0
+int interpolator fast_out_slow_in 0x0
+int layout abc_action_bar_title_item 0x0
+int layout abc_action_bar_up_container 0x0
+int layout abc_action_menu_item_layout 0x0
+int layout abc_action_menu_layout 0x0
+int layout abc_action_mode_bar 0x0
+int layout abc_action_mode_close_item_material 0x0
+int layout abc_activity_chooser_view 0x0
+int layout abc_activity_chooser_view_list_item 0x0
+int layout abc_alert_dialog_button_bar_material 0x0
+int layout abc_alert_dialog_material 0x0
+int layout abc_alert_dialog_title_material 0x0
+int layout abc_cascading_menu_item_layout 0x0
+int layout abc_dialog_title_material 0x0
+int layout abc_expanded_menu_layout 0x0
+int layout abc_list_menu_item_checkbox 0x0
+int layout abc_list_menu_item_icon 0x0
+int layout abc_list_menu_item_layout 0x0
+int layout abc_list_menu_item_radio 0x0
+int layout abc_popup_menu_header_item_layout 0x0
+int layout abc_popup_menu_item_layout 0x0
+int layout abc_screen_content_include 0x0
+int layout abc_screen_simple 0x0
+int layout abc_screen_simple_overlay_action_mode 0x0
+int layout abc_screen_toolbar 0x0
+int layout abc_search_dropdown_item_icons_2line 0x0
+int layout abc_search_view 0x0
+int layout abc_select_dialog_material 0x0
+int layout abc_tooltip 0x0
+int layout amu_info_window 0x0
+int layout amu_text_bubble 0x0
+int layout amu_webview 0x0
+int layout autofill_inline_suggestion 0x0
+int layout custom_dialog 0x0
+int layout dev_loading_view 0x0
+int layout fps_view 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int layout redbox_item_frame 0x0
+int layout redbox_item_title 0x0
+int layout redbox_view 0x0
+int layout select_dialog_item_material 0x0
+int layout select_dialog_multichoice_material 0x0
+int layout select_dialog_singlechoice_material 0x0
+int layout support_simple_spinner_dropdown_item 0x0
+int menu example_menu 0x0
+int menu example_menu2 0x0
+int raw amu_ballon_gx_prefix 0x0
+int raw amu_basic_folder 0x0
+int raw amu_basic_placemark 0x0
+int raw amu_cdata 0x0
+int raw amu_default_balloon 0x0
+int raw amu_document_nest 0x0
+int raw amu_draw_order_ground_overlay 0x0
+int raw amu_extended_data 0x0
+int raw amu_ground_overlay 0x0
+int raw amu_ground_overlay_color 0x0
+int raw amu_inline_style 0x0
+int raw amu_multigeometry_placemarks 0x0
+int raw amu_multiple_placemarks 0x0
+int raw amu_nested_folders 0x0
+int raw amu_nested_multigeometry 0x0
+int raw amu_poly_style_boolean_alpha 0x0
+int raw amu_poly_style_boolean_numeric 0x0
+int raw amu_unknwown_folder 0x0
+int raw amu_unsupported 0x0
+int raw amu_visibility_ground_overlay 0x0
+int string abc_action_bar_home_description 0x0
+int string abc_action_bar_up_description 0x0
+int string abc_action_menu_overflow_description 0x0
+int string abc_action_mode_done 0x0
+int string abc_activity_chooser_view_see_all 0x0
+int string abc_activitychooserview_choose_application 0x0
+int string abc_capital_off 0x0
+int string abc_capital_on 0x0
+int string abc_menu_alt_shortcut_label 0x0
+int string abc_menu_ctrl_shortcut_label 0x0
+int string abc_menu_delete_shortcut_label 0x0
+int string abc_menu_enter_shortcut_label 0x0
+int string abc_menu_function_shortcut_label 0x0
+int string abc_menu_meta_shortcut_label 0x0
+int string abc_menu_shift_shortcut_label 0x0
+int string abc_menu_space_shortcut_label 0x0
+int string abc_menu_sym_shortcut_label 0x0
+int string abc_prepend_shortcut_label 0x0
+int string abc_search_hint 0x0
+int string abc_searchview_description_clear 0x0
+int string abc_searchview_description_query 0x0
+int string abc_searchview_description_search 0x0
+int string abc_searchview_description_submit 0x0
+int string abc_searchview_description_voice 0x0
+int string abc_shareactionprovider_share_with 0x0
+int string abc_shareactionprovider_share_with_application 0x0
+int string abc_toolbar_collapse_description 0x0
+int string alert_description 0x0
+int string androidx_startup 0x0
+int string catalyst_change_bundle_location 0x0
+int string catalyst_copy_button 0x0
+int string catalyst_debug 0x0
+int string catalyst_debug_chrome 0x0
+int string catalyst_debug_chrome_stop 0x0
+int string catalyst_debug_connecting 0x0
+int string catalyst_debug_error 0x0
+int string catalyst_debug_open 0x0
+int string catalyst_debug_stop 0x0
+int string catalyst_devtools_open 0x0
+int string catalyst_dismiss_button 0x0
+int string catalyst_heap_capture 0x0
+int string catalyst_hot_reloading 0x0
+int string catalyst_hot_reloading_auto_disable 0x0
+int string catalyst_hot_reloading_auto_enable 0x0
+int string catalyst_hot_reloading_stop 0x0
+int string catalyst_inspector 0x0
+int string catalyst_inspector_stop 0x0
+int string catalyst_loading_from_url 0x0
+int string catalyst_open_flipper_error 0x0
+int string catalyst_perf_monitor 0x0
+int string catalyst_perf_monitor_stop 0x0
+int string catalyst_reload 0x0
+int string catalyst_reload_button 0x0
+int string catalyst_reload_error 0x0
+int string catalyst_report_button 0x0
+int string catalyst_sample_profiler_disable 0x0
+int string catalyst_sample_profiler_enable 0x0
+int string catalyst_settings 0x0
+int string catalyst_settings_title 0x0
+int string combobox_description 0x0
+int string common_google_play_services_enable_button 0x0
+int string common_google_play_services_enable_text 0x0
+int string common_google_play_services_enable_title 0x0
+int string common_google_play_services_install_button 0x0
+int string common_google_play_services_install_text 0x0
+int string common_google_play_services_install_text_phone 0x0
+int string common_google_play_services_install_text_tablet 0x0
+int string common_google_play_services_install_title 0x0
+int string common_google_play_services_notification_channel_name 0x0
+int string common_google_play_services_notification_ticker 0x0
+int string common_google_play_services_unknown_issue 0x0
+int string common_google_play_services_unsupported_text 0x0
+int string common_google_play_services_unsupported_title 0x0
+int string common_google_play_services_update_button 0x0
+int string common_google_play_services_update_text 0x0
+int string common_google_play_services_update_title 0x0
+int string common_google_play_services_updating_text 0x0
+int string common_google_play_services_updating_title 0x0
+int string common_google_play_services_wear_update_text 0x0
+int string common_open_on_phone 0x0
+int string common_signin_button_text 0x0
+int string common_signin_button_text_long 0x0
+int string header_description 0x0
+int string image_description 0x0
+int string imagebutton_description 0x0
+int string link_description 0x0
+int string menu_description 0x0
+int string menubar_description 0x0
+int string menuitem_description 0x0
+int string progressbar_description 0x0
+int string radiogroup_description 0x0
+int string rn_tab_description 0x0
+int string scrollbar_description 0x0
+int string search_menu_title 0x0
+int string spinbutton_description 0x0
+int string state_busy_description 0x0
+int string state_collapsed_description 0x0
+int string state_expanded_description 0x0
+int string state_mixed_description 0x0
+int string state_off_description 0x0
+int string state_on_description 0x0
+int string state_unselected_description 0x0
+int string status_bar_notification_info_overflow 0x0
+int string summary_description 0x0
+int string tablist_description 0x0
+int string timer_description 0x0
+int string toolbar_description 0x0
+int style AlertDialog_AppCompat 0x0
+int style AlertDialog_AppCompat_Light 0x0
+int style Animation_AppCompat_Dialog 0x0
+int style Animation_AppCompat_DropDownUp 0x0
+int style Animation_AppCompat_Tooltip 0x0
+int style Animation_Catalyst_LogBox 0x0
+int style Animation_Catalyst_RedBox 0x0
+int style Base_AlertDialog_AppCompat 0x0
+int style Base_AlertDialog_AppCompat_Light 0x0
+int style Base_Animation_AppCompat_Dialog 0x0
+int style Base_Animation_AppCompat_DropDownUp 0x0
+int style Base_Animation_AppCompat_Tooltip 0x0
+int style Base_DialogWindowTitleBackground_AppCompat 0x0
+int style Base_DialogWindowTitle_AppCompat 0x0
+int style Base_TextAppearance_AppCompat 0x0
+int style Base_TextAppearance_AppCompat_Body1 0x0
+int style Base_TextAppearance_AppCompat_Body2 0x0
+int style Base_TextAppearance_AppCompat_Button 0x0
+int style Base_TextAppearance_AppCompat_Caption 0x0
+int style Base_TextAppearance_AppCompat_Display1 0x0
+int style Base_TextAppearance_AppCompat_Display2 0x0
+int style Base_TextAppearance_AppCompat_Display3 0x0
+int style Base_TextAppearance_AppCompat_Display4 0x0
+int style Base_TextAppearance_AppCompat_Headline 0x0
+int style Base_TextAppearance_AppCompat_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Large 0x0
+int style Base_TextAppearance_AppCompat_Large_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Medium 0x0
+int style Base_TextAppearance_AppCompat_Medium_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Menu 0x0
+int style Base_TextAppearance_AppCompat_SearchResult 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Title 0x0
+int style Base_TextAppearance_AppCompat_Small 0x0
+int style Base_TextAppearance_AppCompat_Small_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Subhead 0x0
+int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Title 0x0
+int style Base_TextAppearance_AppCompat_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Tooltip 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Widget_Switch 0x0
+int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Base_ThemeOverlay_AppCompat 0x0
+int style Base_ThemeOverlay_AppCompat_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dark 0x0
+int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style Base_ThemeOverlay_AppCompat_Light 0x0
+int style Base_Theme_AppCompat 0x0
+int style Base_Theme_AppCompat_CompactMenu 0x0
+int style Base_Theme_AppCompat_Dialog 0x0
+int style Base_Theme_AppCompat_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Dialog_MinWidth 0x0
+int style Base_Theme_AppCompat_Light 0x0
+int style Base_Theme_AppCompat_Light_DarkActionBar 0x0
+int style Base_Theme_AppCompat_Light_Dialog 0x0
+int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat 0x0
+int style Base_V21_Theme_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat_Light 0x0
+int style Base_V21_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V22_Theme_AppCompat 0x0
+int style Base_V22_Theme_AppCompat_Light 0x0
+int style Base_V23_Theme_AppCompat 0x0
+int style Base_V23_Theme_AppCompat_Light 0x0
+int style Base_V26_Theme_AppCompat 0x0
+int style Base_V26_Theme_AppCompat_Light 0x0
+int style Base_V26_Widget_AppCompat_Toolbar 0x0
+int style Base_V28_Theme_AppCompat 0x0
+int style Base_V28_Theme_AppCompat_Light 0x0
+int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat 0x0
+int style Base_V7_Theme_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat_Light 0x0
+int style Base_V7_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_V7_Widget_AppCompat_EditText 0x0
+int style Base_V7_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_ActionBar 0x0
+int style Base_Widget_AppCompat_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_ActionButton 0x0
+int style Base_Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Base_Widget_AppCompat_ActionButton_Overflow 0x0
+int style Base_Widget_AppCompat_ActionMode 0x0
+int style Base_Widget_AppCompat_ActivityChooserView 0x0
+int style Base_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_Widget_AppCompat_Button 0x0
+int style Base_Widget_AppCompat_ButtonBar 0x0
+int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Borderless 0x0
+int style Base_Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Colored 0x0
+int style Base_Widget_AppCompat_Button_Small 0x0
+int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Base_Widget_AppCompat_CompoundButton_Switch 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x0
+int style Base_Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Base_Widget_AppCompat_EditText 0x0
+int style Base_Widget_AppCompat_ImageButton 0x0
+int style Base_Widget_AppCompat_Light_ActionBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_ListMenuView 0x0
+int style Base_Widget_AppCompat_ListPopupWindow 0x0
+int style Base_Widget_AppCompat_ListView 0x0
+int style Base_Widget_AppCompat_ListView_DropDown 0x0
+int style Base_Widget_AppCompat_ListView_Menu 0x0
+int style Base_Widget_AppCompat_PopupMenu 0x0
+int style Base_Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_PopupWindow 0x0
+int style Base_Widget_AppCompat_ProgressBar 0x0
+int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Base_Widget_AppCompat_RatingBar 0x0
+int style Base_Widget_AppCompat_RatingBar_Indicator 0x0
+int style Base_Widget_AppCompat_RatingBar_Small 0x0
+int style Base_Widget_AppCompat_SearchView 0x0
+int style Base_Widget_AppCompat_SearchView_ActionBar 0x0
+int style Base_Widget_AppCompat_SeekBar 0x0
+int style Base_Widget_AppCompat_SeekBar_Discrete 0x0
+int style Base_Widget_AppCompat_Spinner 0x0
+int style Base_Widget_AppCompat_Spinner_Underlined 0x0
+int style Base_Widget_AppCompat_TextView 0x0
+int style Base_Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Base_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style CalendarDatePickerDialog 0x0
+int style CalendarDatePickerStyle 0x0
+int style DialogAnimationFade 0x0
+int style DialogAnimationSlide 0x0
+int style Platform_AppCompat 0x0
+int style Platform_AppCompat_Light 0x0
+int style Platform_ThemeOverlay_AppCompat 0x0
+int style Platform_ThemeOverlay_AppCompat_Dark 0x0
+int style Platform_ThemeOverlay_AppCompat_Light 0x0
+int style Platform_V21_AppCompat 0x0
+int style Platform_V21_AppCompat_Light 0x0
+int style Platform_V25_AppCompat 0x0
+int style Platform_V25_AppCompat_Light 0x0
+int style Platform_Widget_AppCompat_Spinner 0x0
+int style RtlOverlay_DialogWindowTitle_AppCompat 0x0
+int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x0
+int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x0
+int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x0
+int style SpinnerDatePickerDialog 0x0
+int style SpinnerDatePickerStyle 0x0
+int style TextAppearance_AppCompat 0x0
+int style TextAppearance_AppCompat_Body1 0x0
+int style TextAppearance_AppCompat_Body2 0x0
+int style TextAppearance_AppCompat_Button 0x0
+int style TextAppearance_AppCompat_Caption 0x0
+int style TextAppearance_AppCompat_Display1 0x0
+int style TextAppearance_AppCompat_Display2 0x0
+int style TextAppearance_AppCompat_Display3 0x0
+int style TextAppearance_AppCompat_Display4 0x0
+int style TextAppearance_AppCompat_Headline 0x0
+int style TextAppearance_AppCompat_Inverse 0x0
+int style TextAppearance_AppCompat_Large 0x0
+int style TextAppearance_AppCompat_Large_Inverse 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Medium 0x0
+int style TextAppearance_AppCompat_Medium_Inverse 0x0
+int style TextAppearance_AppCompat_Menu 0x0
+int style TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Small 0x0
+int style TextAppearance_AppCompat_Small_Inverse 0x0
+int style TextAppearance_AppCompat_Subhead 0x0
+int style TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style TextAppearance_AppCompat_Title 0x0
+int style TextAppearance_AppCompat_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Tooltip 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_Button 0x0
+int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Widget_Switch 0x0
+int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Theme 0x0
+int style ThemeOverlay_AppCompat 0x0
+int style ThemeOverlay_AppCompat_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dark 0x0
+int style ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style ThemeOverlay_AppCompat_DayNight 0x0
+int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dialog 0x0
+int style ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style ThemeOverlay_AppCompat_Light 0x0
+int style Theme_AppCompat 0x0
+int style Theme_AppCompat_CompactMenu 0x0
+int style Theme_AppCompat_DayNight 0x0
+int style Theme_AppCompat_DayNight_DarkActionBar 0x0
+int style Theme_AppCompat_DayNight_Dialog 0x0
+int style Theme_AppCompat_DayNight_DialogWhenLarge 0x0
+int style Theme_AppCompat_DayNight_Dialog_Alert 0x0
+int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x0
+int style Theme_AppCompat_DayNight_NoActionBar 0x0
+int style Theme_AppCompat_Dialog 0x0
+int style Theme_AppCompat_DialogWhenLarge 0x0
+int style Theme_AppCompat_Dialog_Alert 0x0
+int style Theme_AppCompat_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Empty 0x0
+int style Theme_AppCompat_Light 0x0
+int style Theme_AppCompat_Light_DarkActionBar 0x0
+int style Theme_AppCompat_Light_Dialog 0x0
+int style Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light_NoActionBar 0x0
+int style Theme_AppCompat_NoActionBar 0x0
+int style Theme_AutofillInlineSuggestion 0x0
+int style Theme_Catalyst 0x0
+int style Theme_Catalyst_LogBox 0x0
+int style Theme_Catalyst_RedBox 0x0
+int style Theme_FullScreenDialog 0x0
+int style Theme_FullScreenDialogAnimatedFade 0x0
+int style Theme_FullScreenDialogAnimatedSlide 0x0
+int style Theme_ReactNative_AppCompat_Light 0x0
+int style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen 0x0
+int style Widget_AppCompat_ActionBar 0x0
+int style Widget_AppCompat_ActionBar_Solid 0x0
+int style Widget_AppCompat_ActionBar_TabBar 0x0
+int style Widget_AppCompat_ActionBar_TabText 0x0
+int style Widget_AppCompat_ActionBar_TabView 0x0
+int style Widget_AppCompat_ActionButton 0x0
+int style Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_ActionButton_Overflow 0x0
+int style Widget_AppCompat_ActionMode 0x0
+int style Widget_AppCompat_ActivityChooserView 0x0
+int style Widget_AppCompat_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Button 0x0
+int style Widget_AppCompat_ButtonBar 0x0
+int style Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Borderless 0x0
+int style Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Colored 0x0
+int style Widget_AppCompat_Button_Small 0x0
+int style Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Widget_AppCompat_CompoundButton_Switch 0x0
+int style Widget_AppCompat_DrawerArrowToggle 0x0
+int style Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_EditText 0x0
+int style Widget_AppCompat_ImageButton 0x0
+int style Widget_AppCompat_Light_ActionBar 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x0
+int style Widget_AppCompat_Light_ActionButton 0x0
+int style Widget_AppCompat_Light_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_Light_ActionButton_Overflow 0x0
+int style Widget_AppCompat_Light_ActionMode_Inverse 0x0
+int style Widget_AppCompat_Light_ActivityChooserView 0x0
+int style Widget_AppCompat_Light_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Light_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_Light_ListPopupWindow 0x0
+int style Widget_AppCompat_Light_ListView_DropDown 0x0
+int style Widget_AppCompat_Light_PopupMenu 0x0
+int style Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_Light_SearchView 0x0
+int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_ListMenuView 0x0
+int style Widget_AppCompat_ListPopupWindow 0x0
+int style Widget_AppCompat_ListView 0x0
+int style Widget_AppCompat_ListView_DropDown 0x0
+int style Widget_AppCompat_ListView_Menu 0x0
+int style Widget_AppCompat_PopupMenu 0x0
+int style Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_PopupWindow 0x0
+int style Widget_AppCompat_ProgressBar 0x0
+int style Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Widget_AppCompat_RatingBar 0x0
+int style Widget_AppCompat_RatingBar_Indicator 0x0
+int style Widget_AppCompat_RatingBar_Small 0x0
+int style Widget_AppCompat_SearchView 0x0
+int style Widget_AppCompat_SearchView_ActionBar 0x0
+int style Widget_AppCompat_SeekBar 0x0
+int style Widget_AppCompat_SeekBar_Discrete 0x0
+int style Widget_AppCompat_Spinner 0x0
+int style Widget_AppCompat_Spinner_DropDown 0x0
+int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_Spinner_Underlined 0x0
+int style Widget_AppCompat_TextView 0x0
+int style Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Widget_AppCompat_Toolbar 0x0
+int style Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style Widget_Autofill 0x0
+int style Widget_Autofill_InlineSuggestionChip 0x0
+int style Widget_Autofill_InlineSuggestionEndIconStyle 0x0
+int style Widget_Autofill_InlineSuggestionStartIconStyle 0x0
+int style Widget_Autofill_InlineSuggestionSubtitle 0x0
+int style Widget_Autofill_InlineSuggestionTitle 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style amu_Bubble_TextAppearance_Dark 0x0
+int style amu_Bubble_TextAppearance_Light 0x0
+int style amu_ClusterIcon_TextAppearance 0x0
+int style redboxButton 0x0
+int[] styleable ActionBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionBar_background 0
+int styleable ActionBar_backgroundSplit 1
+int styleable ActionBar_backgroundStacked 2
+int styleable ActionBar_contentInsetEnd 3
+int styleable ActionBar_contentInsetEndWithActions 4
+int styleable ActionBar_contentInsetLeft 5
+int styleable ActionBar_contentInsetRight 6
+int styleable ActionBar_contentInsetStart 7
+int styleable ActionBar_contentInsetStartWithNavigation 8
+int styleable ActionBar_customNavigationLayout 9
+int styleable ActionBar_displayOptions 10
+int styleable ActionBar_divider 11
+int styleable ActionBar_elevation 12
+int styleable ActionBar_height 13
+int styleable ActionBar_hideOnContentScroll 14
+int styleable ActionBar_homeAsUpIndicator 15
+int styleable ActionBar_homeLayout 16
+int styleable ActionBar_icon 17
+int styleable ActionBar_indeterminateProgressStyle 18
+int styleable ActionBar_itemPadding 19
+int styleable ActionBar_logo 20
+int styleable ActionBar_navigationMode 21
+int styleable ActionBar_popupTheme 22
+int styleable ActionBar_progressBarPadding 23
+int styleable ActionBar_progressBarStyle 24
+int styleable ActionBar_subtitle 25
+int styleable ActionBar_subtitleTextStyle 26
+int styleable ActionBar_title 27
+int styleable ActionBar_titleTextStyle 28
+int[] styleable ActionBarLayout { 0x10100b3 }
+int styleable ActionBarLayout_android_layout_gravity 0
+int[] styleable ActionMenuItemView { 0x101013f }
+int styleable ActionMenuItemView_android_minWidth 0
+int[] styleable ActionMenuView {  }
+int[] styleable ActionMode { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionMode_background 0
+int styleable ActionMode_backgroundSplit 1
+int styleable ActionMode_closeItemLayout 2
+int styleable ActionMode_height 3
+int styleable ActionMode_subtitleTextStyle 4
+int styleable ActionMode_titleTextStyle 5
+int[] styleable ActivityChooserView { 0x0, 0x0 }
+int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
+int styleable ActivityChooserView_initialActivityCount 1
+int[] styleable AlertDialog { 0x10100f2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AlertDialog_android_layout 0
+int styleable AlertDialog_buttonIconDimen 1
+int styleable AlertDialog_buttonPanelSideLayout 2
+int styleable AlertDialog_listItemLayout 3
+int styleable AlertDialog_listLayout 4
+int styleable AlertDialog_multiChoiceItemLayout 5
+int styleable AlertDialog_showTitle 6
+int styleable AlertDialog_singleChoiceItemLayout 7
+int[] styleable AnimatedStateListDrawableCompat { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable AnimatedStateListDrawableCompat_android_constantSize 0
+int styleable AnimatedStateListDrawableCompat_android_dither 1
+int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
+int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
+int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
+int styleable AnimatedStateListDrawableCompat_android_visible 5
+int[] styleable AnimatedStateListDrawableItem { 0x1010199, 0x10100d0 }
+int styleable AnimatedStateListDrawableItem_android_drawable 0
+int styleable AnimatedStateListDrawableItem_android_id 1
+int[] styleable AnimatedStateListDrawableTransition { 0x1010199, 0x101044a, 0x101044b, 0x1010449 }
+int styleable AnimatedStateListDrawableTransition_android_drawable 0
+int styleable AnimatedStateListDrawableTransition_android_fromId 1
+int styleable AnimatedStateListDrawableTransition_android_reversible 2
+int styleable AnimatedStateListDrawableTransition_android_toId 3
+int[] styleable AppCompatEmojiHelper {  }
+int[] styleable AppCompatImageView { 0x1010119, 0x0, 0x0, 0x0 }
+int styleable AppCompatImageView_android_src 0
+int styleable AppCompatImageView_srcCompat 1
+int styleable AppCompatImageView_tint 2
+int styleable AppCompatImageView_tintMode 3
+int[] styleable AppCompatSeekBar { 0x1010142, 0x0, 0x0, 0x0 }
+int styleable AppCompatSeekBar_android_thumb 0
+int styleable AppCompatSeekBar_tickMark 1
+int styleable AppCompatSeekBar_tickMarkTint 2
+int styleable AppCompatSeekBar_tickMarkTintMode 3
+int[] styleable AppCompatTextHelper { 0x101016e, 0x1010393, 0x101016f, 0x1010170, 0x1010392, 0x101016d, 0x1010034 }
+int styleable AppCompatTextHelper_android_drawableBottom 0
+int styleable AppCompatTextHelper_android_drawableEnd 1
+int styleable AppCompatTextHelper_android_drawableLeft 2
+int styleable AppCompatTextHelper_android_drawableRight 3
+int styleable AppCompatTextHelper_android_drawableStart 4
+int styleable AppCompatTextHelper_android_drawableTop 5
+int styleable AppCompatTextHelper_android_textAppearance 6
+int[] styleable AppCompatTextView { 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTextView_android_textAppearance 0
+int styleable AppCompatTextView_autoSizeMaxTextSize 1
+int styleable AppCompatTextView_autoSizeMinTextSize 2
+int styleable AppCompatTextView_autoSizePresetSizes 3
+int styleable AppCompatTextView_autoSizeStepGranularity 4
+int styleable AppCompatTextView_autoSizeTextType 5
+int styleable AppCompatTextView_drawableBottomCompat 6
+int styleable AppCompatTextView_drawableEndCompat 7
+int styleable AppCompatTextView_drawableLeftCompat 8
+int styleable AppCompatTextView_drawableRightCompat 9
+int styleable AppCompatTextView_drawableStartCompat 10
+int styleable AppCompatTextView_drawableTint 11
+int styleable AppCompatTextView_drawableTintMode 12
+int styleable AppCompatTextView_drawableTopCompat 13
+int styleable AppCompatTextView_emojiCompatEnabled 14
+int styleable AppCompatTextView_firstBaselineToTopHeight 15
+int styleable AppCompatTextView_fontFamily 16
+int styleable AppCompatTextView_fontVariationSettings 17
+int styleable AppCompatTextView_lastBaselineToBottomHeight 18
+int styleable AppCompatTextView_lineHeight 19
+int styleable AppCompatTextView_textAllCaps 20
+int styleable AppCompatTextView_textLocale 21
+int[] styleable AppCompatTheme { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10100ae, 0x1010057, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTheme_actionBarDivider 0
+int styleable AppCompatTheme_actionBarItemBackground 1
+int styleable AppCompatTheme_actionBarPopupTheme 2
+int styleable AppCompatTheme_actionBarSize 3
+int styleable AppCompatTheme_actionBarSplitStyle 4
+int styleable AppCompatTheme_actionBarStyle 5
+int styleable AppCompatTheme_actionBarTabBarStyle 6
+int styleable AppCompatTheme_actionBarTabStyle 7
+int styleable AppCompatTheme_actionBarTabTextStyle 8
+int styleable AppCompatTheme_actionBarTheme 9
+int styleable AppCompatTheme_actionBarWidgetTheme 10
+int styleable AppCompatTheme_actionButtonStyle 11
+int styleable AppCompatTheme_actionDropDownStyle 12
+int styleable AppCompatTheme_actionMenuTextAppearance 13
+int styleable AppCompatTheme_actionMenuTextColor 14
+int styleable AppCompatTheme_actionModeBackground 15
+int styleable AppCompatTheme_actionModeCloseButtonStyle 16
+int styleable AppCompatTheme_actionModeCloseContentDescription 17
+int styleable AppCompatTheme_actionModeCloseDrawable 18
+int styleable AppCompatTheme_actionModeCopyDrawable 19
+int styleable AppCompatTheme_actionModeCutDrawable 20
+int styleable AppCompatTheme_actionModeFindDrawable 21
+int styleable AppCompatTheme_actionModePasteDrawable 22
+int styleable AppCompatTheme_actionModePopupWindowStyle 23
+int styleable AppCompatTheme_actionModeSelectAllDrawable 24
+int styleable AppCompatTheme_actionModeShareDrawable 25
+int styleable AppCompatTheme_actionModeSplitBackground 26
+int styleable AppCompatTheme_actionModeStyle 27
+int styleable AppCompatTheme_actionModeTheme 28
+int styleable AppCompatTheme_actionModeWebSearchDrawable 29
+int styleable AppCompatTheme_actionOverflowButtonStyle 30
+int styleable AppCompatTheme_actionOverflowMenuStyle 31
+int styleable AppCompatTheme_activityChooserViewStyle 32
+int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
+int styleable AppCompatTheme_alertDialogCenterButtons 34
+int styleable AppCompatTheme_alertDialogStyle 35
+int styleable AppCompatTheme_alertDialogTheme 36
+int styleable AppCompatTheme_android_windowAnimationStyle 37
+int styleable AppCompatTheme_android_windowIsFloating 38
+int styleable AppCompatTheme_autoCompleteTextViewStyle 39
+int styleable AppCompatTheme_borderlessButtonStyle 40
+int styleable AppCompatTheme_buttonBarButtonStyle 41
+int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
+int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
+int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
+int styleable AppCompatTheme_buttonBarStyle 45
+int styleable AppCompatTheme_buttonStyle 46
+int styleable AppCompatTheme_buttonStyleSmall 47
+int styleable AppCompatTheme_checkboxStyle 48
+int styleable AppCompatTheme_checkedTextViewStyle 49
+int styleable AppCompatTheme_colorAccent 50
+int styleable AppCompatTheme_colorBackgroundFloating 51
+int styleable AppCompatTheme_colorButtonNormal 52
+int styleable AppCompatTheme_colorControlActivated 53
+int styleable AppCompatTheme_colorControlHighlight 54
+int styleable AppCompatTheme_colorControlNormal 55
+int styleable AppCompatTheme_colorError 56
+int styleable AppCompatTheme_colorPrimary 57
+int styleable AppCompatTheme_colorPrimaryDark 58
+int styleable AppCompatTheme_colorSwitchThumbNormal 59
+int styleable AppCompatTheme_controlBackground 60
+int styleable AppCompatTheme_dialogCornerRadius 61
+int styleable AppCompatTheme_dialogPreferredPadding 62
+int styleable AppCompatTheme_dialogTheme 63
+int styleable AppCompatTheme_dividerHorizontal 64
+int styleable AppCompatTheme_dividerVertical 65
+int styleable AppCompatTheme_dropDownListViewStyle 66
+int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
+int styleable AppCompatTheme_editTextBackground 68
+int styleable AppCompatTheme_editTextColor 69
+int styleable AppCompatTheme_editTextStyle 70
+int styleable AppCompatTheme_homeAsUpIndicator 71
+int styleable AppCompatTheme_imageButtonStyle 72
+int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
+int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
+int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
+int styleable AppCompatTheme_listDividerAlertDialog 76
+int styleable AppCompatTheme_listMenuViewStyle 77
+int styleable AppCompatTheme_listPopupWindowStyle 78
+int styleable AppCompatTheme_listPreferredItemHeight 79
+int styleable AppCompatTheme_listPreferredItemHeightLarge 80
+int styleable AppCompatTheme_listPreferredItemHeightSmall 81
+int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
+int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
+int styleable AppCompatTheme_listPreferredItemPaddingRight 84
+int styleable AppCompatTheme_listPreferredItemPaddingStart 85
+int styleable AppCompatTheme_panelBackground 86
+int styleable AppCompatTheme_panelMenuListTheme 87
+int styleable AppCompatTheme_panelMenuListWidth 88
+int styleable AppCompatTheme_popupMenuStyle 89
+int styleable AppCompatTheme_popupWindowStyle 90
+int styleable AppCompatTheme_radioButtonStyle 91
+int styleable AppCompatTheme_ratingBarStyle 92
+int styleable AppCompatTheme_ratingBarStyleIndicator 93
+int styleable AppCompatTheme_ratingBarStyleSmall 94
+int styleable AppCompatTheme_searchViewStyle 95
+int styleable AppCompatTheme_seekBarStyle 96
+int styleable AppCompatTheme_selectableItemBackground 97
+int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
+int styleable AppCompatTheme_spinnerDropDownItemStyle 99
+int styleable AppCompatTheme_spinnerStyle 100
+int styleable AppCompatTheme_switchStyle 101
+int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
+int styleable AppCompatTheme_textAppearanceListItem 103
+int styleable AppCompatTheme_textAppearanceListItemSecondary 104
+int styleable AppCompatTheme_textAppearanceListItemSmall 105
+int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
+int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
+int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
+int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
+int styleable AppCompatTheme_textColorAlertDialogListItem 110
+int styleable AppCompatTheme_textColorSearchUrl 111
+int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
+int styleable AppCompatTheme_toolbarStyle 113
+int styleable AppCompatTheme_tooltipForegroundColor 114
+int styleable AppCompatTheme_tooltipFrameBackground 115
+int styleable AppCompatTheme_viewInflaterClass 116
+int styleable AppCompatTheme_windowActionBar 117
+int styleable AppCompatTheme_windowActionBarOverlay 118
+int styleable AppCompatTheme_windowActionModeOverlay 119
+int styleable AppCompatTheme_windowFixedHeightMajor 120
+int styleable AppCompatTheme_windowFixedHeightMinor 121
+int styleable AppCompatTheme_windowFixedWidthMajor 122
+int styleable AppCompatTheme_windowFixedWidthMinor 123
+int styleable AppCompatTheme_windowMinWidthMajor 124
+int styleable AppCompatTheme_windowMinWidthMinor 125
+int styleable AppCompatTheme_windowNoTitle 126
+int[] styleable Autofill_InlineSuggestion { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionChip 0
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionEndIconStyle 1
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionStartIconStyle 2
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionSubtitle 3
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionTitle 4
+int styleable Autofill_InlineSuggestion_isAutofillInlineSuggestionTheme 5
+int[] styleable ButtonBarLayout { 0x0 }
+int styleable ButtonBarLayout_allowStacking 0
+int[] styleable Capability { 0x0, 0x0 }
+int styleable Capability_queryPatterns 0
+int styleable Capability_shortcutMatchRequired 1
+int[] styleable CheckedTextView { 0x1010108, 0x0, 0x0, 0x0 }
+int styleable CheckedTextView_android_checkMark 0
+int styleable CheckedTextView_checkMarkCompat 1
+int styleable CheckedTextView_checkMarkTint 2
+int styleable CheckedTextView_checkMarkTintMode 3
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5, 0x1010647, 0x0 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int styleable ColorStateListItem_android_lStar 3
+int styleable ColorStateListItem_lStar 4
+int[] styleable CompoundButton { 0x1010107, 0x0, 0x0, 0x0 }
+int styleable CompoundButton_android_button 0
+int styleable CompoundButton_buttonCompat 1
+int styleable CompoundButton_buttonTint 2
+int styleable CompoundButton_buttonTintMode 3
+int[] styleable DrawerArrowToggle { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable DrawerArrowToggle_arrowHeadLength 0
+int styleable DrawerArrowToggle_arrowShaftLength 1
+int styleable DrawerArrowToggle_barLength 2
+int styleable DrawerArrowToggle_color 3
+int styleable DrawerArrowToggle_drawableSize 4
+int styleable DrawerArrowToggle_gapBetweenBars 5
+int styleable DrawerArrowToggle_spinBars 6
+int styleable DrawerArrowToggle_thickness 7
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int styleable FontFamily_fontProviderSystemFontFamily 6
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable Fragment { 0x10100d0, 0x1010003, 0x10100d1 }
+int styleable Fragment_android_id 0
+int styleable Fragment_android_name 1
+int styleable Fragment_android_tag 2
+int[] styleable FragmentContainerView { 0x1010003, 0x10100d1 }
+int styleable FragmentContainerView_android_name 0
+int styleable FragmentContainerView_android_tag 1
+int[] styleable GenericDraweeHierarchy { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable GenericDraweeHierarchy_actualImageScaleType 0
+int styleable GenericDraweeHierarchy_backgroundImage 1
+int styleable GenericDraweeHierarchy_fadeDuration 2
+int styleable GenericDraweeHierarchy_failureImage 3
+int styleable GenericDraweeHierarchy_failureImageScaleType 4
+int styleable GenericDraweeHierarchy_overlayImage 5
+int styleable GenericDraweeHierarchy_placeholderImage 6
+int styleable GenericDraweeHierarchy_placeholderImageScaleType 7
+int styleable GenericDraweeHierarchy_pressedStateOverlayImage 8
+int styleable GenericDraweeHierarchy_progressBarAutoRotateInterval 9
+int styleable GenericDraweeHierarchy_progressBarImage 10
+int styleable GenericDraweeHierarchy_progressBarImageScaleType 11
+int styleable GenericDraweeHierarchy_retryImage 12
+int styleable GenericDraweeHierarchy_retryImageScaleType 13
+int styleable GenericDraweeHierarchy_roundAsCircle 14
+int styleable GenericDraweeHierarchy_roundBottomEnd 15
+int styleable GenericDraweeHierarchy_roundBottomLeft 16
+int styleable GenericDraweeHierarchy_roundBottomRight 17
+int styleable GenericDraweeHierarchy_roundBottomStart 18
+int styleable GenericDraweeHierarchy_roundTopEnd 19
+int styleable GenericDraweeHierarchy_roundTopLeft 20
+int styleable GenericDraweeHierarchy_roundTopRight 21
+int styleable GenericDraweeHierarchy_roundTopStart 22
+int styleable GenericDraweeHierarchy_roundWithOverlayColor 23
+int styleable GenericDraweeHierarchy_roundedCornerRadius 24
+int styleable GenericDraweeHierarchy_roundingBorderColor 25
+int styleable GenericDraweeHierarchy_roundingBorderPadding 26
+int styleable GenericDraweeHierarchy_roundingBorderWidth 27
+int styleable GenericDraweeHierarchy_viewAspectRatio 28
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int[] styleable LinearLayoutCompat { 0x1010126, 0x1010127, 0x10100af, 0x10100c4, 0x1010128, 0x0, 0x0, 0x0, 0x0 }
+int styleable LinearLayoutCompat_android_baselineAligned 0
+int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
+int styleable LinearLayoutCompat_android_gravity 2
+int styleable LinearLayoutCompat_android_orientation 3
+int styleable LinearLayoutCompat_android_weightSum 4
+int styleable LinearLayoutCompat_divider 5
+int styleable LinearLayoutCompat_dividerPadding 6
+int styleable LinearLayoutCompat_measureWithLargestChild 7
+int styleable LinearLayoutCompat_showDividers 8
+int[] styleable LinearLayoutCompat_Layout { 0x10100b3, 0x10100f5, 0x1010181, 0x10100f4 }
+int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
+int styleable LinearLayoutCompat_Layout_android_layout_height 1
+int styleable LinearLayoutCompat_Layout_android_layout_weight 2
+int styleable LinearLayoutCompat_Layout_android_layout_width 3
+int[] styleable ListPopupWindow { 0x10102ac, 0x10102ad }
+int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
+int styleable ListPopupWindow_android_dropDownVerticalOffset 1
+int[] styleable LoadingImageView { 0x0, 0x0, 0x0 }
+int styleable LoadingImageView_circleCrop 0
+int styleable LoadingImageView_imageAspectRatio 1
+int styleable LoadingImageView_imageAspectRatioAdjust 2
+int[] styleable MapAttrs { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MapAttrs_ambientEnabled 0
+int styleable MapAttrs_backgroundColor 1
+int styleable MapAttrs_cameraBearing 2
+int styleable MapAttrs_cameraMaxZoomPreference 3
+int styleable MapAttrs_cameraMinZoomPreference 4
+int styleable MapAttrs_cameraTargetLat 5
+int styleable MapAttrs_cameraTargetLng 6
+int styleable MapAttrs_cameraTilt 7
+int styleable MapAttrs_cameraZoom 8
+int styleable MapAttrs_latLngBoundsNorthEastLatitude 9
+int styleable MapAttrs_latLngBoundsNorthEastLongitude 10
+int styleable MapAttrs_latLngBoundsSouthWestLatitude 11
+int styleable MapAttrs_latLngBoundsSouthWestLongitude 12
+int styleable MapAttrs_liteMode 13
+int styleable MapAttrs_mapId 14
+int styleable MapAttrs_mapType 15
+int styleable MapAttrs_uiCompass 16
+int styleable MapAttrs_uiMapToolbar 17
+int styleable MapAttrs_uiRotateGestures 18
+int styleable MapAttrs_uiScrollGestures 19
+int styleable MapAttrs_uiScrollGesturesDuringRotateOrZoom 20
+int styleable MapAttrs_uiTiltGestures 21
+int styleable MapAttrs_uiZoomControls 22
+int styleable MapAttrs_uiZoomGestures 23
+int styleable MapAttrs_useViewLifecycle 24
+int styleable MapAttrs_zOrderOnTop 25
+int[] styleable MenuGroup { 0x10101e0, 0x101000e, 0x10100d0, 0x10101de, 0x10101df, 0x1010194 }
+int styleable MenuGroup_android_checkableBehavior 0
+int styleable MenuGroup_android_enabled 1
+int styleable MenuGroup_android_id 2
+int styleable MenuGroup_android_menuCategory 3
+int styleable MenuGroup_android_orderInCategory 4
+int styleable MenuGroup_android_visible 5
+int[] styleable MenuItem { 0x0, 0x0, 0x0, 0x0, 0x10101e3, 0x10101e5, 0x1010106, 0x101000e, 0x1010002, 0x10100d0, 0x10101de, 0x10101e4, 0x101026f, 0x10101df, 0x10101e1, 0x10101e2, 0x1010194, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MenuItem_actionLayout 0
+int styleable MenuItem_actionProviderClass 1
+int styleable MenuItem_actionViewClass 2
+int styleable MenuItem_alphabeticModifiers 3
+int styleable MenuItem_android_alphabeticShortcut 4
+int styleable MenuItem_android_checkable 5
+int styleable MenuItem_android_checked 6
+int styleable MenuItem_android_enabled 7
+int styleable MenuItem_android_icon 8
+int styleable MenuItem_android_id 9
+int styleable MenuItem_android_menuCategory 10
+int styleable MenuItem_android_numericShortcut 11
+int styleable MenuItem_android_onClick 12
+int styleable MenuItem_android_orderInCategory 13
+int styleable MenuItem_android_title 14
+int styleable MenuItem_android_titleCondensed 15
+int styleable MenuItem_android_visible 16
+int styleable MenuItem_contentDescription 17
+int styleable MenuItem_iconTint 18
+int styleable MenuItem_iconTintMode 19
+int styleable MenuItem_numericModifiers 20
+int styleable MenuItem_showAsAction 21
+int styleable MenuItem_tooltipText 22
+int[] styleable MenuView { 0x101012f, 0x101012d, 0x1010130, 0x1010131, 0x101012c, 0x101012e, 0x10100ae, 0x0, 0x0 }
+int styleable MenuView_android_headerBackground 0
+int styleable MenuView_android_horizontalDivider 1
+int styleable MenuView_android_itemBackground 2
+int styleable MenuView_android_itemIconDisabledAlpha 3
+int styleable MenuView_android_itemTextAppearance 4
+int styleable MenuView_android_verticalDivider 5
+int styleable MenuView_android_windowAnimationStyle 6
+int styleable MenuView_preserveIconSpacing 7
+int styleable MenuView_subMenuArrow 8
+int[] styleable PopupWindow { 0x10102c9, 0x1010176, 0x0 }
+int styleable PopupWindow_android_popupAnimationStyle 0
+int styleable PopupWindow_android_popupBackground 1
+int styleable PopupWindow_overlapAnchor 2
+int[] styleable PopupWindowBackgroundState { 0x0 }
+int styleable PopupWindowBackgroundState_state_above_anchor 0
+int[] styleable RecycleListView { 0x0, 0x0 }
+int styleable RecycleListView_paddingBottomNoButtons 0
+int styleable RecycleListView_paddingTopNoTitle 1
+int[] styleable SearchView { 0x10100da, 0x1010264, 0x1010220, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SearchView_android_focusable 0
+int styleable SearchView_android_imeOptions 1
+int styleable SearchView_android_inputType 2
+int styleable SearchView_android_maxWidth 3
+int styleable SearchView_closeIcon 4
+int styleable SearchView_commitIcon 5
+int styleable SearchView_defaultQueryHint 6
+int styleable SearchView_goIcon 7
+int styleable SearchView_iconifiedByDefault 8
+int styleable SearchView_layout 9
+int styleable SearchView_queryBackground 10
+int styleable SearchView_queryHint 11
+int styleable SearchView_searchHintIcon 12
+int styleable SearchView_searchIcon 13
+int styleable SearchView_submitBackground 14
+int styleable SearchView_suggestionRowLayout 15
+int styleable SearchView_voiceIcon 16
+int[] styleable SignInButton { 0x0, 0x0, 0x0 }
+int styleable SignInButton_buttonSize 0
+int styleable SignInButton_colorScheme 1
+int styleable SignInButton_scopeUris 2
+int[] styleable SimpleDraweeView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SimpleDraweeView_actualImageResource 0
+int styleable SimpleDraweeView_actualImageScaleType 1
+int styleable SimpleDraweeView_actualImageUri 2
+int styleable SimpleDraweeView_backgroundImage 3
+int styleable SimpleDraweeView_fadeDuration 4
+int styleable SimpleDraweeView_failureImage 5
+int styleable SimpleDraweeView_failureImageScaleType 6
+int styleable SimpleDraweeView_overlayImage 7
+int styleable SimpleDraweeView_placeholderImage 8
+int styleable SimpleDraweeView_placeholderImageScaleType 9
+int styleable SimpleDraweeView_pressedStateOverlayImage 10
+int styleable SimpleDraweeView_progressBarAutoRotateInterval 11
+int styleable SimpleDraweeView_progressBarImage 12
+int styleable SimpleDraweeView_progressBarImageScaleType 13
+int styleable SimpleDraweeView_retryImage 14
+int styleable SimpleDraweeView_retryImageScaleType 15
+int styleable SimpleDraweeView_roundAsCircle 16
+int styleable SimpleDraweeView_roundBottomEnd 17
+int styleable SimpleDraweeView_roundBottomLeft 18
+int styleable SimpleDraweeView_roundBottomRight 19
+int styleable SimpleDraweeView_roundBottomStart 20
+int styleable SimpleDraweeView_roundTopEnd 21
+int styleable SimpleDraweeView_roundTopLeft 22
+int styleable SimpleDraweeView_roundTopRight 23
+int styleable SimpleDraweeView_roundTopStart 24
+int styleable SimpleDraweeView_roundWithOverlayColor 25
+int styleable SimpleDraweeView_roundedCornerRadius 26
+int styleable SimpleDraweeView_roundingBorderColor 27
+int styleable SimpleDraweeView_roundingBorderPadding 28
+int styleable SimpleDraweeView_roundingBorderWidth 29
+int styleable SimpleDraweeView_viewAspectRatio 30
+int[] styleable Spinner { 0x1010262, 0x10100b2, 0x1010176, 0x101017b, 0x0 }
+int styleable Spinner_android_dropDownWidth 0
+int styleable Spinner_android_entries 1
+int styleable Spinner_android_popupBackground 2
+int styleable Spinner_android_prompt 3
+int styleable Spinner_popupTheme 4
+int[] styleable StateListDrawable { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable StateListDrawable_android_constantSize 0
+int styleable StateListDrawable_android_dither 1
+int styleable StateListDrawable_android_enterFadeDuration 2
+int styleable StateListDrawable_android_exitFadeDuration 3
+int styleable StateListDrawable_android_variablePadding 4
+int styleable StateListDrawable_android_visible 5
+int[] styleable StateListDrawableItem { 0x1010199 }
+int styleable StateListDrawableItem_android_drawable 0
+int[] styleable SwitchCompat { 0x1010125, 0x1010124, 0x1010142, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SwitchCompat_android_textOff 0
+int styleable SwitchCompat_android_textOn 1
+int styleable SwitchCompat_android_thumb 2
+int styleable SwitchCompat_showText 3
+int styleable SwitchCompat_splitTrack 4
+int styleable SwitchCompat_switchMinWidth 5
+int styleable SwitchCompat_switchPadding 6
+int styleable SwitchCompat_switchTextAppearance 7
+int styleable SwitchCompat_thumbTextPadding 8
+int styleable SwitchCompat_thumbTint 9
+int styleable SwitchCompat_thumbTintMode 10
+int styleable SwitchCompat_track 11
+int styleable SwitchCompat_trackTint 12
+int styleable SwitchCompat_trackTintMode 13
+int[] styleable TextAppearance { 0x10103ac, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x1010098, 0x101009a, 0x101009b, 0x1010585, 0x1010095, 0x1010097, 0x1010096, 0x0, 0x0, 0x0, 0x0 }
+int styleable TextAppearance_android_fontFamily 0
+int styleable TextAppearance_android_shadowColor 1
+int styleable TextAppearance_android_shadowDx 2
+int styleable TextAppearance_android_shadowDy 3
+int styleable TextAppearance_android_shadowRadius 4
+int styleable TextAppearance_android_textColor 5
+int styleable TextAppearance_android_textColorHint 6
+int styleable TextAppearance_android_textColorLink 7
+int styleable TextAppearance_android_textFontWeight 8
+int styleable TextAppearance_android_textSize 9
+int styleable TextAppearance_android_textStyle 10
+int styleable TextAppearance_android_typeface 11
+int styleable TextAppearance_fontFamily 12
+int styleable TextAppearance_fontVariationSettings 13
+int styleable TextAppearance_textAllCaps 14
+int styleable TextAppearance_textLocale 15
+int[] styleable Toolbar { 0x10100af, 0x1010140, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Toolbar_android_gravity 0
+int styleable Toolbar_android_minHeight 1
+int styleable Toolbar_buttonGravity 2
+int styleable Toolbar_collapseContentDescription 3
+int styleable Toolbar_collapseIcon 4
+int styleable Toolbar_contentInsetEnd 5
+int styleable Toolbar_contentInsetEndWithActions 6
+int styleable Toolbar_contentInsetLeft 7
+int styleable Toolbar_contentInsetRight 8
+int styleable Toolbar_contentInsetStart 9
+int styleable Toolbar_contentInsetStartWithNavigation 10
+int styleable Toolbar_logo 11
+int styleable Toolbar_logoDescription 12
+int styleable Toolbar_maxButtonHeight 13
+int styleable Toolbar_menu 14
+int styleable Toolbar_navigationContentDescription 15
+int styleable Toolbar_navigationIcon 16
+int styleable Toolbar_popupTheme 17
+int styleable Toolbar_subtitle 18
+int styleable Toolbar_subtitleTextAppearance 19
+int styleable Toolbar_subtitleTextColor 20
+int styleable Toolbar_title 21
+int styleable Toolbar_titleMargin 22
+int styleable Toolbar_titleMarginBottom 23
+int styleable Toolbar_titleMarginEnd 24
+int styleable Toolbar_titleMarginStart 25
+int styleable Toolbar_titleMarginTop 26
+int styleable Toolbar_titleMargins 27
+int styleable Toolbar_titleTextAppearance 28
+int styleable Toolbar_titleTextColor 29
+int[] styleable View { 0x10100da, 0x1010000, 0x0, 0x0, 0x0 }
+int styleable View_android_focusable 0
+int styleable View_android_theme 1
+int styleable View_paddingEnd 2
+int styleable View_paddingStart 3
+int styleable View_theme 4
+int[] styleable ViewBackgroundHelper { 0x10100d4, 0x0, 0x0 }
+int styleable ViewBackgroundHelper_android_background 0
+int styleable ViewBackgroundHelper_backgroundTint 1
+int styleable ViewBackgroundHelper_backgroundTintMode 2
+int[] styleable ViewStubCompat { 0x10100d0, 0x10100f3, 0x10100f2 }
+int styleable ViewStubCompat_android_id 0
+int styleable ViewStubCompat_android_inflatedId 1
+int styleable ViewStubCompat_android_layout 2
+int xml rn_dev_preferences 0x0
diff --git a/node_modules/react-native-maps/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/react-native-maps/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..c6abc04
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/react-native-maps/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..5d227e4
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/react-native-maps/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..79634bc
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/assets"/><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/build/intermediates/shader_assets/debug/out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/debug/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties b/node_modules/react-native-maps/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..08062e0
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Feb 28 17:09:25 ICT 2024
diff --git a/node_modules/react-native-maps/android/build/intermediates/incremental/packageDebugResources/merger.xml b/node_modules/react-native-maps/android/build/intermediates/incremental/packageDebugResources/merger.xml
new file mode 100644
index 0000000..d0348ab
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/incremental/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/res"/><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/build/generated/res/rs/debug"/><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/res"/><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/build/generated/res/rs/debug"/><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/debug/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCallout.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCallout.class
new file mode 100644
index 0000000..fd8933c
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCallout.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCalloutManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCalloutManager.class
new file mode 100644
index 0000000..8f6b462
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCalloutManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCircle.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCircle.class
new file mode 100644
index 0000000..f4324d2
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCircle.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCircleManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCircleManager.class
new file mode 100644
index 0000000..055810a
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapCircleManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapFeature.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapFeature.class
new file mode 100644
index 0000000..b8d0011
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapFeature.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolyline$AirMapGradientPolylineProvider.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolyline$AirMapGradientPolylineProvider.class
new file mode 100644
index 0000000..6b6998f
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolyline$AirMapGradientPolylineProvider.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolyline$MutPoint.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolyline$MutPoint.class
new file mode 100644
index 0000000..5503068
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolyline$MutPoint.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolyline.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolyline.class
new file mode 100644
index 0000000..e9d6bd2
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolyline.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolylineManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolylineManager.class
new file mode 100644
index 0000000..24d632a
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapGradientPolylineManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapHeatmap.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapHeatmap.class
new file mode 100644
index 0000000..31202c8
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapHeatmap.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapHeatmapManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapHeatmapManager.class
new file mode 100644
index 0000000..7d6d3fb
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapHeatmapManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLiteManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLiteManager.class
new file mode 100644
index 0000000..06d13f5
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLiteManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLocalTile$AIRMapLocalTileProvider.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLocalTile$AIRMapLocalTileProvider.class
new file mode 100644
index 0000000..79d1cc6
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLocalTile$AIRMapLocalTileProvider.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLocalTile.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLocalTile.class
new file mode 100644
index 0000000..3b764b9
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLocalTile.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLocalTileManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLocalTileManager.class
new file mode 100644
index 0000000..3d47fdf
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapLocalTileManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapManager.class
new file mode 100644
index 0000000..7966f67
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarker$1.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarker$1.class
new file mode 100644
index 0000000..0fd5286
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarker$1.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarker$2.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarker$2.class
new file mode 100644
index 0000000..e2d919a
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarker$2.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarker.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarker.class
new file mode 100644
index 0000000..79ef653
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarker.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarkerManager$AirMapMarkerSharedIcon.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarkerManager$AirMapMarkerSharedIcon.class
new file mode 100644
index 0000000..7f21877
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarkerManager$AirMapMarkerSharedIcon.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarkerManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarkerManager.class
new file mode 100644
index 0000000..fc4c98a
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapMarkerManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$1$1.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$1$1.class
new file mode 100644
index 0000000..f18e0c7
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$1$1.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$1.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$1.class
new file mode 100644
index 0000000..019c3a3
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$1.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$2.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$2.class
new file mode 100644
index 0000000..9ea1eb4
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$2.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$3.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$3.class
new file mode 100644
index 0000000..cf23bbe
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$3.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$4.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$4.class
new file mode 100644
index 0000000..6428883
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$4.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$5.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$5.class
new file mode 100644
index 0000000..89f7b37
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$5.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$6.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$6.class
new file mode 100644
index 0000000..2ac1bbd
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$6.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$7$1.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$7$1.class
new file mode 100644
index 0000000..4428bef
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$7$1.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$7.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$7.class
new file mode 100644
index 0000000..2137dc8
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule$7.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule.class
new file mode 100644
index 0000000..f71d70d
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapModule.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapOverlay.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapOverlay.class
new file mode 100644
index 0000000..34ebab7
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapOverlay.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapOverlayManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapOverlayManager.class
new file mode 100644
index 0000000..4102dc5
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapOverlayManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolygon.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolygon.class
new file mode 100644
index 0000000..69cd636
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolygon.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolygonManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolygonManager.class
new file mode 100644
index 0000000..cbb7ebe
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolygonManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolyline.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolyline.class
new file mode 100644
index 0000000..16757d2
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolyline.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolylineManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolylineManager.class
new file mode 100644
index 0000000..f96ff39
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapPolylineManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapTileProvider$AIRMapUrlTileProvider.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapTileProvider$AIRMapUrlTileProvider.class
new file mode 100644
index 0000000..4f0bc53
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapTileProvider$AIRMapUrlTileProvider.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapTileProvider.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapTileProvider.class
new file mode 100644
index 0000000..8114b30
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapTileProvider.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapTileWorker.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapTileWorker.class
new file mode 100644
index 0000000..f43a810
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapTileWorker.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapUrlTile.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapUrlTile.class
new file mode 100644
index 0000000..97a60b4
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapUrlTile.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapUrlTileManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapUrlTileManager.class
new file mode 100644
index 0000000..aa5ed63
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapUrlTileManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$1.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$1.class
new file mode 100644
index 0000000..4ff1c03
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$1.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$10.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$10.class
new file mode 100644
index 0000000..968db34
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$10.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$11.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$11.class
new file mode 100644
index 0000000..a60b0ec
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$11.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$12.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$12.class
new file mode 100644
index 0000000..b915a52
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$12.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$13.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$13.class
new file mode 100644
index 0000000..f67985d
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$13.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$14.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$14.class
new file mode 100644
index 0000000..916af4c
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$14.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$15.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$15.class
new file mode 100644
index 0000000..039cb1b
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$15.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$16.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$16.class
new file mode 100644
index 0000000..142b695
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$16.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$17.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$17.class
new file mode 100644
index 0000000..642d1e4
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$17.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$2.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$2.class
new file mode 100644
index 0000000..c967d81
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$2.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$3.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$3.class
new file mode 100644
index 0000000..214b8d8
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$3.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$4.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$4.class
new file mode 100644
index 0000000..635e611
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$4.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$5.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$5.class
new file mode 100644
index 0000000..2d3e589
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$5.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$6.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$6.class
new file mode 100644
index 0000000..0917b9b
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$6.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$7.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$7.class
new file mode 100644
index 0000000..d55ba60
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$7.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$8.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$8.class
new file mode 100644
index 0000000..3c7a60e
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$8.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$9.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$9.class
new file mode 100644
index 0000000..943b7fc
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView$9.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView.class
new file mode 100644
index 0000000..5acdbc2
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapView.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTile$AIRMapGSUrlTileProvider$AIRMapWMSTileProvider.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTile$AIRMapGSUrlTileProvider$AIRMapWMSTileProvider.class
new file mode 100644
index 0000000..c5ee55d
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTile$AIRMapGSUrlTileProvider$AIRMapWMSTileProvider.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTile$AIRMapGSUrlTileProvider.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTile$AIRMapGSUrlTileProvider.class
new file mode 100644
index 0000000..7677b2c
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTile$AIRMapGSUrlTileProvider.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTile.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTile.class
new file mode 100644
index 0000000..2effeca
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTile.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTileManager.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTileManager.class
new file mode 100644
index 0000000..c8024bb
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/AirMapWMSTileManager.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/BuildConfig.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/BuildConfig.class
new file mode 100644
index 0000000..66f7189
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/BuildConfig.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FileUtil.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FileUtil.class
new file mode 100644
index 0000000..028813b
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FileUtil.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FusedLocationSource$1.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FusedLocationSource$1.class
new file mode 100644
index 0000000..6987b49
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FusedLocationSource$1.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FusedLocationSource$2.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FusedLocationSource$2.class
new file mode 100644
index 0000000..7431ba6
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FusedLocationSource$2.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FusedLocationSource.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FusedLocationSource.class
new file mode 100644
index 0000000..8068231
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/FusedLocationSource.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageReadable.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageReadable.class
new file mode 100644
index 0000000..6c9194c
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageReadable.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageReader$1.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageReader$1.class
new file mode 100644
index 0000000..b31addf
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageReader$1.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageReader.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageReader.class
new file mode 100644
index 0000000..1d6318f
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageReader.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageUtil.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageUtil.class
new file mode 100644
index 0000000..849f33d
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ImageUtil.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/LatLngBoundsUtils.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/LatLngBoundsUtils.class
new file mode 100644
index 0000000..4acb857
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/LatLngBoundsUtils.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/MapsPackage.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/MapsPackage.class
new file mode 100644
index 0000000..7360b6a
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/MapsPackage.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/RegionChangeEvent.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/RegionChangeEvent.class
new file mode 100644
index 0000000..325250c
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/RegionChangeEvent.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/SizeReportingShadowNode.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/SizeReportingShadowNode.class
new file mode 100644
index 0000000..e10053e
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/SizeReportingShadowNode.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ViewAttacherGroup.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ViewAttacherGroup.class
new file mode 100644
index 0000000..3c9b69d
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ViewAttacherGroup.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ViewChangesTracker$1.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ViewChangesTracker$1.class
new file mode 100644
index 0000000..307742d
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ViewChangesTracker$1.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ViewChangesTracker.class b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ViewChangesTracker.class
new file mode 100644
index 0000000..7072844
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/javac/debug/classes/com/airbnb/android/react/maps/ViewChangesTracker.class differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/react-native-maps/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/react-native-maps/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-maps/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..380d2e4
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.airbnb.android.react.maps" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="23"
+6-->/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..f32c825
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.airbnb.android.react.maps" >
+
+    <uses-sdk
+        android:minSdkVersion="23"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-maps/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/react-native-maps/android/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..a7f4f07
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.airbnb.android.react.maps",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-maps/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/react-native-maps/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..06dfa6d
Binary files /dev/null and b/node_modules/react-native-maps/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-maps/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/react-native-maps/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..22f1d37
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1,1597 @@
+com.airbnb.android.react.maps
+anim abc_fade_in
+anim abc_fade_out
+anim abc_grow_fade_in_from_bottom
+anim abc_popup_enter
+anim abc_popup_exit
+anim abc_shrink_fade_out_from_bottom
+anim abc_slide_in_bottom
+anim abc_slide_in_top
+anim abc_slide_out_bottom
+anim abc_slide_out_top
+anim abc_tooltip_enter
+anim abc_tooltip_exit
+anim btn_checkbox_to_checked_box_inner_merged_animation
+anim btn_checkbox_to_checked_box_outer_merged_animation
+anim btn_checkbox_to_checked_icon_null_animation
+anim btn_checkbox_to_unchecked_box_inner_merged_animation
+anim btn_checkbox_to_unchecked_check_path_merged_animation
+anim btn_checkbox_to_unchecked_icon_null_animation
+anim btn_radio_to_off_mtrl_dot_group_animation
+anim btn_radio_to_off_mtrl_ring_outer_animation
+anim btn_radio_to_off_mtrl_ring_outer_path_animation
+anim btn_radio_to_on_mtrl_dot_group_animation
+anim btn_radio_to_on_mtrl_ring_outer_animation
+anim btn_radio_to_on_mtrl_ring_outer_path_animation
+anim catalyst_fade_in
+anim catalyst_fade_out
+anim catalyst_push_up_in
+anim catalyst_push_up_out
+anim catalyst_slide_down
+anim catalyst_slide_up
+anim fragment_fast_out_extra_slow_in
+animator fragment_close_enter
+animator fragment_close_exit
+animator fragment_fade_enter
+animator fragment_fade_exit
+animator fragment_open_enter
+animator fragment_open_exit
+attr actionBarDivider
+attr actionBarItemBackground
+attr actionBarPopupTheme
+attr actionBarSize
+attr actionBarSplitStyle
+attr actionBarStyle
+attr actionBarTabBarStyle
+attr actionBarTabStyle
+attr actionBarTabTextStyle
+attr actionBarTheme
+attr actionBarWidgetTheme
+attr actionButtonStyle
+attr actionDropDownStyle
+attr actionLayout
+attr actionMenuTextAppearance
+attr actionMenuTextColor
+attr actionModeBackground
+attr actionModeCloseButtonStyle
+attr actionModeCloseContentDescription
+attr actionModeCloseDrawable
+attr actionModeCopyDrawable
+attr actionModeCutDrawable
+attr actionModeFindDrawable
+attr actionModePasteDrawable
+attr actionModePopupWindowStyle
+attr actionModeSelectAllDrawable
+attr actionModeShareDrawable
+attr actionModeSplitBackground
+attr actionModeStyle
+attr actionModeTheme
+attr actionModeWebSearchDrawable
+attr actionOverflowButtonStyle
+attr actionOverflowMenuStyle
+attr actionProviderClass
+attr actionViewClass
+attr activityChooserViewStyle
+attr actualImageResource
+attr actualImageScaleType
+attr actualImageUri
+attr alertDialogButtonGroupStyle
+attr alertDialogCenterButtons
+attr alertDialogStyle
+attr alertDialogTheme
+attr allowStacking
+attr alpha
+attr alphabeticModifiers
+attr ambientEnabled
+attr arrowHeadLength
+attr arrowShaftLength
+attr autoCompleteTextViewStyle
+attr autoSizeMaxTextSize
+attr autoSizeMinTextSize
+attr autoSizePresetSizes
+attr autoSizeStepGranularity
+attr autoSizeTextType
+attr autofillInlineSuggestionChip
+attr autofillInlineSuggestionEndIconStyle
+attr autofillInlineSuggestionStartIconStyle
+attr autofillInlineSuggestionSubtitle
+attr autofillInlineSuggestionTitle
+attr background
+attr backgroundColor
+attr backgroundImage
+attr backgroundSplit
+attr backgroundStacked
+attr backgroundTint
+attr backgroundTintMode
+attr barLength
+attr borderlessButtonStyle
+attr buttonBarButtonStyle
+attr buttonBarNegativeButtonStyle
+attr buttonBarNeutralButtonStyle
+attr buttonBarPositiveButtonStyle
+attr buttonBarStyle
+attr buttonCompat
+attr buttonGravity
+attr buttonIconDimen
+attr buttonPanelSideLayout
+attr buttonSize
+attr buttonStyle
+attr buttonStyleSmall
+attr buttonTint
+attr buttonTintMode
+attr cameraBearing
+attr cameraMaxZoomPreference
+attr cameraMinZoomPreference
+attr cameraTargetLat
+attr cameraTargetLng
+attr cameraTilt
+attr cameraZoom
+attr checkMarkCompat
+attr checkMarkTint
+attr checkMarkTintMode
+attr checkboxStyle
+attr checkedTextViewStyle
+attr circleCrop
+attr closeIcon
+attr closeItemLayout
+attr collapseContentDescription
+attr collapseIcon
+attr color
+attr colorAccent
+attr colorBackgroundFloating
+attr colorButtonNormal
+attr colorControlActivated
+attr colorControlHighlight
+attr colorControlNormal
+attr colorError
+attr colorPrimary
+attr colorPrimaryDark
+attr colorScheme
+attr colorSwitchThumbNormal
+attr commitIcon
+attr contentDescription
+attr contentInsetEnd
+attr contentInsetEndWithActions
+attr contentInsetLeft
+attr contentInsetRight
+attr contentInsetStart
+attr contentInsetStartWithNavigation
+attr controlBackground
+attr customNavigationLayout
+attr defaultQueryHint
+attr dialogCornerRadius
+attr dialogPreferredPadding
+attr dialogTheme
+attr displayOptions
+attr divider
+attr dividerHorizontal
+attr dividerPadding
+attr dividerVertical
+attr drawableBottomCompat
+attr drawableEndCompat
+attr drawableLeftCompat
+attr drawableRightCompat
+attr drawableSize
+attr drawableStartCompat
+attr drawableTint
+attr drawableTintMode
+attr drawableTopCompat
+attr drawerArrowStyle
+attr dropDownListViewStyle
+attr dropdownListPreferredItemHeight
+attr editTextBackground
+attr editTextColor
+attr editTextStyle
+attr elevation
+attr emojiCompatEnabled
+attr expandActivityOverflowButtonDrawable
+attr fadeDuration
+attr failureImage
+attr failureImageScaleType
+attr firstBaselineToTopHeight
+attr font
+attr fontFamily
+attr fontProviderAuthority
+attr fontProviderCerts
+attr fontProviderFetchStrategy
+attr fontProviderFetchTimeout
+attr fontProviderPackage
+attr fontProviderQuery
+attr fontProviderSystemFontFamily
+attr fontStyle
+attr fontVariationSettings
+attr fontWeight
+attr gapBetweenBars
+attr goIcon
+attr height
+attr hideOnContentScroll
+attr homeAsUpIndicator
+attr homeLayout
+attr icon
+attr iconTint
+attr iconTintMode
+attr iconifiedByDefault
+attr imageAspectRatio
+attr imageAspectRatioAdjust
+attr imageButtonStyle
+attr indeterminateProgressStyle
+attr initialActivityCount
+attr isAutofillInlineSuggestionTheme
+attr isLightTheme
+attr itemPadding
+attr lStar
+attr lastBaselineToBottomHeight
+attr latLngBoundsNorthEastLatitude
+attr latLngBoundsNorthEastLongitude
+attr latLngBoundsSouthWestLatitude
+attr latLngBoundsSouthWestLongitude
+attr layout
+attr lineHeight
+attr listChoiceBackgroundIndicator
+attr listChoiceIndicatorMultipleAnimated
+attr listChoiceIndicatorSingleAnimated
+attr listDividerAlertDialog
+attr listItemLayout
+attr listLayout
+attr listMenuViewStyle
+attr listPopupWindowStyle
+attr listPreferredItemHeight
+attr listPreferredItemHeightLarge
+attr listPreferredItemHeightSmall
+attr listPreferredItemPaddingEnd
+attr listPreferredItemPaddingLeft
+attr listPreferredItemPaddingRight
+attr listPreferredItemPaddingStart
+attr liteMode
+attr logo
+attr logoDescription
+attr mapId
+attr mapType
+attr maxButtonHeight
+attr measureWithLargestChild
+attr menu
+attr multiChoiceItemLayout
+attr navigationContentDescription
+attr navigationIcon
+attr navigationMode
+attr nestedScrollViewStyle
+attr numericModifiers
+attr overlapAnchor
+attr overlayImage
+attr paddingBottomNoButtons
+attr paddingEnd
+attr paddingStart
+attr paddingTopNoTitle
+attr panelBackground
+attr panelMenuListTheme
+attr panelMenuListWidth
+attr placeholderImage
+attr placeholderImageScaleType
+attr popupMenuStyle
+attr popupTheme
+attr popupWindowStyle
+attr preserveIconSpacing
+attr pressedStateOverlayImage
+attr progressBarAutoRotateInterval
+attr progressBarImage
+attr progressBarImageScaleType
+attr progressBarPadding
+attr progressBarStyle
+attr queryBackground
+attr queryHint
+attr queryPatterns
+attr radioButtonStyle
+attr ratingBarStyle
+attr ratingBarStyleIndicator
+attr ratingBarStyleSmall
+attr retryImage
+attr retryImageScaleType
+attr roundAsCircle
+attr roundBottomEnd
+attr roundBottomLeft
+attr roundBottomRight
+attr roundBottomStart
+attr roundTopEnd
+attr roundTopLeft
+attr roundTopRight
+attr roundTopStart
+attr roundWithOverlayColor
+attr roundedCornerRadius
+attr roundingBorderColor
+attr roundingBorderPadding
+attr roundingBorderWidth
+attr scopeUris
+attr searchHintIcon
+attr searchIcon
+attr searchViewStyle
+attr seekBarStyle
+attr selectableItemBackground
+attr selectableItemBackgroundBorderless
+attr shortcutMatchRequired
+attr showAsAction
+attr showDividers
+attr showText
+attr showTitle
+attr singleChoiceItemLayout
+attr spinBars
+attr spinnerDropDownItemStyle
+attr spinnerStyle
+attr splitTrack
+attr srcCompat
+attr state_above_anchor
+attr subMenuArrow
+attr submitBackground
+attr subtitle
+attr subtitleTextAppearance
+attr subtitleTextColor
+attr subtitleTextStyle
+attr suggestionRowLayout
+attr switchMinWidth
+attr switchPadding
+attr switchStyle
+attr switchTextAppearance
+attr textAllCaps
+attr textAppearanceLargePopupMenu
+attr textAppearanceListItem
+attr textAppearanceListItemSecondary
+attr textAppearanceListItemSmall
+attr textAppearancePopupMenuHeader
+attr textAppearanceSearchResultSubtitle
+attr textAppearanceSearchResultTitle
+attr textAppearanceSmallPopupMenu
+attr textColorAlertDialogListItem
+attr textColorSearchUrl
+attr textLocale
+attr theme
+attr thickness
+attr thumbTextPadding
+attr thumbTint
+attr thumbTintMode
+attr tickMark
+attr tickMarkTint
+attr tickMarkTintMode
+attr tint
+attr tintMode
+attr title
+attr titleMargin
+attr titleMarginBottom
+attr titleMarginEnd
+attr titleMarginStart
+attr titleMarginTop
+attr titleMargins
+attr titleTextAppearance
+attr titleTextColor
+attr titleTextStyle
+attr toolbarNavigationButtonStyle
+attr toolbarStyle
+attr tooltipForegroundColor
+attr tooltipFrameBackground
+attr tooltipText
+attr track
+attr trackTint
+attr trackTintMode
+attr ttcIndex
+attr uiCompass
+attr uiMapToolbar
+attr uiRotateGestures
+attr uiScrollGestures
+attr uiScrollGesturesDuringRotateOrZoom
+attr uiTiltGestures
+attr uiZoomControls
+attr uiZoomGestures
+attr useViewLifecycle
+attr viewAspectRatio
+attr viewInflaterClass
+attr voiceIcon
+attr windowActionBar
+attr windowActionBarOverlay
+attr windowActionModeOverlay
+attr windowFixedHeightMajor
+attr windowFixedHeightMinor
+attr windowFixedWidthMajor
+attr windowFixedWidthMinor
+attr windowMinWidthMajor
+attr windowMinWidthMinor
+attr windowNoTitle
+attr zOrderOnTop
+bool abc_action_bar_embed_tabs
+bool abc_config_actionMenuItemAllCaps
+bool enable_system_alarm_service_default
+bool enable_system_foreground_service_default
+bool enable_system_job_service_default
+bool workmanager_test_configuration
+color abc_background_cache_hint_selector_material_dark
+color abc_background_cache_hint_selector_material_light
+color abc_btn_colored_borderless_text_material
+color abc_btn_colored_text_material
+color abc_color_highlight_material
+color abc_decor_view_status_guard
+color abc_decor_view_status_guard_light
+color abc_hint_foreground_material_dark
+color abc_hint_foreground_material_light
+color abc_primary_text_disable_only_material_dark
+color abc_primary_text_disable_only_material_light
+color abc_primary_text_material_dark
+color abc_primary_text_material_light
+color abc_search_url_text
+color abc_search_url_text_normal
+color abc_search_url_text_pressed
+color abc_search_url_text_selected
+color abc_secondary_text_material_dark
+color abc_secondary_text_material_light
+color abc_tint_btn_checkable
+color abc_tint_default
+color abc_tint_edittext
+color abc_tint_seek_thumb
+color abc_tint_spinner
+color abc_tint_switch_track
+color accent_material_dark
+color accent_material_light
+color androidx_core_ripple_material_light
+color androidx_core_secondary_text_default_material_light
+color background_floating_material_dark
+color background_floating_material_light
+color background_material_dark
+color background_material_light
+color bright_foreground_disabled_material_dark
+color bright_foreground_disabled_material_light
+color bright_foreground_inverse_material_dark
+color bright_foreground_inverse_material_light
+color bright_foreground_material_dark
+color bright_foreground_material_light
+color button_material_dark
+color button_material_light
+color catalyst_logbox_background
+color catalyst_redbox_background
+color common_google_signin_btn_text_dark
+color common_google_signin_btn_text_dark_default
+color common_google_signin_btn_text_dark_disabled
+color common_google_signin_btn_text_dark_focused
+color common_google_signin_btn_text_dark_pressed
+color common_google_signin_btn_text_light
+color common_google_signin_btn_text_light_default
+color common_google_signin_btn_text_light_disabled
+color common_google_signin_btn_text_light_focused
+color common_google_signin_btn_text_light_pressed
+color common_google_signin_btn_tint
+color dim_foreground_disabled_material_dark
+color dim_foreground_disabled_material_light
+color dim_foreground_material_dark
+color dim_foreground_material_light
+color error_color_material_dark
+color error_color_material_light
+color foreground_material_dark
+color foreground_material_light
+color highlighted_text_material_dark
+color highlighted_text_material_light
+color material_blue_grey_800
+color material_blue_grey_900
+color material_blue_grey_950
+color material_deep_teal_200
+color material_deep_teal_500
+color material_grey_100
+color material_grey_300
+color material_grey_50
+color material_grey_600
+color material_grey_800
+color material_grey_850
+color material_grey_900
+color notification_action_color_filter
+color notification_icon_bg_color
+color primary_dark_material_dark
+color primary_dark_material_light
+color primary_material_dark
+color primary_material_light
+color primary_text_default_material_dark
+color primary_text_default_material_light
+color primary_text_disabled_material_dark
+color primary_text_disabled_material_light
+color ripple_material_dark
+color ripple_material_light
+color secondary_text_default_material_dark
+color secondary_text_default_material_light
+color secondary_text_disabled_material_dark
+color secondary_text_disabled_material_light
+color switch_thumb_disabled_material_dark
+color switch_thumb_disabled_material_light
+color switch_thumb_material_dark
+color switch_thumb_material_light
+color switch_thumb_normal_material_dark
+color switch_thumb_normal_material_light
+color tooltip_background_dark
+color tooltip_background_light
+dimen abc_action_bar_content_inset_material
+dimen abc_action_bar_content_inset_with_nav
+dimen abc_action_bar_default_height_material
+dimen abc_action_bar_default_padding_end_material
+dimen abc_action_bar_default_padding_start_material
+dimen abc_action_bar_elevation_material
+dimen abc_action_bar_icon_vertical_padding_material
+dimen abc_action_bar_overflow_padding_end_material
+dimen abc_action_bar_overflow_padding_start_material
+dimen abc_action_bar_stacked_max_height
+dimen abc_action_bar_stacked_tab_max_width
+dimen abc_action_bar_subtitle_bottom_margin_material
+dimen abc_action_bar_subtitle_top_margin_material
+dimen abc_action_button_min_height_material
+dimen abc_action_button_min_width_material
+dimen abc_action_button_min_width_overflow_material
+dimen abc_alert_dialog_button_bar_height
+dimen abc_alert_dialog_button_dimen
+dimen abc_button_inset_horizontal_material
+dimen abc_button_inset_vertical_material
+dimen abc_button_padding_horizontal_material
+dimen abc_button_padding_vertical_material
+dimen abc_cascading_menus_min_smallest_width
+dimen abc_config_prefDialogWidth
+dimen abc_control_corner_material
+dimen abc_control_inset_material
+dimen abc_control_padding_material
+dimen abc_dialog_corner_radius_material
+dimen abc_dialog_fixed_height_major
+dimen abc_dialog_fixed_height_minor
+dimen abc_dialog_fixed_width_major
+dimen abc_dialog_fixed_width_minor
+dimen abc_dialog_list_padding_bottom_no_buttons
+dimen abc_dialog_list_padding_top_no_title
+dimen abc_dialog_min_width_major
+dimen abc_dialog_min_width_minor
+dimen abc_dialog_padding_material
+dimen abc_dialog_padding_top_material
+dimen abc_dialog_title_divider_material
+dimen abc_disabled_alpha_material_dark
+dimen abc_disabled_alpha_material_light
+dimen abc_dropdownitem_icon_width
+dimen abc_dropdownitem_text_padding_left
+dimen abc_dropdownitem_text_padding_right
+dimen abc_edit_text_inset_bottom_material
+dimen abc_edit_text_inset_horizontal_material
+dimen abc_edit_text_inset_top_material
+dimen abc_floating_window_z
+dimen abc_list_item_height_large_material
+dimen abc_list_item_height_material
+dimen abc_list_item_height_small_material
+dimen abc_list_item_padding_horizontal_material
+dimen abc_panel_menu_list_width
+dimen abc_progress_bar_height_material
+dimen abc_search_view_preferred_height
+dimen abc_search_view_preferred_width
+dimen abc_seekbar_track_background_height_material
+dimen abc_seekbar_track_progress_height_material
+dimen abc_select_dialog_padding_start_material
+dimen abc_star_big
+dimen abc_star_medium
+dimen abc_star_small
+dimen abc_switch_padding
+dimen abc_text_size_body_1_material
+dimen abc_text_size_body_2_material
+dimen abc_text_size_button_material
+dimen abc_text_size_caption_material
+dimen abc_text_size_display_1_material
+dimen abc_text_size_display_2_material
+dimen abc_text_size_display_3_material
+dimen abc_text_size_display_4_material
+dimen abc_text_size_headline_material
+dimen abc_text_size_large_material
+dimen abc_text_size_medium_material
+dimen abc_text_size_menu_header_material
+dimen abc_text_size_menu_material
+dimen abc_text_size_small_material
+dimen abc_text_size_subhead_material
+dimen abc_text_size_subtitle_material_toolbar
+dimen abc_text_size_title_material
+dimen abc_text_size_title_material_toolbar
+dimen autofill_inline_suggestion_icon_size
+dimen compat_button_inset_horizontal_material
+dimen compat_button_inset_vertical_material
+dimen compat_button_padding_horizontal_material
+dimen compat_button_padding_vertical_material
+dimen compat_control_corner_material
+dimen compat_notification_large_icon_max_height
+dimen compat_notification_large_icon_max_width
+dimen disabled_alpha_material_dark
+dimen disabled_alpha_material_light
+dimen highlight_alpha_material_colored
+dimen highlight_alpha_material_dark
+dimen highlight_alpha_material_light
+dimen hint_alpha_material_dark
+dimen hint_alpha_material_light
+dimen hint_pressed_alpha_material_dark
+dimen hint_pressed_alpha_material_light
+dimen notification_action_icon_size
+dimen notification_action_text_size
+dimen notification_big_circle_margin
+dimen notification_content_margin_start
+dimen notification_large_icon_height
+dimen notification_large_icon_width
+dimen notification_main_column_padding_top
+dimen notification_media_narrow_margin
+dimen notification_right_icon_size
+dimen notification_right_side_padding_top
+dimen notification_small_icon_background_padding
+dimen notification_small_icon_size_as_large
+dimen notification_subtext_size
+dimen notification_top_pad
+dimen notification_top_pad_large_text
+dimen tooltip_corner_radius
+dimen tooltip_horizontal_padding
+dimen tooltip_margin
+dimen tooltip_precise_anchor_extra_offset
+dimen tooltip_precise_anchor_threshold
+dimen tooltip_vertical_padding
+dimen tooltip_y_offset_non_touch
+dimen tooltip_y_offset_touch
+drawable abc_ab_share_pack_mtrl_alpha
+drawable abc_action_bar_item_background_material
+drawable abc_btn_borderless_material
+drawable abc_btn_check_material
+drawable abc_btn_check_material_anim
+drawable abc_btn_check_to_on_mtrl_000
+drawable abc_btn_check_to_on_mtrl_015
+drawable abc_btn_colored_material
+drawable abc_btn_default_mtrl_shape
+drawable abc_btn_radio_material
+drawable abc_btn_radio_material_anim
+drawable abc_btn_radio_to_on_mtrl_000
+drawable abc_btn_radio_to_on_mtrl_015
+drawable abc_btn_switch_to_on_mtrl_00001
+drawable abc_btn_switch_to_on_mtrl_00012
+drawable abc_cab_background_internal_bg
+drawable abc_cab_background_top_material
+drawable abc_cab_background_top_mtrl_alpha
+drawable abc_control_background_material
+drawable abc_dialog_material_background
+drawable abc_edit_text_material
+drawable abc_ic_ab_back_material
+drawable abc_ic_arrow_drop_right_black_24dp
+drawable abc_ic_clear_material
+drawable abc_ic_commit_search_api_mtrl_alpha
+drawable abc_ic_go_search_api_material
+drawable abc_ic_menu_copy_mtrl_am_alpha
+drawable abc_ic_menu_cut_mtrl_alpha
+drawable abc_ic_menu_overflow_material
+drawable abc_ic_menu_paste_mtrl_am_alpha
+drawable abc_ic_menu_selectall_mtrl_alpha
+drawable abc_ic_menu_share_mtrl_alpha
+drawable abc_ic_search_api_material
+drawable abc_ic_voice_search_api_material
+drawable abc_item_background_holo_dark
+drawable abc_item_background_holo_light
+drawable abc_list_divider_material
+drawable abc_list_divider_mtrl_alpha
+drawable abc_list_focused_holo
+drawable abc_list_longpressed_holo
+drawable abc_list_pressed_holo_dark
+drawable abc_list_pressed_holo_light
+drawable abc_list_selector_background_transition_holo_dark
+drawable abc_list_selector_background_transition_holo_light
+drawable abc_list_selector_disabled_holo_dark
+drawable abc_list_selector_disabled_holo_light
+drawable abc_list_selector_holo_dark
+drawable abc_list_selector_holo_light
+drawable abc_menu_hardkey_panel_mtrl_mult
+drawable abc_popup_background_mtrl_mult
+drawable abc_ratingbar_indicator_material
+drawable abc_ratingbar_material
+drawable abc_ratingbar_small_material
+drawable abc_scrubber_control_off_mtrl_alpha
+drawable abc_scrubber_control_to_pressed_mtrl_000
+drawable abc_scrubber_control_to_pressed_mtrl_005
+drawable abc_scrubber_primary_mtrl_alpha
+drawable abc_scrubber_track_mtrl_alpha
+drawable abc_seekbar_thumb_material
+drawable abc_seekbar_tick_mark_material
+drawable abc_seekbar_track_material
+drawable abc_spinner_mtrl_am_alpha
+drawable abc_spinner_textfield_background_material
+drawable abc_star_black_48dp
+drawable abc_star_half_black_48dp
+drawable abc_switch_thumb_material
+drawable abc_switch_track_mtrl_alpha
+drawable abc_tab_indicator_material
+drawable abc_tab_indicator_mtrl_alpha
+drawable abc_text_cursor_material
+drawable abc_text_select_handle_left_mtrl
+drawable abc_text_select_handle_middle_mtrl
+drawable abc_text_select_handle_right_mtrl
+drawable abc_textfield_activated_mtrl_alpha
+drawable abc_textfield_default_mtrl_alpha
+drawable abc_textfield_search_activated_mtrl_alpha
+drawable abc_textfield_search_default_mtrl_alpha
+drawable abc_textfield_search_material
+drawable abc_vector_test
+drawable amu_bubble_mask
+drawable amu_bubble_shadow
+drawable autofill_inline_suggestion_chip_background
+drawable btn_checkbox_checked_mtrl
+drawable btn_checkbox_checked_to_unchecked_mtrl_animation
+drawable btn_checkbox_unchecked_mtrl
+drawable btn_checkbox_unchecked_to_checked_mtrl_animation
+drawable btn_radio_off_mtrl
+drawable btn_radio_off_to_on_mtrl_animation
+drawable btn_radio_on_mtrl
+drawable btn_radio_on_to_off_mtrl_animation
+drawable common_full_open_on_phone
+drawable common_google_signin_btn_icon_dark
+drawable common_google_signin_btn_icon_dark_disabled
+drawable common_google_signin_btn_icon_dark_focused
+drawable common_google_signin_btn_icon_dark_normal
+drawable common_google_signin_btn_icon_dark_normal_background
+drawable common_google_signin_btn_icon_dark_pressed
+drawable common_google_signin_btn_icon_disabled
+drawable common_google_signin_btn_icon_light
+drawable common_google_signin_btn_icon_light_disabled
+drawable common_google_signin_btn_icon_light_focused
+drawable common_google_signin_btn_icon_light_normal
+drawable common_google_signin_btn_icon_light_normal_background
+drawable common_google_signin_btn_icon_light_pressed
+drawable common_google_signin_btn_text_dark
+drawable common_google_signin_btn_text_dark_disabled
+drawable common_google_signin_btn_text_dark_focused
+drawable common_google_signin_btn_text_dark_normal
+drawable common_google_signin_btn_text_dark_normal_background
+drawable common_google_signin_btn_text_dark_pressed
+drawable common_google_signin_btn_text_disabled
+drawable common_google_signin_btn_text_light
+drawable common_google_signin_btn_text_light_disabled
+drawable common_google_signin_btn_text_light_focused
+drawable common_google_signin_btn_text_light_normal
+drawable common_google_signin_btn_text_light_normal_background
+drawable common_google_signin_btn_text_light_pressed
+drawable common_ic_googleplayservices
+drawable googleg_disabled_color_18
+drawable googleg_standard_color_18
+drawable notification_action_background
+drawable notification_bg
+drawable notification_bg_low
+drawable notification_bg_low_normal
+drawable notification_bg_low_pressed
+drawable notification_bg_normal
+drawable notification_bg_normal_pressed
+drawable notification_icon_background
+drawable notification_template_icon_bg
+drawable notification_template_icon_low_bg
+drawable notification_tile_bg
+drawable notify_panel_notification_icon_bg
+drawable redbox_top_border_background
+drawable test_level_drawable
+drawable tooltip_frame_dark
+drawable tooltip_frame_light
+id accessibility_action_clickable_span
+id accessibility_actions
+id accessibility_collection
+id accessibility_collection_item
+id accessibility_custom_action_0
+id accessibility_custom_action_1
+id accessibility_custom_action_10
+id accessibility_custom_action_11
+id accessibility_custom_action_12
+id accessibility_custom_action_13
+id accessibility_custom_action_14
+id accessibility_custom_action_15
+id accessibility_custom_action_16
+id accessibility_custom_action_17
+id accessibility_custom_action_18
+id accessibility_custom_action_19
+id accessibility_custom_action_2
+id accessibility_custom_action_20
+id accessibility_custom_action_21
+id accessibility_custom_action_22
+id accessibility_custom_action_23
+id accessibility_custom_action_24
+id accessibility_custom_action_25
+id accessibility_custom_action_26
+id accessibility_custom_action_27
+id accessibility_custom_action_28
+id accessibility_custom_action_29
+id accessibility_custom_action_3
+id accessibility_custom_action_30
+id accessibility_custom_action_31
+id accessibility_custom_action_4
+id accessibility_custom_action_5
+id accessibility_custom_action_6
+id accessibility_custom_action_7
+id accessibility_custom_action_8
+id accessibility_custom_action_9
+id accessibility_hint
+id accessibility_label
+id accessibility_links
+id accessibility_role
+id accessibility_state
+id accessibility_value
+id action_bar
+id action_bar_activity_content
+id action_bar_container
+id action_bar_root
+id action_bar_spinner
+id action_bar_subtitle
+id action_bar_title
+id action_container
+id action_context_bar
+id action_divider
+id action_image
+id action_menu_divider
+id action_menu_presenter
+id action_mode_bar
+id action_mode_bar_stub
+id action_mode_close_button
+id action_text
+id actions
+id activity_chooser_view_content
+id add
+id adjust_height
+id adjust_width
+id alertTitle
+id amu_text
+id async
+id auto
+id autofill_inline_suggestion_end_icon
+id autofill_inline_suggestion_start_icon
+id autofill_inline_suggestion_subtitle
+id autofill_inline_suggestion_title
+id blocking
+id buttonPanel
+id catalyst_redbox_title
+id center
+id centerCrop
+id centerInside
+id checkbox
+id checked
+id chronometer
+id content
+id contentPanel
+id custom
+id customPanel
+id dark
+id decor_content_parent
+id default_activity_button
+id dialog_button
+id edit_query
+id expand_activities_button
+id expanded_menu
+id fitBottomStart
+id fitCenter
+id fitEnd
+id fitStart
+id fitXY
+id focusCrop
+id forever
+id fps_text
+id fragment_container_view_tag
+id group_divider
+id home
+id hybrid
+id icon
+id icon_group
+id icon_only
+id image
+id info
+id italic
+id item1
+id item2
+id item3
+id item4
+id labelled_by
+id light
+id line1
+id line3
+id listMode
+id list_item
+id message
+id multiply
+id none
+id normal
+id notification_background
+id notification_main_column
+id notification_main_column_container
+id off
+id on
+id parentPanel
+id pointer_enter
+id pointer_enter_capture
+id pointer_leave
+id pointer_leave_capture
+id pointer_move
+id pointer_move_capture
+id progress_circular
+id progress_horizontal
+id radio
+id react_test_id
+id right_icon
+id right_side
+id rn_frame_file
+id rn_frame_method
+id rn_redbox_dismiss_button
+id rn_redbox_line_separator
+id rn_redbox_loading_indicator
+id rn_redbox_reload_button
+id rn_redbox_report_button
+id rn_redbox_report_label
+id rn_redbox_stack
+id satellite
+id screen
+id scrollIndicatorDown
+id scrollIndicatorUp
+id scrollView
+id search_badge
+id search_bar
+id search_button
+id search_close_btn
+id search_edit_frame
+id search_go_btn
+id search_mag_icon
+id search_plate
+id search_src_text
+id search_voice_btn
+id select_dialog_listview
+id shortcut
+id spacer
+id special_effects_controller_view_tag
+id split_action_bar
+id src_atop
+id src_in
+id src_over
+id standard
+id submenuarrow
+id submit_area
+id tabMode
+id tag_accessibility_actions
+id tag_accessibility_clickable_spans
+id tag_accessibility_heading
+id tag_accessibility_pane_title
+id tag_on_apply_window_listener
+id tag_on_receive_content_listener
+id tag_on_receive_content_mime_types
+id tag_screen_reader_focusable
+id tag_state_description
+id tag_transition_group
+id tag_unhandled_key_event_manager
+id tag_unhandled_key_listeners
+id tag_window_insets_animation_callback
+id terrain
+id text
+id text2
+id textSpacerNoButtons
+id textSpacerNoTitle
+id time
+id title
+id titleDividerNoCustom
+id title_template
+id topPanel
+id unchecked
+id uniform
+id up
+id view_tag_instance_handle
+id view_tag_native_id
+id view_tree_lifecycle_owner
+id view_tree_saved_state_registry_owner
+id view_tree_view_model_store_owner
+id visible_removing_fragment_view_tag
+id webview
+id wide
+id window
+id wrap_content
+integer abc_config_activityDefaultDur
+integer abc_config_activityShortDur
+integer cancel_button_image_alpha
+integer config_tooltipAnimTime
+integer google_play_services_version
+integer react_native_dev_server_port
+integer react_native_inspector_proxy_port
+integer status_bar_notification_info_maxnum
+interpolator btn_checkbox_checked_mtrl_animation_interpolator_0
+interpolator btn_checkbox_checked_mtrl_animation_interpolator_1
+interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0
+interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1
+interpolator btn_radio_to_off_mtrl_animation_interpolator_0
+interpolator btn_radio_to_on_mtrl_animation_interpolator_0
+interpolator fast_out_slow_in
+layout abc_action_bar_title_item
+layout abc_action_bar_up_container
+layout abc_action_menu_item_layout
+layout abc_action_menu_layout
+layout abc_action_mode_bar
+layout abc_action_mode_close_item_material
+layout abc_activity_chooser_view
+layout abc_activity_chooser_view_list_item
+layout abc_alert_dialog_button_bar_material
+layout abc_alert_dialog_material
+layout abc_alert_dialog_title_material
+layout abc_cascading_menu_item_layout
+layout abc_dialog_title_material
+layout abc_expanded_menu_layout
+layout abc_list_menu_item_checkbox
+layout abc_list_menu_item_icon
+layout abc_list_menu_item_layout
+layout abc_list_menu_item_radio
+layout abc_popup_menu_header_item_layout
+layout abc_popup_menu_item_layout
+layout abc_screen_content_include
+layout abc_screen_simple
+layout abc_screen_simple_overlay_action_mode
+layout abc_screen_toolbar
+layout abc_search_dropdown_item_icons_2line
+layout abc_search_view
+layout abc_select_dialog_material
+layout abc_tooltip
+layout amu_info_window
+layout amu_text_bubble
+layout amu_webview
+layout autofill_inline_suggestion
+layout custom_dialog
+layout dev_loading_view
+layout fps_view
+layout notification_action
+layout notification_action_tombstone
+layout notification_template_custom_big
+layout notification_template_icon_group
+layout notification_template_part_chronometer
+layout notification_template_part_time
+layout redbox_item_frame
+layout redbox_item_title
+layout redbox_view
+layout select_dialog_item_material
+layout select_dialog_multichoice_material
+layout select_dialog_singlechoice_material
+layout support_simple_spinner_dropdown_item
+menu example_menu
+menu example_menu2
+raw amu_ballon_gx_prefix
+raw amu_basic_folder
+raw amu_basic_placemark
+raw amu_cdata
+raw amu_default_balloon
+raw amu_document_nest
+raw amu_draw_order_ground_overlay
+raw amu_extended_data
+raw amu_ground_overlay
+raw amu_ground_overlay_color
+raw amu_inline_style
+raw amu_multigeometry_placemarks
+raw amu_multiple_placemarks
+raw amu_nested_folders
+raw amu_nested_multigeometry
+raw amu_poly_style_boolean_alpha
+raw amu_poly_style_boolean_numeric
+raw amu_unknwown_folder
+raw amu_unsupported
+raw amu_visibility_ground_overlay
+string abc_action_bar_home_description
+string abc_action_bar_up_description
+string abc_action_menu_overflow_description
+string abc_action_mode_done
+string abc_activity_chooser_view_see_all
+string abc_activitychooserview_choose_application
+string abc_capital_off
+string abc_capital_on
+string abc_menu_alt_shortcut_label
+string abc_menu_ctrl_shortcut_label
+string abc_menu_delete_shortcut_label
+string abc_menu_enter_shortcut_label
+string abc_menu_function_shortcut_label
+string abc_menu_meta_shortcut_label
+string abc_menu_shift_shortcut_label
+string abc_menu_space_shortcut_label
+string abc_menu_sym_shortcut_label
+string abc_prepend_shortcut_label
+string abc_search_hint
+string abc_searchview_description_clear
+string abc_searchview_description_query
+string abc_searchview_description_search
+string abc_searchview_description_submit
+string abc_searchview_description_voice
+string abc_shareactionprovider_share_with
+string abc_shareactionprovider_share_with_application
+string abc_toolbar_collapse_description
+string alert_description
+string androidx_startup
+string catalyst_change_bundle_location
+string catalyst_copy_button
+string catalyst_debug
+string catalyst_debug_chrome
+string catalyst_debug_chrome_stop
+string catalyst_debug_connecting
+string catalyst_debug_error
+string catalyst_debug_open
+string catalyst_debug_stop
+string catalyst_devtools_open
+string catalyst_dismiss_button
+string catalyst_heap_capture
+string catalyst_hot_reloading
+string catalyst_hot_reloading_auto_disable
+string catalyst_hot_reloading_auto_enable
+string catalyst_hot_reloading_stop
+string catalyst_inspector
+string catalyst_inspector_stop
+string catalyst_loading_from_url
+string catalyst_open_flipper_error
+string catalyst_perf_monitor
+string catalyst_perf_monitor_stop
+string catalyst_reload
+string catalyst_reload_button
+string catalyst_reload_error
+string catalyst_report_button
+string catalyst_sample_profiler_disable
+string catalyst_sample_profiler_enable
+string catalyst_settings
+string catalyst_settings_title
+string combobox_description
+string common_google_play_services_enable_button
+string common_google_play_services_enable_text
+string common_google_play_services_enable_title
+string common_google_play_services_install_button
+string common_google_play_services_install_text
+string common_google_play_services_install_text_phone
+string common_google_play_services_install_text_tablet
+string common_google_play_services_install_title
+string common_google_play_services_notification_channel_name
+string common_google_play_services_notification_ticker
+string common_google_play_services_unknown_issue
+string common_google_play_services_unsupported_text
+string common_google_play_services_unsupported_title
+string common_google_play_services_update_button
+string common_google_play_services_update_text
+string common_google_play_services_update_title
+string common_google_play_services_updating_text
+string common_google_play_services_updating_title
+string common_google_play_services_wear_update_text
+string common_open_on_phone
+string common_signin_button_text
+string common_signin_button_text_long
+string header_description
+string image_description
+string imagebutton_description
+string link_description
+string menu_description
+string menubar_description
+string menuitem_description
+string progressbar_description
+string radiogroup_description
+string rn_tab_description
+string scrollbar_description
+string search_menu_title
+string spinbutton_description
+string state_busy_description
+string state_collapsed_description
+string state_expanded_description
+string state_mixed_description
+string state_off_description
+string state_on_description
+string state_unselected_description
+string status_bar_notification_info_overflow
+string summary_description
+string tablist_description
+string timer_description
+string toolbar_description
+style AlertDialog_AppCompat
+style AlertDialog_AppCompat_Light
+style Animation_AppCompat_Dialog
+style Animation_AppCompat_DropDownUp
+style Animation_AppCompat_Tooltip
+style Animation_Catalyst_LogBox
+style Animation_Catalyst_RedBox
+style Base_AlertDialog_AppCompat
+style Base_AlertDialog_AppCompat_Light
+style Base_Animation_AppCompat_Dialog
+style Base_Animation_AppCompat_DropDownUp
+style Base_Animation_AppCompat_Tooltip
+style Base_DialogWindowTitleBackground_AppCompat
+style Base_DialogWindowTitle_AppCompat
+style Base_TextAppearance_AppCompat
+style Base_TextAppearance_AppCompat_Body1
+style Base_TextAppearance_AppCompat_Body2
+style Base_TextAppearance_AppCompat_Button
+style Base_TextAppearance_AppCompat_Caption
+style Base_TextAppearance_AppCompat_Display1
+style Base_TextAppearance_AppCompat_Display2
+style Base_TextAppearance_AppCompat_Display3
+style Base_TextAppearance_AppCompat_Display4
+style Base_TextAppearance_AppCompat_Headline
+style Base_TextAppearance_AppCompat_Inverse
+style Base_TextAppearance_AppCompat_Large
+style Base_TextAppearance_AppCompat_Large_Inverse
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Medium
+style Base_TextAppearance_AppCompat_Medium_Inverse
+style Base_TextAppearance_AppCompat_Menu
+style Base_TextAppearance_AppCompat_SearchResult
+style Base_TextAppearance_AppCompat_SearchResult_Subtitle
+style Base_TextAppearance_AppCompat_SearchResult_Title
+style Base_TextAppearance_AppCompat_Small
+style Base_TextAppearance_AppCompat_Small_Inverse
+style Base_TextAppearance_AppCompat_Subhead
+style Base_TextAppearance_AppCompat_Subhead_Inverse
+style Base_TextAppearance_AppCompat_Title
+style Base_TextAppearance_AppCompat_Title_Inverse
+style Base_TextAppearance_AppCompat_Tooltip
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Title
+style Base_TextAppearance_AppCompat_Widget_Button
+style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Inverse
+style Base_TextAppearance_AppCompat_Widget_DropDownItem
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Widget_Switch
+style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Title
+style Base_ThemeOverlay_AppCompat
+style Base_ThemeOverlay_AppCompat_ActionBar
+style Base_ThemeOverlay_AppCompat_Dark
+style Base_ThemeOverlay_AppCompat_Dark_ActionBar
+style Base_ThemeOverlay_AppCompat_Dialog
+style Base_ThemeOverlay_AppCompat_Dialog_Alert
+style Base_ThemeOverlay_AppCompat_Light
+style Base_Theme_AppCompat
+style Base_Theme_AppCompat_CompactMenu
+style Base_Theme_AppCompat_Dialog
+style Base_Theme_AppCompat_DialogWhenLarge
+style Base_Theme_AppCompat_Dialog_Alert
+style Base_Theme_AppCompat_Dialog_FixedSize
+style Base_Theme_AppCompat_Dialog_MinWidth
+style Base_Theme_AppCompat_Light
+style Base_Theme_AppCompat_Light_DarkActionBar
+style Base_Theme_AppCompat_Light_Dialog
+style Base_Theme_AppCompat_Light_DialogWhenLarge
+style Base_Theme_AppCompat_Light_Dialog_Alert
+style Base_Theme_AppCompat_Light_Dialog_FixedSize
+style Base_Theme_AppCompat_Light_Dialog_MinWidth
+style Base_V21_ThemeOverlay_AppCompat_Dialog
+style Base_V21_Theme_AppCompat
+style Base_V21_Theme_AppCompat_Dialog
+style Base_V21_Theme_AppCompat_Light
+style Base_V21_Theme_AppCompat_Light_Dialog
+style Base_V22_Theme_AppCompat
+style Base_V22_Theme_AppCompat_Light
+style Base_V23_Theme_AppCompat
+style Base_V23_Theme_AppCompat_Light
+style Base_V26_Theme_AppCompat
+style Base_V26_Theme_AppCompat_Light
+style Base_V26_Widget_AppCompat_Toolbar
+style Base_V28_Theme_AppCompat
+style Base_V28_Theme_AppCompat_Light
+style Base_V7_ThemeOverlay_AppCompat_Dialog
+style Base_V7_Theme_AppCompat
+style Base_V7_Theme_AppCompat_Dialog
+style Base_V7_Theme_AppCompat_Light
+style Base_V7_Theme_AppCompat_Light_Dialog
+style Base_V7_Widget_AppCompat_AutoCompleteTextView
+style Base_V7_Widget_AppCompat_EditText
+style Base_V7_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_ActionBar
+style Base_Widget_AppCompat_ActionBar_Solid
+style Base_Widget_AppCompat_ActionBar_TabBar
+style Base_Widget_AppCompat_ActionBar_TabText
+style Base_Widget_AppCompat_ActionBar_TabView
+style Base_Widget_AppCompat_ActionButton
+style Base_Widget_AppCompat_ActionButton_CloseMode
+style Base_Widget_AppCompat_ActionButton_Overflow
+style Base_Widget_AppCompat_ActionMode
+style Base_Widget_AppCompat_ActivityChooserView
+style Base_Widget_AppCompat_AutoCompleteTextView
+style Base_Widget_AppCompat_Button
+style Base_Widget_AppCompat_ButtonBar
+style Base_Widget_AppCompat_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Borderless
+style Base_Widget_AppCompat_Button_Borderless_Colored
+style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Colored
+style Base_Widget_AppCompat_Button_Small
+style Base_Widget_AppCompat_CompoundButton_CheckBox
+style Base_Widget_AppCompat_CompoundButton_RadioButton
+style Base_Widget_AppCompat_CompoundButton_Switch
+style Base_Widget_AppCompat_DrawerArrowToggle
+style Base_Widget_AppCompat_DrawerArrowToggle_Common
+style Base_Widget_AppCompat_DropDownItem_Spinner
+style Base_Widget_AppCompat_EditText
+style Base_Widget_AppCompat_ImageButton
+style Base_Widget_AppCompat_Light_ActionBar
+style Base_Widget_AppCompat_Light_ActionBar_Solid
+style Base_Widget_AppCompat_Light_ActionBar_TabBar
+style Base_Widget_AppCompat_Light_ActionBar_TabText
+style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Base_Widget_AppCompat_Light_ActionBar_TabView
+style Base_Widget_AppCompat_Light_PopupMenu
+style Base_Widget_AppCompat_Light_PopupMenu_Overflow
+style Base_Widget_AppCompat_ListMenuView
+style Base_Widget_AppCompat_ListPopupWindow
+style Base_Widget_AppCompat_ListView
+style Base_Widget_AppCompat_ListView_DropDown
+style Base_Widget_AppCompat_ListView_Menu
+style Base_Widget_AppCompat_PopupMenu
+style Base_Widget_AppCompat_PopupMenu_Overflow
+style Base_Widget_AppCompat_PopupWindow
+style Base_Widget_AppCompat_ProgressBar
+style Base_Widget_AppCompat_ProgressBar_Horizontal
+style Base_Widget_AppCompat_RatingBar
+style Base_Widget_AppCompat_RatingBar_Indicator
+style Base_Widget_AppCompat_RatingBar_Small
+style Base_Widget_AppCompat_SearchView
+style Base_Widget_AppCompat_SearchView_ActionBar
+style Base_Widget_AppCompat_SeekBar
+style Base_Widget_AppCompat_SeekBar_Discrete
+style Base_Widget_AppCompat_Spinner
+style Base_Widget_AppCompat_Spinner_Underlined
+style Base_Widget_AppCompat_TextView
+style Base_Widget_AppCompat_TextView_SpinnerItem
+style Base_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_Toolbar_Button_Navigation
+style CalendarDatePickerDialog
+style CalendarDatePickerStyle
+style DialogAnimationFade
+style DialogAnimationSlide
+style Platform_AppCompat
+style Platform_AppCompat_Light
+style Platform_ThemeOverlay_AppCompat
+style Platform_ThemeOverlay_AppCompat_Dark
+style Platform_ThemeOverlay_AppCompat_Light
+style Platform_V21_AppCompat
+style Platform_V21_AppCompat_Light
+style Platform_V25_AppCompat
+style Platform_V25_AppCompat_Light
+style Platform_Widget_AppCompat_Spinner
+style RtlOverlay_DialogWindowTitle_AppCompat
+style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
+style RtlOverlay_Widget_AppCompat_DialogTitle_Icon
+style RtlOverlay_Widget_AppCompat_PopupMenuItem
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
+style RtlOverlay_Widget_AppCompat_SearchView_MagIcon
+style RtlOverlay_Widget_AppCompat_Search_DropDown
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Query
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Text
+style RtlUnderlay_Widget_AppCompat_ActionButton
+style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow
+style SpinnerDatePickerDialog
+style SpinnerDatePickerStyle
+style TextAppearance_AppCompat
+style TextAppearance_AppCompat_Body1
+style TextAppearance_AppCompat_Body2
+style TextAppearance_AppCompat_Button
+style TextAppearance_AppCompat_Caption
+style TextAppearance_AppCompat_Display1
+style TextAppearance_AppCompat_Display2
+style TextAppearance_AppCompat_Display3
+style TextAppearance_AppCompat_Display4
+style TextAppearance_AppCompat_Headline
+style TextAppearance_AppCompat_Inverse
+style TextAppearance_AppCompat_Large
+style TextAppearance_AppCompat_Large_Inverse
+style TextAppearance_AppCompat_Light_SearchResult_Subtitle
+style TextAppearance_AppCompat_Light_SearchResult_Title
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Medium
+style TextAppearance_AppCompat_Medium_Inverse
+style TextAppearance_AppCompat_Menu
+style TextAppearance_AppCompat_SearchResult_Subtitle
+style TextAppearance_AppCompat_SearchResult_Title
+style TextAppearance_AppCompat_Small
+style TextAppearance_AppCompat_Small_Inverse
+style TextAppearance_AppCompat_Subhead
+style TextAppearance_AppCompat_Subhead_Inverse
+style TextAppearance_AppCompat_Title
+style TextAppearance_AppCompat_Title_Inverse
+style TextAppearance_AppCompat_Tooltip
+style TextAppearance_AppCompat_Widget_ActionBar_Menu
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionBar_Title
+style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Title
+style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse
+style TextAppearance_AppCompat_Widget_Button
+style TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style TextAppearance_AppCompat_Widget_Button_Colored
+style TextAppearance_AppCompat_Widget_Button_Inverse
+style TextAppearance_AppCompat_Widget_DropDownItem
+style TextAppearance_AppCompat_Widget_PopupMenu_Header
+style TextAppearance_AppCompat_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Widget_Switch
+style TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style TextAppearance_Compat_Notification
+style TextAppearance_Compat_Notification_Info
+style TextAppearance_Compat_Notification_Line2
+style TextAppearance_Compat_Notification_Time
+style TextAppearance_Compat_Notification_Title
+style TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style TextAppearance_Widget_AppCompat_Toolbar_Title
+style Theme
+style ThemeOverlay_AppCompat
+style ThemeOverlay_AppCompat_ActionBar
+style ThemeOverlay_AppCompat_Dark
+style ThemeOverlay_AppCompat_Dark_ActionBar
+style ThemeOverlay_AppCompat_DayNight
+style ThemeOverlay_AppCompat_DayNight_ActionBar
+style ThemeOverlay_AppCompat_Dialog
+style ThemeOverlay_AppCompat_Dialog_Alert
+style ThemeOverlay_AppCompat_Light
+style Theme_AppCompat
+style Theme_AppCompat_CompactMenu
+style Theme_AppCompat_DayNight
+style Theme_AppCompat_DayNight_DarkActionBar
+style Theme_AppCompat_DayNight_Dialog
+style Theme_AppCompat_DayNight_DialogWhenLarge
+style Theme_AppCompat_DayNight_Dialog_Alert
+style Theme_AppCompat_DayNight_Dialog_MinWidth
+style Theme_AppCompat_DayNight_NoActionBar
+style Theme_AppCompat_Dialog
+style Theme_AppCompat_DialogWhenLarge
+style Theme_AppCompat_Dialog_Alert
+style Theme_AppCompat_Dialog_MinWidth
+style Theme_AppCompat_Empty
+style Theme_AppCompat_Light
+style Theme_AppCompat_Light_DarkActionBar
+style Theme_AppCompat_Light_Dialog
+style Theme_AppCompat_Light_DialogWhenLarge
+style Theme_AppCompat_Light_Dialog_Alert
+style Theme_AppCompat_Light_Dialog_MinWidth
+style Theme_AppCompat_Light_NoActionBar
+style Theme_AppCompat_NoActionBar
+style Theme_AutofillInlineSuggestion
+style Theme_Catalyst
+style Theme_Catalyst_LogBox
+style Theme_Catalyst_RedBox
+style Theme_FullScreenDialog
+style Theme_FullScreenDialogAnimatedFade
+style Theme_FullScreenDialogAnimatedSlide
+style Theme_ReactNative_AppCompat_Light
+style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen
+style Widget_AppCompat_ActionBar
+style Widget_AppCompat_ActionBar_Solid
+style Widget_AppCompat_ActionBar_TabBar
+style Widget_AppCompat_ActionBar_TabText
+style Widget_AppCompat_ActionBar_TabView
+style Widget_AppCompat_ActionButton
+style Widget_AppCompat_ActionButton_CloseMode
+style Widget_AppCompat_ActionButton_Overflow
+style Widget_AppCompat_ActionMode
+style Widget_AppCompat_ActivityChooserView
+style Widget_AppCompat_AutoCompleteTextView
+style Widget_AppCompat_Button
+style Widget_AppCompat_ButtonBar
+style Widget_AppCompat_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Borderless
+style Widget_AppCompat_Button_Borderless_Colored
+style Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Colored
+style Widget_AppCompat_Button_Small
+style Widget_AppCompat_CompoundButton_CheckBox
+style Widget_AppCompat_CompoundButton_RadioButton
+style Widget_AppCompat_CompoundButton_Switch
+style Widget_AppCompat_DrawerArrowToggle
+style Widget_AppCompat_DropDownItem_Spinner
+style Widget_AppCompat_EditText
+style Widget_AppCompat_ImageButton
+style Widget_AppCompat_Light_ActionBar
+style Widget_AppCompat_Light_ActionBar_Solid
+style Widget_AppCompat_Light_ActionBar_Solid_Inverse
+style Widget_AppCompat_Light_ActionBar_TabBar
+style Widget_AppCompat_Light_ActionBar_TabBar_Inverse
+style Widget_AppCompat_Light_ActionBar_TabText
+style Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Widget_AppCompat_Light_ActionBar_TabView
+style Widget_AppCompat_Light_ActionBar_TabView_Inverse
+style Widget_AppCompat_Light_ActionButton
+style Widget_AppCompat_Light_ActionButton_CloseMode
+style Widget_AppCompat_Light_ActionButton_Overflow
+style Widget_AppCompat_Light_ActionMode_Inverse
+style Widget_AppCompat_Light_ActivityChooserView
+style Widget_AppCompat_Light_AutoCompleteTextView
+style Widget_AppCompat_Light_DropDownItem_Spinner
+style Widget_AppCompat_Light_ListPopupWindow
+style Widget_AppCompat_Light_ListView_DropDown
+style Widget_AppCompat_Light_PopupMenu
+style Widget_AppCompat_Light_PopupMenu_Overflow
+style Widget_AppCompat_Light_SearchView
+style Widget_AppCompat_Light_Spinner_DropDown_ActionBar
+style Widget_AppCompat_ListMenuView
+style Widget_AppCompat_ListPopupWindow
+style Widget_AppCompat_ListView
+style Widget_AppCompat_ListView_DropDown
+style Widget_AppCompat_ListView_Menu
+style Widget_AppCompat_PopupMenu
+style Widget_AppCompat_PopupMenu_Overflow
+style Widget_AppCompat_PopupWindow
+style Widget_AppCompat_ProgressBar
+style Widget_AppCompat_ProgressBar_Horizontal
+style Widget_AppCompat_RatingBar
+style Widget_AppCompat_RatingBar_Indicator
+style Widget_AppCompat_RatingBar_Small
+style Widget_AppCompat_SearchView
+style Widget_AppCompat_SearchView_ActionBar
+style Widget_AppCompat_SeekBar
+style Widget_AppCompat_SeekBar_Discrete
+style Widget_AppCompat_Spinner
+style Widget_AppCompat_Spinner_DropDown
+style Widget_AppCompat_Spinner_DropDown_ActionBar
+style Widget_AppCompat_Spinner_Underlined
+style Widget_AppCompat_TextView
+style Widget_AppCompat_TextView_SpinnerItem
+style Widget_AppCompat_Toolbar
+style Widget_AppCompat_Toolbar_Button_Navigation
+style Widget_Autofill
+style Widget_Autofill_InlineSuggestionChip
+style Widget_Autofill_InlineSuggestionEndIconStyle
+style Widget_Autofill_InlineSuggestionStartIconStyle
+style Widget_Autofill_InlineSuggestionSubtitle
+style Widget_Autofill_InlineSuggestionTitle
+style Widget_Compat_NotificationActionContainer
+style Widget_Compat_NotificationActionText
+style amu_Bubble_TextAppearance_Dark
+style amu_Bubble_TextAppearance_Light
+style amu_ClusterIcon_TextAppearance
+style redboxButton
+styleable ActionBar background backgroundSplit backgroundStacked contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation customNavigationLayout displayOptions divider elevation height hideOnContentScroll homeAsUpIndicator homeLayout icon indeterminateProgressStyle itemPadding logo navigationMode popupTheme progressBarPadding progressBarStyle subtitle subtitleTextStyle title titleTextStyle
+styleable ActionBarLayout android_layout_gravity
+styleable ActionMenuItemView android_minWidth
+styleable ActionMenuView
+styleable ActionMode background backgroundSplit closeItemLayout height subtitleTextStyle titleTextStyle
+styleable ActivityChooserView expandActivityOverflowButtonDrawable initialActivityCount
+styleable AlertDialog android_layout buttonIconDimen buttonPanelSideLayout listItemLayout listLayout multiChoiceItemLayout showTitle singleChoiceItemLayout
+styleable AnimatedStateListDrawableCompat android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable AnimatedStateListDrawableItem android_drawable android_id
+styleable AnimatedStateListDrawableTransition android_drawable android_fromId android_reversible android_toId
+styleable AppCompatEmojiHelper
+styleable AppCompatImageView android_src srcCompat tint tintMode
+styleable AppCompatSeekBar android_thumb tickMark tickMarkTint tickMarkTintMode
+styleable AppCompatTextHelper android_drawableBottom android_drawableEnd android_drawableLeft android_drawableRight android_drawableStart android_drawableTop android_textAppearance
+styleable AppCompatTextView android_textAppearance autoSizeMaxTextSize autoSizeMinTextSize autoSizePresetSizes autoSizeStepGranularity autoSizeTextType drawableBottomCompat drawableEndCompat drawableLeftCompat drawableRightCompat drawableStartCompat drawableTint drawableTintMode drawableTopCompat emojiCompatEnabled firstBaselineToTopHeight fontFamily fontVariationSettings lastBaselineToBottomHeight lineHeight textAllCaps textLocale
+styleable AppCompatTheme actionBarDivider actionBarItemBackground actionBarPopupTheme actionBarSize actionBarSplitStyle actionBarStyle actionBarTabBarStyle actionBarTabStyle actionBarTabTextStyle actionBarTheme actionBarWidgetTheme actionButtonStyle actionDropDownStyle actionMenuTextAppearance actionMenuTextColor actionModeBackground actionModeCloseButtonStyle actionModeCloseContentDescription actionModeCloseDrawable actionModeCopyDrawable actionModeCutDrawable actionModeFindDrawable actionModePasteDrawable actionModePopupWindowStyle actionModeSelectAllDrawable actionModeShareDrawable actionModeSplitBackground actionModeStyle actionModeTheme actionModeWebSearchDrawable actionOverflowButtonStyle actionOverflowMenuStyle activityChooserViewStyle alertDialogButtonGroupStyle alertDialogCenterButtons alertDialogStyle alertDialogTheme android_windowAnimationStyle android_windowIsFloating autoCompleteTextViewStyle borderlessButtonStyle buttonBarButtonStyle buttonBarNegativeButtonStyle buttonBarNeutralButtonStyle buttonBarPositiveButtonStyle buttonBarStyle buttonStyle buttonStyleSmall checkboxStyle checkedTextViewStyle colorAccent colorBackgroundFloating colorButtonNormal colorControlActivated colorControlHighlight colorControlNormal colorError colorPrimary colorPrimaryDark colorSwitchThumbNormal controlBackground dialogCornerRadius dialogPreferredPadding dialogTheme dividerHorizontal dividerVertical dropDownListViewStyle dropdownListPreferredItemHeight editTextBackground editTextColor editTextStyle homeAsUpIndicator imageButtonStyle listChoiceBackgroundIndicator listChoiceIndicatorMultipleAnimated listChoiceIndicatorSingleAnimated listDividerAlertDialog listMenuViewStyle listPopupWindowStyle listPreferredItemHeight listPreferredItemHeightLarge listPreferredItemHeightSmall listPreferredItemPaddingEnd listPreferredItemPaddingLeft listPreferredItemPaddingRight listPreferredItemPaddingStart panelBackground panelMenuListTheme panelMenuListWidth popupMenuStyle popupWindowStyle radioButtonStyle ratingBarStyle ratingBarStyleIndicator ratingBarStyleSmall searchViewStyle seekBarStyle selectableItemBackground selectableItemBackgroundBorderless spinnerDropDownItemStyle spinnerStyle switchStyle textAppearanceLargePopupMenu textAppearanceListItem textAppearanceListItemSecondary textAppearanceListItemSmall textAppearancePopupMenuHeader textAppearanceSearchResultSubtitle textAppearanceSearchResultTitle textAppearanceSmallPopupMenu textColorAlertDialogListItem textColorSearchUrl toolbarNavigationButtonStyle toolbarStyle tooltipForegroundColor tooltipFrameBackground viewInflaterClass windowActionBar windowActionBarOverlay windowActionModeOverlay windowFixedHeightMajor windowFixedHeightMinor windowFixedWidthMajor windowFixedWidthMinor windowMinWidthMajor windowMinWidthMinor windowNoTitle
+styleable Autofill_InlineSuggestion autofillInlineSuggestionChip autofillInlineSuggestionEndIconStyle autofillInlineSuggestionStartIconStyle autofillInlineSuggestionSubtitle autofillInlineSuggestionTitle isAutofillInlineSuggestionTheme
+styleable ButtonBarLayout allowStacking
+styleable Capability queryPatterns shortcutMatchRequired
+styleable CheckedTextView android_checkMark checkMarkCompat checkMarkTint checkMarkTintMode
+styleable ColorStateListItem alpha android_alpha android_color android_lStar lStar
+styleable CompoundButton android_button buttonCompat buttonTint buttonTintMode
+styleable DrawerArrowToggle arrowHeadLength arrowShaftLength barLength color drawableSize gapBetweenBars spinBars thickness
+styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
+styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
+styleable Fragment android_id android_name android_tag
+styleable FragmentContainerView android_name android_tag
+styleable GenericDraweeHierarchy actualImageScaleType backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
+styleable GradientColorItem android_color android_offset
+styleable LinearLayoutCompat android_baselineAligned android_baselineAlignedChildIndex android_gravity android_orientation android_weightSum divider dividerPadding measureWithLargestChild showDividers
+styleable LinearLayoutCompat_Layout android_layout_gravity android_layout_height android_layout_weight android_layout_width
+styleable ListPopupWindow android_dropDownHorizontalOffset android_dropDownVerticalOffset
+styleable LoadingImageView circleCrop imageAspectRatio imageAspectRatioAdjust
+styleable MapAttrs ambientEnabled backgroundColor cameraBearing cameraMaxZoomPreference cameraMinZoomPreference cameraTargetLat cameraTargetLng cameraTilt cameraZoom latLngBoundsNorthEastLatitude latLngBoundsNorthEastLongitude latLngBoundsSouthWestLatitude latLngBoundsSouthWestLongitude liteMode mapId mapType uiCompass uiMapToolbar uiRotateGestures uiScrollGestures uiScrollGesturesDuringRotateOrZoom uiTiltGestures uiZoomControls uiZoomGestures useViewLifecycle zOrderOnTop
+styleable MenuGroup android_checkableBehavior android_enabled android_id android_menuCategory android_orderInCategory android_visible
+styleable MenuItem actionLayout actionProviderClass actionViewClass alphabeticModifiers android_alphabeticShortcut android_checkable android_checked android_enabled android_icon android_id android_menuCategory android_numericShortcut android_onClick android_orderInCategory android_title android_titleCondensed android_visible contentDescription iconTint iconTintMode numericModifiers showAsAction tooltipText
+styleable MenuView android_headerBackground android_horizontalDivider android_itemBackground android_itemIconDisabledAlpha android_itemTextAppearance android_verticalDivider android_windowAnimationStyle preserveIconSpacing subMenuArrow
+styleable PopupWindow android_popupAnimationStyle android_popupBackground overlapAnchor
+styleable PopupWindowBackgroundState state_above_anchor
+styleable RecycleListView paddingBottomNoButtons paddingTopNoTitle
+styleable SearchView android_focusable android_imeOptions android_inputType android_maxWidth closeIcon commitIcon defaultQueryHint goIcon iconifiedByDefault layout queryBackground queryHint searchHintIcon searchIcon submitBackground suggestionRowLayout voiceIcon
+styleable SignInButton buttonSize colorScheme scopeUris
+styleable SimpleDraweeView actualImageResource actualImageScaleType actualImageUri backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable Spinner android_dropDownWidth android_entries android_popupBackground android_prompt popupTheme
+styleable StateListDrawable android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable StateListDrawableItem android_drawable
+styleable SwitchCompat android_textOff android_textOn android_thumb showText splitTrack switchMinWidth switchPadding switchTextAppearance thumbTextPadding thumbTint thumbTintMode track trackTint trackTintMode
+styleable TextAppearance android_fontFamily android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_textColor android_textColorHint android_textColorLink android_textFontWeight android_textSize android_textStyle android_typeface fontFamily fontVariationSettings textAllCaps textLocale
+styleable Toolbar android_gravity android_minHeight buttonGravity collapseContentDescription collapseIcon contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation logo logoDescription maxButtonHeight menu navigationContentDescription navigationIcon popupTheme subtitle subtitleTextAppearance subtitleTextColor title titleMargin titleMarginBottom titleMarginEnd titleMarginStart titleMarginTop titleMargins titleTextAppearance titleTextColor
+styleable View android_focusable android_theme paddingEnd paddingStart theme
+styleable ViewBackgroundHelper android_background backgroundTint backgroundTintMode
+styleable ViewStubCompat android_id android_inflatedId android_layout
+xml rn_dev_preferences
diff --git a/node_modules/react-native-maps/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-maps/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..852e326
--- /dev/null
+++ b/node_modules/react-native-maps/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml:1:1-3:12
+INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml:1:1-3:12
+INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml:1:1-3:12
+	package
+		ADDED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml:2:5-44
+		INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+		INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml:1:11-69
+uses-sdk
+INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+		ADDED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+		INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+		ADDED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
+		INJECTED from /Volumes/Mac-Data/ReactNative/MyTMPlusApp/node_modules/react-native-maps/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-maps/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/react-native-maps/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..ce8a1d3
Binary files /dev/null and b/node_modules/react-native-maps/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapManager.java b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapManager.java
index 004766f..f3c1b08 100644
--- a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapManager.java
+++ b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapManager.java
@@ -1,5 +1,6 @@
 package com.airbnb.android.react.maps;
 
+import android.util.Log;
 import android.view.View;
 
 import androidx.annotation.NonNull;
@@ -7,6 +8,7 @@ import androidx.annotation.Nullable;
 
 import com.facebook.react.bridge.Arguments;
 import com.facebook.react.bridge.ReactApplicationContext;
+import com.facebook.react.bridge.ReactContext;
 import com.facebook.react.bridge.ReadableArray;
 import com.facebook.react.bridge.ReadableMap;
 import com.facebook.react.bridge.WritableMap;
@@ -20,6 +22,7 @@ import com.facebook.react.uimanager.events.RCTEventEmitter;
 import com.google.android.gms.location.Priority;
 import com.google.android.gms.maps.GoogleMap;
 import com.google.android.gms.maps.GoogleMapOptions;
+import com.google.android.gms.maps.MapView;
 import com.google.android.gms.maps.model.LatLng;
 import com.google.android.gms.maps.model.LatLngBounds;
 
@@ -46,6 +49,8 @@ public class AirMapManager extends ViewGroupManager<AirMapView> {
 
   private final ReactApplicationContext appContext;
   private AirMapMarkerManager markerManager;
+  private AirMapPolylineManager polylineManager;
+
 
   protected GoogleMapOptions googleMapOptions;
 
@@ -54,6 +59,12 @@ public class AirMapManager extends ViewGroupManager<AirMapView> {
     this.googleMapOptions = new GoogleMapOptions();
   }
 
+  public AirMapManager(ReactApplicationContext context, AirMapPolylineManager polylineManager) {
+    this.appContext = context;
+    this.googleMapOptions = new GoogleMapOptions();
+    this.polylineManager = polylineManager;
+  }
+
   public AirMapMarkerManager getMarkerManager() {
     return this.markerManager;
   }
@@ -285,8 +296,19 @@ public class AirMapManager extends ViewGroupManager<AirMapView> {
     double latDelta;
     ReadableMap region;
     ReadableMap camera;
-
     switch (commandId) {
+      case "setTapMarker":
+        String id = args.getString(0);
+        view.setTapMarker(id);
+        break;
+      case "resetMarkerSelected":
+        view.resetMarkerSelected();
+        break;
+      case "setPolygonToZoom":
+        ReadableMap data = args.getMap(0);
+        ReadableArray polygons = data.getArray("polygons");
+        view.setPolygonToZoom(data, polygons);
+        break;
       case "setCamera":
         if(args == null) {
           break;
@@ -369,6 +391,7 @@ public class AirMapManager extends ViewGroupManager<AirMapView> {
         "onMarkerSelect", MapBuilder.of("registrationName", "onMarkerSelect"),
         "onMarkerDeselect", MapBuilder.of("registrationName", "onMarkerDeselect"),
         "onCalloutPress", MapBuilder.of("registrationName", "onCalloutPress")
+
     );
 
     map.putAll(MapBuilder.of(
@@ -385,7 +408,8 @@ public class AirMapManager extends ViewGroupManager<AirMapView> {
         "onIndoorLevelActivated", MapBuilder.of("registrationName", "onIndoorLevelActivated"),
         "onIndoorBuildingFocused", MapBuilder.of("registrationName", "onIndoorBuildingFocused"),
         "onDoublePress", MapBuilder.of("registrationName", "onDoublePress"),
-        "onMapLoaded", MapBuilder.of("registrationName", "onMapLoaded")
+        "onMapLoaded", MapBuilder.of("registrationName", "onMapLoaded"),
+            "onChangeViewMap", MapBuilder.of("registrationName", "onChangeViewMap")
     ));
 
     return map;
@@ -428,6 +452,46 @@ public class AirMapManager extends ViewGroupManager<AirMapView> {
         .receiveEvent(view.getId(), name, data);
   }
 
+  public void updatePolyline(LatLng latLng){
+    if(polylineManager != null)
+      polylineManager.changeCoordinate(latLng);
+  }
+
+  public void resetPolygon(){
+    if(polylineManager != null)
+      polylineManager.resetView();
+  }
+
+  public void updateMarkerLongPress(LatLng latLng, AirMapMarker view, AirMapView mapView){
+    if(checkCanUserLongPress(view, mapView)) {
+      if(view != null){
+        markerManager.changeCoordinate(latLng, view);
+      }else {
+        markerManager.changeCoordinate(latLng, getMarkerDistance());
+      }
+      if(polylineManager != null)
+        polylineManager.changeCoordinate(latLng);
+    }
+  }
+
+  public boolean checkCanUserLongPress(AirMapMarker view, AirMapView mapView){
+    if(this.markerManager != null && view != null && this.markerManager.cacheMarkers.contains(view)){
+      return true;
+    }else if(this.markerManager != null && view != null && !this.markerManager.cacheMarkers.contains(view)){
+      mapView.resetMarkerSelected();
+      return this.markerManager != null && getMarkerDistance() != null;
+    }
+    return this.markerManager != null && view == null && getMarkerDistance() != null;
+  }
+  private AirMapMarker getMarkerDistance(){
+    for (int i = 0; i < this.markerManager.cacheMarkers.size(); i++) {
+      if (this.markerManager.cacheMarkers.get(i).getIdentifier().contains("distanceFromPinToBall")){
+        return this.markerManager.cacheMarkers.get(i);
+      }
+    }
+    return null;
+  }
+
   @Override
   public void onDropViewInstance(AirMapView view) {
     view.doDestroy();
diff --git a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapMarker.java b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapMarker.java
index b51854c..64d6650 100644
--- a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapMarker.java
+++ b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapMarker.java
@@ -5,6 +5,7 @@ import android.graphics.Bitmap;
 import android.graphics.BitmapFactory;
 import android.graphics.Canvas;
 import android.graphics.Color;
+import android.graphics.Point;
 import android.graphics.drawable.Animatable;
 import android.graphics.drawable.Drawable;
 import android.net.Uri;
@@ -67,6 +68,7 @@ public class AirMapMarker extends AirMapFeature {
   private float rotation = 0.0f;
   private boolean flat = false;
   private boolean draggable = false;
+  private boolean tappable = true;
   private int zIndex = 0;
   private float opacity = 1.0f;
 
@@ -162,6 +164,13 @@ public class AirMapMarker extends AirMapFeature {
     update(false);
   }
 
+  public void setCoordinateLatLng(LatLng coordinate) {
+    if (marker != null) {
+      marker.setPosition(coordinate);
+    }
+    update(false);
+  }
+
   public void setIdentifier(String identifier) {
     this.identifier = identifier;
     update(false);
@@ -211,6 +220,15 @@ public class AirMapMarker extends AirMapFeature {
     update(false);
   }
 
+  public void setTappable(boolean tappable) {
+    this.tappable = tappable;
+    update(false);
+  }
+
+  public boolean getTappable() {
+    return this.tappable;
+  }
+
   public void setZIndex(int zIndex) {
     this.zIndex = zIndex;
     if (marker != null) {
@@ -287,7 +305,6 @@ public class AirMapMarker extends AirMapFeature {
 
   public void updateMarkerIcon() {
     if (marker == null) return;
-
     marker.setIcon(getIcon());
   }
 
diff --git a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapMarkerManager.java b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapMarkerManager.java
index 17215bd..fa3f36c 100644
--- a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapMarkerManager.java
+++ b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapMarkerManager.java
@@ -2,6 +2,7 @@ package com.airbnb.android.react.maps;
 
 import android.graphics.Bitmap;
 import android.graphics.Color;
+import android.util.Log;
 import android.view.View;
 
 import androidx.annotation.NonNull;
@@ -18,13 +19,17 @@ import com.google.android.gms.maps.model.Marker;
 import com.google.android.gms.maps.model.BitmapDescriptor;
 import com.google.android.gms.maps.model.LatLng;
 
+import java.util.ArrayList;
 import java.util.HashMap;
+import java.util.List;
 import java.util.Map;
 import java.util.WeakHashMap;
 import java.util.concurrent.ConcurrentHashMap;
 
 public class AirMapMarkerManager extends ViewGroupManager<AirMapMarker> {
 
+  public List<AirMapMarker> cacheMarkers = new ArrayList<>();
+
   public static class AirMapMarkerSharedIcon {
     private BitmapDescriptor iconBitmapDescriptor;
     private Bitmap bitmap;
@@ -62,6 +67,7 @@ public class AirMapMarkerManager extends ViewGroupManager<AirMapMarker> {
       if (this.iconBitmapDescriptor != null) {
         marker.setIconBitmapDescriptor(this.iconBitmapDescriptor, this.bitmap);
       }
+
     }
 
     /**
@@ -147,6 +153,12 @@ public class AirMapMarkerManager extends ViewGroupManager<AirMapMarker> {
     }
   }
 
+  @Override
+  public void onDropViewInstance(@NonNull AirMapMarker view) {
+    super.onDropViewInstance(view);
+    this.cacheMarkers.remove(view);
+  }
+
   public AirMapMarkerManager() {
   }
 
@@ -246,6 +258,11 @@ public class AirMapMarkerManager extends ViewGroupManager<AirMapMarker> {
     view.setDraggable(draggable);
   }
 
+  @ReactProp(name = "tappable", defaultBoolean = true)
+  public void setTappable(AirMapMarker view, boolean tappable) {
+    view.setTappable(tappable);
+  }
+
   @Override
   @ReactProp(name = "zIndex", defaultFloat = 0.0f)
   public void setZIndex(AirMapMarker view, float zIndex) {
@@ -266,6 +283,20 @@ public class AirMapMarkerManager extends ViewGroupManager<AirMapMarker> {
     view.setTracksViewChanges(tracksViewChanges);
   }
 
+  @ReactProp(name = "enableMove", defaultBoolean = false)
+  public void setEnableMove(AirMapMarker view, boolean enableMove) {
+    if(enableMove)
+      this.cacheMarkers.add(view);
+    else
+    this.cacheMarkers.remove(view);
+  }
+
+  public void changeCoordinate(LatLng latLng, AirMapMarker view) {
+    if(this.cacheMarkers.contains(view)) {
+      view.setCoordinateLatLng(latLng);
+    }
+  }
+
   @Override
   public void addView(AirMapMarker parent, View child, int index) {
     // if an <Callout /> component is a child, then it is a callout view, NOT part of the
diff --git a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapPolyline.java b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapPolyline.java
index ed0b06f..2ea4270 100644
--- a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapPolyline.java
+++ b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapPolyline.java
@@ -1,6 +1,10 @@
 package com.airbnb.android.react.maps;
 
 import android.content.Context;
+import android.content.res.Resources;
+import android.graphics.Color;
+import android.graphics.Point;
+import android.util.Log;
 
 import com.facebook.react.bridge.ReadableArray;
 import com.facebook.react.bridge.ReadableMap;
@@ -22,8 +26,8 @@ public class AirMapPolyline extends AirMapFeature {
 
   private PolylineOptions polylineOptions;
   private Polyline polyline;
-
-  private List<LatLng> coordinates;
+  private Polyline polylineNew;
+  private List<LatLng> coordinates = new ArrayList<>();
   private int color;
   private float width;
   private boolean tappable;
@@ -32,23 +36,92 @@ public class AirMapPolyline extends AirMapFeature {
   private Cap lineCap = new RoundCap();
   private ReadableArray patternValues;
   private List<PatternItem> pattern;
+  private GoogleMap map;
+  private List<LatLng> coordinatesNew = new ArrayList<>();
+  private String identifier = "";
 
   public AirMapPolyline(Context context) {
     super(context);
   }
 
   public void setCoordinates(ReadableArray coordinates) {
+    this.coordinatesNew = new ArrayList<>(0);
     this.coordinates = new ArrayList<>(coordinates.size());
+
     for (int i = 0; i < coordinates.size(); i++) {
       ReadableMap coordinate = coordinates.getMap(i);
-      this.coordinates.add(i,
-          new LatLng(coordinate.getDouble("latitude"), coordinate.getDouble("longitude")));
+      LatLng latLng = new LatLng(coordinate.getDouble("latitude"), coordinate.getDouble("longitude"));
+      this.coordinates.add(i, latLng);
     }
-    if (polyline != null) {
+
+    if(polylineNew != null) {
+      polylineNew.setColor(Color.TRANSPARENT);
+      polylineNew.setPoints(this.coordinatesNew);
+    }
+    if (polyline != null && !this.identifier.equals("distancePolyline")) {
       polyline.setPoints(this.coordinates);
     }
   }
 
+  public void setCoordinatesLatLng(LatLng latLng) {
+    if(this.coordinates != null && !this.identifier.contains("-1")) {
+      if (this.map != null && this.identifier.equals("distancePolyline")) {
+        if(polylineNew != null) {
+          polylineNew.setColor(polyline.getColor());
+        }
+        updateCoordinates(latLng, 1, false);
+      } else {
+          this.coordinates.set(this.identifier.equals("distancePolyline") ? 1 : Integer.parseInt(this.identifier), latLng);
+      }
+      if (polyline != null) {
+        polyline.setPoints(this.coordinates);
+      }
+    }
+  }
+
+  private void updateCoordinates(LatLng latLng, int indexChange, boolean isFirst){
+    LatLng latLngPin = getStartCoordForLine(latLng, this.coordinates.get(indexChange-1));
+    LatLng latLngLast = this.coordinatesNew.size() > 0 ? this.coordinatesNew.get(1) : this.coordinates.get(2);
+    LatLng latLngTee = getStartCoordForLine(latLng, latLngLast);
+    if (this.coordinatesNew.size() > 0){
+      this.coordinatesNew.set(0, latLngTee);
+    } else {
+      this.coordinatesNew.add(0,latLngTee);
+      this.coordinatesNew.add(1, this.coordinates.get(2));
+    }
+    this.coordinates.set(indexChange, latLngPin);
+    if(this.coordinates.size() > 2){
+      this.coordinates.remove(this.coordinates.size()-1);
+    }
+    if (polylineNew != null) {
+      polylineNew.setPoints(this.coordinatesNew);
+    }
+    if(isFirst){
+      this.addToMap(this.map);
+    }
+  }
+
+  private LatLng getStartCoordForLine(LatLng startCoord, LatLng endCoord) {
+    if(this.map != null) {
+      Point centerPoint = this.map.getProjection().toScreenLocation(startCoord);
+      Point endPoint = this.map.getProjection().toScreenLocation(endCoord);
+
+      double xDistance = (endPoint.x - centerPoint.x);
+      double yDistance = (endPoint.y - centerPoint.y);
+
+      double distanceBetweenPoint = Math.sqrt(Math.pow(xDistance, 2) + Math.pow(yDistance, 2));
+      int radiusTest = (int) (44 * Resources.getSystem().getDisplayMetrics().density);
+      double factor = distanceBetweenPoint / radiusTest;
+
+      int newX = (int) (centerPoint.x + (xDistance / factor));
+      int newY = (int) (centerPoint.y + (yDistance / factor));
+
+      Point newPoint = new Point(newX, newY);
+      return this.map.getProjection().fromScreenLocation(newPoint);
+    }
+    return null;
+  }
+
   public void setColor(int color) {
     this.color = color;
     if (polyline != null) {
@@ -144,6 +217,19 @@ public class AirMapPolyline extends AirMapFeature {
     return options;
   }
 
+  private PolylineOptions createNewPolylineOptions() {
+    PolylineOptions options = new PolylineOptions();
+    options.addAll(coordinatesNew);
+    options.color(color);
+    options.width(width);
+    options.geodesic(geodesic);
+    options.zIndex(zIndex);
+    options.startCap(lineCap);
+    options.endCap(lineCap);
+    options.pattern(this.pattern);
+    return options;
+  }
+
   @Override
   public Object getFeature() {
     return polyline;
@@ -151,12 +237,35 @@ public class AirMapPolyline extends AirMapFeature {
 
   @Override
   public void addToMap(GoogleMap map) {
-    polyline = map.addPolyline(getPolylineOptions());
-    polyline.setClickable(this.tappable);
+      this.map = map;
+      polyline = map.addPolyline(getPolylineOptions());
+      polyline.setClickable(this.tappable);
+      polylineNew = map.addPolyline(createNewPolylineOptions());
+
   }
 
   @Override
   public void removeFromMap(GoogleMap map) {
-    polyline.remove();
+    if(polyline != null){
+      polyline.remove();
+    }
+    if(polylineNew != null){
+      polylineNew.remove();
+    }
+  }
+
+  public String getIdentifier() {
+    return identifier;
+  }
+
+  public void setIdentifier(String identifier) {
+    this.identifier = identifier;
+  }
+
+  public void changeCoordinatesWhenMarkerCircle(GoogleMap map){
+    this.map = map;
+    if(this.identifier.equals("distancePolyline")) {
+      updateCoordinates(this.coordinates.get(1), 1, true);
+    }
   }
 }
diff --git a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapPolylineManager.java b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapPolylineManager.java
index ea2a4f2..3641d6a 100644
--- a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapPolylineManager.java
+++ b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapPolylineManager.java
@@ -3,18 +3,22 @@ package com.airbnb.android.react.maps;
 import android.content.Context;
 import android.graphics.Color;
 import android.util.DisplayMetrics;
+import android.util.Log;
 import android.view.WindowManager;
 
+import androidx.annotation.NonNull;
 import androidx.annotation.Nullable;
 
 import com.facebook.react.bridge.ReactApplicationContext;
 import com.facebook.react.bridge.ReadableArray;
+import com.facebook.react.bridge.ReadableMap;
 import com.facebook.react.common.MapBuilder;
 import com.facebook.react.uimanager.ThemedReactContext;
 import com.facebook.react.uimanager.ViewGroupManager;
 import com.facebook.react.uimanager.annotations.ReactProp;
 import com.google.android.gms.maps.model.ButtCap;
 import com.google.android.gms.maps.model.Cap;
+import com.google.android.gms.maps.model.LatLng;
 import com.google.android.gms.maps.model.RoundCap;
 import com.google.android.gms.maps.model.SquareCap;
 
@@ -22,6 +26,7 @@ import java.util.Map;
 
 public class AirMapPolylineManager extends ViewGroupManager<AirMapPolyline> {
   private final DisplayMetrics metrics;
+  private AirMapPolyline viewCoordinates;
 
   public AirMapPolylineManager(ReactApplicationContext reactContext) {
     super();
@@ -31,6 +36,11 @@ public class AirMapPolylineManager extends ViewGroupManager<AirMapPolyline> {
         .getRealMetrics(metrics);
   }
 
+  @Override
+  public void onDropViewInstance(@NonNull AirMapPolyline view) {
+    super.onDropViewInstance(view);
+  }
+
   @Override
   public String getName() {
     return "AIRMapPolyline";
@@ -40,6 +50,11 @@ public class AirMapPolylineManager extends ViewGroupManager<AirMapPolyline> {
   public AirMapPolyline createViewInstance(ThemedReactContext context) {
     return new AirMapPolyline(context);
   }
+  @ReactProp(name = "identifier")
+  public void setIdentifier(AirMapPolyline view, String identifier) {
+    this.viewCoordinates = view;
+    view.setIdentifier(identifier);
+  }
 
   @ReactProp(name = "coordinates")
   public void setCoordinate(AirMapPolyline view, ReadableArray coordinates) {
@@ -97,6 +112,15 @@ public class AirMapPolylineManager extends ViewGroupManager<AirMapPolyline> {
       view.setLineDashPattern(patternValues);
   }
 
+  public void changeCoordinate(LatLng latLng) {
+    if(this.viewCoordinates != null) {
+      this.viewCoordinates.setCoordinatesLatLng(latLng);
+    }
+  }
+
+  public void resetView() {
+    this.viewCoordinates = null;
+  }
   @Override
   @Nullable
   public Map getExportedCustomDirectEventTypeConstants() {
diff --git a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapView.java b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapView.java
index 077127d..f44487b 100644
--- a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapView.java
+++ b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/AirMapView.java
@@ -2,6 +2,7 @@ package com.airbnb.android.react.maps;
 
 import android.content.Context;
 import android.content.res.ColorStateList;
+import android.content.res.Resources;
 import android.graphics.Bitmap;
 import android.graphics.Color;
 import android.graphics.Point;
@@ -11,6 +12,9 @@ import androidx.annotation.Nullable;
 import androidx.core.content.PermissionChecker;
 import androidx.core.view.GestureDetectorCompat;
 import androidx.core.view.MotionEventCompat;
+
+import android.graphics.Rect;
+import android.util.Log;
 import android.view.GestureDetector;
 import android.view.MotionEvent;
 import android.view.View;
@@ -53,6 +57,8 @@ import com.google.android.gms.maps.model.Polyline;
 import com.google.android.gms.maps.model.TileOverlay;
 import com.google.android.gms.maps.model.IndoorBuilding;
 import com.google.android.gms.maps.model.IndoorLevel;
+import com.google.android.gms.maps.model.VisibleRegion;
+import com.google.maps.android.PolyUtil;
 import com.google.maps.android.data.kml.KmlContainer;
 import com.google.maps.android.data.kml.KmlLayer;
 import com.google.maps.android.data.kml.KmlPlacemark;
@@ -72,1305 +78,1504 @@ import java.util.concurrent.ExecutionException;
 import static androidx.core.content.PermissionChecker.checkSelfPermission;
 
 public class AirMapView extends MapView implements GoogleMap.InfoWindowAdapter,
-    GoogleMap.OnMarkerDragListener, OnMapReadyCallback, GoogleMap.OnPoiClickListener, GoogleMap.OnIndoorStateChangeListener {
-  public GoogleMap map;
-  private ProgressBar mapLoadingProgressBar;
-  private RelativeLayout mapLoadingLayout;
-  private ImageView cacheImageView;
-  private Boolean isMapLoaded = false;
-  private Integer loadingBackgroundColor = null;
-  private Integer loadingIndicatorColor = null;
-  private final int baseMapPadding = 50;
-
-  private LatLngBounds boundsToMove;
-  private CameraUpdate cameraToSet;
-  private boolean showUserLocation = false;
-  private boolean handlePanDrag = false;
-  private boolean moveOnMarkerPress = true;
-  private boolean cacheEnabled = false;
-  private ReadableMap initialRegion;
-  private ReadableMap initialCamera;
-  private ReadableMap region;
-  private ReadableMap camera;
-  private String customMapStyleString;
-  private boolean initialRegionSet = false;
-  private boolean initialCameraSet = false;
-  private LatLngBounds cameraLastIdleBounds;
-  private int cameraMoveReason = 0;
-
-  private static final String[] PERMISSIONS = new String[]{
-      "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION"};
-
-  private final List<AirMapFeature> features = new ArrayList<>();
-  private final Map<Marker, AirMapMarker> markerMap = new HashMap<>();
-  private final Map<Polyline, AirMapPolyline> polylineMap = new HashMap<>();
-  private final Map<Polygon, AirMapPolygon> polygonMap = new HashMap<>();
-  private final Map<GroundOverlay, AirMapOverlay> overlayMap = new HashMap<>();
-  private final Map<TileOverlay, AirMapHeatmap> heatmapMap = new HashMap<>();
-  private final Map<TileOverlay, AirMapGradientPolyline> gradientPolylineMap = new HashMap<>();
-  private final GestureDetectorCompat gestureDetector;
-  private final AirMapManager manager;
-  private LifecycleEventListener lifecycleListener;
-  private boolean paused = false;
-  private boolean destroyed = false;
-  private final ThemedReactContext context;
-  private final EventDispatcher eventDispatcher;
-  private final FusedLocationSource fusedLocationSource;
-
-  private final ViewAttacherGroup attacherGroup;
-  private LatLng tapLocation;
-
-  private static boolean contextHasBug(Context context) {
-    return context == null ||
-        context.getResources() == null ||
-        context.getResources().getConfiguration() == null;
-  }
-
-  // We do this to fix this bug:
-  // https://github.com/react-native-maps/react-native-maps/issues/271
-  //
-  // which conflicts with another bug regarding the passed in context:
-  // https://github.com/react-native-maps/react-native-maps/issues/1147
-  //
-  // Doing this allows us to avoid both bugs.
-  private static Context getNonBuggyContext(ThemedReactContext reactContext,
-      ReactApplicationContext appContext) {
-    Context superContext = reactContext;
-    if (!contextHasBug(appContext.getCurrentActivity())) {
-      superContext = appContext.getCurrentActivity();
-    } else if (contextHasBug(superContext)) {
-      // we have the bug! let's try to find a better context to use
-      if (!contextHasBug(reactContext.getCurrentActivity())) {
-        superContext = reactContext.getCurrentActivity();
-      } else if (!contextHasBug(reactContext.getApplicationContext())) {
-        superContext = reactContext.getApplicationContext();
-      }
-
-    }
-    return superContext;
-  }
-
-  public AirMapView(ThemedReactContext reactContext, ReactApplicationContext appContext,
-      AirMapManager manager,
-      GoogleMapOptions googleMapOptions) {
-    super(getNonBuggyContext(reactContext, appContext), googleMapOptions);
-
-    this.manager = manager;
-    this.context = reactContext;
-
-    super.onCreate(null);
-    // TODO(lmr): what about onStart????
-    super.onResume();
-    super.getMapAsync(this);
-
-    final AirMapView view = this;
-
-    fusedLocationSource = new FusedLocationSource(context);
-
-    gestureDetector =
-        new GestureDetectorCompat(reactContext, new GestureDetector.SimpleOnGestureListener() {
-
-          @Override
-          public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX,
-              float distanceY) {
-            if (handlePanDrag) {
-              onPanDrag(e2);
+        GoogleMap.OnMarkerDragListener, OnMapReadyCallback, GoogleMap.OnPoiClickListener, GoogleMap.OnIndoorStateChangeListener {
+    public GoogleMap map;
+    private ProgressBar mapLoadingProgressBar;
+    private RelativeLayout mapLoadingLayout;
+    private ImageView cacheImageView;
+    private Boolean isMapLoaded = false;
+    private Integer loadingBackgroundColor = null;
+    private Integer loadingIndicatorColor = null;
+    private final int baseMapPadding = 50;
+
+    private LatLngBounds boundsToMove;
+    private CameraUpdate cameraToSet;
+    private boolean showUserLocation = false;
+    private boolean handlePanDrag = false;
+    private boolean moveOnMarkerPress = true;
+    private boolean cacheEnabled = false;
+    private ReadableMap initialRegion;
+    private ReadableMap initialCamera;
+    private ReadableMap region;
+    private ReadableMap camera;
+    private String customMapStyleString;
+    private boolean initialRegionSet = false;
+    private boolean initialCameraSet = false;
+    private LatLngBounds cameraLastIdleBounds;
+    private int cameraMoveReason = 0;
+
+    private static final String[] PERMISSIONS = new String[]{
+            "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION"};
+
+    private final List<AirMapFeature> features = new ArrayList<>();
+    private final Map<Marker, AirMapMarker> markerMap = new HashMap<>();
+    private final Map<Polyline, AirMapPolyline> polylineMap = new HashMap<>();
+    private final Map<Polygon, AirMapPolygon> polygonMap = new HashMap<>();
+    private final Map<GroundOverlay, AirMapOverlay> overlayMap = new HashMap<>();
+    private final Map<TileOverlay, AirMapHeatmap> heatmapMap = new HashMap<>();
+    private final Map<TileOverlay, AirMapGradientPolyline> gradientPolylineMap = new HashMap<>();
+    private final GestureDetectorCompat gestureDetector;
+    private final AirMapManager manager;
+    private LifecycleEventListener lifecycleListener;
+    private boolean paused = false;
+    private boolean destroyed = false;
+    private final ThemedReactContext context;
+    private final EventDispatcher eventDispatcher;
+    private final FusedLocationSource fusedLocationSource;
+
+    private final ViewAttacherGroup attacherGroup;
+    private LatLng tapLocation;
+    private boolean isLongPress = false;
+    private Point pointLongPress;
+    private int finger = (int) (60 * Resources.getSystem().getDisplayMetrics().density);
+    private String TAG = "AirMapView";
+    private AirMapMarker markerSelect;
+    private String idMarker = "";
+    private boolean isZoom = false;
+    private boolean tapAtMarker = false;
+
+    private static boolean contextHasBug(Context context) {
+        return context == null ||
+                context.getResources() == null ||
+                context.getResources().getConfiguration() == null;
+    }
+
+    // We do this to fix this bug:
+    // https://github.com/react-native-maps/react-native-maps/issues/271
+    //
+    // which conflicts with another bug regarding the passed in context:
+    // https://github.com/react-native-maps/react-native-maps/issues/1147
+    //
+    // Doing this allows us to avoid both bugs.
+    private static Context getNonBuggyContext(ThemedReactContext reactContext,
+                                              ReactApplicationContext appContext) {
+        Context superContext = reactContext;
+        if (!contextHasBug(appContext.getCurrentActivity())) {
+            superContext = appContext.getCurrentActivity();
+        } else if (contextHasBug(superContext)) {
+            // we have the bug! let's try to find a better context to use
+            if (!contextHasBug(reactContext.getCurrentActivity())) {
+                superContext = reactContext.getCurrentActivity();
+            } else if (!contextHasBug(reactContext.getApplicationContext())) {
+                superContext = reactContext.getApplicationContext();
+            }
+
+        }
+        return superContext;
+    }
+
+    public AirMapView(ThemedReactContext reactContext, ReactApplicationContext appContext,
+                      AirMapManager manager,
+                      GoogleMapOptions googleMapOptions) {
+        super(getNonBuggyContext(reactContext, appContext), googleMapOptions);
+
+        this.manager = manager;
+        this.context = reactContext;
+
+        super.onCreate(null);
+        // TODO(lmr): what about onStart????
+        super.onResume();
+        super.getMapAsync(this);
+
+        final AirMapView view = this;
+
+        fusedLocationSource = new FusedLocationSource(context);
+
+        gestureDetector =
+                new GestureDetectorCompat(reactContext, new GestureDetector.SimpleOnGestureListener() {
+
+                    @Override
+                    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX,
+                                            float distanceY) {
+                        if (handlePanDrag && tapAtMarker && !isZoom) {
+                            onPanDrag(e2);
+                        }
+                        return false;
+                    }
+
+                    @Override
+                    public boolean onDoubleTap(MotionEvent ev) {
+                        onDoublePress(ev);
+                        return false;
+                    }
+                });
+
+        this.addOnLayoutChangeListener(new OnLayoutChangeListener() {
+            @Override
+            public void onLayoutChange(View v, int left, int top, int right, int bottom,
+                                       int oldLeft, int oldTop, int oldRight, int oldBottom) {
+                if (!paused) {
+                    AirMapView.this.cacheView();
+                }
+            }
+        });
+
+        eventDispatcher = reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher();
+
+        // Set up a parent view for triggering visibility in subviews that depend on it.
+        // Mainly ReactImageView depends on Fresco which depends on onVisibilityChanged() event
+        attacherGroup = new ViewAttacherGroup(context);
+        LayoutParams attacherLayoutParams = new LayoutParams(0, 0);
+        attacherLayoutParams.width = 0;
+        attacherLayoutParams.height = 0;
+        attacherLayoutParams.leftMargin = 99999999;
+        attacherLayoutParams.topMargin = 99999999;
+        attacherGroup.setLayoutParams(attacherLayoutParams);
+        addView(attacherGroup);
+    }
+
+    @Override
+    public void onMapReady(@NonNull final GoogleMap map) {
+        if (destroyed) {
+            return;
+        }
+        this.map = map;
+        this.map.setInfoWindowAdapter(this);
+        this.map.setOnMarkerDragListener(this);
+        this.map.setOnPoiClickListener(this);
+        this.map.setOnIndoorStateChangeListener(this);
+
+        applyBridgedProps();
+
+        manager.pushEvent(context, this, "onMapReady", new WritableNativeMap());
+
+        final AirMapView view = this;
+
+        map.setOnMyLocationChangeListener(new GoogleMap.OnMyLocationChangeListener() {
+            @Override
+            public void onMyLocationChange(Location location) {
+                WritableMap event = new WritableNativeMap();
+
+                WritableMap coordinate = new WritableNativeMap();
+                coordinate.putDouble("latitude", location.getLatitude());
+                coordinate.putDouble("longitude", location.getLongitude());
+                coordinate.putDouble("altitude", location.getAltitude());
+                coordinate.putDouble("timestamp", location.getTime());
+                coordinate.putDouble("accuracy", location.getAccuracy());
+                coordinate.putDouble("speed", location.getSpeed());
+                coordinate.putDouble("heading", location.getBearing());
+                coordinate.putBoolean("isFromMockProvider", location.isFromMockProvider());
+
+                event.putMap("coordinate", coordinate);
+
+                manager.pushEvent(context, view, "onUserLocationChange", event);
+            }
+        });
+
+        map.setOnMarkerClickListener(new GoogleMap.OnMarkerClickListener() {
+            @Override
+            public boolean onMarkerClick(Marker marker) {
+                WritableMap event;
+                idMarker = "";
+
+                AirMapMarker airMapMarker = getMarkerMap(marker);
+                if(!airMapMarker.getTappable()){
+                    event = makeClickEventData(tapLocation);
+                    event.putString("action", "press");
+                    manager.pushEvent(context, view, "onPress", event);
+                } else{
+                    if (markerSelect != null && markerSelect.getIdentifier().contains(airMapMarker.getIdentifier())) {
+                        markerSelect = null;
+                    } else {
+                        markerSelect = airMapMarker;
+                    }
+                    event = makeClickEventData(marker.getPosition());
+                    event.putString("action", "marker-press");
+                    event.putString("id", airMapMarker.getIdentifier());
+                    manager.pushEvent(context, view, "onMarkerPress", event);
+
+                    event = makeClickEventData(marker.getPosition());
+                    event.putString("action", "marker-press");
+                    event.putString("id", airMapMarker.getIdentifier());
+                    manager.pushEvent(context, airMapMarker, "onPress", event);
+                }
+                // Return false to open the callout info window and center on the marker
+                // https://developers.google.com/android/reference/com/google/android/gms/maps/GoogleMap
+                // .OnMarkerClickListener
+                if (view.moveOnMarkerPress) {
+                    return false;
+                } else {
+                    marker.showInfoWindow();
+                    return true;
+                }
+            }
+        });
+
+
+        map.setOnPolygonClickListener(new GoogleMap.OnPolygonClickListener() {
+            @Override
+            public void onPolygonClick(Polygon polygon) {
+                WritableMap event = makeClickEventData(tapLocation);
+                event.putString("action", "polygon-press");
+                manager.pushEvent(context, polygonMap.get(polygon), "onPress", event);
+            }
+        });
+
+        map.setOnPolylineClickListener(new GoogleMap.OnPolylineClickListener() {
+            @Override
+            public void onPolylineClick(Polyline polyline) {
+                WritableMap event = makeClickEventData(tapLocation);
+                event.putString("action", "polyline-press");
+                manager.pushEvent(context, polylineMap.get(polyline), "onPress", event);
+            }
+        });
+
+        map.setOnInfoWindowClickListener(new GoogleMap.OnInfoWindowClickListener() {
+            @Override
+            public void onInfoWindowClick(Marker marker) {
+                WritableMap event;
+
+                event = makeClickEventData(marker.getPosition());
+                event.putString("action", "callout-press");
+                manager.pushEvent(context, view, "onCalloutPress", event);
+
+                event = makeClickEventData(marker.getPosition());
+                event.putString("action", "callout-press");
+                AirMapMarker markerView = getMarkerMap(marker);
+                manager.pushEvent(context, markerView, "onCalloutPress", event);
+
+                event = makeClickEventData(marker.getPosition());
+                event.putString("action", "callout-press");
+                AirMapCallout infoWindow = markerView.getCalloutView();
+                if (infoWindow != null) manager.pushEvent(context, infoWindow, "onPress", event);
+            }
+        });
+
+        map.setOnMapClickListener(new GoogleMap.OnMapClickListener() {
+            @Override
+            public void onMapClick(LatLng point) {
+                WritableMap event = makeClickEventData(point);
+                event.putString("action", "press");
+                manager.pushEvent(context, view, "onPress", event);
+            }
+        });
+
+        map.setOnMapLongClickListener(new GoogleMap.OnMapLongClickListener() {
+            @Override
+            public void onMapLongClick(LatLng point) {
+                isLongPress = true;
+                tapAtMarker = true;
+                map.getUiSettings().setScrollGesturesEnabled(false);
+                if (pointLongPress != null) {
+                    LatLng coords = map.getProjection().fromScreenLocation(pointLongPress);
+                    WritableMap event = makeClickEventData(coords);
+                    event.putString("action", "long-press");
+                    manager.pushEvent(context, view, "onLongPress", event);
+                } else {
+                    WritableMap event = makeClickEventData(point);
+                    event.putString("action", "long-press");
+                    manager.pushEvent(context, view, "onLongPress", event);
+                }
+            }
+        });
+
+        map.setOnGroundOverlayClickListener(new GoogleMap.OnGroundOverlayClickListener() {
+            @Override
+            public void onGroundOverlayClick(GroundOverlay groundOverlay) {
+                WritableMap event = makeClickEventData(groundOverlay.getPosition());
+                event.putString("action", "overlay-press");
+                manager.pushEvent(context, overlayMap.get(groundOverlay), "onPress", event);
+            }
+        });
+
+        map.setOnCameraMoveStartedListener(new GoogleMap.OnCameraMoveStartedListener() {
+            @Override
+            public void onCameraMoveStarted(int reason) {
+                cameraMoveReason = reason;
+            }
+        });
+
+        map.setOnCameraMoveListener(new GoogleMap.OnCameraMoveListener() {
+            @Override
+            public void onCameraMove() {
+                LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;
+
+                cameraLastIdleBounds = null;
+                boolean isGesture = GoogleMap.OnCameraMoveStartedListener.REASON_GESTURE == cameraMoveReason;
+
+                RegionChangeEvent event = new RegionChangeEvent(getId(), bounds, true, isGesture);
+                eventDispatcher.dispatchEvent(event);
+            }
+        });
+
+        map.setOnCameraIdleListener(new GoogleMap.OnCameraIdleListener() {
+            @Override
+            public void onCameraIdle() {
+                LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;
+                if ((cameraMoveReason != 0) &&
+                        ((cameraLastIdleBounds == null) ||
+                                LatLngBoundsUtils.BoundsAreDifferent(bounds, cameraLastIdleBounds))) {
+
+                    cameraLastIdleBounds = bounds;
+                    boolean isGesture = GoogleMap.OnCameraMoveStartedListener.REASON_GESTURE == cameraMoveReason;
+
+                    RegionChangeEvent event = new RegionChangeEvent(getId(), bounds, false, isGesture);
+                    eventDispatcher.dispatchEvent(event);
+                }
+            }
+        });
+
+        map.setOnMapLoadedCallback(new GoogleMap.OnMapLoadedCallback() {
+            @Override
+            public void onMapLoaded() {
+                isMapLoaded = true;
+                manager.pushEvent(context, view, "onMapLoaded", new WritableNativeMap());
+                AirMapView.this.cacheView();
             }
-            return false;
-          }
-
-          @Override
-          public boolean onDoubleTap(MotionEvent ev) {
-            onDoublePress(ev);
-            return false;
-          }
         });
 
-    this.addOnLayoutChangeListener(new OnLayoutChangeListener() {
-      @Override public void onLayoutChange(View v, int left, int top, int right, int bottom,
-          int oldLeft, int oldTop, int oldRight, int oldBottom) {
+        // We need to be sure to disable location-tracking when app enters background, in-case some
+        // other module
+        // has acquired a wake-lock and is controlling location-updates, otherwise, location-manager
+        // will be left
+        // updating location constantly, killing the battery, even though some other location-mgmt
+        // module may
+        // desire to shut-down location-services.
+        lifecycleListener = new LifecycleEventListener() {
+            @Override
+            public void onHostResume() {
+                if (hasPermissions() && map != null) {
+                    //noinspection MissingPermission
+                    map.setMyLocationEnabled(showUserLocation);
+                    map.setLocationSource(fusedLocationSource);
+                }
+                synchronized (AirMapView.this) {
+                    if (!destroyed) {
+                        AirMapView.this.onResume();
+                    }
+                    paused = false;
+                }
+            }
+
+            @Override
+            public void onHostPause() {
+                if (hasPermissions() && map != null) {
+                    //noinspection MissingPermission
+                    map.setMyLocationEnabled(false);
+                }
+                synchronized (AirMapView.this) {
+                    if (!destroyed) {
+                        AirMapView.this.onPause();
+                    }
+                    paused = true;
+                }
+            }
+
+            @Override
+            public void onHostDestroy() {
+                AirMapView.this.doDestroy();
+            }
+        };
+
+        context.addLifecycleEventListener(lifecycleListener);
+    }
+
+    private boolean hasPermissions() {
+        return checkSelfPermission(getContext(), PERMISSIONS[0]) == PermissionChecker.PERMISSION_GRANTED ||
+                checkSelfPermission(getContext(), PERMISSIONS[1]) == PermissionChecker.PERMISSION_GRANTED;
+    }
+
+
+    /*
+    onDestroy is final method so I can't override it.
+     */
+    public synchronized void doDestroy() {
+        if (destroyed) {
+            return;
+        }
+        destroyed = true;
+
+        if (lifecycleListener != null && context != null) {
+            context.removeLifecycleEventListener(lifecycleListener);
+            lifecycleListener = null;
+        }
         if (!paused) {
-          AirMapView.this.cacheView();
+            onPause();
+            paused = true;
         }
-      }
-    });
-
-    eventDispatcher = reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher();
-
-    // Set up a parent view for triggering visibility in subviews that depend on it.
-    // Mainly ReactImageView depends on Fresco which depends on onVisibilityChanged() event
-    attacherGroup = new ViewAttacherGroup(context);
-    LayoutParams attacherLayoutParams = new LayoutParams(0, 0);
-    attacherLayoutParams.width = 0;
-    attacherLayoutParams.height = 0;
-    attacherLayoutParams.leftMargin = 99999999;
-    attacherLayoutParams.topMargin = 99999999;
-    attacherGroup.setLayoutParams(attacherLayoutParams);
-    addView(attacherGroup);
-  }
-
-  @Override
-  public void onMapReady(@NonNull final GoogleMap map) {
-    if (destroyed) {
-      return;
-    }
-    this.map = map;
-    this.map.setInfoWindowAdapter(this);
-    this.map.setOnMarkerDragListener(this);
-    this.map.setOnPoiClickListener(this);
-    this.map.setOnIndoorStateChangeListener(this);
-
-    applyBridgedProps();
-
-    manager.pushEvent(context, this, "onMapReady", new WritableNativeMap());
-
-    final AirMapView view = this;
-
-    map.setOnMyLocationChangeListener(new GoogleMap.OnMyLocationChangeListener() {
-      @Override
-      public void onMyLocationChange(Location location){
-        WritableMap event = new WritableNativeMap();
+        onDestroy();
+    }
 
-        WritableMap coordinate = new WritableNativeMap();
-        coordinate.putDouble("latitude", location.getLatitude());
-        coordinate.putDouble("longitude", location.getLongitude());
-        coordinate.putDouble("altitude", location.getAltitude());
-        coordinate.putDouble("timestamp", location.getTime());
-        coordinate.putDouble("accuracy", location.getAccuracy());
-        coordinate.putDouble("speed", location.getSpeed());
-        coordinate.putDouble("heading", location.getBearing());
-        coordinate.putBoolean("isFromMockProvider", location.isFromMockProvider());
+    public void setInitialRegion(ReadableMap initialRegion) {
+        this.initialRegion = initialRegion;
+        // Theoretically onMapReady might be called before setInitialRegion
+        // In that case, trigger moveToRegion manually
+        if (!initialRegionSet && map != null) {
+            moveToRegion(initialRegion);
+            initialRegionSet = true;
+        }
+    }
 
-        event.putMap("coordinate", coordinate);
+    public void setInitialCamera(ReadableMap initialCamera) {
+        this.initialCamera = initialCamera;
+        // Theoretically onMapReady might be called before setInitialCamera
+        // In that case, trigger moveToCamera manually
+        if (!initialCameraSet && map != null) {
+            moveToCamera(initialCamera);
+            initialCameraSet = true;
+        }
+    }
 
-        manager.pushEvent(context, view, "onUserLocationChange", event);
-      }
-    });
+    public void setPolygonToZoom(ReadableMap data, ReadableArray polygons) {
+        if (polygons.size() > 0 && this.map != null && data != null) {
+            ReadableMap center = data.getMap("center");
+            LatLng target;
+            double lng = center.getDouble("longitude");
+            double lat = center.getDouble("latitude");
+            target = new LatLng(lat, lng);
+            float calculatedZoom = 14f;
+            map.setMinZoomPreference(14);
+            ReadableMap northEast = data.getMap("northEast");
+            ReadableMap southWest = data.getMap("southWest");
+            int paddingTop = data.getInt("paddingTop");
+
+            List<LatLng> arrPolygon = new ArrayList<>();
+            for (int i = 0; i < polygons.size(); i++) {
+                LatLng latLng = new LatLng(polygons.getMap(i).getDouble("latitude"), polygons.getMap(i).getDouble("longitude"));
+                arrPolygon.add(latLng);
+            }
+            setMapBoundaries(northEast, southWest);
+            float bearing = (float) data.getDouble("heading");
+            float tilt = (float) data.getDouble("pitch");
+            ReadableMap centerH = data.getMap("centerH");
+
+            LatLng targetH;
+            double lngH = centerH.getDouble("longitude");
+            double latH = centerH.getDouble("latitude");
+            targetH = new LatLng(latH, lngH);
+            CameraPosition cameraBearing = new CameraPosition(target, calculatedZoom, tilt, bearing);
+            map.moveCamera(CameraUpdateFactory.newCameraPosition(cameraBearing));
+
+            Point pointH = map.getProjection().toScreenLocation(targetH);
+            Point pointV = map.getProjection().toScreenLocation(target);
+            Point pointFormat = new Point(pointH.x, pointV.y);
+            LatLng centerLatLng =  map.getProjection().fromScreenLocation(pointFormat);
+            if(centerLatLng.latitude == 0 && centerLatLng.longitude == 0){
+                centerLatLng = target;
+            }
+            while (isValidMapZoomLevel(arrPolygon, paddingTop)) {
+                calculatedZoom += 0.05;
+                cameraBearing = new CameraPosition(centerLatLng, calculatedZoom, tilt, bearing);
+                map.moveCamera(CameraUpdateFactory.newCameraPosition(cameraBearing));
+                if (calculatedZoom > 25) {
+                    break;
+                }
+            }
+            if(super.getHeight() <= 0 && calculatedZoom == 15){
+                calculatedZoom = 16;
+            }else {
+                calculatedZoom -= 0.69;
+            }
+            map.setMinZoomPreference(calculatedZoom);
+            Point pointPaddingTop = new Point(pointH.x, pointV.y - paddingTop * (int) Resources.getSystem().getDisplayMetrics().density);
+            LatLng centerPaddingTop =  map.getProjection().fromScreenLocation(pointPaddingTop);
+
+            cameraBearing = new CameraPosition(centerPaddingTop, calculatedZoom, tilt, bearing);
+            CameraUpdate update = CameraUpdateFactory.newCameraPosition(cameraBearing);
+            map.moveCamera(update);
+            if(super.getHeight() > 0) {
+                WritableMap event = new WritableNativeMap();
+                WritableMap coordinate = new WritableNativeMap();
+                coordinate.putDouble("latitude", centerPaddingTop.latitude);
+                coordinate.putDouble("longitude", centerPaddingTop.longitude);
+                event.putMap("center", coordinate);
+                event.putDouble("zoom", calculatedZoom);
+                manager.pushEvent(context, this, "onChangeViewMap", event);
+            }
+        }
+    }
 
-    map.setOnMarkerClickListener(new GoogleMap.OnMarkerClickListener() {
-      @Override
-      public boolean onMarkerClick(Marker marker) {
-        WritableMap event;
-        AirMapMarker airMapMarker = getMarkerMap(marker);
 
-        event = makeClickEventData(marker.getPosition());
-        event.putString("action", "marker-press");
-        event.putString("id", airMapMarker.getIdentifier());
-        manager.pushEvent(context, view, "onMarkerPress", event);
 
-        event = makeClickEventData(marker.getPosition());
-        event.putString("action", "marker-press");
-        event.putString("id", airMapMarker.getIdentifier());
-        manager.pushEvent(context, airMapMarker, "onPress", event);
-
-        // Return false to open the callout info window and center on the marker
-        // https://developers.google.com/android/reference/com/google/android/gms/maps/GoogleMap
-        // .OnMarkerClickListener
-        if (view.moveOnMarkerPress) {
-          return false;
-        } else {
-          marker.showInfoWindow();
-          return true;
+    private boolean isValidMapZoomLevel(List<LatLng> listLocations, int paddingTop) {
+        for (LatLng latLng : listLocations) {
+            Point point = map.getProjection().toScreenLocation(latLng);
+            int padding = 0;
+            if(paddingTop > 0){
+                padding = 100 * (int) Resources.getSystem().getDisplayMetrics().density;
+            }
+            if(point.x < -55 || point.y <= padding || point.x > super.getWidth() + 55 || point.y >= super.getHeight()){
+                return false;
+            }
         }
-      }
-    });
-
-    map.setOnPolygonClickListener(new GoogleMap.OnPolygonClickListener() {
-      @Override
-      public void onPolygonClick(Polygon polygon) {
-        WritableMap event = makeClickEventData(tapLocation);
-        event.putString("action", "polygon-press");
-        manager.pushEvent(context, polygonMap.get(polygon), "onPress", event);
-      }
-    });
-
-    map.setOnPolylineClickListener(new GoogleMap.OnPolylineClickListener() {
-      @Override
-      public void onPolylineClick(Polyline polyline) {
-        WritableMap event = makeClickEventData(tapLocation);
-        event.putString("action", "polyline-press");
-        manager.pushEvent(context, polylineMap.get(polyline), "onPress", event);
-      }
-    });
-
-    map.setOnInfoWindowClickListener(new GoogleMap.OnInfoWindowClickListener() {
-      @Override
-      public void onInfoWindowClick(Marker marker) {
-        WritableMap event;
+        return true;
+    }
 
-        event = makeClickEventData(marker.getPosition());
-        event.putString("action", "callout-press");
-        manager.pushEvent(context, view, "onCalloutPress", event);
+    public void resetMarkerSelected(){
+        markerSelect = null;
+        this.idMarker = "";
+    }
 
-        event = makeClickEventData(marker.getPosition());
-        event.putString("action", "callout-press");
-        AirMapMarker markerView = getMarkerMap(marker);
-        manager.pushEvent(context, markerView, "onCalloutPress", event);
+    public void setTapMarker(String idMarker){
+        this.idMarker = idMarker;
+        for (Map.Entry<Marker, AirMapMarker> entryMarker : markerMap.entrySet()) {
+            if (entryMarker.getValue().getIdentifier().equals(idMarker)) {
+                markerSelect = entryMarker.getValue();
+                break;
+            }
+        }
+    }
 
-        event = makeClickEventData(marker.getPosition());
-        event.putString("action", "callout-press");
-        AirMapCallout infoWindow = markerView.getCalloutView();
-        if (infoWindow != null) manager.pushEvent(context, infoWindow, "onPress", event);
-      }
-    });
-
-    map.setOnMapClickListener(new GoogleMap.OnMapClickListener() {
-      @Override
-      public void onMapClick(LatLng point) {
-        WritableMap event = makeClickEventData(point);
-        event.putString("action", "press");
-        manager.pushEvent(context, view, "onPress", event);
-      }
-    });
-
-    map.setOnMapLongClickListener(new GoogleMap.OnMapLongClickListener() {
-      @Override
-      public void onMapLongClick(LatLng point) {
-        WritableMap event = makeClickEventData(point);
-        event.putString("action", "long-press");
-        manager.pushEvent(context, view, "onLongPress", makeClickEventData(point));
-      }
-    });
-
-    map.setOnGroundOverlayClickListener(new GoogleMap.OnGroundOverlayClickListener() {
-      @Override
-      public void onGroundOverlayClick(GroundOverlay groundOverlay) {
-        WritableMap event = makeClickEventData(groundOverlay.getPosition());
-        event.putString("action", "overlay-press");
-        manager.pushEvent(context, overlayMap.get(groundOverlay), "onPress", event);
-      }
-    });
-
-    map.setOnCameraMoveStartedListener(new GoogleMap.OnCameraMoveStartedListener() {
-      @Override
-      public void onCameraMoveStarted(int reason) {
-        cameraMoveReason = reason;
-      }
-    });
-
-    map.setOnCameraMoveListener(new GoogleMap.OnCameraMoveListener() {
-      @Override
-      public void onCameraMove() {
-        LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;
+    private boolean viewForMarker(AirMapMarker airMarker, Point pointTouch){
+        Point pointStart = map.getProjection().toScreenLocation(airMarker.getMarkerOptions().getPosition());
+        float anchorX = airMarker.getMarkerOptions().getAnchorU();
+        float anchorY = airMarker.getMarkerOptions().getAnchorV();
+        int height = airMarker.getHeight();
+        int width = airMarker.getWidth();
+        pointStart.x -= (int) (anchorX * width);
+        pointStart.y -= (int) (anchorY * height);
+        Point pointEnd = new Point((pointStart.x + width), pointStart.y + height);
+        Rect rect = new Rect(pointStart.x, pointStart.y, pointEnd.x, pointEnd.y);
+        if(rect.contains(pointTouch.x, pointTouch.y)){
+            return true;
+        }
+        return false;
+    }
+    private AirMapMarker getTapAtMarker(Point pointTouch) {
 
-        cameraLastIdleBounds = null;
-        boolean isGesture = GoogleMap.OnCameraMoveStartedListener.REASON_GESTURE == cameraMoveReason;
+        for (Map.Entry<Marker, AirMapMarker> entryMarker : markerMap.entrySet()) {
+            if (viewForMarker(entryMarker.getValue(), pointTouch)) {
+                return entryMarker.getValue();
+            }
+        }
 
-        RegionChangeEvent event = new RegionChangeEvent(getId(), bounds, true, isGesture);
-        eventDispatcher.dispatchEvent(event);
-      }
-    });
+        return null;
+    }
+    private void applyBridgedProps() {
+        if (initialRegion != null) {
+            moveToRegion(initialRegion);
+            initialRegionSet = true;
+        } else if (initialCamera != null) {
+            moveToCamera(initialCamera);
+            initialCameraSet = true;
+        } else if (region != null) {
+            moveToRegion(region);
+        } else {
+            moveToCamera(camera);
+        }
+        if (customMapStyleString != null) {
+            map.setMapStyle(new MapStyleOptions(customMapStyleString));
+        }
+    }
 
-    map.setOnCameraIdleListener(new GoogleMap.OnCameraIdleListener() {
-      @Override
-      public void onCameraIdle() {
-        LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;
-        if ((cameraMoveReason != 0) &&
-          ((cameraLastIdleBounds == null) ||
-            LatLngBoundsUtils.BoundsAreDifferent(bounds, cameraLastIdleBounds))) {
+    private void moveToRegion(ReadableMap region) {
+        if (region == null) return;
+
+        double lng = region.getDouble("longitude");
+        double lat = region.getDouble("latitude");
+        double lngDelta = region.getDouble("longitudeDelta");
+        double latDelta = region.getDouble("latitudeDelta");
+        LatLngBounds bounds = new LatLngBounds(
+                new LatLng(lat - latDelta / 2, lng - lngDelta / 2), // southwest
+                new LatLng(lat + latDelta / 2, lng + lngDelta / 2)  // northeast
+        );
+        if (super.getHeight() <= 0 || super.getWidth() <= 0) {
+            // in this case, our map has not been laid out yet, so we save the bounds in a local
+            // variable, and make a guess of zoomLevel 10. Not to worry, though: as soon as layout
+            // occurs, we will move the camera to the saved bounds. Note that if we tried to move
+            // to the bounds now, it would trigger an exception.
+            map.moveCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(lat, lng), 10));
+            boundsToMove = bounds;
+        } else {
+            map.moveCamera(CameraUpdateFactory.newLatLngBounds(bounds, 0));
+            boundsToMove = null;
+        }
+    }
 
-          cameraLastIdleBounds = bounds;
-          boolean isGesture = GoogleMap.OnCameraMoveStartedListener.REASON_GESTURE == cameraMoveReason;
+    public void setRegion(ReadableMap region) {
+        this.region = region;
+        if (region != null && map != null) {
+            moveToRegion(region);
+        }
+    }
 
-          RegionChangeEvent event = new RegionChangeEvent(getId(), bounds, false, isGesture);
-          eventDispatcher.dispatchEvent(event);
+    public void setCamera(ReadableMap camera) {
+        this.camera = camera;
+        if (camera != null && map != null) {
+            moveToCamera(camera);
         }
-      }
-    });
-
-    map.setOnMapLoadedCallback(new GoogleMap.OnMapLoadedCallback() {
-      @Override public void onMapLoaded() {
-        isMapLoaded = true;
-        manager.pushEvent(context, view, "onMapLoaded", new WritableNativeMap());
-        AirMapView.this.cacheView();
-      }
-    });
-
-    // We need to be sure to disable location-tracking when app enters background, in-case some
-    // other module
-    // has acquired a wake-lock and is controlling location-updates, otherwise, location-manager
-    // will be left
-    // updating location constantly, killing the battery, even though some other location-mgmt
-    // module may
-    // desire to shut-down location-services.
-    lifecycleListener = new LifecycleEventListener() {
-      @Override
-      public void onHostResume() {
-        if (hasPermissions() && map != null) {
-          //noinspection MissingPermission
-          map.setMyLocationEnabled(showUserLocation);
-          map.setLocationSource(fusedLocationSource);
+    }
+
+    public void moveToCamera(ReadableMap camera) {
+        if (camera == null) return;
+
+        CameraPosition.Builder builder = new CameraPosition.Builder();
+
+        ReadableMap center = camera.getMap("center");
+        if (center != null) {
+            double lng = center.getDouble("longitude");
+            double lat = center.getDouble("latitude");
+            builder.target(new LatLng(lat, lng));
         }
-        synchronized (AirMapView.this) {
-          if (!destroyed) {
-            AirMapView.this.onResume();
-          }
-          paused = false;
+
+        builder.tilt((float) camera.getDouble("pitch"));
+        builder.bearing((float) camera.getDouble("heading"));
+        builder.zoom((float) camera.getDouble("zoom"));
+
+        CameraUpdate update = CameraUpdateFactory.newCameraPosition(builder.build());
+
+        if (super.getHeight() <= 0 || super.getWidth() <= 0) {
+            // in this case, our map has not been laid out yet, so we save the camera update in a
+            // local variable. As soon as layout occurs, we will move the camera to the saved update.
+            // Note that if we tried to move to the camera now, it would trigger an exception.
+            cameraToSet = update;
+        } else {
+            map.moveCamera(update);
+            cameraToSet = null;
         }
-      }
+    }
 
-      @Override
-      public void onHostPause() {
-        if (hasPermissions() && map != null) {
-          //noinspection MissingPermission
-          map.setMyLocationEnabled(false);
+    public void setMapStyle(@Nullable String customMapStyleString) {
+        this.customMapStyleString = customMapStyleString;
+        if (map != null && customMapStyleString != null) {
+            map.setMapStyle(new MapStyleOptions(customMapStyleString));
         }
-        synchronized (AirMapView.this) {
-          if (!destroyed) {
-            AirMapView.this.onPause();
-          }
-          paused = true;
+    }
+
+    public void setShowsUserLocation(boolean showUserLocation) {
+        this.showUserLocation = showUserLocation; // hold onto this for lifecycle handling
+        if (hasPermissions()) {
+            map.setLocationSource(fusedLocationSource);
+            //noinspection MissingPermission
+            map.setMyLocationEnabled(showUserLocation);
         }
-      }
+    }
 
-      @Override
-      public void onHostDestroy() {
-        AirMapView.this.doDestroy();
-      }
-    };
+    public void setUserLocationPriority(int priority) {
+        fusedLocationSource.setPriority(priority);
+    }
 
-    context.addLifecycleEventListener(lifecycleListener);
-  }
-
-  private boolean hasPermissions() {
-    return checkSelfPermission(getContext(), PERMISSIONS[0]) == PermissionChecker.PERMISSION_GRANTED ||
-        checkSelfPermission(getContext(), PERMISSIONS[1]) == PermissionChecker.PERMISSION_GRANTED;
-  }
-
-
-  /*
-  onDestroy is final method so I can't override it.
-   */
-  public synchronized void doDestroy() {
-    if (destroyed) {
-      return;
-    }
-    destroyed = true;
-
-    if (lifecycleListener != null && context != null) {
-      context.removeLifecycleEventListener(lifecycleListener);
-      lifecycleListener = null;
-    }
-    if (!paused) {
-      onPause();
-      paused = true;
-    }
-    onDestroy();
-  }
-
-  public void setInitialRegion(ReadableMap initialRegion) {
-    this.initialRegion = initialRegion;
-    // Theoretically onMapReady might be called before setInitialRegion
-    // In that case, trigger moveToRegion manually
-    if (!initialRegionSet && map != null) {
-      moveToRegion(initialRegion);
-      initialRegionSet = true;
-    }
-  }
-
-  public void setInitialCamera(ReadableMap initialCamera) {
-    this.initialCamera = initialCamera;
-    // Theoretically onMapReady might be called before setInitialCamera
-    // In that case, trigger moveToCamera manually
-    if (!initialCameraSet && map != null) {
-      moveToCamera(initialCamera);
-      initialCameraSet = true;
-    }
-  }
-
-  private void applyBridgedProps() {
-    if(initialRegion != null) {
-      moveToRegion(initialRegion);
-      initialRegionSet = true;
-    } else if(initialCamera != null) {
-      moveToCamera(initialCamera);
-      initialCameraSet = true;
-    } else if(region != null) {
-      moveToRegion(region);
-    } else {
-      moveToCamera(camera);
-    }
-    if(customMapStyleString != null) {
-      map.setMapStyle(new MapStyleOptions(customMapStyleString));
-    }
-  }
-
-  private void moveToRegion(ReadableMap region) {
-    if (region == null) return;
-
-    double lng = region.getDouble("longitude");
-    double lat = region.getDouble("latitude");
-    double lngDelta = region.getDouble("longitudeDelta");
-    double latDelta = region.getDouble("latitudeDelta");
-    LatLngBounds bounds = new LatLngBounds(
-            new LatLng(lat - latDelta / 2, lng - lngDelta / 2), // southwest
-            new LatLng(lat + latDelta / 2, lng + lngDelta / 2)  // northeast
-    );
-    if (super.getHeight() <= 0 || super.getWidth() <= 0) {
-      // in this case, our map has not been laid out yet, so we save the bounds in a local
-      // variable, and make a guess of zoomLevel 10. Not to worry, though: as soon as layout
-      // occurs, we will move the camera to the saved bounds. Note that if we tried to move
-      // to the bounds now, it would trigger an exception.
-      map.moveCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(lat, lng), 10));
-      boundsToMove = bounds;
-    } else {
-      map.moveCamera(CameraUpdateFactory.newLatLngBounds(bounds, 0));
-      boundsToMove = null;
-    }
-  }
-
-  public void setRegion(ReadableMap region) {
-    this.region = region;
-    if(region != null && map != null) {
-      moveToRegion(region);
-    }
-  }
-
-  public void setCamera(ReadableMap camera) {
-    this.camera = camera;
-    if(camera != null && map != null) {
-      moveToCamera(camera);
-    }
-  }
-
-  public void moveToCamera(ReadableMap camera) {
-    if (camera == null) return;
-
-    CameraPosition.Builder builder = new CameraPosition.Builder();
-
-    ReadableMap center = camera.getMap("center");
-    if (center != null) {
-      double lng = center.getDouble("longitude");
-      double lat = center.getDouble("latitude");
-      builder.target(new LatLng(lat, lng));
-    }
-
-    builder.tilt((float)camera.getDouble("pitch"));
-    builder.bearing((float)camera.getDouble("heading"));
-    builder.zoom((float)camera.getDouble("zoom"));
-
-    CameraUpdate update = CameraUpdateFactory.newCameraPosition(builder.build());
-
-    if (super.getHeight() <= 0 || super.getWidth() <= 0) {
-      // in this case, our map has not been laid out yet, so we save the camera update in a
-      // local variable. As soon as layout occurs, we will move the camera to the saved update.
-      // Note that if we tried to move to the camera now, it would trigger an exception.
-      cameraToSet = update;
-    } else {
-      map.moveCamera(update);
-      cameraToSet = null;
-    }
-  }
-
-  public void setMapStyle(@Nullable String customMapStyleString) {
-    this.customMapStyleString = customMapStyleString;
-    if(map != null && customMapStyleString != null) {
-      map.setMapStyle(new MapStyleOptions(customMapStyleString));
-    }
-  }
-
-  public void setShowsUserLocation(boolean showUserLocation) {
-    this.showUserLocation = showUserLocation; // hold onto this for lifecycle handling
-    if (hasPermissions()) {
-      map.setLocationSource(fusedLocationSource);
-      //noinspection MissingPermission
-      map.setMyLocationEnabled(showUserLocation);
-    }
-  }
-
-  public void setUserLocationPriority(int priority){
-    fusedLocationSource.setPriority(priority);
-  }
-
-  public void setUserLocationUpdateInterval(int interval){
-    fusedLocationSource.setInterval(interval);
-  }
-
-  public void setUserLocationFastestInterval(int interval){
-    fusedLocationSource.setFastestInterval(interval);
-  }
-
-  public void setShowsMyLocationButton(boolean showMyLocationButton) {
-    if (hasPermissions() || !showMyLocationButton) {
-      map.getUiSettings().setMyLocationButtonEnabled(showMyLocationButton);
-    }
-  }
-
-  public void setToolbarEnabled(boolean toolbarEnabled) {
-    if (hasPermissions() || !toolbarEnabled) {
-      map.getUiSettings().setMapToolbarEnabled(toolbarEnabled);
-    }
-  }
-
-  public void setCacheEnabled(boolean cacheEnabled) {
-    this.cacheEnabled = cacheEnabled;
-    this.cacheView();
-  }
-
-  public void enableMapLoading(boolean loadingEnabled) {
-    if (loadingEnabled && !this.isMapLoaded) {
-      this.getMapLoadingLayoutView().setVisibility(View.VISIBLE);
-    }
-  }
-
-  public void setMoveOnMarkerPress(boolean moveOnPress) {
-    this.moveOnMarkerPress = moveOnPress;
-  }
-
-  public void setLoadingBackgroundColor(Integer loadingBackgroundColor) {
-    this.loadingBackgroundColor = loadingBackgroundColor;
-
-    if (this.mapLoadingLayout != null) {
-      if (loadingBackgroundColor == null) {
-        this.mapLoadingLayout.setBackgroundColor(Color.WHITE);
-      } else {
-        this.mapLoadingLayout.setBackgroundColor(this.loadingBackgroundColor);
-      }
-    }
-  }
-
-  public void setLoadingIndicatorColor(Integer loadingIndicatorColor) {
-    this.loadingIndicatorColor = loadingIndicatorColor;
-    if (this.mapLoadingProgressBar != null) {
-      Integer color = loadingIndicatorColor;
-      if (color == null) {
-        color = Color.parseColor("#606060");
-      }
-
-      ColorStateList progressTintList = ColorStateList.valueOf(loadingIndicatorColor);
-      ColorStateList secondaryProgressTintList = ColorStateList.valueOf(loadingIndicatorColor);
-      ColorStateList indeterminateTintList = ColorStateList.valueOf(loadingIndicatorColor);
-
-      this.mapLoadingProgressBar.setProgressTintList(progressTintList);
-      this.mapLoadingProgressBar.setSecondaryProgressTintList(secondaryProgressTintList);
-      this.mapLoadingProgressBar.setIndeterminateTintList(indeterminateTintList);
-    }
-  }
-
-  public void setHandlePanDrag(boolean handlePanDrag) {
-    this.handlePanDrag = handlePanDrag;
-  }
-
-  public void addFeature(View child, int index) {
-    // Our desired API is to pass up annotations/overlays as children to the mapview component.
-    // This is where we intercept them and do the appropriate underlying mapview action.
-    if (child instanceof AirMapMarker) {
-      AirMapMarker annotation = (AirMapMarker) child;
-      annotation.addToMap(map);
-      features.add(index, annotation);
-
-      // Allow visibility event to be triggered later
-      int visibility = annotation.getVisibility();
-      annotation.setVisibility(INVISIBLE);
-
-      // Remove from a view group if already present, prevent "specified child
-      // already had a parent" error.
-      ViewGroup annotationParent = (ViewGroup)annotation.getParent();
-      if (annotationParent != null) {
-        annotationParent.removeView(annotation);
-      }
-
-      // Add to the parent group
-      attacherGroup.addView(annotation);
-
-      // Trigger visibility event if necessary.
-      // With some testing, seems like it is not always
-      //   triggered just by being added to a parent view.
-      annotation.setVisibility(visibility);
-
-      Marker marker = (Marker) annotation.getFeature();
-      markerMap.put(marker, annotation);
-    } else if (child instanceof AirMapPolyline) {
-      AirMapPolyline polylineView = (AirMapPolyline) child;
-      polylineView.addToMap(map);
-      features.add(index, polylineView);
-      Polyline polyline = (Polyline) polylineView.getFeature();
-      polylineMap.put(polyline, polylineView);
-    } else if (child instanceof AirMapGradientPolyline) {
-      AirMapGradientPolyline polylineView = (AirMapGradientPolyline) child;
-      polylineView.addToMap(map);
-      features.add(index, polylineView);
-      TileOverlay tileOverlay = (TileOverlay) polylineView.getFeature();
-      gradientPolylineMap.put(tileOverlay, polylineView);
-    } else if (child instanceof AirMapPolygon) {
-      AirMapPolygon polygonView = (AirMapPolygon) child;
-      polygonView.addToMap(map);
-      features.add(index, polygonView);
-      Polygon polygon = (Polygon) polygonView.getFeature();
-      polygonMap.put(polygon, polygonView);
-    } else if (child instanceof AirMapCircle) {
-      AirMapCircle circleView = (AirMapCircle) child;
-      circleView.addToMap(map);
-      features.add(index, circleView);
-    } else if (child instanceof AirMapUrlTile) {
-      AirMapUrlTile urlTileView = (AirMapUrlTile) child;
-      urlTileView.addToMap(map);
-      features.add(index, urlTileView);
-    } else if (child instanceof AirMapWMSTile) {
-      AirMapWMSTile urlTileView = (AirMapWMSTile) child;
-      urlTileView.addToMap(map);
-      features.add(index, urlTileView);
-    } else if (child instanceof AirMapLocalTile) {
-      AirMapLocalTile localTileView = (AirMapLocalTile) child;
-      localTileView.addToMap(map);
-      features.add(index, localTileView);
-    } else if (child instanceof AirMapOverlay) {
-      AirMapOverlay overlayView = (AirMapOverlay) child;
-      overlayView.addToMap(map);
-      features.add(index, overlayView);
-      GroundOverlay overlay = (GroundOverlay) overlayView.getFeature();
-      overlayMap.put(overlay, overlayView);
-    } else if (child instanceof AirMapHeatmap) {
-      AirMapHeatmap heatmapView = (AirMapHeatmap) child;
-      heatmapView.addToMap(map);
-      features.add(index, heatmapView);
-      TileOverlay heatmap = (TileOverlay)heatmapView.getFeature();
-      heatmapMap.put(heatmap, heatmapView);
-    } else if (child instanceof ViewGroup) {
-      ViewGroup children = (ViewGroup) child;
-      for (int i = 0; i < children.getChildCount(); i++) {
-        addFeature(children.getChildAt(i), index);
-      }
-    } else {
-      addView(child, index);
-    }
-  }
-
-  public int getFeatureCount() {
-    return features.size();
-  }
-
-  public View getFeatureAt(int index) {
-    return features.get(index);
-  }
-
-  public void removeFeatureAt(int index) {
-    AirMapFeature feature = features.remove(index);
-    if (feature instanceof AirMapMarker) {
-      markerMap.remove(feature.getFeature());
-    } else if (feature instanceof AirMapHeatmap) {
-      heatmapMap.remove(feature.getFeature());
-    }
-    feature.removeFromMap(map);
-  }
-
-  public WritableMap makeClickEventData(LatLng point) {
-    WritableMap event = new WritableNativeMap();
-
-    WritableMap coordinate = new WritableNativeMap();
-    coordinate.putDouble("latitude", point.latitude);
-    coordinate.putDouble("longitude", point.longitude);
-    event.putMap("coordinate", coordinate);
-
-    Projection projection = map.getProjection();
-    Point screenPoint = projection.toScreenLocation(point);
-
-    WritableMap position = new WritableNativeMap();
-    position.putDouble("x", screenPoint.x);
-    position.putDouble("y", screenPoint.y);
-    event.putMap("position", position);
-
-    return event;
-  }
-
-  public void updateExtraData(Object extraData) {
-    // if boundsToMove is not null, we now have the MapView's width/height, so we can apply
-    // a proper camera move
-    if (boundsToMove != null) {
-      HashMap<String, Float> data = (HashMap<String, Float>) extraData;
-      int width = data.get("width") == null ? 0 : data.get("width").intValue();
-      int height = data.get("height") == null ? 0 : data.get("height").intValue();
-
-      //fix for https://github.com/react-native-maps/react-native-maps/issues/245,
-      //it's not guaranteed the passed-in height and width would be greater than 0.
-      if (width <= 0 || height <= 0) {
-        map.moveCamera(CameraUpdateFactory.newLatLngBounds(boundsToMove, 0));
-      } else {
-        map.moveCamera(CameraUpdateFactory.newLatLngBounds(boundsToMove, width, height, 0));
-      }
-
-      boundsToMove = null;
-      cameraToSet = null;
-    }
-    else if (cameraToSet != null) {
-      map.moveCamera(cameraToSet);
-      cameraToSet = null;
-    }
-  }
-
-  public void animateToCamera(ReadableMap camera, int duration) {
-    if (map == null) return;
-    CameraPosition.Builder builder = new CameraPosition.Builder(map.getCameraPosition());
-    if (camera.hasKey("zoom")) {
-      builder.zoom((float)camera.getDouble("zoom"));
-    }
-    if (camera.hasKey("heading")) {
-      builder.bearing((float)camera.getDouble("heading"));
-    }
-    if (camera.hasKey("pitch")) {
-      builder.tilt((float)camera.getDouble("pitch"));
-    }
-    if (camera.hasKey("center")) {
-      ReadableMap center = camera.getMap("center");
-      builder.target(new LatLng(center.getDouble("latitude"), center.getDouble("longitude")));
-    }
-
-    CameraUpdate update = CameraUpdateFactory.newCameraPosition(builder.build());
-
-    if (duration <= 0) {
-      map.moveCamera(update);
-    }
-    else {
-      map.animateCamera(update, duration, null);
+    public void setUserLocationUpdateInterval(int interval) {
+        fusedLocationSource.setInterval(interval);
     }
-  }
 
-  public void animateToRegion(LatLngBounds bounds, int duration) {
-    if (map == null) return;
-    if(duration <= 0) {
-      map.moveCamera(CameraUpdateFactory.newLatLngBounds(bounds, 0));
-    } else {
-      map.animateCamera(CameraUpdateFactory.newLatLngBounds(bounds, 0), duration, null);
+    public void setUserLocationFastestInterval(int interval) {
+        fusedLocationSource.setFastestInterval(interval);
     }
-  }
 
-  public void fitToElements(ReadableMap edgePadding, boolean animated) {
-    if (map == null) return;
+    public void setShowsMyLocationButton(boolean showMyLocationButton) {
+        if (hasPermissions() || !showMyLocationButton) {
+            map.getUiSettings().setMyLocationButtonEnabled(showMyLocationButton);
+        }
+    }
 
-    LatLngBounds.Builder builder = new LatLngBounds.Builder();
+    public void setToolbarEnabled(boolean toolbarEnabled) {
+        if (hasPermissions() || !toolbarEnabled) {
+            map.getUiSettings().setMapToolbarEnabled(toolbarEnabled);
+        }
+    }
+
+    public void setCacheEnabled(boolean cacheEnabled) {
+        this.cacheEnabled = cacheEnabled;
+        this.cacheView();
+    }
 
-    boolean addedPosition = false;
+    public void enableMapLoading(boolean loadingEnabled) {
+        if (loadingEnabled && !this.isMapLoaded) {
+            this.getMapLoadingLayoutView().setVisibility(View.VISIBLE);
+        }
+    }
 
-    for (AirMapFeature feature : features) {
-      if (feature instanceof AirMapMarker) {
-        Marker marker = (Marker) feature.getFeature();
-        builder.include(marker.getPosition());
-        addedPosition = true;
-      }
-      // TODO(lmr): may want to include shapes / etc.
+    public void setMoveOnMarkerPress(boolean moveOnPress) {
+        this.moveOnMarkerPress = moveOnPress;
     }
-    if (addedPosition) {
-      LatLngBounds bounds = builder.build();
-      CameraUpdate cu = CameraUpdateFactory.newLatLngBounds(bounds, baseMapPadding);
 
-      if (edgePadding != null) {
-        map.setPadding(edgePadding.getInt("left"), edgePadding.getInt("top"),
-          edgePadding.getInt("right"), edgePadding.getInt("bottom"));
-      }
+    public void setLoadingBackgroundColor(Integer loadingBackgroundColor) {
+        this.loadingBackgroundColor = loadingBackgroundColor;
 
-      if (animated) {
-        map.animateCamera(cu);
-      } else {
-        map.moveCamera(cu);
-      }
+        if (this.mapLoadingLayout != null) {
+            if (loadingBackgroundColor == null) {
+                this.mapLoadingLayout.setBackgroundColor(Color.WHITE);
+            } else {
+                this.mapLoadingLayout.setBackgroundColor(this.loadingBackgroundColor);
+            }
+        }
     }
-  }
 
-  public void fitToSuppliedMarkers(ReadableArray markerIDsArray, ReadableMap edgePadding, boolean animated) {
-    if (map == null) return;
+    public void setLoadingIndicatorColor(Integer loadingIndicatorColor) {
+        this.loadingIndicatorColor = loadingIndicatorColor;
+        if (this.mapLoadingProgressBar != null) {
+            Integer color = loadingIndicatorColor;
+            if (color == null) {
+                color = Color.parseColor("#606060");
+            }
+
+            ColorStateList progressTintList = ColorStateList.valueOf(loadingIndicatorColor);
+            ColorStateList secondaryProgressTintList = ColorStateList.valueOf(loadingIndicatorColor);
+            ColorStateList indeterminateTintList = ColorStateList.valueOf(loadingIndicatorColor);
 
-    LatLngBounds.Builder builder = new LatLngBounds.Builder();
+            this.mapLoadingProgressBar.setProgressTintList(progressTintList);
+            this.mapLoadingProgressBar.setSecondaryProgressTintList(secondaryProgressTintList);
+            this.mapLoadingProgressBar.setIndeterminateTintList(indeterminateTintList);
+        }
+    }
 
-    String[] markerIDs = new String[markerIDsArray.size()];
-    for (int i = 0; i < markerIDsArray.size(); i++) {
-      markerIDs[i] = markerIDsArray.getString(i);
+    public void setHandlePanDrag(boolean handlePanDrag) {
+        this.handlePanDrag = handlePanDrag;
     }
 
-    boolean addedPosition = false;
+    public void addFeature(View child, int index) {
+        // Our desired API is to pass up annotations/overlays as children to the mapview component.
+        // This is where we intercept them and do the appropriate underlying mapview action.
+
+        if (child instanceof AirMapMarker) {
+            AirMapMarker annotation = (AirMapMarker) child;
+            annotation.addToMap(map);
+            features.add(index, annotation);
+            if(annotation.getIdentifier().equals(this.idMarker)){
+                markerSelect = annotation;
+            }
+            // Allow visibility event to be triggered later
+            int visibility = annotation.getVisibility();
+            annotation.setVisibility(INVISIBLE);
+
+            // Remove from a view group if already present, prevent "specified child
+            // already had a parent" error.
+            ViewGroup annotationParent = (ViewGroup) annotation.getParent();
+            if (annotationParent != null) {
+                annotationParent.removeView(annotation);
+            }
 
-    List<String> markerIDList = Arrays.asList(markerIDs);
+            // Add to the parent group
+            attacherGroup.addView(annotation);
 
-    for (AirMapFeature feature : features) {
-      if (feature instanceof AirMapMarker) {
-        String identifier = ((AirMapMarker) feature).getIdentifier();
-        Marker marker = (Marker) feature.getFeature();
-        if (markerIDList.contains(identifier)) {
-          builder.include(marker.getPosition());
-          addedPosition = true;
+            // Trigger visibility event if necessary.
+            // With some testing, seems like it is not always
+            //   triggered just by being added to a parent view.
+            annotation.setVisibility(visibility);
+
+            Marker marker = (Marker) annotation.getFeature();
+            markerMap.put(marker, annotation);
+
+        } else if (child instanceof AirMapPolyline) {
+            AirMapPolyline polylineView = (AirMapPolyline) child;
+            if (polylineView.getIdentifier().equals("distancePolyline")) {
+                polylineView.changeCoordinatesWhenMarkerCircle(map);
+                polylineMap.clear();
+            } else {
+                polylineView.addToMap(map);
+            }
+            features.add(index, polylineView);
+            Polyline polyline = (Polyline) polylineView.getFeature();
+            polylineMap.put(polyline, polylineView);
+        } else if (child instanceof AirMapGradientPolyline) {
+            AirMapGradientPolyline polylineView = (AirMapGradientPolyline) child;
+            polylineView.addToMap(map);
+            features.add(index, polylineView);
+            TileOverlay tileOverlay = (TileOverlay) polylineView.getFeature();
+            gradientPolylineMap.put(tileOverlay, polylineView);
+        } else if (child instanceof AirMapPolygon) {
+            AirMapPolygon polygonView = (AirMapPolygon) child;
+            polygonView.addToMap(map);
+            features.add(index, polygonView);
+            Polygon polygon = (Polygon) polygonView.getFeature();
+            polygonMap.put(polygon, polygonView);
+        } else if (child instanceof AirMapCircle) {
+            AirMapCircle circleView = (AirMapCircle) child;
+            circleView.addToMap(map);
+            features.add(index, circleView);
+        } else if (child instanceof AirMapUrlTile) {
+            AirMapUrlTile urlTileView = (AirMapUrlTile) child;
+            urlTileView.addToMap(map);
+            features.add(index, urlTileView);
+        } else if (child instanceof AirMapWMSTile) {
+            AirMapWMSTile urlTileView = (AirMapWMSTile) child;
+            urlTileView.addToMap(map);
+            features.add(index, urlTileView);
+        } else if (child instanceof AirMapLocalTile) {
+            AirMapLocalTile localTileView = (AirMapLocalTile) child;
+            localTileView.addToMap(map);
+            features.add(index, localTileView);
+        } else if (child instanceof AirMapOverlay) {
+            AirMapOverlay overlayView = (AirMapOverlay) child;
+            overlayView.addToMap(map);
+            features.add(index, overlayView);
+            GroundOverlay overlay = (GroundOverlay) overlayView.getFeature();
+            overlayMap.put(overlay, overlayView);
+        } else if (child instanceof AirMapHeatmap) {
+            AirMapHeatmap heatmapView = (AirMapHeatmap) child;
+            heatmapView.addToMap(map);
+            features.add(index, heatmapView);
+            TileOverlay heatmap = (TileOverlay) heatmapView.getFeature();
+            heatmapMap.put(heatmap, heatmapView);
+        } else if (child instanceof ViewGroup) {
+            ViewGroup children = (ViewGroup) child;
+            for (int i = 0; i < children.getChildCount(); i++) {
+                addFeature(children.getChildAt(i), index);
+            }
+        } else {
+            addView(child, index);
         }
-      }
-    }
-
-    if (addedPosition) {
-      LatLngBounds bounds = builder.build();
-      CameraUpdate cu = CameraUpdateFactory.newLatLngBounds(bounds, baseMapPadding);
-
-      if (edgePadding != null) {
-        map.setPadding(edgePadding.getInt("left"), edgePadding.getInt("top"),
-          edgePadding.getInt("right"), edgePadding.getInt("bottom"));
-      }
-
-      if (animated) {
-        map.animateCamera(cu);
-      } else {
-        map.moveCamera(cu);
-      }
-    }
-  }
-
-  int baseLeftMapPadding;
-  int baseRightMapPadding;
-  int baseTopMapPadding;
-  int baseBottomMapPadding;
-
-  public void applyBaseMapPadding(int left, int top, int right, int bottom){
-    this.map.setPadding(left, top, right, bottom);
-    baseLeftMapPadding = left;
-    baseRightMapPadding = right;
-    baseTopMapPadding = top;
-    baseBottomMapPadding = bottom;
-  }
-
-  public void fitToCoordinates(ReadableArray coordinatesArray, ReadableMap edgePadding,
-      boolean animated) {
-    if (map == null) return;
-
-    LatLngBounds.Builder builder = new LatLngBounds.Builder();
-
-    for (int i = 0; i < coordinatesArray.size(); i++) {
-      ReadableMap latLng = coordinatesArray.getMap(i);
-      double lat = latLng.getDouble("latitude");
-      double lng = latLng.getDouble("longitude");
-      builder.include(new LatLng(lat, lng));
-    }
-
-    LatLngBounds bounds = builder.build();
-    CameraUpdate cu = CameraUpdateFactory.newLatLngBounds(bounds, baseMapPadding);
-
-    if (edgePadding != null) {
-      appendMapPadding(edgePadding.getInt("left"), edgePadding.getInt("top"), edgePadding.getInt("right"), edgePadding.getInt("bottom"));
-    }
-
-    if (animated) {
-      map.animateCamera(cu);
-    } else {
-      map.moveCamera(cu);
-    }
-    // Move the google logo to the default base padding value.
-    map.setPadding(baseLeftMapPadding, baseTopMapPadding, baseRightMapPadding, baseBottomMapPadding);
-  }
-
-  private void appendMapPadding(int iLeft,int iTop, int iRight, int iBottom) {
-    int left;
-    int top;
-    int right;
-    int bottom;
-    double density = getResources().getDisplayMetrics().density;
-
-    left = (int) (iLeft * density);
-    top = (int) (iTop * density);
-    right = (int) (iRight * density);
-    bottom = (int) (iBottom * density);
-
-    map.setPadding(left + baseLeftMapPadding,
-            top + baseTopMapPadding,
-            right + baseRightMapPadding,
-            bottom + baseBottomMapPadding);
-  }
-
-  public double[][] getMapBoundaries() {
-    LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;
-    LatLng northEast = bounds.northeast;
-    LatLng southWest = bounds.southwest;
-
-    return new double[][] {
-      {northEast.longitude, northEast.latitude},
-      {southWest.longitude, southWest.latitude}
-    };
-  }
+    }
 
-  public void setMapBoundaries(ReadableMap northEast, ReadableMap southWest) {
-    if (map == null) return;
+    public int getFeatureCount() {
+        return features.size();
+    }
 
-    LatLngBounds.Builder builder = new LatLngBounds.Builder();
+    public View getFeatureAt(int index) {
+        return features.get(index);
+    }
 
-    double latNE = northEast.getDouble("latitude");
-    double lngNE = northEast.getDouble("longitude");
-    builder.include(new LatLng(latNE, lngNE));
+    public void removeFeatureAt(int index) {
+        AirMapFeature feature = features.remove(index);
+        if (feature instanceof AirMapMarker) {
+            markerMap.remove(feature.getFeature());
+        } else if (feature instanceof AirMapHeatmap) {
+            heatmapMap.remove(feature.getFeature());
+        }
+        feature.removeFromMap(map);
+    }
 
-    double latSW = southWest.getDouble("latitude");
-    double lngSW = southWest.getDouble("longitude");
-    builder.include(new LatLng(latSW, lngSW));
+    public WritableMap makeClickEventData(LatLng point) {
+        WritableMap event = new WritableNativeMap();
 
-    LatLngBounds bounds = builder.build();
-
-    map.setLatLngBoundsForCameraTarget(bounds);
-  }
-
-  // InfoWindowAdapter interface
-
-  @Override
-  public View getInfoWindow(Marker marker) {
-    AirMapMarker markerView = getMarkerMap(marker);
-    return markerView.getCallout();
-  }
-
-  @Override
-  public View getInfoContents(Marker marker) {
-    AirMapMarker markerView = getMarkerMap(marker);
-    return markerView.getInfoContents();
-  }
-
-  @Override
-  public boolean dispatchTouchEvent(MotionEvent ev) {
-    gestureDetector.onTouchEvent(ev);
-
-    int X = (int)ev.getX();          
-    int Y = (int)ev.getY();
-    if(map != null) {
-      tapLocation = map.getProjection().fromScreenLocation(new Point(X,Y));
-    }
-
-    int action = MotionEventCompat.getActionMasked(ev);
-
-    switch (action) {
-      case (MotionEvent.ACTION_DOWN):
-        this.getParent().requestDisallowInterceptTouchEvent(
-            map != null && map.getUiSettings().isScrollGesturesEnabled());
-        break;
-      case (MotionEvent.ACTION_UP):
-        // Clear this regardless, since isScrollGesturesEnabled() may have been updated
-        this.getParent().requestDisallowInterceptTouchEvent(false);
-        break;
-    }
-    super.dispatchTouchEvent(ev);
-    return true;
-  }
-
-  @Override
-  public void onMarkerDragStart(Marker marker) {
-    WritableMap event = makeClickEventData(marker.getPosition());
-    manager.pushEvent(context, this, "onMarkerDragStart", event);
-
-    AirMapMarker markerView = getMarkerMap(marker);
-    event = makeClickEventData(marker.getPosition());
-    manager.pushEvent(context, markerView, "onDragStart", event);
-  }
-
-  @Override
-  public void onMarkerDrag(Marker marker) {
-    WritableMap event = makeClickEventData(marker.getPosition());
-    manager.pushEvent(context, this, "onMarkerDrag", event);
-
-    AirMapMarker markerView = getMarkerMap(marker);
-    event = makeClickEventData(marker.getPosition());
-    manager.pushEvent(context, markerView, "onDrag", event);
-  }
-
-  @Override
-  public void onMarkerDragEnd(Marker marker) {
-    WritableMap event = makeClickEventData(marker.getPosition());
-    manager.pushEvent(context, this, "onMarkerDragEnd", event);
-
-    AirMapMarker markerView = getMarkerMap(marker);
-    event = makeClickEventData(marker.getPosition());
-    manager.pushEvent(context, markerView, "onDragEnd", event);
-  }
-
-  @Override
-  public void onPoiClick(PointOfInterest poi) {
-    WritableMap event = makeClickEventData(poi.latLng);
-
-    event.putString("placeId", poi.placeId);
-    event.putString("name", poi.name);
-
-    manager.pushEvent(context, this, "onPoiClick", event);
-  }
-
-  private ProgressBar getMapLoadingProgressBar() {
-    if (this.mapLoadingProgressBar == null) {
-      this.mapLoadingProgressBar = new ProgressBar(getContext());
-      this.mapLoadingProgressBar.setIndeterminate(true);
-    }
-    if (this.loadingIndicatorColor != null) {
-      this.setLoadingIndicatorColor(this.loadingIndicatorColor);
-    }
-    return this.mapLoadingProgressBar;
-  }
-
-  private RelativeLayout getMapLoadingLayoutView() {
-    if (this.mapLoadingLayout == null) {
-      this.mapLoadingLayout = new RelativeLayout(getContext());
-      this.mapLoadingLayout.setBackgroundColor(Color.LTGRAY);
-      this.addView(this.mapLoadingLayout,
-          new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
-              ViewGroup.LayoutParams.MATCH_PARENT));
-
-      RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
-          RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
-      params.addRule(RelativeLayout.CENTER_IN_PARENT);
-      this.mapLoadingLayout.addView(this.getMapLoadingProgressBar(), params);
-
-      this.mapLoadingLayout.setVisibility(View.INVISIBLE);
-    }
-    this.setLoadingBackgroundColor(this.loadingBackgroundColor);
-    return this.mapLoadingLayout;
-  }
-
-  private ImageView getCacheImageView() {
-    if (this.cacheImageView == null) {
-      this.cacheImageView = new ImageView(getContext());
-      this.addView(this.cacheImageView,
-          new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
-              ViewGroup.LayoutParams.MATCH_PARENT));
-      this.cacheImageView.setVisibility(View.INVISIBLE);
-    }
-    return this.cacheImageView;
-  }
-
-  private void removeCacheImageView() {
-    if (this.cacheImageView != null) {
-      ((ViewGroup) this.cacheImageView.getParent()).removeView(this.cacheImageView);
-      this.cacheImageView = null;
-    }
-  }
-
-  private void removeMapLoadingProgressBar() {
-    if (this.mapLoadingProgressBar != null) {
-      ((ViewGroup) this.mapLoadingProgressBar.getParent()).removeView(this.mapLoadingProgressBar);
-      this.mapLoadingProgressBar = null;
-    }
-  }
-
-  private void removeMapLoadingLayoutView() {
-    this.removeMapLoadingProgressBar();
-    if (this.mapLoadingLayout != null) {
-      ((ViewGroup) this.mapLoadingLayout.getParent()).removeView(this.mapLoadingLayout);
-      this.mapLoadingLayout = null;
-    }
-  }
-
-  private void cacheView() {
-    if (this.cacheEnabled) {
-      final ImageView cacheImageView = this.getCacheImageView();
-      final RelativeLayout mapLoadingLayout = this.getMapLoadingLayoutView();
-      cacheImageView.setVisibility(View.INVISIBLE);
-      mapLoadingLayout.setVisibility(View.VISIBLE);
-      if (this.isMapLoaded) {
-        this.map.snapshot(new GoogleMap.SnapshotReadyCallback() {
-          @Override public void onSnapshotReady(Bitmap bitmap) {
-            cacheImageView.setImageBitmap(bitmap);
-            cacheImageView.setVisibility(View.VISIBLE);
-            mapLoadingLayout.setVisibility(View.INVISIBLE);
-          }
-        });
-      }
-    } else {
-      this.removeCacheImageView();
-      if (this.isMapLoaded) {
-        this.removeMapLoadingLayoutView();
-      }
-    }
-  }
-
-  public void onPanDrag(MotionEvent ev) {
-    Point point = new Point((int) ev.getX(), (int) ev.getY());
-    LatLng coords = this.map.getProjection().fromScreenLocation(point);
-    WritableMap event = makeClickEventData(coords);
-    manager.pushEvent(context, this, "onPanDrag", event);
-  }
-
-  public void onDoublePress(MotionEvent ev) {
-    if (this.map == null) return;
-    Point point = new Point((int) ev.getX(), (int) ev.getY());
-    LatLng coords = this.map.getProjection().fromScreenLocation(point);
-    WritableMap event = makeClickEventData(coords);
-    manager.pushEvent(context, this, "onDoublePress", event);
-  }
-
-  public void setKmlSrc(String kmlSrc) {
-    try {
-      InputStream kmlStream =  new FileUtil(context).execute(kmlSrc).get();
-
-      if (kmlStream == null) {
-        return;
-      }
-
-      KmlLayer kmlLayer = new KmlLayer(map, kmlStream, context);
-      kmlLayer.addLayerToMap();
-
-      WritableMap pointers = new WritableNativeMap();
-      WritableArray markers = new WritableNativeArray();
-
-      if (kmlLayer.getContainers() == null) {
-        manager.pushEvent(context, this, "onKmlReady", pointers);
-        return;
-      }
-
-      //Retrieve a nested container within the first container
-      KmlContainer container = kmlLayer.getContainers().iterator().next();
-      if (container == null || container.getContainers() == null) {
-        manager.pushEvent(context, this, "onKmlReady", pointers);
-        return;
-      }
-
-
-      if (container.getContainers().iterator().hasNext()) {
-        container = container.getContainers().iterator().next();
-      }
-
-      int index = 0;
-      for (KmlPlacemark placemark : container.getPlacemarks()) {
-        MarkerOptions options = new MarkerOptions();
-
-        if (placemark.getInlineStyle() != null) {
-          options = placemark.getMarkerOptions();
+        WritableMap coordinate = new WritableNativeMap();
+        coordinate.putDouble("latitude", point.latitude);
+        coordinate.putDouble("longitude", point.longitude);
+        event.putMap("coordinate", coordinate);
+
+        Projection projection = map.getProjection();
+        Point screenPoint = projection.toScreenLocation(point);
+
+        WritableMap position = new WritableNativeMap();
+        position.putDouble("x", screenPoint.x);
+        position.putDouble("y", screenPoint.y);
+        event.putMap("position", position);
+
+        return event;
+    }
+
+    public void updateExtraData(Object extraData) {
+        // if boundsToMove is not null, we now have the MapView's width/height, so we can apply
+        // a proper camera move
+        if (boundsToMove != null) {
+            HashMap<String, Float> data = (HashMap<String, Float>) extraData;
+            int width = data.get("width") == null ? 0 : data.get("width").intValue();
+            int height = data.get("height") == null ? 0 : data.get("height").intValue();
+
+            //fix for https://github.com/react-native-maps/react-native-maps/issues/245,
+            //it's not guaranteed the passed-in height and width would be greater than 0.
+            if (width <= 0 || height <= 0) {
+                map.moveCamera(CameraUpdateFactory.newLatLngBounds(boundsToMove, 0));
+            } else {
+                map.moveCamera(CameraUpdateFactory.newLatLngBounds(boundsToMove, width, height, 0));
+            }
+
+            boundsToMove = null;
+            cameraToSet = null;
+        } else if (cameraToSet != null) {
+            map.moveCamera(cameraToSet);
+            cameraToSet = null;
+        }
+    }
+
+    public void animateToCamera(ReadableMap camera, int duration) {
+        if (map == null) return;
+        CameraPosition.Builder builder = new CameraPosition.Builder(map.getCameraPosition());
+        if (camera.hasKey("zoom")) {
+            builder.zoom((float) camera.getDouble("zoom"));
+        }
+        if (camera.hasKey("heading")) {
+            builder.bearing((float) camera.getDouble("heading"));
+        }
+        if (camera.hasKey("pitch")) {
+            builder.tilt((float) camera.getDouble("pitch"));
+        }
+        if (camera.hasKey("center")) {
+            ReadableMap center = camera.getMap("center");
+            builder.target(new LatLng(center.getDouble("latitude"), center.getDouble("longitude")));
+        }
+
+        CameraUpdate update = CameraUpdateFactory.newCameraPosition(builder.build());
+
+        if (duration <= 0) {
+            map.moveCamera(update);
         } else {
-          options.icon(BitmapDescriptorFactory.defaultMarker());
+            map.animateCamera(update, duration, null);
+        }
+    }
+
+    public void animateToRegion(LatLngBounds bounds, int duration) {
+        if (map == null) return;
+        if (duration <= 0) {
+            map.moveCamera(CameraUpdateFactory.newLatLngBounds(bounds, 0));
+        } else {
+            map.animateCamera(CameraUpdateFactory.newLatLngBounds(bounds, 0), duration, null);
+        }
+    }
+
+    public void fitToElements(ReadableMap edgePadding, boolean animated) {
+        if (map == null) return;
+        if(edgePadding.getBoolean("isDefault")){
+            int density = (int) Resources.getSystem().getDisplayMetrics().density;
+            map.setPadding(edgePadding.getInt("left") * density, edgePadding.getInt("top") * density,
+                    edgePadding.getInt("right") * density, edgePadding.getInt("bottom") * density);
+        }else {
+            LatLngBounds.Builder builder = new LatLngBounds.Builder();
+
+            boolean addedPosition = false;
+
+            for (AirMapFeature feature : features) {
+                if (feature instanceof AirMapMarker) {
+                    Marker marker = (Marker) feature.getFeature();
+                    builder.include(marker.getPosition());
+                    addedPosition = true;
+                }
+                // TODO(lmr): may want to include shapes / etc.
+            }
+            if (addedPosition) {
+                LatLngBounds bounds = builder.build();
+                CameraUpdate cu = CameraUpdateFactory.newLatLngBounds(bounds, baseMapPadding);
+
+                if (edgePadding != null) {
+                    map.setPadding(edgePadding.getInt("left"), edgePadding.getInt("top"),
+                            edgePadding.getInt("right"), edgePadding.getInt("bottom"));
+                }
+
+                if (animated) {
+                    map.animateCamera(cu);
+                } else {
+                    map.moveCamera(cu);
+                }
+            }
         }
+    }
+
+    public void fitToSuppliedMarkers(ReadableArray markerIDsArray, ReadableMap edgePadding, boolean animated) {
+        if (map == null) return;
 
-        LatLng latLng = ((LatLng) placemark.getGeometry().getGeometryObject());
-        String title = "";
-        String snippet = "";
+        LatLngBounds.Builder builder = new LatLngBounds.Builder();
 
-        if (placemark.hasProperty("name")) {
-          title = placemark.getProperty("name");
+        String[] markerIDs = new String[markerIDsArray.size()];
+        for (int i = 0; i < markerIDsArray.size(); i++) {
+            markerIDs[i] = markerIDsArray.getString(i);
         }
 
-        if (placemark.hasProperty("description")) {
-          snippet = placemark.getProperty("description");
+        boolean addedPosition = false;
+
+        List<String> markerIDList = Arrays.asList(markerIDs);
+
+        for (AirMapFeature feature : features) {
+            if (feature instanceof AirMapMarker) {
+                String identifier = ((AirMapMarker) feature).getIdentifier();
+                Marker marker = (Marker) feature.getFeature();
+                if (markerIDList.contains(identifier)) {
+                    builder.include(marker.getPosition());
+                    addedPosition = true;
+                }
+            }
         }
 
-        options.position(latLng);
-        options.title(title);
-        options.snippet(snippet);
+        if (addedPosition) {
+            LatLngBounds bounds = builder.build();
+            CameraUpdate cu = CameraUpdateFactory.newLatLngBounds(bounds, baseMapPadding);
 
-        AirMapMarker marker = new AirMapMarker(context, options, this.manager.getMarkerManager());
+            if (edgePadding != null) {
+                map.setPadding(edgePadding.getInt("left"), edgePadding.getInt("top"),
+                        edgePadding.getInt("right"), edgePadding.getInt("bottom"));
+            }
 
-        if (placemark.getInlineStyle() != null
-            && placemark.getInlineStyle().getIconUrl() != null) {
-          marker.setImage(placemark.getInlineStyle().getIconUrl());
-        } else if (container.getStyle(placemark.getStyleId()) != null) {
-          KmlStyle style = container.getStyle(placemark.getStyleId());
-          marker.setImage(style.getIconUrl());
+            if (animated) {
+                map.animateCamera(cu);
+            } else {
+                map.moveCamera(cu);
+            }
         }
+    }
+
+    int baseLeftMapPadding;
+    int baseRightMapPadding;
+    int baseTopMapPadding;
+    int baseBottomMapPadding;
+
+    public void applyBaseMapPadding(int left, int top, int right, int bottom) {
+        this.map.setPadding(left, top, right, bottom);
+        baseLeftMapPadding = left;
+        baseRightMapPadding = right;
+        baseTopMapPadding = top;
+        baseBottomMapPadding = bottom;
+    }
 
-        String identifier = title + " - " + index;
+    public void fitToCoordinates(ReadableArray coordinatesArray, ReadableMap edgePadding,
+                                 boolean animated) {
+        if (map == null) return;
 
-        marker.setIdentifier(identifier);
+        LatLngBounds.Builder builder = new LatLngBounds.Builder();
+
+        for (int i = 0; i < coordinatesArray.size(); i++) {
+            ReadableMap latLng = coordinatesArray.getMap(i);
+            double lat = latLng.getDouble("latitude");
+            double lng = latLng.getDouble("longitude");
+            builder.include(new LatLng(lat, lng));
+        }
 
-        addFeature(marker, index++);
+        LatLngBounds bounds = builder.build();
+        CameraUpdate cu = CameraUpdateFactory.newLatLngBounds(bounds, baseMapPadding);
 
-        WritableMap loadedMarker = makeClickEventData(latLng);
-        loadedMarker.putString("id", identifier);
-        loadedMarker.putString("title", title);
-        loadedMarker.putString("description", snippet);
+        if (edgePadding != null) {
+            appendMapPadding(edgePadding.getInt("left"), edgePadding.getInt("top"), edgePadding.getInt("right"), edgePadding.getInt("bottom"));
+        }
 
-        markers.pushMap(loadedMarker);
-      }
+        if (animated) {
+            map.animateCamera(cu);
+        } else {
+            map.moveCamera(cu);
+        }
+        // Move the google logo to the default base padding value.
+        map.setPadding(baseLeftMapPadding, baseTopMapPadding, baseRightMapPadding, baseBottomMapPadding);
+    }
 
-      pointers.putArray("markers", markers);
+    private void appendMapPadding(int iLeft, int iTop, int iRight, int iBottom) {
+        int left;
+        int top;
+        int right;
+        int bottom;
+        double density = getResources().getDisplayMetrics().density;
+
+        left = (int) (iLeft * density);
+        top = (int) (iTop * density);
+        right = (int) (iRight * density);
+        bottom = (int) (iBottom * density);
+
+        map.setPadding(left + baseLeftMapPadding,
+                top + baseTopMapPadding,
+                right + baseRightMapPadding,
+                bottom + baseBottomMapPadding);
+    }
 
-      manager.pushEvent(context, this, "onKmlReady", pointers);
+    public double[][] getMapBoundaries() {
+        LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;
+        LatLng northEast = bounds.northeast;
+        LatLng southWest = bounds.southwest;
 
-    } catch (XmlPullParserException | IOException | InterruptedException | ExecutionException e) {
-      e.printStackTrace();
+        return new double[][]{
+                {northEast.longitude, northEast.latitude},
+                {southWest.longitude, southWest.latitude}
+        };
     }
-  }
 
-  @Override
-  public void onIndoorBuildingFocused() {
-    IndoorBuilding building = this.map.getFocusedBuilding();
-    if (building != null) {
-      List<IndoorLevel> levels = building.getLevels();
-      int index = 0;
-      WritableArray levelsArray = Arguments.createArray();
-      for (IndoorLevel level : levels) {
-        WritableMap levelMap = Arguments.createMap();
-        levelMap.putInt("index", index);
-        levelMap.putString("name", level.getName());
-        levelMap.putString("shortName", level.getShortName());
-        levelsArray.pushMap(levelMap);
-        index++;
-      }
-      WritableMap event = Arguments.createMap();
-      WritableMap indoorBuilding = Arguments.createMap();
-      indoorBuilding.putArray("levels", levelsArray);
-      indoorBuilding.putInt("activeLevelIndex", building.getActiveLevelIndex());
-      indoorBuilding.putBoolean("underground", building.isUnderground());
+    public void setMapBoundaries(ReadableMap northEast, ReadableMap southWest) {
+        if (map == null) return;
+
+        LatLngBounds.Builder builder = new LatLngBounds.Builder();
 
-      event.putMap("IndoorBuilding", indoorBuilding);
+        double latNE = northEast.getDouble("latitude");
+        double lngNE = northEast.getDouble("longitude");
+        builder.include(new LatLng(latNE, lngNE));
 
-      manager.pushEvent(context, this, "onIndoorBuildingFocused", event);
-    } else {
-      WritableMap event = Arguments.createMap();
-      WritableArray levelsArray = Arguments.createArray();
-      WritableMap indoorBuilding = Arguments.createMap();
-      indoorBuilding.putArray("levels", levelsArray);
-      indoorBuilding.putInt("activeLevelIndex", 0);
-      indoorBuilding.putBoolean("underground", false);
+        double latSW = southWest.getDouble("latitude");
+        double lngSW = southWest.getDouble("longitude");
+        builder.include(new LatLng(latSW, lngSW));
 
-      event.putMap("IndoorBuilding", indoorBuilding);
+        LatLngBounds bounds = builder.build();
 
-      manager.pushEvent(context, this, "onIndoorBuildingFocused", event);
+        map.setLatLngBoundsForCameraTarget(bounds);
     }
-  }
 
-  @Override
-  public void onIndoorLevelActivated(IndoorBuilding building) {
-    if (building == null) {
-      return;
+    // InfoWindowAdapter interface
+
+    @Override
+    public View getInfoWindow(Marker marker) {
+        AirMapMarker markerView = getMarkerMap(marker);
+        return markerView.getCallout();
     }
-    int activeLevelIndex = building.getActiveLevelIndex();
-    if (activeLevelIndex < 0 || activeLevelIndex >= building.getLevels().size()) {
-      return;
+
+    @Override
+    public View getInfoContents(Marker marker) {
+        AirMapMarker markerView = getMarkerMap(marker);
+        return markerView.getInfoContents();
     }
-    IndoorLevel level = building.getLevels().get(activeLevelIndex);
 
-    WritableMap event = Arguments.createMap();
-    WritableMap indoorlevel = Arguments.createMap();
+    @Override
+    public boolean dispatchTouchEvent(MotionEvent ev) {
+        int countTouch = MotionEventCompat.getPointerCount(ev);
+        if(countTouch > 1){
+            isZoom = true;
+        }
+        gestureDetector.onTouchEvent(ev);
+        int X = (int) ev.getX();
+        int Y = (int) ev.getY();
+        if (map != null) {
+            tapLocation = map.getProjection().fromScreenLocation(new Point(X, Y));
+        }
+        int action = MotionEventCompat.getActionMasked(ev);
+        switch (action) {
+            case (MotionEvent.ACTION_DOWN):
+                if(countTouch == 1) {
+                    Point point = new Point((int) ev.getX(), (int) ev.getY() - finger);
+                    pointLongPress = point;
+                    if (markerSelect != null) {
+                        AirMapMarker marker = getTapAtMarker(new Point(X, Y));
+                        if(marker == null) break;
+                        if(!marker.getIdentifier().equals(markerSelect.getIdentifier())) break;
+                        tapAtMarker = true;
+                        map.getUiSettings().setScrollGesturesEnabled(false);
+                    }
+                    this.getParent().requestDisallowInterceptTouchEvent(
+                            map != null && map.getUiSettings().isScrollGesturesEnabled());
+                }
+                break;
+            case (MotionEvent.ACTION_MOVE):
+                if (!isZoom && isLongPress && manager.checkCanUserLongPress(markerSelect, this) && tapAtMarker) {
+                    onPanDrag(ev);
+                }
+                break;
+            case (MotionEvent.ACTION_UP):
+                isZoom = false;
+                pointLongPress = null;
+                isLongPress = false;
+                tapAtMarker = false;
+                map.getUiSettings().setScrollGesturesEnabled(true);
+                this.getParent().requestDisallowInterceptTouchEvent(false);
+                break;
+        }
+        super.dispatchTouchEvent(ev);
+        return true;
+    }
+
+    @Override
+    public void onMarkerDragStart(Marker marker) {
+        manager.updatePolyline(marker.getPosition());
+
+        WritableMap event = makeClickEventData(marker.getPosition());
+        manager.pushEvent(context, this, "onMarkerDragStart", event);
 
-    indoorlevel.putInt("activeLevelIndex", activeLevelIndex);
-    indoorlevel.putString("name", level.getName());
-    indoorlevel.putString("shortName", level.getShortName());
+        AirMapMarker markerView = getMarkerMap(marker);
+        event = makeClickEventData(marker.getPosition());
+        manager.pushEvent(context, markerView, "onDragStart", event);
+    }
 
-    event.putMap("IndoorLevel", indoorlevel);
+    @Override
+    public void onMarkerDrag(Marker marker) {
 
-    manager.pushEvent(context, this, "onIndoorLevelActivated", event);
-  }
+        manager.updatePolyline(marker.getPosition());
 
-  public void setIndoorActiveLevelIndex(int activeLevelIndex) {
-    IndoorBuilding building = this.map.getFocusedBuilding();
-    if (building != null) {
-      if (activeLevelIndex >= 0 && activeLevelIndex < building.getLevels().size()) {
-        IndoorLevel level = building.getLevels().get(activeLevelIndex);
-        if (level != null) {
-          level.activate();
+        WritableMap event = makeClickEventData(marker.getPosition());
+        manager.pushEvent(context, this, "onMarkerDrag", event);
+
+        AirMapMarker markerView = getMarkerMap(marker);
+        event = makeClickEventData(marker.getPosition());
+        manager.pushEvent(context, markerView, "onDrag", event);
+
+    }
+
+    @Override
+    public void onMarkerDragEnd(Marker marker) {
+        WritableMap event = makeClickEventData(marker.getPosition());
+        manager.pushEvent(context, this, "onMarkerDragEnd", event);
+
+        AirMapMarker markerView = getMarkerMap(marker);
+        event = makeClickEventData(marker.getPosition());
+        manager.pushEvent(context, markerView, "onDragEnd", event);
+    }
+
+    @Override
+    public void onPoiClick(PointOfInterest poi) {
+        WritableMap event = makeClickEventData(poi.latLng);
+
+        event.putString("placeId", poi.placeId);
+        event.putString("name", poi.name);
+
+        manager.pushEvent(context, this, "onPoiClick", event);
+    }
+
+    private ProgressBar getMapLoadingProgressBar() {
+        if (this.mapLoadingProgressBar == null) {
+            this.mapLoadingProgressBar = new ProgressBar(getContext());
+            this.mapLoadingProgressBar.setIndeterminate(true);
+        }
+        if (this.loadingIndicatorColor != null) {
+            this.setLoadingIndicatorColor(this.loadingIndicatorColor);
         }
-      }
+        return this.mapLoadingProgressBar;
     }
-  }
 
-  private AirMapMarker getMarkerMap(Marker marker) {
-    AirMapMarker airMarker = markerMap.get(marker);
+    private RelativeLayout getMapLoadingLayoutView() {
+        if (this.mapLoadingLayout == null) {
+            this.mapLoadingLayout = new RelativeLayout(getContext());
+            this.mapLoadingLayout.setBackgroundColor(Color.LTGRAY);
+            this.addView(this.mapLoadingLayout,
+                    new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
+                            ViewGroup.LayoutParams.MATCH_PARENT));
+
+            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
+                    RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
+            params.addRule(RelativeLayout.CENTER_IN_PARENT);
+            this.mapLoadingLayout.addView(this.getMapLoadingProgressBar(), params);
 
-    if (airMarker != null) {
-      return airMarker;
+            this.mapLoadingLayout.setVisibility(View.INVISIBLE);
+        }
+        this.setLoadingBackgroundColor(this.loadingBackgroundColor);
+        return this.mapLoadingLayout;
     }
 
-    for (Map.Entry<Marker, AirMapMarker> entryMarker : markerMap.entrySet()) {
-      if (entryMarker.getKey().getPosition().equals(marker.getPosition())
-          && entryMarker.getKey().getTitle().equals(marker.getTitle())) {
-        airMarker = entryMarker.getValue();
-        break;
-      }
+    private ImageView getCacheImageView() {
+        if (this.cacheImageView == null) {
+            this.cacheImageView = new ImageView(getContext());
+            this.addView(this.cacheImageView,
+                    new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
+                            ViewGroup.LayoutParams.MATCH_PARENT));
+            this.cacheImageView.setVisibility(View.INVISIBLE);
+        }
+        return this.cacheImageView;
     }
 
-    return airMarker;
-  }
+    private void removeCacheImageView() {
+        if (this.cacheImageView != null) {
+            ((ViewGroup) this.cacheImageView.getParent()).removeView(this.cacheImageView);
+            this.cacheImageView = null;
+        }
+    }
+
+    private void removeMapLoadingProgressBar() {
+        if (this.mapLoadingProgressBar != null) {
+            ((ViewGroup) this.mapLoadingProgressBar.getParent()).removeView(this.mapLoadingProgressBar);
+            this.mapLoadingProgressBar = null;
+        }
+    }
+
+    private void removeMapLoadingLayoutView() {
+        this.removeMapLoadingProgressBar();
+        if (this.mapLoadingLayout != null) {
+            ((ViewGroup) this.mapLoadingLayout.getParent()).removeView(this.mapLoadingLayout);
+            this.mapLoadingLayout = null;
+        }
+    }
+
+    private void cacheView() {
+        if (this.cacheEnabled) {
+            final ImageView cacheImageView = this.getCacheImageView();
+            final RelativeLayout mapLoadingLayout = this.getMapLoadingLayoutView();
+            cacheImageView.setVisibility(View.INVISIBLE);
+            mapLoadingLayout.setVisibility(View.VISIBLE);
+            if (this.isMapLoaded) {
+                this.map.snapshot(new GoogleMap.SnapshotReadyCallback() {
+                    @Override
+                    public void onSnapshotReady(Bitmap bitmap) {
+                        cacheImageView.setImageBitmap(bitmap);
+                        cacheImageView.setVisibility(View.VISIBLE);
+                        mapLoadingLayout.setVisibility(View.INVISIBLE);
+                    }
+                });
+            }
+        } else {
+            this.removeCacheImageView();
+            if (this.isMapLoaded) {
+                this.removeMapLoadingLayoutView();
+            }
+        }
+    }
+
+    public void onPanDrag(MotionEvent ev) {
+        int finger = this.finger;
+        Point point = new Point((int) ev.getX(), (int) ev.getY() - finger);
+        LatLng coords = this.map.getProjection().fromScreenLocation(point);
+        manager.updateMarkerLongPress(coords, markerSelect, this);
+        WritableMap event = makeClickEventData(coords);
+        manager.pushEvent(context, this, "onPanDrag", event);
+    }
+
+    public void onDoublePress(MotionEvent ev) {
+        if (this.map == null) return;
+        Point point = new Point((int) ev.getX(), (int) ev.getY());
+        LatLng coords = this.map.getProjection().fromScreenLocation(point);
+        WritableMap event = makeClickEventData(coords);
+        manager.pushEvent(context, this, "onDoublePress", event);
+    }
+
+    public void setKmlSrc(String kmlSrc) {
+        try {
+            InputStream kmlStream = new FileUtil(context).execute(kmlSrc).get();
+
+            if (kmlStream == null) {
+                return;
+            }
 
-  @Override
-  public void requestLayout() {
-    super.requestLayout();
-    post(measureAndLayout);
-  }
+            KmlLayer kmlLayer = new KmlLayer(map, kmlStream, context);
+            kmlLayer.addLayerToMap();
+
+            WritableMap pointers = new WritableNativeMap();
+            WritableArray markers = new WritableNativeArray();
+
+            if (kmlLayer.getContainers() == null) {
+                manager.pushEvent(context, this, "onKmlReady", pointers);
+                return;
+            }
+
+            //Retrieve a nested container within the first container
+            KmlContainer container = kmlLayer.getContainers().iterator().next();
+            if (container == null || container.getContainers() == null) {
+                manager.pushEvent(context, this, "onKmlReady", pointers);
+                return;
+            }
+
+
+            if (container.getContainers().iterator().hasNext()) {
+                container = container.getContainers().iterator().next();
+            }
+
+            int index = 0;
+            for (KmlPlacemark placemark : container.getPlacemarks()) {
+                MarkerOptions options = new MarkerOptions();
+
+                if (placemark.getInlineStyle() != null) {
+                    options = placemark.getMarkerOptions();
+                } else {
+                    options.icon(BitmapDescriptorFactory.defaultMarker());
+                }
+
+                LatLng latLng = ((LatLng) placemark.getGeometry().getGeometryObject());
+                String title = "";
+                String snippet = "";
+
+                if (placemark.hasProperty("name")) {
+                    title = placemark.getProperty("name");
+                }
+
+                if (placemark.hasProperty("description")) {
+                    snippet = placemark.getProperty("description");
+                }
+
+                options.position(latLng);
+                options.title(title);
+                options.snippet(snippet);
+
+                AirMapMarker marker = new AirMapMarker(context, options, this.manager.getMarkerManager());
+
+                if (placemark.getInlineStyle() != null
+                        && placemark.getInlineStyle().getIconUrl() != null) {
+                    marker.setImage(placemark.getInlineStyle().getIconUrl());
+                } else if (container.getStyle(placemark.getStyleId()) != null) {
+                    KmlStyle style = container.getStyle(placemark.getStyleId());
+                    marker.setImage(style.getIconUrl());
+                }
+
+                String identifier = title + " - " + index;
+
+                marker.setIdentifier(identifier);
+
+                addFeature(marker, index++);
+
+                WritableMap loadedMarker = makeClickEventData(latLng);
+                loadedMarker.putString("id", identifier);
+                loadedMarker.putString("title", title);
+                loadedMarker.putString("description", snippet);
+
+                markers.pushMap(loadedMarker);
+            }
+
+            pointers.putArray("markers", markers);
+
+            manager.pushEvent(context, this, "onKmlReady", pointers);
+
+        } catch (XmlPullParserException | IOException | InterruptedException | ExecutionException e) {
+            e.printStackTrace();
+        }
+    }
 
-  private final Runnable measureAndLayout = new Runnable() {
     @Override
-    public void run() {
-      measure(MeasureSpec.makeMeasureSpec(getWidth(), MeasureSpec.EXACTLY),
-              MeasureSpec.makeMeasureSpec(getHeight(), MeasureSpec.EXACTLY));
-      layout(getLeft(), getTop(), getRight(), getBottom());
+    public void onIndoorBuildingFocused() {
+        IndoorBuilding building = this.map.getFocusedBuilding();
+        if (building != null) {
+            List<IndoorLevel> levels = building.getLevels();
+            int index = 0;
+            WritableArray levelsArray = Arguments.createArray();
+            for (IndoorLevel level : levels) {
+                WritableMap levelMap = Arguments.createMap();
+                levelMap.putInt("index", index);
+                levelMap.putString("name", level.getName());
+                levelMap.putString("shortName", level.getShortName());
+                levelsArray.pushMap(levelMap);
+                index++;
+            }
+            WritableMap event = Arguments.createMap();
+            WritableMap indoorBuilding = Arguments.createMap();
+            indoorBuilding.putArray("levels", levelsArray);
+            indoorBuilding.putInt("activeLevelIndex", building.getActiveLevelIndex());
+            indoorBuilding.putBoolean("underground", building.isUnderground());
+
+            event.putMap("IndoorBuilding", indoorBuilding);
+
+            manager.pushEvent(context, this, "onIndoorBuildingFocused", event);
+        } else {
+            WritableMap event = Arguments.createMap();
+            WritableArray levelsArray = Arguments.createArray();
+            WritableMap indoorBuilding = Arguments.createMap();
+            indoorBuilding.putArray("levels", levelsArray);
+            indoorBuilding.putInt("activeLevelIndex", 0);
+            indoorBuilding.putBoolean("underground", false);
+
+            event.putMap("IndoorBuilding", indoorBuilding);
+
+            manager.pushEvent(context, this, "onIndoorBuildingFocused", event);
+        }
     }
-  };
+
+    @Override
+    public void onIndoorLevelActivated(IndoorBuilding building) {
+        if (building == null) {
+            return;
+        }
+        int activeLevelIndex = building.getActiveLevelIndex();
+        if (activeLevelIndex < 0 || activeLevelIndex >= building.getLevels().size()) {
+            return;
+        }
+        IndoorLevel level = building.getLevels().get(activeLevelIndex);
+
+        WritableMap event = Arguments.createMap();
+        WritableMap indoorlevel = Arguments.createMap();
+
+        indoorlevel.putInt("activeLevelIndex", activeLevelIndex);
+        indoorlevel.putString("name", level.getName());
+        indoorlevel.putString("shortName", level.getShortName());
+
+        event.putMap("IndoorLevel", indoorlevel);
+
+        manager.pushEvent(context, this, "onIndoorLevelActivated", event);
+    }
+
+    public void setIndoorActiveLevelIndex(int activeLevelIndex) {
+        IndoorBuilding building = this.map.getFocusedBuilding();
+        if (building != null) {
+            if (activeLevelIndex >= 0 && activeLevelIndex < building.getLevels().size()) {
+                IndoorLevel level = building.getLevels().get(activeLevelIndex);
+                if (level != null) {
+                    level.activate();
+                }
+            }
+        }
+    }
+
+    private AirMapMarker getMarkerMap(Marker marker) {
+        AirMapMarker airMarker = markerMap.get(marker);
+
+        if (airMarker != null) {
+            return airMarker;
+        }
+
+        for (Map.Entry<Marker, AirMapMarker> entryMarker : markerMap.entrySet()) {
+            if (entryMarker.getKey().getPosition().equals(marker.getPosition())
+                    && entryMarker.getKey().getTitle().equals(marker.getTitle())) {
+                airMarker = entryMarker.getValue();
+                break;
+            }
+        }
+
+        return airMarker;
+    }
+
+    @Override
+    public void requestLayout() {
+        super.requestLayout();
+        post(measureAndLayout);
+    }
+
+    private final Runnable measureAndLayout = new Runnable() {
+        @Override
+        public void run() {
+            measure(MeasureSpec.makeMeasureSpec(getWidth(), MeasureSpec.EXACTLY),
+                    MeasureSpec.makeMeasureSpec(getHeight(), MeasureSpec.EXACTLY));
+            layout(getLeft(), getTop(), getRight(), getBottom());
+        }
+    };
 }
diff --git a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/MapsPackage.java b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/MapsPackage.java
index 2f140b3..1518855 100644
--- a/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/MapsPackage.java
+++ b/node_modules/react-native-maps/android/src/main/java/com/airbnb/android/react/maps/MapsPackage.java
@@ -32,12 +32,12 @@ public class MapsPackage implements ReactPackage {
   @Override
   public List<ViewManager> createViewManagers(ReactApplicationContext reactContext) {
     AirMapCalloutManager calloutManager = new AirMapCalloutManager();
-    AirMapMarkerManager annotationManager = new AirMapMarkerManager();
     AirMapPolylineManager polylineManager = new AirMapPolylineManager(reactContext);
+    AirMapMarkerManager annotationManager = new AirMapMarkerManager();
     AirMapGradientPolylineManager gradientPolylineManager = new AirMapGradientPolylineManager(reactContext);
     AirMapPolygonManager polygonManager = new AirMapPolygonManager(reactContext);
     AirMapCircleManager circleManager = new AirMapCircleManager(reactContext);
-    AirMapManager mapManager = new AirMapManager(reactContext);
+    AirMapManager mapManager = new AirMapManager(reactContext, polylineManager);
     AirMapLiteManager mapLiteManager = new AirMapLiteManager(reactContext);
     AirMapUrlTileManager urlTileManager = new AirMapUrlTileManager(reactContext);
     AirMapWMSTileManager gsUrlTileManager = new AirMapWMSTileManager(reactContext);
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGMSMarker.h b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGMSMarker.h
index 79a05ad..8c3f356 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGMSMarker.h
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGMSMarker.h
@@ -14,8 +14,10 @@
 
 @interface AIRGMSMarker : GMSMarker
 @property (nonatomic, strong) NSString *identifier;
+@property (nonatomic, assign) BOOL enableMoveMarker;
 @property (nonatomic, weak) AIRGoogleMapMarker *fakeMarker;
 @property (nonatomic, copy) RCTBubblingEventBlock onPress;
+
 @end
 
 
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMap.h b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMap.h
index 7c31282..c88d7cf 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMap.h
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMap.h
@@ -26,6 +26,7 @@
 @property (nonatomic, assign) NSString *customMapStyleString;
 @property (nonatomic, assign) UIEdgeInsets mapPadding;
 @property (nonatomic, assign) NSString *paddingAdjustmentBehaviorString;
+@property (nonatomic, nonatomic) NSString *idTapMarker;
 @property (nonatomic, copy) RCTBubblingEventBlock onMapReady;
 @property (nonatomic, copy) RCTBubblingEventBlock onMapLoaded;
 @property (nonatomic, copy) RCTBubblingEventBlock onKmlReady;
@@ -36,10 +37,12 @@
 @property (nonatomic, copy) RCTBubblingEventBlock onMarkerPress;
 @property (nonatomic, copy) RCTBubblingEventBlock onChange;
 @property (nonatomic, copy) RCTBubblingEventBlock onPoiClick;
+@property (nonatomic, copy) RCTBubblingEventBlock onPanDragEnd;
 @property (nonatomic, copy) RCTDirectEventBlock onRegionChange;
 @property (nonatomic, copy) RCTDirectEventBlock onRegionChangeComplete;
 @property (nonatomic, copy) RCTDirectEventBlock onIndoorLevelActivated;
 @property (nonatomic, copy) RCTDirectEventBlock onIndoorBuildingFocused;
+@property (nonatomic, copy) RCTBubblingEventBlock onChangeViewMap;
 @property (nonatomic, strong) NSMutableArray *markers;
 @property (nonatomic, strong) NSMutableArray *polygons;
 @property (nonatomic, strong) NSMutableArray *polylines;
@@ -74,6 +77,12 @@
 - (void)idleAtCameraPosition:(GMSCameraPosition *)position isGesture:(BOOL)isGesture;
 - (void)didTapPOIWithPlaceID:(NSString *)placeID name:(NSString *) name location:(CLLocationCoordinate2D) location;
 - (NSArray *)getMapBoundaries;
+- (void)setPolygonToZoom:(NSDictionary *) data;
+- (AIRGMSMarker*)getMarkerSelected;
+- (void)resetMarkerSelected;
+- (void)setTapMarker: (NSString *) id;
+- (AIRGoogleMapMarker*)markerAtPoint: (CGPoint)point;
+
 
 + (MKCoordinateRegion)makeGMSCameraPositionFromMap:(GMSMapView *)map andGMSCameraPosition:(GMSCameraPosition *)position;
 + (GMSCameraPosition*)makeGMSCameraPositionFromMap:(GMSMapView *)map andMKCoordinateRegion:(MKCoordinateRegion)region;
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMap.m b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMap.m
index 8a4215c..ec607c8 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMap.m
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMap.m
@@ -39,7 +39,7 @@
 } while (0)
 #endif
 
-
+AIRGMSMarker *markerSeleted;
 id regionAsJSON(MKCoordinateRegion region) {
   return @{
            @"latitude": [NSNumber numberWithDouble:region.center.latitude],
@@ -91,6 +91,7 @@ - (instancetype)init
     _didPrepareMap = false;
     _didCallOnMapReady = false;
     _zoomTapEnabled = YES;
+    _idTapMarker = nil;
 
     // Listen to the myLocation property of GMSMapView.
     [self addObserver:self
@@ -135,6 +136,10 @@ - (void)insertReactSubview:(id<RCTComponent>)subview atIndex:(NSInteger)atIndex
   if ([subview isKindOfClass:[AIRGoogleMapMarker class]]) {
     AIRGoogleMapMarker *marker = (AIRGoogleMapMarker*)subview;
     marker.realMarker.map = self;
+      if(self.idTapMarker != nil && [marker.identifier isEqualToString:self.idTapMarker]){
+          markerSeleted = marker.realMarker;
+          [self setSelectedMarker:marker.realMarker];
+      }
     [self.markers addObject:marker];
   } else if ([subview isKindOfClass:[AIRGoogleMapPolygon class]]) {
     AIRGoogleMapPolygon *polygon = (AIRGoogleMapPolygon*)subview;
@@ -144,6 +149,12 @@ - (void)insertReactSubview:(id<RCTComponent>)subview atIndex:(NSInteger)atIndex
     AIRGoogleMapPolyline *polyline = (AIRGoogleMapPolyline*)subview;
     polyline.polyline.map = self;
     [self.polylines addObject:polyline];
+      if([polyline.identifier isEqualToString:@"distancePolyline"]){
+          [polyline setCoordinateChange:polyline.coordinates[1].coordinate setIndex:1 setMap:self isMarkerCircle:true];
+          polyline.polylineDistance.map = self;
+          [self.polylines addObject:polyline];
+      }
+      
   } else if ([subview isKindOfClass:[AIRGoogleMapCircle class]]) {
     AIRGoogleMapCircle *circle = (AIRGoogleMapCircle*)subview;
     circle.circle.map = self;
@@ -183,15 +194,20 @@ - (void)removeReactSubview:(id<RCTComponent>)subview {
   if ([subview isKindOfClass:[AIRGoogleMapMarker class]]) {
     AIRGoogleMapMarker *marker = (AIRGoogleMapMarker*)subview;
     marker.realMarker.map = nil;
+
     [self.markers removeObject:marker];
   } else if ([subview isKindOfClass:[AIRGoogleMapPolygon class]]) {
     AIRGoogleMapPolygon *polygon = (AIRGoogleMapPolygon*)subview;
     polygon.polygon.map = nil;
     [self.polygons removeObject:polygon];
   } else if ([subview isKindOfClass:[AIRGoogleMapPolyline class]]) {
-    AIRGoogleMapPolyline *polyline = (AIRGoogleMapPolyline*)subview;
-    polyline.polyline.map = nil;
-    [self.polylines removeObject:polyline];
+      AIRGoogleMapPolyline *polyline = (AIRGoogleMapPolyline*)subview;
+      polyline.polyline.map = nil;
+      [self.polylines removeObject:polyline];
+      if([polyline.identifier isEqualToString:@"distancePolyline"]){
+         polyline.polylineDistance.map = nil;
+         [self.polylines removeObject:polyline];
+        }
   } else if ([subview isKindOfClass:[AIRGoogleMapCircle class]]) {
     AIRGoogleMapCircle *circle = (AIRGoogleMapCircle*)subview;
     circle.circle.map = nil;
@@ -322,7 +338,12 @@ - (void)mapViewDidFinishTileRendering {
 
 - (BOOL)didTapMarker:(GMSMarker *)marker {
   AIRGMSMarker *airMarker = (AIRGMSMarker *)marker;
-
+    self.idTapMarker = nil;
+    if(markerSeleted!=nil && [markerSeleted.identifier isEqualToString:airMarker.identifier]){
+        markerSeleted = nil;
+    }else {
+        markerSeleted = airMarker;
+    }
   id event = @{@"action": @"marker-press",
                @"id": airMarker.identifier ?: @"unknown",
                @"coordinate": @{
@@ -335,9 +356,34 @@ - (BOOL)didTapMarker:(GMSMarker *)marker {
   if (self.onMarkerPress) self.onMarkerPress(event);
 
   // TODO: not sure why this is necessary
-  [self setSelectedMarker:marker];
+    if(airMarker.fakeMarker.enableMove){
+        [self setSelectedMarker:marker];
+    }
   return NO;
 }
+- (AIRGMSMarker*)getMarkerSelected{
+    return markerSeleted;
+};
+
+- (void)resetMarkerSelected{
+    markerSeleted = nil;
+    self.idTapMarker = nil;
+    for (int i = 0; i < self.polylines.count; i++) {
+        AIRGoogleMapPolyline *polyline = [self.polylines objectAtIndex:i];
+        polyline.polylineDistance.strokeColor = [UIColor clearColor];
+        polyline.polylineDistance.path = [GMSMutablePath path];
+    }
+};
+- (void)setTapMarker: (NSString *) data{
+    self.idTapMarker = data;
+    for (AIRGoogleMapMarker* mrkView in self.markers) {
+        if([mrkView.identifier isEqualToString:data]){
+            [self setSelectedMarker:mrkView.realMarker];
+            markerSeleted = mrkView.realMarker;
+            break;
+        }
+    }
+};
 
 - (void)didTapPolyline:(GMSOverlay *)polyline {
   AIRGMSPolyline *airPolyline = (AIRGMSPolyline *)polyline;
@@ -964,6 +1010,92 @@ - (void) didChangeActiveLevel: (nullable GMSIndoorLevel *)     level {
     });
 }
 
+- (void) setPolygonToZoom: (NSDictionary*) data{
+    if(data != nil){
+        float zoom = 15.0;
+        [self setMinZoom:zoom maxZoom:25];
+        CLLocationCoordinate2D center = [self parseDataCoord: [data objectForKey: @"center"]];
+        CLLocationCoordinate2D centerH = [self parseDataCoord: [data objectForKey: @"centerH"]];
+        CLLocationCoordinate2D northEast = [self parseDataCoord: [data objectForKey: @"northEast"]];
+        CLLocationCoordinate2D southWest = [self parseDataCoord: [data objectForKey: @"southWest"]];
+        float bearing = [[data valueForKey: @"heading"] floatValue];
+        float pitch = [[data valueForKey: @"pitch"] floatValue];
+        CGFloat paddingTop = [RCTConvert CGFloat:data[@"paddingTop"]];
+        NSArray *polygons = [data objectForKey: @"polygons"];
+        GMSMutablePath *arrayPolygon = [[GMSMutablePath alloc]init];
+        for (NSDictionary *polygon in polygons) {
+            CLLocationCoordinate2D coord = [self parseDataCoord:polygon];
+            [arrayPolygon addCoordinate: coord];
+        }
+        GMSCoordinateBounds *bounds = [[GMSCoordinateBounds alloc] initWithCoordinate:northEast coordinate:southWest];
+        GMSCameraUpdate *update = [GMSCameraUpdate fitBounds:bounds];
+        
+        [self moveCamera:update];
+        [self setCamera:[GMSCameraPosition cameraWithTarget:center zoom:zoom bearing:bearing viewingAngle:pitch]];
+        [self animateToCameraPosition:[GMSCameraPosition cameraWithTarget:center zoom:zoom bearing:bearing viewingAngle:pitch]];
+        CGPoint centerVPoint = [self.projection pointForCoordinate:center];
+        CGPoint centerHPoint = [self.projection pointForCoordinate:centerH];
+        CGPoint newCenterPoint = CGPointMake(centerHPoint.x, centerVPoint.y);
+
+        CLLocationCoordinate2D newCenter = [self.projection coordinateForPoint:newCenterPoint];
+
+        
+        while ([self checkPath:arrayPolygon paddingTop:paddingTop]) {
+            zoom += 0.05;
+            [self setCamera:[GMSCameraPosition cameraWithTarget:newCenter zoom:zoom bearing:bearing viewingAngle:pitch]];
+            [self animateToCameraPosition:[GMSCameraPosition cameraWithTarget:newCenter zoom:zoom bearing:bearing viewingAngle:pitch]];
+            if(zoom > 25) {
+                break;
+            }
+        }
+        if(self.frame.size.height == 0 && zoom == 15){
+            zoom = 16;
+        }else {
+            zoom -= 0.69;
+        }
+        [self setMinZoom:zoom maxZoom:25];
+        [CATransaction begin];
+        [CATransaction setAnimationDuration:0.0];
+        CGPoint centerPointPaddingTop = CGPointMake(centerHPoint.x, centerVPoint.y - paddingTop);
+
+        CLLocationCoordinate2D centerPaddingTop = [self.projection coordinateForPoint:centerPointPaddingTop];
+        [self setCamera:[GMSCameraPosition cameraWithTarget:centerPaddingTop zoom:zoom bearing:bearing viewingAngle:pitch]];
+        [self animateToCameraPosition:[GMSCameraPosition cameraWithTarget:centerPaddingTop zoom:zoom bearing:bearing viewingAngle:pitch]];
+        [CATransaction commit];
+        if(self.frame.size.height != 0){
+            self.onChangeViewMap(@{
+                @"center": @{
+                    @"latitude": @(centerPaddingTop.latitude),
+                    @"longitude": @(centerPaddingTop.longitude),
+                },
+                @"zoom": @(zoom),
+            });
+        }
+    }
+}
+
+- (BOOL)checkPath:(GMSMutablePath*)path paddingTop:(CGFloat) paddingTop {
+    for (int i = 0; i < path.count; i++) {
+        CLLocationCoordinate2D coordinate=[path coordinateAtIndex:i];
+        CGPoint markerPoint = [self.projection pointForCoordinate:coordinate];
+        int padding = 0;
+        if(paddingTop > 0){
+            padding = 100;
+        }
+        if (markerPoint.x < -55 || markerPoint.y <= padding || markerPoint.x > self.frame.size.width + 55 || markerPoint.y >= self.frame.size.height) {
+            return NO;
+        }
+    }
+    return YES;
+}
+
+- (CLLocationCoordinate2D)parseDataCoord:(NSDictionary*)data {
+    double lat = [[data valueForKey:@"latitude"] doubleValue];
+    double lng = [[data valueForKey:@"longitude"] doubleValue];
+    CLLocationCoordinate2D coordinate = CLLocationCoordinate2DMake(lat,lng);
+    return coordinate;
+}
+
 
 @end
 
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapManager.h b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapManager.h
index 9efa0fd..641de4a 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapManager.h
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapManager.h
@@ -11,6 +11,7 @@
 
 @interface AIRGoogleMapManager : RCTViewManager
 @property (nonatomic) BOOL isGesture;
+@property (nonatomic) BOOL tapAtMarker;
 
 @end
 
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapManager.m b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapManager.m
index 23121c5..64b59a9 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapManager.m
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapManager.m
@@ -26,13 +26,16 @@
 #import "SMCalloutView.h"
 #import "AIRGoogleMapMarker.h"
 #import "RCTConvert+AirMap.h"
+#import "AIRGoogleMapPolyline.h"
 
 #import <MapKit/MapKit.h>
 #import <QuartzCore/QuartzCore.h>
+#import <Foundation/Foundation.h>
 
 static NSString *const RCTMapViewKey = @"MapView";
-
-
+int finger = 60;
+float timeout = 0.002;
+NSTimer *timeOutTest = nil;
 @interface AIRGoogleMapManager() <GMSMapViewDelegate>
 {
   BOOL didCallOnMapReady;
@@ -56,9 +59,18 @@ - (UIView *)view
 
   UIPanGestureRecognizer *drag = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handleMapDrag:)];
   [drag setMinimumNumberOfTouches:1];
-  [drag setMaximumNumberOfTouches:1];
+  [drag setMaximumNumberOfTouches:2];
   [map addGestureRecognizer:drag];
 
+  UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleMapTap:)];
+  tap.cancelsTouchesInView = NO;
+  [map addGestureRecognizer:tap];
+    
+    UILongPressGestureRecognizer *longTap = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(handleMapLongTap:)];
+    [longTap setMinimumPressDuration: 1.0];
+    [longTap setNumberOfTapsRequired: 0];
+    [map addGestureRecognizer:longTap];
+
   UIPinchGestureRecognizer *pinch = [[UIPinchGestureRecognizer alloc] initWithTarget:self action:@selector(handleMapDrag:)];
   [map addGestureRecognizer:pinch];
 
@@ -94,6 +106,8 @@ - (UIView *)view
 RCT_EXPORT_VIEW_PROPERTY(onPress, RCTBubblingEventBlock)
 RCT_EXPORT_VIEW_PROPERTY(onLongPress, RCTBubblingEventBlock)
 RCT_EXPORT_VIEW_PROPERTY(onPanDrag, RCTBubblingEventBlock)
+RCT_EXPORT_VIEW_PROPERTY(onPanDragEnd, RCTBubblingEventBlock)
+RCT_EXPORT_VIEW_PROPERTY(onChangeViewMap, RCTBubblingEventBlock)
 RCT_EXPORT_VIEW_PROPERTY(onUserLocationChange, RCTBubblingEventBlock)
 RCT_EXPORT_VIEW_PROPERTY(onChange, RCTBubblingEventBlock)
 RCT_EXPORT_VIEW_PROPERTY(onMarkerPress, RCTDirectEventBlock)
@@ -196,31 +210,41 @@ - (UIView *)view
       RCTLogError(@"Invalid view returned from registry, expecting AIRGoogleMap, got: %@", view);
     } else {
       AIRGoogleMap *mapView = (AIRGoogleMap *)view;
-
-      CLLocationCoordinate2D myLocation = ((AIRGoogleMapMarker *)(mapView.markers.firstObject)).realMarker.position;
-      GMSCoordinateBounds *bounds = [[GMSCoordinateBounds alloc] initWithCoordinate:myLocation coordinate:myLocation];
-
-      for (AIRGoogleMapMarker *marker in mapView.markers)
-        bounds = [bounds includingCoordinate:marker.realMarker.position];
-        
-        GMSCameraUpdate* cameraUpdate;
-        
-        if ([edgePadding count] != 0) {
-            // Set Map viewport
+      bool isDefault = [[edgePadding valueForKey: @"isDefault"] boolValue];
+        if(isDefault){
             CGFloat top = [RCTConvert CGFloat:edgePadding[@"top"]];
             CGFloat right = [RCTConvert CGFloat:edgePadding[@"right"]];
             CGFloat bottom = [RCTConvert CGFloat:edgePadding[@"bottom"]];
             CGFloat left = [RCTConvert CGFloat:edgePadding[@"left"]];
+            [mapView setMapPadding:UIEdgeInsetsMake(top, left, bottom, right)];
             
-            cameraUpdate = [GMSCameraUpdate fitBounds:bounds withEdgeInsets:UIEdgeInsetsMake(top, left, bottom, right)];
         } else {
-            cameraUpdate = [GMSCameraUpdate fitBounds:bounds withPadding:55.0f];
+            CLLocationCoordinate2D myLocation = ((AIRGoogleMapMarker *)(mapView.markers.firstObject)).realMarker.position;
+            GMSCoordinateBounds *bounds = [[GMSCoordinateBounds alloc] initWithCoordinate:myLocation coordinate:myLocation];
+            
+            for (AIRGoogleMapMarker *marker in mapView.markers)
+                bounds = [bounds includingCoordinate:marker.realMarker.position];
+            
+            GMSCameraUpdate* cameraUpdate;
+            
+            if ([edgePadding count] != 0) {
+                // Set Map viewport
+                CGFloat top = [RCTConvert CGFloat:edgePadding[@"top"]];
+                CGFloat right = [RCTConvert CGFloat:edgePadding[@"right"]];
+                CGFloat bottom = [RCTConvert CGFloat:edgePadding[@"bottom"]];
+                CGFloat left = [RCTConvert CGFloat:edgePadding[@"left"]];
+                
+                cameraUpdate = [GMSCameraUpdate fitBounds:bounds withEdgeInsets:UIEdgeInsetsMake(top, left, bottom, right)];
+            } else {
+                cameraUpdate = [GMSCameraUpdate fitBounds:bounds withPadding:55.0f];
+            }
+            if (animated) {
+                [mapView animateWithCameraUpdate: cameraUpdate];
+            } else {
+                [mapView moveCamera: cameraUpdate];
+                
+            }
         }
-      if (animated) {
-        [mapView animateWithCameraUpdate: cameraUpdate];
-      } else {
-        [mapView moveCamera: cameraUpdate];
-      }
     }
   }];
 }
@@ -482,6 +506,40 @@ - (UIView *)view
   }];
  }
 
+RCT_EXPORT_METHOD(setPolygonToZoom:(nonnull NSNumber *)reactTag setData:(NSDictionary *) data){
+    [self.bridge.uiManager addUIBlock:^(__unused RCTUIManager *uiManager, NSDictionary<NSNumber *, UIView *> *viewRegistry) {
+        id view = viewRegistry[reactTag];
+        if (![view isKindOfClass:[AIRGoogleMap class]]) {
+            RCTLogError(@"Invalid view returned from registry, expecting AIRGoogleMap, got: %@", view);
+        } else {
+            AIRGoogleMap *mapView = (AIRGoogleMap *)view;
+            [mapView setPolygonToZoom: data];
+        }
+    }];
+}
+RCT_EXPORT_METHOD(resetMarkerSelected:(nonnull NSNumber *)reactTag){
+    [self.bridge.uiManager addUIBlock:^(__unused RCTUIManager *uiManager, NSDictionary<NSNumber *, UIView *> *viewRegistry) {
+        id view = viewRegistry[reactTag];
+        if (![view isKindOfClass:[AIRGoogleMap class]]) {
+            RCTLogError(@"Invalid view returned from registry, expecting AIRGoogleMap, got: %@", view);
+        } else {
+            AIRGoogleMap *mapView = (AIRGoogleMap *)view;
+            [mapView resetMarkerSelected];
+        }
+    }];
+}
+
+RCT_EXPORT_METHOD(setTapMarker:(nonnull NSNumber *)reactTag setId:(NSString *) data){
+    [self.bridge.uiManager addUIBlock:^(__unused RCTUIManager *uiManager, NSDictionary<NSNumber *, UIView *> *viewRegistry) {
+        id view = viewRegistry[reactTag];
+        if (![view isKindOfClass:[AIRGoogleMap class]]) {
+            RCTLogError(@"Invalid view returned from registry, expecting AIRGoogleMap, got: %@", view);
+        } else {
+            AIRGoogleMap *mapView = (AIRGoogleMap *)view;
+            [mapView setTapMarker: data];
+        }
+    }];
+}
 + (BOOL)requiresMainQueueSetup {
   return YES;
 }
@@ -520,8 +578,33 @@ - (void)mapView:(GMSMapView *)mapView didTapAtCoordinate:(CLLocationCoordinate2D
 }
 
 - (void)mapView:(GMSMapView *)mapView didLongPressAtCoordinate:(CLLocationCoordinate2D)coordinate {
+  CGPoint touchPoint = [mapView.projection pointForCoordinate:coordinate];
+  CGPoint temp = touchPoint;
+  touchPoint.y = touchPoint.y - finger;
+  CLLocationCoordinate2D coord = [mapView.projection coordinateForPoint:touchPoint];
   AIRGoogleMap *googleMapView = (AIRGoogleMap *)mapView;
-  [googleMapView didLongPressAtCoordinate:coordinate];
+    bool isMeasure = NO;
+    for (int i = 0; i < googleMapView.polylines.count; i++ ) {
+        AIRGoogleMapPolyline *polyline = googleMapView.polylines[i];
+        if([polyline.identifier isEqualToString:@"distancePolyline"]){
+//            isMeasure = YES; // add to reset marker when user selected
+            self.tapAtMarker = YES;
+            [googleMapView setScrollEnabled: NO];
+            break;
+        }
+    }
+  [googleMapView didLongPressAtCoordinate:coord];
+    if(!isMeasure){
+        AIRGoogleMapMarker *marker = [googleMapView markerAtPoint:temp];
+        if(marker != nil && [marker.identifier isEqualToString:[googleMapView.getMarkerSelected identifier]]){
+            self.tapAtMarker = YES;
+            [googleMapView setScrollEnabled: NO];
+        }
+        [self updateMarker:googleMapView setCoord:coord];
+    }else{
+        [googleMapView resetMarkerSelected];
+    }
+    [self updatePolyline:googleMapView setCoord:coord];
 }
 
 - (void)mapView:(GMSMapView *)mapView didChangeCameraPosition:(GMSCameraPosition *)position {
@@ -570,26 +653,239 @@ - (void)mapView:(GMSMapView *)mapView
     AIRGoogleMap *googleMapView = (AIRGoogleMap *)mapView;
     [googleMapView didTapPOIWithPlaceID:placeID name:name location:location];
 }
+-(void)updatePolyline:(AIRGoogleMap*) map setCoord:(CLLocationCoordinate2D)coord {
+    if(![self checkEnableMove:map]) return;
+    int startNum = 0;
+    for (int i = startNum; i < map.polylines.count; i++ ) {
+        AIRGoogleMapPolyline *polyline = map.polylines[i];
+
+        if([polyline.identifier isEqualToString:@"distancePolyline"]){
+            [polyline setCoordinateChange:coord setIndex:1 setMap:map isMarkerCircle:true];
+            break;
+        }else if([polyline.identifier intValue] <= polyline.coordinates.count){
+            [polyline setCoordinateChange:coord setIndex:[polyline.identifier intValue] setMap:map isMarkerCircle:false];
+            break;
+        }
+    }
+}
+
+-(void)updateMarker:(AIRGoogleMap*) map setCoord:(CLLocationCoordinate2D)coord {
+    if(![self checkEnableMove:map]) return;
+    int startNum = 0;
+    for (int i = startNum; i < map.markers.count; i++ ) {
+        AIRGoogleMapMarker *marker = map.markers[i];
+        if([marker.identifier isEqualToString:@"distanceFromPinToBall"]){
+            [marker setCoordinate:coord];
+            break;
+        }else if([marker.identifier isEqualToString:[map.getMarkerSelected identifier]]){
+            [marker setCoordinate:coord];
+        }
+    }
 
+}
+-(bool)checkEnableMove:(AIRGoogleMap*) map {
+    int startNum = 0;
+    for (int i = startNum; i < map.markers.count; i++ ) {
+        AIRGoogleMapMarker *marker = map.markers[i];
+        if([marker.identifier isEqualToString:@"distanceFromPinToBall"]){
+            if(marker.enableMove){
+                return YES;
+            }
+            break;
+        }else if([marker.identifier isEqualToString:[map.getMarkerSelected identifier]]){
+            if(marker.enableMove){
+                return YES;
+            }
+        }
+    }
+    return NO;
+}
+-(BOOL)checkDistancePolyline:(AIRGoogleMap*) map{
+    int startNum = 0;
+    for (int i = startNum; i < map.polylines.count; i++ ) {
+        AIRGoogleMapPolyline *polyline = map.polylines[i];
+        if([polyline.identifier isEqualToString:@"distancePolyline"]){
+            return YES;
+        }
+    }
+    return NO;
+}
 #pragma mark Gesture Recognizer Handlers
 
 - (void)handleMapDrag:(UIPanGestureRecognizer*)recognizer {
   AIRGoogleMap *map = (AIRGoogleMap *)recognizer.view;
   if (!map.onPanDrag) return;
+    if(recognizer.numberOfTouches > 1) return;
+        CGPoint touchPoint = [recognizer locationInView:map];
+        if([self checkDistancePolyline:map]){
+            self.tapAtMarker = YES;
+        }
+        if(recognizer.state == UIGestureRecognizerStateBegan && !self.tapAtMarker){
+            AIRGoogleMapMarker *marker = [map markerAtPoint:touchPoint];
+            if(marker == nil) return;
+            if(![marker.identifier isEqualToString:[map.getMarkerSelected identifier]]) return;
+            self.tapAtMarker = YES;
+        }
+
+    if(!self.tapAtMarker) return;
+        [map setScrollEnabled: NO];
+        touchPoint.y = touchPoint.y - finger;
+        CLLocationCoordinate2D coord = [map.projection coordinateForPoint:touchPoint];
+
+        [self updateMarker:map setCoord:coord];
+        [self updatePolyline:map setCoord:coord];
+
+    NSDictionary * NStouchPoint = [NSDictionary dictionaryWithObject:[NSValue valueWithCGPoint:touchPoint] forKey:@"location"];
+    NSDictionary * NScoord = [NSDictionary dictionaryWithObject:[NSValue valueWithMKCoordinate:coord] forKey:@"location"];
+
+    NSMutableDictionary *cb = [[NSMutableDictionary alloc] init];
+        [cb setObject:map forKey:@"map"];
+        [cb setObject:NScoord forKey:@"coord"];
+        [cb setObject:NStouchPoint forKey:@"touchPoint"];
+
+    [self clearTimeout];
+
+    timeOutTest = [NSTimer scheduledTimerWithTimeInterval:timeout
+                                                 target:self
+                                                 selector:@selector(updatePanDrag:)
+                                               userInfo:cb
+                                                  repeats:NO];
+    
+        if(recognizer.state == UIGestureRecognizerStateEnded){
+            [self clearTimeout];
+            self.tapAtMarker = NO;
+            [map setScrollEnabled: YES];
+            map.onPanDragEnd(@{
+                @"coordinate": @{
+                    @"latitude": @(coord.latitude),
+                    @"longitude": @(coord.longitude),
+                },
+                @"position": @{
+                    @"x": @(touchPoint.x),
+                    @"y": @(touchPoint.y),
+                },
+            });
+        }
+    
+}
+
+- (void)handleMapTap:(UITapGestureRecognizer *)recognizer {
+    AIRGoogleMap *map = (AIRGoogleMap *)recognizer.view;
+    if(recognizer.numberOfTouches > 1) return;
+        CGPoint touchPoint = [recognizer locationInView:map];
+        if([self checkDistancePolyline:map]){
+            self.tapAtMarker = YES;
+        }
+        if(recognizer.state == UIGestureRecognizerStateBegan && !self.tapAtMarker){
+            AIRGoogleMapMarker *marker = [map markerAtPoint:touchPoint];
+            if(marker == nil) return;
+            if(![marker.identifier isEqualToString:[map.getMarkerSelected identifier]]) return;
+            self.tapAtMarker = YES;
+        }
+    if(!self.tapAtMarker) return;
+        [map setScrollEnabled: NO];
+        CLLocationCoordinate2D coord = [map.projection coordinateForPoint:touchPoint];
+        touchPoint.y = touchPoint.y - finger;
+        if(recognizer.state == UIGestureRecognizerStateEnded){
+            self.tapAtMarker = NO;
+            [map setScrollEnabled: YES];
+            map.onPanDragEnd(@{
+                @"coordinate": @{
+                    @"latitude": @(coord.latitude),
+                    @"longitude": @(coord.longitude),
+                },
+                @"position": @{
+                    @"x": @(touchPoint.x),
+                    @"y": @(touchPoint.y),
+                },
+            });
+        }
+    
+}
 
-  CGPoint touchPoint = [recognizer locationInView:map];
-  CLLocationCoordinate2D coord = [map.projection coordinateForPoint:touchPoint];
-  map.onPanDrag(@{
-                  @"coordinate": @{
-                      @"latitude": @(coord.latitude),
-                      @"longitude": @(coord.longitude),
-                      },
-                  @"position": @{
-                      @"x": @(touchPoint.x),
-                      @"y": @(touchPoint.y),
-                      },
-                  });
+- (void)handleMapLongTap:(UITapGestureRecognizer *)recognizer {
+    AIRGoogleMap *map = (AIRGoogleMap *)recognizer.view;
 
+    if (!map.onPanDrag) return;
+    if(recognizer.numberOfTouches > 1) return;
+        CGPoint touchPoint = [recognizer locationInView:map];
+        touchPoint.y = touchPoint.y - finger;
+        if([self checkDistancePolyline:map]){
+            self.tapAtMarker = YES;
+        }
+        if(recognizer.state == UIGestureRecognizerStateBegan && !self.tapAtMarker){
+            AIRGoogleMapMarker *marker = [map markerAtPoint:touchPoint];
+            if(marker == nil) return;
+            if(![marker.identifier isEqualToString:[map.getMarkerSelected identifier]]) return;
+            self.tapAtMarker = YES;
+        }
+    if(!self.tapAtMarker) return;
+        [map setScrollEnabled: NO];
+    
+        CLLocationCoordinate2D coord = [map.projection coordinateForPoint:touchPoint];
+        [self updateMarker:map setCoord:coord];
+        [self updatePolyline:map setCoord:coord];
+        
+    NSDictionary * NStouchPoint = [NSDictionary dictionaryWithObject:[NSValue valueWithCGPoint:touchPoint] forKey:@"location"];
+    NSDictionary * NScoord = [NSDictionary dictionaryWithObject:[NSValue valueWithMKCoordinate:coord] forKey:@"location"];
+
+    NSMutableDictionary *cb = [[NSMutableDictionary alloc] init];
+        [cb setObject:map forKey:@"map"];
+        [cb setObject:NScoord forKey:@"coord"];
+        [cb setObject:NStouchPoint forKey:@"touchPoint"];
+
+    [self clearTimeout];
+
+    timeOutTest = [NSTimer scheduledTimerWithTimeInterval:timeout
+                                                 target:self
+                                                 selector:@selector(updatePanDrag:)
+                                               userInfo:cb
+                                                  repeats:NO];
+        if(recognizer.state == UIGestureRecognizerStateEnded){
+            [self clearTimeout];
+            self.tapAtMarker = NO;
+            [map setScrollEnabled: YES];
+            map.onPanDragEnd(@{
+                @"coordinate": @{
+                    @"latitude": @(coord.latitude),
+                    @"longitude": @(coord.longitude),
+                },
+                @"position": @{
+                    @"x": @(touchPoint.x),
+                    @"y": @(touchPoint.y),
+                },
+            });
+        }
+}
+- (CLLocationCoordinate2D)parseDataCoord:(NSDictionary*)data {
+    double lat = [[data valueForKey:@"latitude"] doubleValue];
+    double lng = [[data valueForKey:@"longitude"] doubleValue];
+    CLLocationCoordinate2D coordinate = CLLocationCoordinate2DMake(lat,lng);
+    return coordinate;
+}
+
+- (void)updatePanDrag:(NSTimer *)timer {
+    NSDictionary *dict = [timer userInfo];
+    AIRGoogleMap *map = [dict objectForKey:@"map"];
+    NSDictionary *nscoord = [dict objectForKey:@"coord"];
+    NSDictionary *nstouchPoint = [dict objectForKey:@"touchPoint"];
+    CGPoint touchPoint = [[nstouchPoint valueForKey:@"location"] CGPointValue];
+    CLLocationCoordinate2D coord = [[nscoord valueForKey:@"location"] MKCoordinateValue];
+    map.onPanDrag(@{
+        @"coordinate": @{
+            @"latitude": @(coord.latitude),
+            @"longitude": @(coord.longitude),
+        },
+        @"position": @{
+            @"x": @(touchPoint.x),
+            @"y": @(touchPoint.y),
+        },
+    });
+}
+
+- (void)clearTimeout {
+    [timeOutTest invalidate];
+    timeOutTest = nil;
 }
 
 @end
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarker.h b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarker.h
index bad400c..5b38882 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarker.h
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarker.h
@@ -39,6 +39,7 @@
 @property (nonatomic, assign) BOOL tappable;
 @property (nonatomic, assign) BOOL tracksViewChanges;
 @property (nonatomic, assign) BOOL tracksInfoWindowChanges;
+@property (nonatomic, assign) BOOL enableMove;
 
 - (void)showCalloutView;
 - (void)hideCalloutView;
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarker.m b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarker.m
index b40577a..beff7c2 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarker.m
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarker.m
@@ -40,6 +40,7 @@ - (instancetype)init
     _realMarker.fakeMarker = self;
     _realMarker.tracksViewChanges = true;
     _realMarker.tracksInfoWindowChanges = false;
+    _realMarker.enableMoveMarker = false;
   }
   return self;
 }
@@ -61,7 +62,6 @@ - (void)layoutSubviews {
 }
 
 - (id)eventFromMarker:(AIRGMSMarker*)marker {
-
   CLLocationCoordinate2D coordinate = marker.position;
   CGPoint position = [self.realMarker.map.projection pointForCoordinate:coordinate];
 
@@ -119,7 +119,6 @@ - (void)redraw {
   if (!_realMarker.iconView) return;
 
   BOOL oldValue = _realMarker.tracksViewChanges;
-
   if (oldValue == YES)
   {
     // Immediate refresh, like right now. Not waiting for next frame.
@@ -213,7 +212,10 @@ - (void)didDragMarker:(AIRGMSMarker *)marker {
 }
 
 - (void)setCoordinate:(CLLocationCoordinate2D)coordinate {
-  _realMarker.position = coordinate;
+    [CATransaction begin];
+    [CATransaction setDisableActions:YES];
+    _realMarker.position = coordinate;
+    [CATransaction commit];
 }
 
 - (CLLocationCoordinate2D)coordinate {
@@ -429,6 +431,20 @@ - (BOOL)tracksInfoWindowChanges {
   return _realMarker.tracksInfoWindowChanges;
 }
 
+- (void)setEnableMove:(BOOL)enableMove {
+    _realMarker.enableMoveMarker = enableMove;
+    AIRGoogleMap *map = (AIRGoogleMap *)self.realMarker.map;
+    if([self.identifier isEqualToString:[map.getMarkerSelected identifier]] && !enableMove){
+        [map resetMarkerSelected];
+    }else if([self.identifier isEqualToString:[map.getMarkerSelected identifier]] && enableMove){
+        [map setSelectedMarker:self.realMarker];
+    }
+}
+
+- (BOOL)enableMove {
+  return _realMarker.enableMoveMarker;
+}
+
 @end
 
 #endif
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarkerManager.m b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarkerManager.m
index 5180be9..c4b5276 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarkerManager.m
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapMarkerManager.m
@@ -52,6 +52,7 @@ - (UIView *)view
 RCT_EXPORT_VIEW_PROPERTY(onDragStart, RCTDirectEventBlock)
 RCT_EXPORT_VIEW_PROPERTY(onDrag, RCTDirectEventBlock)
 RCT_EXPORT_VIEW_PROPERTY(onDragEnd, RCTDirectEventBlock)
+RCT_EXPORT_VIEW_PROPERTY(enableMove, BOOL)
 
 RCT_EXPORT_METHOD(showCallout:(nonnull NSNumber *)reactTag)
 {
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolygon.m b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolygon.m
index d26da77..1682d57 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolygon.m
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolygon.m
@@ -20,6 +20,7 @@ - (instancetype)init
   if (self = [super init]) {
     _didMoveToWindow = false;
     _polygon = [[AIRGMSPolygon alloc] init];
+    _polygon.fillColor = [UIColor clearColor];
   }
 
   return self;
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolyline.h b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolyline.h
index 83d8956..05ea1f7 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolyline.h
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolyline.h
@@ -17,6 +17,7 @@
 @property (nonatomic, weak) RCTBridge *bridge;
 @property (nonatomic, strong) NSString *identifier;
 @property (nonatomic, strong) AIRGMSPolyline *polyline;
+@property (nonatomic, strong) AIRGMSPolyline *polylineDistance;
 @property (nonatomic, strong) NSArray<AIRMapCoordinate *> *coordinates;
 @property (nonatomic, copy) RCTBubblingEventBlock onPress;
 
@@ -29,6 +30,11 @@
 @property (nonatomic, assign) NSString *title;
 @property (nonatomic, assign) int zIndex;
 @property (nonatomic, assign) BOOL tappable;
+@property (nonatomic, assign) AIRGoogleMap *mapTemp;
+@property (nonatomic, assign) BOOL isMarkerCircle;
+
+
+- (void)setCoordinateChange:(CLLocationCoordinate2D) coord  setIndex: (int)index setMap:(AIRGoogleMap*)map isMarkerCircle: (Boolean)markerCircle;
 
 @end
 
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolyline.m b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolyline.m
index 904e2d2..8f122d8 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolyline.m
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolyline.m
@@ -19,30 +19,107 @@ @implementation AIRGoogleMapPolyline
 - (instancetype)init
 {
   if (self = [super init]) {
+    _isMarkerCircle = NO;
     _polyline = [[AIRGMSPolyline alloc] init];
+    _polylineDistance = [[AIRGMSPolyline alloc] init];
   }
   return self;
 }
 
 -(void)setCoordinates:(NSArray<AIRMapCoordinate *> *)coordinates
 {
-  _coordinates = coordinates;
+    if(!_isMarkerCircle){
+        _coordinates = coordinates;
+        _polyline.strokeColor = [UIColor clearColor];
+        _polyline.path = [GMSMutablePath path];
+        GMSMutablePath *path = [GMSMutablePath path];
+        for(int i = 0; i < coordinates.count; i++)
+        {
+            [path addCoordinate:coordinates[i].coordinate];
+        }
+        _polyline.path = path;
+        _polyline.strokeColor = self.strokeColor;
+        _polylineDistance.strokeColor = [UIColor clearColor];
+        _polylineDistance.path = [GMSMutablePath path];
+        [self configureStyleSpansIfNeeded];
+        [self configureStyleSpansIfNeededDistance];
 
-  GMSMutablePath *path = [GMSMutablePath path];
-  for(int i = 0; i < coordinates.count; i++)
-  {
-    [path addCoordinate:coordinates[i].coordinate];
-  }
+    } else {
+        _polylineDistance.strokeColor = self.strokeColor;
+        GMSMutablePath *pathDistance = _polylineDistance.path.count > 0 ?  [_polylineDistance.path mutableCopy] : [GMSMutablePath path];
+        if(pathDistance.count > 0){
+            [pathDistance replaceCoordinateAtIndex:1 withCoordinate:coordinates[2].coordinate];
+        }
+        _polylineDistance.path = pathDistance;
+        [self configureStyleSpansIfNeededDistance];
+    }
 
-  _polyline.path = path;
+}
+
+-(void)setCoordinateChange:(CLLocationCoordinate2D) coord  setIndex: (int)index setMap:(AIRGoogleMap*)map
+    isMarkerCircle: (Boolean)markerCircle
+{
+    _mapTemp = map;
+    GMSMutablePath *path = [_polyline.path mutableCopy];
+    if (index > -1) {
+        if(markerCircle){
+            _polylineDistance.strokeColor = self.strokeColor;
+            _isMarkerCircle = YES;
+            GMSMutablePath *pathDistance = _polylineDistance.path.count > 0 ?  [_polylineDistance.path mutableCopy] : [GMSMutablePath path];
+            CLLocationCoordinate2D coordToPin = [self getStartCoordForLine:coord setEndCoord:[path coordinateAtIndex:index-1] setMap:map];
+            CLLocationCoordinate2D coordLast = pathDistance.count > 0 ? [pathDistance coordinateAtIndex:1]:[path coordinateAtIndex:2];
+            CLLocationCoordinate2D coordToTee = [self getStartCoordForLine:coord setEndCoord:coordLast setMap:map];
+            if(pathDistance.count > 0){
+                [pathDistance replaceCoordinateAtIndex:0 withCoordinate:coordToTee];
+            }else{
+                [pathDistance addCoordinate:coordToTee];
+                [pathDistance addCoordinate:[path coordinateAtIndex:2]];
+            }
+            [path replaceCoordinateAtIndex:(NSUInteger)index withCoordinate:coordToPin];
+            if(path.count > 2){
+                [path removeLastCoordinate];
+            }
+            _polylineDistance.path = pathDistance;
+            [self configureStyleSpansIfNeededDistance];
+        } else {
+            _isMarkerCircle = NO;
+            [path replaceCoordinateAtIndex:[self.identifier intValue] withCoordinate:coord];
+        }
+        _polyline.path = path;
+        [self configureStyleSpansIfNeeded];
+    }
 
-  [self configureStyleSpansIfNeeded];
 }
 
+-(CLLocationCoordinate2D)getStartCoordForLine: (CLLocationCoordinate2D) centerCoord setEndCoord:(CLLocationCoordinate2D) endCoord setMap:(AIRGoogleMap*) map
+{
+    if(map != nil){
+        CGPoint centerPoint = [map.projection pointForCoordinate:centerCoord];
+        CGPoint endPoint = [map.projection pointForCoordinate:endCoord];
+        
+        double xDistance = (endPoint.x - centerPoint.x);
+        double yDistance = (endPoint.y - centerPoint.y);
+        
+        double distanceBetweenPoint = sqrt(pow(xDistance, 2) + pow(yDistance, 2));
+
+        int radiusMarker = 42;
+        double factor = distanceBetweenPoint / radiusMarker;
+        CGPoint newPoint = CGPointZero;
+        newPoint.x = (int) (centerPoint.x + (xDistance / factor));
+        newPoint.y = (int) (centerPoint.y + (yDistance / factor));
+        
+        return [map.projection coordinateForPoint:newPoint];
+    }
+    return centerCoord;
+}
+
+
 -(void)setStrokeColor:(UIColor *)strokeColor
 {
   _strokeColor = strokeColor;
   _polyline.strokeColor = strokeColor;
+  _polylineDistance.strokeColor = strokeColor;
+  [self configureStyleSpansIfNeededDistance];
   [self configureStyleSpansIfNeeded];
 }
 
@@ -70,12 +147,14 @@ -(void)setStrokeWidth:(double)strokeWidth
 {
   _strokeWidth = strokeWidth;
   _polyline.strokeWidth = strokeWidth;
+  _polylineDistance.strokeWidth = strokeWidth;
 }
 
 -(void)setFillColor:(UIColor *)fillColor
 {
   _fillColor = fillColor;
   _polyline.spans = @[[GMSStyleSpan spanWithColor:fillColor]];
+  _polylineDistance.spans = @[[GMSStyleSpan spanWithColor:fillColor]];
 }
 
 - (void)setLineDashPattern:(NSArray<NSNumber *> *)lineDashPattern {
@@ -99,6 +178,7 @@ -(void) setZIndex:(int)zIndex
 {
   _zIndex = zIndex;
   _polyline.zIndex = zIndex;
+  _polylineDistance.zIndex = zIndex;
 }
 
 -(void)setTappable:(BOOL)tappable
@@ -107,6 +187,7 @@ -(void)setTappable:(BOOL)tappable
   _polyline.tappable = tappable;
 }
 
+
 - (void)setOnPress:(RCTBubblingEventBlock)onPress {
   _polyline.onPress = onPress;
 }
@@ -130,6 +211,25 @@ - (void)configureStyleSpansIfNeeded {
   _polyline.spans = GMSStyleSpans(_polyline.path, styles, _lineDashPattern, kGMSLengthRhumb);
 }
 
+- (void)configureStyleSpansIfNeededDistance {
+  if (!_strokeColor || !_lineDashPattern || !_polylineDistance.path) {
+      return;
+  }
+
+  BOOL isLine = YES;
+  NSMutableArray *styles = [[NSMutableArray alloc] init];
+  for (NSInteger i = 0; i < _lineDashPattern.count; i++) {
+    if (isLine) {
+      [styles addObject:[GMSStrokeStyle solidColor:_strokeColor]];
+    } else {
+      [styles addObject:[GMSStrokeStyle solidColor:[UIColor clearColor]]];
+    }
+    isLine = !isLine;
+  }
+
+    _polylineDistance.spans = GMSStyleSpans(_polylineDistance.path, styles, _lineDashPattern, kGMSLengthRhumb);
+}
+
 @end
 
 #endif
diff --git a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolylineManager.m b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolylineManager.m
index db41dfa..9b823df 100644
--- a/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolylineManager.m
+++ b/node_modules/react-native-maps/ios/AirGoogleMaps/AIRGoogleMapPolylineManager.m
@@ -42,6 +42,7 @@ - (UIView *)view
 RCT_EXPORT_VIEW_PROPERTY(zIndex, int)
 RCT_EXPORT_VIEW_PROPERTY(tappable, BOOL)
 RCT_EXPORT_VIEW_PROPERTY(onPress, RCTBubblingEventBlock)
+RCT_EXPORT_VIEW_PROPERTY(identifier, NSString)
 
 @end
 
diff --git a/node_modules/react-native-maps/lib/MapView.d.ts b/node_modules/react-native-maps/lib/MapView.d.ts
index c93b63e..f29677f 100644
--- a/node_modules/react-native-maps/lib/MapView.d.ts
+++ b/node_modules/react-native-maps/lib/MapView.d.ts
@@ -1,7 +1,7 @@
 import * as React from 'react';
 import { Animated as RNAnimated, Animated, NativeSyntheticEvent, ViewProps } from 'react-native';
 import { CalloutPressEvent, ClickEvent, Frame, LatLng, MarkerDeselectEvent, MarkerDragEvent, MarkerDragStartEndEvent, MarkerPressEvent, MarkerSelectEvent, Point, Provider, Region } from './sharedTypes';
-import { Address, BoundingBox, Camera, ChangeEvent, Details, EdgePadding, FitToOptions, IndoorBuildingEvent, IndoorLevelActivatedEvent, KmlMapEvent, LongPressEvent, MapPressEvent, MapStyleElement, MapType, MapTypes, PanDragEvent, PoiClickEvent, SnapshotOptions, UserLocationChangeEvent } from './MapView.types';
+import { Address, BoundingBox, Camera, ChangeEvent, DataToZoom, Details, EdgePadding, FitToOptions, IndoorBuildingEvent, IndoorLevelActivatedEvent, KmlMapEvent, LongPressEvent, MapPressEvent, MapStyleElement, MapType, MapTypes, PanDragEvent, PoiClickEvent, SnapshotOptions, UserLocationChangeEvent } from './MapView.types';
 import { Modify } from './sharedTypesInternal';
 import { MapViewNativeComponentType } from './MapViewNativeComponent';
 export declare const MAP_TYPES: MapTypes;
@@ -589,6 +589,7 @@ declare class MapView extends React.Component<MapViewProps, State> {
     fitToElements(options?: FitToOptions): void;
     fitToSuppliedMarkers(markers: string[], options?: FitToOptions): void;
     fitToCoordinates(coordinates?: LatLng[], options?: FitToOptions): void;
+
     /**
      * Get visible boudaries
      *
@@ -597,6 +598,9 @@ declare class MapView extends React.Component<MapViewProps, State> {
     getMapBoundaries(): Promise<BoundingBox>;
     setMapBoundaries(northEast: LatLng, southWest: LatLng): void;
     setIndoorActiveLevelIndex(activeLevelIndex: number): void;
+    setPolygonToZoom(data: DataToZoom): void;
+    resetMarkerSelected(): void;
+    setTapMarker(id: String): void;
     /**
      * Takes a snapshot of the map and saves it to a picture
      * file or returns the image as a base64 encoded string.
diff --git a/node_modules/react-native-maps/lib/MapView.js b/node_modules/react-native-maps/lib/MapView.js
index dcc81c3..00e25a2 100644
--- a/node_modules/react-native-maps/lib/MapView.js
+++ b/node_modules/react-native-maps/lib/MapView.js
@@ -145,6 +145,21 @@ class MapView extends React.Component {
             MapViewNativeComponent_1.Commands.setIndoorActiveLevelIndex(this.map.current, activeLevelIndex);
         }
     }
+    setPolygonToZoom(data) {
+        if (this.map.current) {
+            MapViewNativeComponent_1.Commands.setPolygonToZoom(this.map.current, data);
+        }
+    }
+    resetMarkerSelected() {
+        if (this.map.current) {
+            MapViewNativeComponent_1.Commands.resetMarkerSelected(this.map.current);
+        }
+    }
+    setTapMarker(id) {
+        if (this.map.current) {
+            MapViewNativeComponent_1.Commands.setTapMarker(this.map.current, id);
+        }
+    }
     /**
      * Takes a snapshot of the map and saves it to a picture
      * file or returns the image as a base64 encoded string.
diff --git a/node_modules/react-native-maps/lib/MapView.types.d.ts b/node_modules/react-native-maps/lib/MapView.types.d.ts
index 4a55a66..9a8b217 100644
--- a/node_modules/react-native-maps/lib/MapView.types.d.ts
+++ b/node_modules/react-native-maps/lib/MapView.types.d.ts
@@ -23,6 +23,7 @@ export declare type EdgePadding = {
     right: Number;
     bottom: Number;
     left: Number;
+    isDefault?: boolean;
 };
 export declare type MapType = 'hybrid' | 'mutedStandard' | 'none' | 'satellite' | 'standard' | 'terrain';
 export declare type MapTypes = {
@@ -150,4 +151,13 @@ export declare type Address = {
     subLocality: string;
     thoroughfare: string;
 };
-export declare type NativeCommandName = 'animateCamera' | 'animateToRegion' | 'coordinateForPoint' | 'fitToCoordinates' | 'fitToElements' | 'fitToSuppliedMarkers' | 'getAddressFromCoordinates' | 'getCamera' | 'getMapBoundaries' | 'getMarkersFrames' | 'pointForCoordinate' | 'setCamera' | 'setIndoorActiveLevelIndex' | 'setMapBoundaries' | 'takeSnapshot';
+export declare type DataToZoom = {
+    heading: number,
+    pitch: number,
+    northEast: LatLng,
+    southWest: LatLng,
+    center: LatLng,
+    polygons: LatLng[],
+    centerH: LatLng,
+};
+export declare type NativeCommandName = 'animateCamera' | 'animateToRegion' | 'coordinateForPoint' | 'fitToCoordinates' | 'fitToElements' | 'fitToSuppliedMarkers' | 'getAddressFromCoordinates' | 'getCamera' | 'getMapBoundaries' | 'getMarkersFrames' | 'pointForCoordinate' | 'setCamera' | 'setIndoorActiveLevelIndex' | 'setMapBoundaries' | 'takeSnapshot' | 'setPolygonToZoom' | 'resetMarkerSelected' | 'setTapMarker';
diff --git a/node_modules/react-native-maps/lib/MapViewNativeComponent.d.ts b/node_modules/react-native-maps/lib/MapViewNativeComponent.d.ts
index a9caecb..98610e8 100644
--- a/node_modules/react-native-maps/lib/MapViewNativeComponent.d.ts
+++ b/node_modules/react-native-maps/lib/MapViewNativeComponent.d.ts
@@ -1,7 +1,7 @@
 /// <reference types="react" />
 import type { HostComponent } from 'react-native';
 import { NativeProps } from './MapView';
-import { Camera, EdgePadding } from './MapView.types';
+import { Camera, EdgePadding, DataToZoom } from './MapView.types';
 import { LatLng, Region } from './sharedTypes';
 export declare type MapViewNativeComponentType = HostComponent<NativeProps>;
 interface NativeCommands {
@@ -13,6 +13,9 @@ interface NativeCommands {
     fitToCoordinates: (viewRef: NonNullable<React.RefObject<MapViewNativeComponentType>['current']>, coordinates: LatLng[], edgePadding: EdgePadding, animated: boolean) => void;
     setMapBoundaries: (viewRef: NonNullable<React.RefObject<MapViewNativeComponentType>['current']>, northEast: LatLng, southWest: LatLng) => void;
     setIndoorActiveLevelIndex: (viewRef: NonNullable<React.RefObject<MapViewNativeComponentType>['current']>, activeLevelIndex: number) => void;
+    setPolygonToZoom: (viewRef: NonNullable<React.RefObject<MapViewNativeComponentType>['current']>, data: DataToZoom) => void;
+    resetMarkerSelected: (viewRef: NonNullable<React.RefObject<MapViewNativeComponentType>['current']>) => void;
+
 }
 export declare const Commands: NativeCommands;
 export {};
diff --git a/node_modules/react-native-maps/lib/MapViewNativeComponent.js b/node_modules/react-native-maps/lib/MapViewNativeComponent.js
index db158b2..8d3cd83 100644
--- a/node_modules/react-native-maps/lib/MapViewNativeComponent.js
+++ b/node_modules/react-native-maps/lib/MapViewNativeComponent.js
@@ -15,5 +15,8 @@ exports.Commands = (0, codegenNativeCommands_1.default)({
         'fitToCoordinates',
         'setMapBoundaries',
         'setIndoorActiveLevelIndex',
+        'setPolygonToZoom',
+        'resetMarkerSelected',
+        'setTapMarker',
     ],
 });
