diff --git a/node_modules/expo-constants/android/build.gradle b/node_modules/expo-constants/android/build.gradle
index fcc166a..bbffda4 100644
--- a/node_modules/expo-constants/android/build.gradle
+++ b/node_modules/expo-constants/android/build.gradle
@@ -1,6 +1,6 @@
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
-apply plugin: 'maven'
+apply plugin: 'maven-publish'
 
 group = 'host.exp.exponent'
 version = '11.0.2'
@@ -36,15 +36,6 @@ artifacts {
   archives androidSourcesJar
 }
 
-uploadArchives {
-  repositories {
-    mavenDeployer {
-      configuration = configurations.deployerJars
-      repository(url: mavenLocal().url)
-    }
-  }
-}
-
 android {
   compileSdkVersion safeExtGet("compileSdkVersion", 30)
 
diff --git a/node_modules/expo-constants/android/build/.transforms/2ce6a64dd93dd8f511f18f30c3179a32/results.bin b/node_modules/expo-constants/android/build/.transforms/2ce6a64dd93dd8f511f18f30c3179a32/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/2ce6a64dd93dd8f511f18f30c3179a32/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-constants/android/build/.transforms/2ce6a64dd93dd8f511f18f30c3179a32/transformed/classes/classes.dex b/node_modules/expo-constants/android/build/.transforms/2ce6a64dd93dd8f511f18f30c3179a32/transformed/classes/classes.dex
new file mode 100644
index 0000000..7da7f72
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/2ce6a64dd93dd8f511f18f30c3179a32/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/results.bin b/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/AndroidManifest.xml b/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..2c379b9
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.constants" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/R.txt b/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/jars/classes.jar b/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..6b6d2fb
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/70db84d731ee01d71082ae537bf89ba0/transformed/out/jars/classes.jar differ
diff --git a/node_modules/expo-constants/android/build/.transforms/732646db9363a82fbc207570ae9cb366/results.bin b/node_modules/expo-constants/android/build/.transforms/732646db9363a82fbc207570ae9cb366/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/732646db9363a82fbc207570ae9cb366/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/results.bin b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/results.bin
new file mode 100644
index 0000000..9e53e72
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/results.bin
@@ -0,0 +1 @@
+o/release
diff --git a/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/desugar_graph.bin b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/desugar_graph.bin differ
diff --git a/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/BuildConfig.dex b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/BuildConfig.dex
new file mode 100644
index 0000000..87337e9
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/BuildConfig.dex differ
diff --git a/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsModule.dex b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsModule.dex
new file mode 100644
index 0000000..36015a6
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsModule.dex differ
diff --git a/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsPackage.dex b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsPackage.dex
new file mode 100644
index 0000000..4c25a34
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsPackage.dex differ
diff --git a/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsService$ExecutionEnvironment.dex b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsService$ExecutionEnvironment.dex
new file mode 100644
index 0000000..2f86caa
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsService$ExecutionEnvironment.dex differ
diff --git a/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsService.dex b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsService.dex
new file mode 100644
index 0000000..0729d14
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ConstantsService.dex differ
diff --git a/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ExponentInstallationId.dex b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ExponentInstallationId.dex
new file mode 100644
index 0000000..044c849
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/81552540360761acb7a5c81a167986d1/transformed/release/expo/modules/constants/ExponentInstallationId.dex differ
diff --git a/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/results.bin b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/AndroidManifest.xml b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..2c379b9
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.constants" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..d8f70c0
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,3 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
diff --git a/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
new file mode 100644
index 0000000..6ee9e16
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=expo-constants
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/R.txt b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/jars/classes.jar b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..6b6d2fb
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/d3061665289552cd06b03396097538bc/transformed/out/jars/classes.jar differ
diff --git a/node_modules/expo-constants/android/build/.transforms/e91614c7ccfd3662de8402337038e4ae/results.bin b/node_modules/expo-constants/android/build/.transforms/e91614c7ccfd3662de8402337038e4ae/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-constants/android/build/.transforms/e91614c7ccfd3662de8402337038e4ae/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-constants/android/build/.transforms/e91614c7ccfd3662de8402337038e4ae/transformed/classes/classes.dex b/node_modules/expo-constants/android/build/.transforms/e91614c7ccfd3662de8402337038e4ae/transformed/classes/classes.dex
new file mode 100644
index 0000000..b332ce6
Binary files /dev/null and b/node_modules/expo-constants/android/build/.transforms/e91614c7ccfd3662de8402337038e4ae/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-constants/android/build/generated/source/buildConfig/release/expo/modules/constants/BuildConfig.java b/node_modules/expo-constants/android/build/generated/source/buildConfig/release/expo/modules/constants/BuildConfig.java
new file mode 100644
index 0000000..a934724
--- /dev/null
+++ b/node_modules/expo-constants/android/build/generated/source/buildConfig/release/expo/modules/constants/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package expo.modules.constants;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "expo.modules.constants";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/expo-constants/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/expo-constants/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..2c379b9
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.constants" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/expo-constants/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..aabd9b7
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules.constants",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/aar_main_jar/release/classes.jar b/node_modules/expo-constants/android/build/intermediates/aar_main_jar/release/classes.jar
new file mode 100644
index 0000000..6b6d2fb
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/aar_main_jar/release/classes.jar differ
diff --git a/node_modules/expo-constants/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/expo-constants/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-constants/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/expo-constants/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/annotations_typedef_file/release/typedefs.txt b/node_modules/expo-constants/android/build/intermediates/annotations_typedef_file/release/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-constants/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/expo-constants/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..b2e5c9a
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/compile_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/expo-constants/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/expo-constants/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..b5b1d04
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/compile_r_class_jar/release/R.jar differ
diff --git a/node_modules/expo-constants/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/expo-constants/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-constants/android/build/intermediates/full_jar/release/full.jar b/node_modules/expo-constants/android/build/intermediates/full_jar/release/full.jar
new file mode 100644
index 0000000..f9aaf90
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/full_jar/release/full.jar differ
diff --git a/node_modules/expo-constants/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/expo-constants/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..0932a91
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/expo-constants/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..85d98f7
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/expo-constants/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..1b67903
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/build/intermediates/shader_assets/release/out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/release/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties b/node_modules/expo-constants/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..df2f5cc
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:15:40 ICT 2022
diff --git a/node_modules/expo-constants/android/build/intermediates/incremental/packageReleaseResources/merger.xml b/node_modules/expo-constants/android/build/intermediates/incremental/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..93e647c
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/incremental/packageReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/release/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/incremental/release-mergeJavaRes/merge-state b/node_modules/expo-constants/android/build/intermediates/incremental/release-mergeJavaRes/merge-state
new file mode 100644
index 0000000..c7d1fc0
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/incremental/release-mergeJavaRes/merge-state differ
diff --git a/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/BuildConfig.class b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/BuildConfig.class
new file mode 100644
index 0000000..d39b514
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/BuildConfig.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsModule.class b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsModule.class
new file mode 100644
index 0000000..6b0fd5c
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsModule.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsPackage.class b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsPackage.class
new file mode 100644
index 0000000..5bbcc8e
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsPackage.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsService$ExecutionEnvironment.class b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsService$ExecutionEnvironment.class
new file mode 100644
index 0000000..1e0c76a
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsService$ExecutionEnvironment.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsService.class b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsService.class
new file mode 100644
index 0000000..f122912
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ConstantsService.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ExponentInstallationId.class b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ExponentInstallationId.class
new file mode 100644
index 0000000..3a54828
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/javac/release/classes/expo/modules/constants/ExponentInstallationId.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties b/node_modules/expo-constants/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
new file mode 100644
index 0000000..6ee9e16
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=expo-constants
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/local_aar_for_lint/release/out.aar b/node_modules/expo-constants/android/build/intermediates/local_aar_for_lint/release/out.aar
new file mode 100644
index 0000000..d4b9634
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/local_aar_for_lint/release/out.aar differ
diff --git a/node_modules/expo-constants/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/expo-constants/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/expo-constants/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/expo-constants/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..8e51199
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="expo.modules.constants" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/expo-constants/android/build/intermediates/merged_java_res/release/feature-expo-constants.jar b/node_modules/expo-constants/android/build/intermediates/merged_java_res/release/feature-expo-constants.jar
new file mode 100644
index 0000000..15cb0ec
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/merged_java_res/release/feature-expo-constants.jar differ
diff --git a/node_modules/expo-constants/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/expo-constants/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..2c379b9
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.constants" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/expo-constants/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/packaged_manifests/release/output-metadata.json b/node_modules/expo-constants/android/build/intermediates/packaged_manifests/release/output-metadata.json
new file mode 100644
index 0000000..eda9ff8
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/packaged_manifests/release/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules.constants",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/release/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/BuildConfig.class b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/BuildConfig.class
new file mode 100644
index 0000000..d39b514
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/BuildConfig.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsModule.class b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsModule.class
new file mode 100644
index 0000000..6b0fd5c
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsModule.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsPackage.class b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsPackage.class
new file mode 100644
index 0000000..5bbcc8e
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsPackage.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsService$ExecutionEnvironment.class b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsService$ExecutionEnvironment.class
new file mode 100644
index 0000000..1e0c76a
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsService$ExecutionEnvironment.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsService.class b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsService.class
new file mode 100644
index 0000000..f122912
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ConstantsService.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ExponentInstallationId.class b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ExponentInstallationId.class
new file mode 100644
index 0000000..3a54828
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/constants/ExponentInstallationId.class differ
diff --git a/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..f9aaf90
Binary files /dev/null and b/node_modules/expo-constants/android/build/intermediates/runtime_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/expo-constants/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/expo-constants/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..010a114
--- /dev/null
+++ b/node_modules/expo-constants/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1 @@
+expo.modules.constants
diff --git a/node_modules/expo-constants/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/expo-constants/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..a0b18b1
--- /dev/null
+++ b/node_modules/expo-constants/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml:2:1-4:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml:2:11-43
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml:2:1-4:12
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-constants/android/src/main/AndroidManifest.xml
diff --git a/node_modules/expo-constants/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin b/node_modules/expo-constants/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..c5d3aa5
Binary files /dev/null and b/node_modules/expo-constants/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/expo-constants/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt b/node_modules/expo-constants/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
new file mode 100644
index 0000000..5cbf69b
--- /dev/null
+++ b/node_modules/expo-constants/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
@@ -0,0 +1,11 @@
+expo/modules/constants/BuildConfig.java
+ expo.modules.constants.BuildConfig
+expo/modules/constants/ConstantsService.java
+ expo.modules.constants.ConstantsService
+ expo.modules.constants.ConstantsService$ExecutionEnvironment
+expo/modules/constants/ConstantsPackage.java
+ expo.modules.constants.ConstantsPackage
+expo/modules/constants/ConstantsModule.java
+ expo.modules.constants.ConstantsModule
+expo/modules/constants/ExponentInstallationId.java
+ expo.modules.constants.ExponentInstallationId
