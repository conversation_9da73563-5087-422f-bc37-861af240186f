diff --git a/node_modules/unimodules-app-loader/android/build.gradle b/node_modules/unimodules-app-loader/android/build.gradle
index 4a83d0e..61015d9 100644
--- a/node_modules/unimodules-app-loader/android/build.gradle
+++ b/node_modules/unimodules-app-loader/android/build.gradle
@@ -1,6 +1,6 @@
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
-apply plugin: 'maven'
+apply plugin: 'maven-publish'
 
 group = 'host.exp.exponent'
 version = '2.2.0'
@@ -36,15 +36,6 @@ artifacts {
   archives androidSourcesJar
 }
 
-uploadArchives {
-  repositories {
-    mavenDeployer {
-      configuration = configurations.deployerJars
-      repository(url: mavenLocal().url)
-    }
-  }
-}
-
 android {
   compileSdkVersion safeExtGet("compileSdkVersion", 30)
 
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/results.bin b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/results.bin
new file mode 100644
index 0000000..9e53e72
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/results.bin
@@ -0,0 +1 @@
+o/release
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/desugar_graph.bin b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/desugar_graph.bin differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/AppLoaderPackagesProviderInterface.dex b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/AppLoaderPackagesProviderInterface.dex
new file mode 100644
index 0000000..df9f7a6
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/AppLoaderPackagesProviderInterface.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/AppLoaderProvider$Callback.dex b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/AppLoaderProvider$Callback.dex
new file mode 100644
index 0000000..afdfd7e
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/AppLoaderProvider$Callback.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/AppLoaderProvider.dex b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/AppLoaderProvider.dex
new file mode 100644
index 0000000..df956bf
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/AppLoaderProvider.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/BuildConfig.dex b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/BuildConfig.dex
new file mode 100644
index 0000000..8b6bb37
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/BuildConfig.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.dex b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.dex
new file mode 100644
index 0000000..d772594
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/HeadlessAppLoader$Params.dex b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/HeadlessAppLoader$Params.dex
new file mode 100644
index 0000000..f809696
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/HeadlessAppLoader$Params.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/HeadlessAppLoader.dex b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/HeadlessAppLoader.dex
new file mode 100644
index 0000000..76b0afd
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/54a7fd638656764a9265567fdcfb3fc9/transformed/release/org/unimodules/apploader/HeadlessAppLoader.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/6d9ee08a9b10ba98a9094798f71e065e/results.bin b/node_modules/unimodules-app-loader/android/build/.transforms/6d9ee08a9b10ba98a9094798f71e065e/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/6d9ee08a9b10ba98a9094798f71e065e/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/results.bin b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/AndroidManifest.xml b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..4a66f7d
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.apploader" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..d8f70c0
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,3 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
new file mode 100644
index 0000000..b4f9e9c
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=unimodules-app-loader
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/R.txt b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/jars/classes.jar b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..20ca432
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/816c7a5c6ca2b5bc3a6378dd61fb05b5/transformed/out/jars/classes.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/9a91cd0f861988e2442430687e625894/results.bin b/node_modules/unimodules-app-loader/android/build/.transforms/9a91cd0f861988e2442430687e625894/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/9a91cd0f861988e2442430687e625894/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/9a91cd0f861988e2442430687e625894/transformed/classes/classes.dex b/node_modules/unimodules-app-loader/android/build/.transforms/9a91cd0f861988e2442430687e625894/transformed/classes/classes.dex
new file mode 100644
index 0000000..6891833
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/9a91cd0f861988e2442430687e625894/transformed/classes/classes.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/results.bin b/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/AndroidManifest.xml b/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..4a66f7d
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.apploader" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/R.txt b/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/jars/classes.jar b/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..20ca432
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/a0d99f0f7dba96bad76168c3fc690088/transformed/out/jars/classes.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/e7b766f4ee52d148f222a6bdd5f43a12/results.bin b/node_modules/unimodules-app-loader/android/build/.transforms/e7b766f4ee52d148f222a6bdd5f43a12/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/e7b766f4ee52d148f222a6bdd5f43a12/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/e7b766f4ee52d148f222a6bdd5f43a12/transformed/classes/classes.dex b/node_modules/unimodules-app-loader/android/build/.transforms/e7b766f4ee52d148f222a6bdd5f43a12/transformed/classes/classes.dex
new file mode 100644
index 0000000..4849001
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/e7b766f4ee52d148f222a6bdd5f43a12/transformed/classes/classes.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/f9c1ae84ad5b82836a48a86297bc83fd/results.bin b/node_modules/unimodules-app-loader/android/build/.transforms/f9c1ae84ad5b82836a48a86297bc83fd/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/f9c1ae84ad5b82836a48a86297bc83fd/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/f9c1ae84ad5b82836a48a86297bc83fd/transformed/classes/classes.dex b/node_modules/unimodules-app-loader/android/build/.transforms/f9c1ae84ad5b82836a48a86297bc83fd/transformed/classes/classes.dex
new file mode 100644
index 0000000..4849001
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/f9c1ae84ad5b82836a48a86297bc83fd/transformed/classes/classes.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/ff95efc1e61defa83695d39c1c046233/results.bin b/node_modules/unimodules-app-loader/android/build/.transforms/ff95efc1e61defa83695d39c1c046233/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/.transforms/ff95efc1e61defa83695d39c1c046233/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/unimodules-app-loader/android/build/.transforms/ff95efc1e61defa83695d39c1c046233/transformed/classes/classes.dex b/node_modules/unimodules-app-loader/android/build/.transforms/ff95efc1e61defa83695d39c1c046233/transformed/classes/classes.dex
new file mode 100644
index 0000000..38680e1
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/.transforms/ff95efc1e61defa83695d39c1c046233/transformed/classes/classes.dex differ
diff --git a/node_modules/unimodules-app-loader/android/build/generated/source/buildConfig/debug/org/unimodules/apploader/BuildConfig.java b/node_modules/unimodules-app-loader/android/build/generated/source/buildConfig/debug/org/unimodules/apploader/BuildConfig.java
new file mode 100644
index 0000000..65a16ac
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/generated/source/buildConfig/debug/org/unimodules/apploader/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package org.unimodules.apploader;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "org.unimodules.apploader";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/unimodules-app-loader/android/build/generated/source/buildConfig/release/org/unimodules/apploader/BuildConfig.java b/node_modules/unimodules-app-loader/android/build/generated/source/buildConfig/release/org/unimodules/apploader/BuildConfig.java
new file mode 100644
index 0000000..311206e
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/generated/source/buildConfig/release/org/unimodules/apploader/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package org.unimodules.apploader;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "org.unimodules.apploader";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..4a66f7d
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.apploader" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..187e5d2
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.apploader",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..4a66f7d
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.apploader" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..1042e45
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.apploader",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/aar_main_jar/release/classes.jar b/node_modules/unimodules-app-loader/android/build/intermediates/aar_main_jar/release/classes.jar
new file mode 100644
index 0000000..20ca432
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/aar_main_jar/release/classes.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/unimodules-app-loader/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/unimodules-app-loader/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/unimodules-app-loader/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/unimodules-app-loader/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/annotations_typedef_file/release/typedefs.txt b/node_modules/unimodules-app-loader/android/build/intermediates/annotations_typedef_file/release/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/unimodules-app-loader/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..f151d73
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/unimodules-app-loader/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..27f18b9
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/compile_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/unimodules-app-loader/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..85fbeb4
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/unimodules-app-loader/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..85fbeb4
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/compile_r_class_jar/release/R.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/unimodules-app-loader/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/unimodules-app-loader/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/full_jar/release/full.jar b/node_modules/unimodules-app-loader/android/build/intermediates/full_jar/release/full.jar
new file mode 100644
index 0000000..061609e
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/full_jar/release/full.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..64ced83
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..bade265
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..2037a8e
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..5bf42e9
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..1729844
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/intermediates/shader_assets/debug/out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/debug/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..d5aeeb6
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:34:33 ICT 2022
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageDebugResources/merger.xml b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageDebugResources/merger.xml
new file mode 100644
index 0000000..638eb9f
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/debug/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..8d13733
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/intermediates/shader_assets/release/out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/release/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..df2f5cc
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:15:40 ICT 2022
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageReleaseResources/merger.xml b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..7a54ae8
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/packageReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/release/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/incremental/release-mergeJavaRes/merge-state b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/release-mergeJavaRes/merge-state
new file mode 100644
index 0000000..a6b66f4
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/incremental/release-mergeJavaRes/merge-state differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/AppLoaderPackagesProviderInterface.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/AppLoaderPackagesProviderInterface.class
new file mode 100644
index 0000000..c1586ed
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/AppLoaderPackagesProviderInterface.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/AppLoaderProvider$Callback.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/AppLoaderProvider$Callback.class
new file mode 100644
index 0000000..5430891
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/AppLoaderProvider$Callback.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/AppLoaderProvider.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/AppLoaderProvider.class
new file mode 100644
index 0000000..642215c
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/AppLoaderProvider.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/BuildConfig.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/BuildConfig.class
new file mode 100644
index 0000000..7b5fed1
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/BuildConfig.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.class
new file mode 100644
index 0000000..7b35819
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/HeadlessAppLoader$Params.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/HeadlessAppLoader$Params.class
new file mode 100644
index 0000000..165334c
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/HeadlessAppLoader$Params.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/HeadlessAppLoader.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/HeadlessAppLoader.class
new file mode 100644
index 0000000..c136120
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/debug/classes/org/unimodules/apploader/HeadlessAppLoader.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/AppLoaderPackagesProviderInterface.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/AppLoaderPackagesProviderInterface.class
new file mode 100644
index 0000000..c1586ed
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/AppLoaderPackagesProviderInterface.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/AppLoaderProvider$Callback.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/AppLoaderProvider$Callback.class
new file mode 100644
index 0000000..5430891
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/AppLoaderProvider$Callback.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/AppLoaderProvider.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/AppLoaderProvider.class
new file mode 100644
index 0000000..642215c
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/AppLoaderProvider.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/BuildConfig.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/BuildConfig.class
new file mode 100644
index 0000000..24406a8
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/BuildConfig.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.class
new file mode 100644
index 0000000..7b35819
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/HeadlessAppLoader$Params.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/HeadlessAppLoader$Params.class
new file mode 100644
index 0000000..165334c
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/HeadlessAppLoader$Params.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/HeadlessAppLoader.class b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/HeadlessAppLoader.class
new file mode 100644
index 0000000..c136120
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/javac/release/classes/org/unimodules/apploader/HeadlessAppLoader.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties b/node_modules/unimodules-app-loader/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
new file mode 100644
index 0000000..b4f9e9c
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=unimodules-app-loader
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/local_aar_for_lint/release/out.aar b/node_modules/unimodules-app-loader/android/build/intermediates/local_aar_for_lint/release/out.aar
new file mode 100644
index 0000000..8c4f96c
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/local_aar_for_lint/release/out.aar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/unimodules-app-loader/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/unimodules-app-loader/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/unimodules-app-loader/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..ba0b091
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="org.unimodules.apploader" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/unimodules-app-loader/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..ba0b091
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="org.unimodules.apploader" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/merged_java_res/release/feature-unimodules-app-loader.jar b/node_modules/unimodules-app-loader/android/build/intermediates/merged_java_res/release/feature-unimodules-app-loader.jar
new file mode 100644
index 0000000..15cb0ec
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/merged_java_res/release/feature-unimodules-app-loader.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/unimodules-app-loader/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..4a66f7d
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.apploader" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/unimodules-app-loader/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..4a66f7d
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.apploader" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/unimodules-app-loader/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/unimodules-app-loader/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/unimodules-app-loader/android/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..52eb43a
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.apploader",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/packaged_manifests/release/output-metadata.json b/node_modules/unimodules-app-loader/android/build/intermediates/packaged_manifests/release/output-metadata.json
new file mode 100644
index 0000000..470edd1
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/packaged_manifests/release/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.apploader",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/release/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/AppLoaderPackagesProviderInterface.class b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/AppLoaderPackagesProviderInterface.class
new file mode 100644
index 0000000..6f7b636
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/AppLoaderPackagesProviderInterface.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/AppLoaderProvider$Callback.class b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/AppLoaderProvider$Callback.class
new file mode 100644
index 0000000..2fbee05
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/AppLoaderProvider$Callback.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/AppLoaderProvider.class b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/AppLoaderProvider.class
new file mode 100644
index 0000000..e30c42e
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/AppLoaderProvider.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/BuildConfig.class b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/BuildConfig.class
new file mode 100644
index 0000000..4493e59
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/BuildConfig.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.class b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.class
new file mode 100644
index 0000000..a5e681a
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/HeadlessAppLoader$AppConfigurationError.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/HeadlessAppLoader$Params.class b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/HeadlessAppLoader$Params.class
new file mode 100644
index 0000000..c3fed79
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/HeadlessAppLoader$Params.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/HeadlessAppLoader.class b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/HeadlessAppLoader.class
new file mode 100644
index 0000000..951e64e
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/apploader/HeadlessAppLoader.class differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..efe299e
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..0b81622
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/intermediates/runtime_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/unimodules-app-loader/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..05bf500
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1 @@
+org.unimodules.apploader
diff --git a/node_modules/unimodules-app-loader/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/unimodules-app-loader/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..05bf500
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1 @@
+org.unimodules.apploader
diff --git a/node_modules/unimodules-app-loader/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/unimodules-app-loader/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..c3e0cb7
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:1-57
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:1-57
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:1-57
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:11-45
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:1-57
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
diff --git a/node_modules/unimodules-app-loader/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/unimodules-app-loader/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..c3e0cb7
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:1-57
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:1-57
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:1-57
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:11-45
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml:1:1-57
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-app-loader/android/src/main/AndroidManifest.xml
diff --git a/node_modules/unimodules-app-loader/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/unimodules-app-loader/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..920db4b
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/unimodules-app-loader/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin b/node_modules/unimodules-app-loader/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..79591b8
Binary files /dev/null and b/node_modules/unimodules-app-loader/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/unimodules-app-loader/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt b/node_modules/unimodules-app-loader/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
new file mode 100644
index 0000000..05fbb66
--- /dev/null
+++ b/node_modules/unimodules-app-loader/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
@@ -0,0 +1,11 @@
+org/unimodules/apploader/HeadlessAppLoader.java
+ org.unimodules.apploader.HeadlessAppLoader
+ org.unimodules.apploader.HeadlessAppLoader$AppConfigurationError
+ org.unimodules.apploader.HeadlessAppLoader$Params
+org/unimodules/apploader/BuildConfig.java
+ org.unimodules.apploader.BuildConfig
+org/unimodules/apploader/AppLoaderProvider.java
+ org.unimodules.apploader.AppLoaderProvider
+ org.unimodules.apploader.AppLoaderProvider$Callback
+org/unimodules/apploader/AppLoaderPackagesProviderInterface.java
+ org.unimodules.apploader.AppLoaderPackagesProviderInterface
