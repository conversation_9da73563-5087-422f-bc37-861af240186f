diff --git a/node_modules/unimodules-task-manager-interface/android/build.gradle b/node_modules/unimodules-task-manager-interface/android/build.gradle
index 192739a..305340c 100644
--- a/node_modules/unimodules-task-manager-interface/android/build.gradle
+++ b/node_modules/unimodules-task-manager-interface/android/build.gradle
@@ -1,6 +1,6 @@
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
-apply plugin: 'maven'
+apply plugin: 'maven-publish'
 
 group = 'org.unimodules'
 version = '6.2.0'
@@ -36,15 +36,6 @@ artifacts {
   archives androidSourcesJar
 }
 
-uploadArchives {
-  repositories {
-    mavenDeployer {
-      configuration = configurations.deployerJars
-      repository(url: mavenLocal().url)
-    }
-  }
-}
-
 android {
   compileSdkVersion safeExtGet("compileSdkVersion", 30)
 
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/results.bin b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/AndroidManifest.xml b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..02c55cf
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.interfaces.taskManager" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..d8f70c0
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,3 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
new file mode 100644
index 0000000..51a6697
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=unimodules-task-manager-interface
+mavenGroupId=org.unimodules
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/R.txt b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/jars/classes.jar b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..9bc9058
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59187cc958f457cae4a94d951c4b1bbd/transformed/out/jars/classes.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/59b57a9f811ec99f0f58a9e3eceb900e/results.bin b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59b57a9f811ec99f0f58a9e3eceb900e/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59b57a9f811ec99f0f58a9e3eceb900e/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/59b57a9f811ec99f0f58a9e3eceb900e/transformed/classes/classes.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59b57a9f811ec99f0f58a9e3eceb900e/transformed/classes/classes.dex
new file mode 100644
index 0000000..32b5d7f
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/59b57a9f811ec99f0f58a9e3eceb900e/transformed/classes/classes.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/5b1e96de574b7e953f7d538d560243d2/results.bin b/node_modules/unimodules-task-manager-interface/android/build/.transforms/5b1e96de574b7e953f7d538d560243d2/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/5b1e96de574b7e953f7d538d560243d2/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/5b1e96de574b7e953f7d538d560243d2/transformed/classes/classes.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/5b1e96de574b7e953f7d538d560243d2/transformed/classes/classes.dex
new file mode 100644
index 0000000..32b5d7f
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/5b1e96de574b7e953f7d538d560243d2/transformed/classes/classes.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/abcdc9552a86cec630a27aad1a5917ca/results.bin b/node_modules/unimodules-task-manager-interface/android/build/.transforms/abcdc9552a86cec630a27aad1a5917ca/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/abcdc9552a86cec630a27aad1a5917ca/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/abcdc9552a86cec630a27aad1a5917ca/transformed/classes/classes.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/abcdc9552a86cec630a27aad1a5917ca/transformed/classes/classes.dex
new file mode 100644
index 0000000..1422473
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/abcdc9552a86cec630a27aad1a5917ca/transformed/classes/classes.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/results.bin b/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/AndroidManifest.xml b/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..02c55cf
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.interfaces.taskManager" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/R.txt b/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/jars/classes.jar b/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..9bc9058
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/b40f41a11bd2d6bb639f85b3fd13ead5/transformed/out/jars/classes.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/results.bin b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/results.bin
new file mode 100644
index 0000000..9e53e72
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/results.bin
@@ -0,0 +1 @@
+o/release
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/desugar_graph.bin b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/desugar_graph.bin differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/BuildConfig.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/BuildConfig.dex
new file mode 100644
index 0000000..940cb70
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/BuildConfig.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskConsumer.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskConsumer.dex
new file mode 100644
index 0000000..37380eb
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskConsumer.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskConsumerInterface.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskConsumerInterface.dex
new file mode 100644
index 0000000..6ef02a4
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskConsumerInterface.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskExecutionCallback.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskExecutionCallback.dex
new file mode 100644
index 0000000..1a3bf4d
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskExecutionCallback.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskInterface.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskInterface.dex
new file mode 100644
index 0000000..caa6341
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskInterface.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskManagerInterface.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskManagerInterface.dex
new file mode 100644
index 0000000..1823945
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskManagerInterface.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.dex
new file mode 100644
index 0000000..97f1907
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskServiceInterface.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskServiceInterface.dex
new file mode 100644
index 0000000..b6e0c37
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c4a80e5007f14432ec8f57097c5a25d3/transformed/release/org/unimodules/interfaces/taskManager/TaskServiceInterface.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/c52a620625b93d416d9a9a161c785f2b/results.bin b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c52a620625b93d416d9a9a161c785f2b/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/c52a620625b93d416d9a9a161c785f2b/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/e201aa587edf1f7c64f8369865f6af8b/results.bin b/node_modules/unimodules-task-manager-interface/android/build/.transforms/e201aa587edf1f7c64f8369865f6af8b/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/.transforms/e201aa587edf1f7c64f8369865f6af8b/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/unimodules-task-manager-interface/android/build/.transforms/e201aa587edf1f7c64f8369865f6af8b/transformed/classes/classes.dex b/node_modules/unimodules-task-manager-interface/android/build/.transforms/e201aa587edf1f7c64f8369865f6af8b/transformed/classes/classes.dex
new file mode 100644
index 0000000..0993862
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/.transforms/e201aa587edf1f7c64f8369865f6af8b/transformed/classes/classes.dex differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/generated/source/buildConfig/debug/org/unimodules/interfaces/taskManager/BuildConfig.java b/node_modules/unimodules-task-manager-interface/android/build/generated/source/buildConfig/debug/org/unimodules/interfaces/taskManager/BuildConfig.java
new file mode 100644
index 0000000..43f5911
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/generated/source/buildConfig/debug/org/unimodules/interfaces/taskManager/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package org.unimodules.interfaces.taskManager;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "org.unimodules.interfaces.taskManager";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/unimodules-task-manager-interface/android/build/generated/source/buildConfig/release/org/unimodules/interfaces/taskManager/BuildConfig.java b/node_modules/unimodules-task-manager-interface/android/build/generated/source/buildConfig/release/org/unimodules/interfaces/taskManager/BuildConfig.java
new file mode 100644
index 0000000..c53f41e
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/generated/source/buildConfig/release/org/unimodules/interfaces/taskManager/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package org.unimodules.interfaces.taskManager;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "org.unimodules.interfaces.taskManager";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..02c55cf
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.interfaces.taskManager" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..88b063d
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.interfaces.taskManager",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..02c55cf
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.interfaces.taskManager" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..b52fd7d
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.interfaces.taskManager",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/aar_main_jar/release/classes.jar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aar_main_jar/release/classes.jar
new file mode 100644
index 0000000..9bc9058
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aar_main_jar/release/classes.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/unimodules-task-manager-interface/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/unimodules-task-manager-interface/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/annotations_typedef_file/release/typedefs.txt b/node_modules/unimodules-task-manager-interface/android/build/intermediates/annotations_typedef_file/release/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..836342f
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..a7ab53e
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..2ca9e8c
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..2ca9e8c
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_r_class_jar/release/R.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/unimodules-task-manager-interface/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/full_jar/release/full.jar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/full_jar/release/full.jar
new file mode 100644
index 0000000..4c06799
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/full_jar/release/full.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..628f7b8
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..c2c6bf9
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..7e9c35e
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..67cf85b
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..e8bc500
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/intermediates/shader_assets/debug/out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/debug/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..d5aeeb6
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:34:33 ICT 2022
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageDebugResources/merger.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageDebugResources/merger.xml
new file mode 100644
index 0000000..d32d664
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/debug/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..fc406ce
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/intermediates/shader_assets/release/out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/release/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..992cab2
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:16:22 ICT 2022
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageReleaseResources/merger.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..ad39da6
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/packageReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/release/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/release-mergeJavaRes/merge-state b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/release-mergeJavaRes/merge-state
new file mode 100644
index 0000000..82982f6
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/incremental/release-mergeJavaRes/merge-state differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/BuildConfig.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/BuildConfig.class
new file mode 100644
index 0000000..8b09492
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/BuildConfig.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskConsumer.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskConsumer.class
new file mode 100644
index 0000000..6dfd7f8
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskConsumer.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskConsumerInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskConsumerInterface.class
new file mode 100644
index 0000000..d6b9bae
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskConsumerInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskExecutionCallback.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskExecutionCallback.class
new file mode 100644
index 0000000..a7e312b
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskExecutionCallback.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskInterface.class
new file mode 100644
index 0000000..cef020d
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskManagerInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskManagerInterface.class
new file mode 100644
index 0000000..85fb00d
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskManagerInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.class
new file mode 100644
index 0000000..f924caf
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskServiceInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskServiceInterface.class
new file mode 100644
index 0000000..3a3f5fb
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/debug/classes/org/unimodules/interfaces/taskManager/TaskServiceInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/BuildConfig.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/BuildConfig.class
new file mode 100644
index 0000000..6146dc9
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/BuildConfig.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskConsumer.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskConsumer.class
new file mode 100644
index 0000000..6dfd7f8
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskConsumer.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskConsumerInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskConsumerInterface.class
new file mode 100644
index 0000000..d6b9bae
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskConsumerInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskExecutionCallback.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskExecutionCallback.class
new file mode 100644
index 0000000..a7e312b
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskExecutionCallback.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskInterface.class
new file mode 100644
index 0000000..cef020d
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskManagerInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskManagerInterface.class
new file mode 100644
index 0000000..85fb00d
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskManagerInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.class
new file mode 100644
index 0000000..f924caf
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskServiceInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskServiceInterface.class
new file mode 100644
index 0000000..3a3f5fb
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/javac/release/classes/org/unimodules/interfaces/taskManager/TaskServiceInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties b/node_modules/unimodules-task-manager-interface/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
new file mode 100644
index 0000000..51a6697
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=unimodules-task-manager-interface
+mavenGroupId=org.unimodules
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/local_aar_for_lint/release/out.aar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/local_aar_for_lint/release/out.aar
new file mode 100644
index 0000000..16d72bc
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/local_aar_for_lint/release/out.aar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/unimodules-task-manager-interface/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/unimodules-task-manager-interface/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/unimodules-task-manager-interface/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..66748ea
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="org.unimodules.interfaces.taskManager" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/unimodules-task-manager-interface/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..66748ea
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="org.unimodules.interfaces.taskManager" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/merged_java_res/release/feature-unimodules-task-manager-interface.jar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/merged_java_res/release/feature-unimodules-task-manager-interface.jar
new file mode 100644
index 0000000..15cb0ec
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/merged_java_res/release/feature-unimodules-task-manager-interface.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..02c55cf
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.interfaces.taskManager" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/unimodules-task-manager-interface/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..02c55cf
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.interfaces.taskManager" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/unimodules-task-manager-interface/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/unimodules-task-manager-interface/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/unimodules-task-manager-interface/android/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..8e6c30e
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.interfaces.taskManager",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/packaged_manifests/release/output-metadata.json b/node_modules/unimodules-task-manager-interface/android/build/intermediates/packaged_manifests/release/output-metadata.json
new file mode 100644
index 0000000..56831a4
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/packaged_manifests/release/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.interfaces.taskManager",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/release/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/BuildConfig.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/BuildConfig.class
new file mode 100644
index 0000000..feac911
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/BuildConfig.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskConsumer.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskConsumer.class
new file mode 100644
index 0000000..0d27a0a
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskConsumer.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskConsumerInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskConsumerInterface.class
new file mode 100644
index 0000000..db1539d
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskConsumerInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskExecutionCallback.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskExecutionCallback.class
new file mode 100644
index 0000000..7da9777
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskExecutionCallback.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskInterface.class
new file mode 100644
index 0000000..3eba0db
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskManagerInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskManagerInterface.class
new file mode 100644
index 0000000..a56d8e5
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskManagerInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.class
new file mode 100644
index 0000000..8ac118e
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskServiceInterface.class b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskServiceInterface.class
new file mode 100644
index 0000000..644895c
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/interfaces/taskManager/TaskServiceInterface.class differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..3c92665
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..ee5c891
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/intermediates/runtime_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/unimodules-task-manager-interface/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..115eb64
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1 @@
+org.unimodules.interfaces.taskManager
diff --git a/node_modules/unimodules-task-manager-interface/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/unimodules-task-manager-interface/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..115eb64
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1 @@
+org.unimodules.interfaces.taskManager
diff --git a/node_modules/unimodules-task-manager-interface/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/unimodules-task-manager-interface/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..de6438e
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:1-4:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:11-58
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:1-4:12
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
diff --git a/node_modules/unimodules-task-manager-interface/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/unimodules-task-manager-interface/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..de6438e
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:1-4:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:11-58
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml:2:1-4:12
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/unimodules-task-manager-interface/android/src/main/AndroidManifest.xml
diff --git a/node_modules/unimodules-task-manager-interface/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/unimodules-task-manager-interface/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..966d2bd
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin b/node_modules/unimodules-task-manager-interface/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..8343b40
Binary files /dev/null and b/node_modules/unimodules-task-manager-interface/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/unimodules-task-manager-interface/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt b/node_modules/unimodules-task-manager-interface/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
new file mode 100644
index 0000000..9b8c4e1
--- /dev/null
+++ b/node_modules/unimodules-task-manager-interface/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
@@ -0,0 +1,16 @@
+org/unimodules/interfaces/taskManager/TaskServiceInterface.java
+ org.unimodules.interfaces.taskManager.TaskServiceInterface
+org/unimodules/interfaces/taskManager/TaskConsumer.java
+ org.unimodules.interfaces.taskManager.TaskConsumer
+org/unimodules/interfaces/taskManager/TaskInterface.java
+ org.unimodules.interfaces.taskManager.TaskInterface
+org/unimodules/interfaces/taskManager/BuildConfig.java
+ org.unimodules.interfaces.taskManager.BuildConfig
+org/unimodules/interfaces/taskManager/TaskExecutionCallback.java
+ org.unimodules.interfaces.taskManager.TaskExecutionCallback
+org/unimodules/interfaces/taskManager/TaskManagerInterface.java
+ org.unimodules.interfaces.taskManager.TaskManagerInterface
+org/unimodules/interfaces/taskManager/TaskConsumerInterface.java
+ org.unimodules.interfaces.taskManager.TaskConsumerInterface
+org/unimodules/interfaces/taskManager/TaskManagerUtilsInterface.java
+ org.unimodules.interfaces.taskManager.TaskManagerUtilsInterface
