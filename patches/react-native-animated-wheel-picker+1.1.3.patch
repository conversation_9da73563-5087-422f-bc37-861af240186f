diff --git a/node_modules/react-native-animated-wheel-picker/src/components/Picker.tsx b/node_modules/react-native-animated-wheel-picker/src/components/Picker.tsx
index f3bd872..0c648ed 100644
--- a/node_modules/react-native-animated-wheel-picker/src/components/Picker.tsx
+++ b/node_modules/react-native-animated-wheel-picker/src/components/Picker.tsx
@@ -224,17 +224,18 @@ const Item = ({
   );
 
   const textAnimation = useAnimatedStyle(() => {
+
     return {
-      opacity: 1 / (1 + Math.abs(y.value)),
+      opacity: (1 / (1 + Math.abs(y.value))),
       transform: [
-        {
-          scale: 1 - 0.1 * Math.abs(y.value),
-        },
+        // {
+        //   scale: 1 - 0.1 * Math.abs(y.value),
+        // },
         {
           perspective: 500,
         },
         {
-          rotateX: `${65 * y.value}deg`,
+          rotateX: `${20 * y.value}deg`,
         },
       ],
     };
