diff --git a/node_modules/react-native-snap-carousel/src/carousel/Carousel.js b/node_modules/react-native-snap-carousel/src/carousel/Carousel.js
index dae71a3..72868e0 100644
--- a/node_modules/react-native-snap-carousel/src/carousel/Carousel.js
+++ b/node_modules/react-native-snap-carousel/src/carousel/Carousel.js
@@ -1,5 +1,6 @@
 import React, { Component } from 'react';
-import { Animated, Easing, FlatList, I18nManager, Platform, ScrollView, View, ViewPropTypes } from 'react-native';
+import { Animated, Easing, FlatList, I18nManager, Platform, ScrollView, View } from 'react-native';
+import {ViewPropTypes} from "deprecated-react-native-prop-types";
 import PropTypes from 'prop-types';
 import shallowCompare from 'react-addons-shallow-compare';
 import {
diff --git a/node_modules/react-native-snap-carousel/src/pagination/Pagination.js b/node_modules/react-native-snap-carousel/src/pagination/Pagination.js
index 5c021cf..2bcea8a 100644
--- a/node_modules/react-native-snap-carousel/src/pagination/Pagination.js
+++ b/node_modules/react-native-snap-carousel/src/pagination/Pagination.js
@@ -1,5 +1,6 @@
 import React, { PureComponent } from 'react';
-import { I18nManager, Platform, View, ViewPropTypes } from 'react-native';
+import { I18nManager, Platform, View } from 'react-native';
+import {ViewPropTypes} from "deprecated-react-native-prop-types";
 import PropTypes from 'prop-types';
 import PaginationDot from './PaginationDot';
 import styles from './Pagination.style';
diff --git a/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js b/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js
index e59d196..77b5ac9 100644
--- a/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js
+++ b/node_modules/react-native-snap-carousel/src/pagination/PaginationDot.js
@@ -1,7 +1,8 @@
 import React, { PureComponent } from 'react';
-import { View, Animated, Easing, TouchableOpacity, ViewPropTypes } from 'react-native';
+import { View, Animated, Easing, TouchableOpacity } from 'react-native';
 import PropTypes from 'prop-types';
 import styles from './Pagination.style';
+import {ViewPropTypes} from "deprecated-react-native-prop-types";
 
 export default class PaginationDot extends PureComponent {
 
diff --git a/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js b/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js
index 8bc774a..bac73ed 100644
--- a/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js
+++ b/node_modules/react-native-snap-carousel/src/parallaximage/ParallaxImage.js
@@ -1,7 +1,8 @@
 // Parallax effect inspired by https://github.com/oblador/react-native-parallax/
 
 import React, { Component } from 'react';
-import { View, ViewPropTypes, Image, Animated, Easing, ActivityIndicator, findNodeHandle } from 'react-native';
+import { View, Image, Animated, Easing, ActivityIndicator, findNodeHandle } from 'react-native';
+import { ViewPropTypes } from "deprecated-react-native-prop-types";
 import PropTypes from 'prop-types';
 import styles from './ParallaxImage.style';
 
