diff --git a/node_modules/expo-file-system/android/build.gradle b/node_modules/expo-file-system/android/build.gradle
index fd2598b..40b064b 100644
--- a/node_modules/expo-file-system/android/build.gradle
+++ b/node_modules/expo-file-system/android/build.gradle
@@ -1,6 +1,6 @@
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
-apply plugin: 'maven'
+apply plugin: 'maven-publish'
 
 group = 'host.exp.exponent'
 version = '11.1.3'
@@ -36,15 +36,6 @@ artifacts {
   archives androidSourcesJar
 }
 
-uploadArchives {
-  repositories {
-    mavenDeployer {
-      configuration = configurations.deployerJars
-      repository(url: mavenLocal().url)
-    }
-  }
-}
-
 android {
   compileSdkVersion safeExtGet("compileSdkVersion", 30)
 
diff --git a/node_modules/expo-file-system/android/build/.transforms/26a60d1c22763d34e32e638df103c348/results.bin b/node_modules/expo-file-system/android/build/.transforms/26a60d1c22763d34e32e638df103c348/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/26a60d1c22763d34e32e638df103c348/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/results.bin b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/AndroidManifest.xml b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..a89fbe0
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/AndroidManifest.xml
@@ -0,0 +1,35 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="expo.modules.filesystem" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
+    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
+
+    <queries>
+
+        <!-- Query open documents -->
+        <intent>
+            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
+        </intent>
+    </queries>
+
+    <application>
+        <provider
+            android:name="expo.modules.filesystem.FileSystemFileProvider"
+            android:authorities="${applicationId}.FileSystemFileProvider"
+            android:exported="false"
+            android:grantUriPermissions="true"
+            tools:replace="android:authorities" >
+            <meta-data
+                android:name="android.support.FILE_PROVIDER_PATHS"
+                android:resource="@xml/file_system_provider_paths" />
+        </provider>
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/R.txt b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/R.txt
new file mode 100644
index 0000000..77cdfa9
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/R.txt
@@ -0,0 +1,187 @@
+int attr alpha 0x0
+int attr coordinatorLayoutStyle 0x0
+int attr font 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr keylines 0x0
+int attr layout_anchor 0x0
+int attr layout_anchorGravity 0x0
+int attr layout_behavior 0x0
+int attr layout_dodgeInsetEdges 0x0
+int attr layout_insetEdge 0x0
+int attr layout_keyline 0x0
+int attr statusBarBackground 0x0
+int attr ttcIndex 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color notification_material_background_media_default_color 0x0
+int color primary_text_default_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen subtitle_corner_radius 0x0
+int dimen subtitle_outline_width 0x0
+int dimen subtitle_shadow_offset 0x0
+int dimen subtitle_shadow_radius 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int id action0 0x0
+int id action_container 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_text 0x0
+int id actions 0x0
+int id async 0x0
+int id blocking 0x0
+int id bottom 0x0
+int id cancel_action 0x0
+int id chronometer 0x0
+int id end 0x0
+int id end_padder 0x0
+int id forever 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id info 0x0
+int id italic 0x0
+int id left 0x0
+int id line1 0x0
+int id line3 0x0
+int id media_actions 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id right 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id start 0x0
+int id status_bar_latest_event_content 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id text 0x0
+int id text2 0x0
+int id time 0x0
+int id title 0x0
+int id top 0x0
+int integer cancel_button_image_alpha 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_media_action 0x0
+int layout notification_media_cancel_action 0x0
+int layout notification_template_big_media 0x0
+int layout notification_template_big_media_custom 0x0
+int layout notification_template_big_media_narrow 0x0
+int layout notification_template_big_media_narrow_custom 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_lines_media 0x0
+int layout notification_template_media 0x0
+int layout notification_template_media_custom 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int string status_bar_notification_info_overflow 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Info_Media 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Line2_Media 0x0
+int style TextAppearance_Compat_Notification_Media 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Time_Media 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Compat_Notification_Title_Media 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style Widget_Support_CoordinatorLayout 0x0
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int[] styleable CoordinatorLayout { 0x0, 0x0 }
+int styleable CoordinatorLayout_keylines 0
+int styleable CoordinatorLayout_statusBarBackground 1
+int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CoordinatorLayout_Layout_android_layout_gravity 0
+int styleable CoordinatorLayout_Layout_layout_anchor 1
+int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
+int styleable CoordinatorLayout_Layout_layout_behavior 3
+int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
+int styleable CoordinatorLayout_Layout_layout_insetEdge 5
+int styleable CoordinatorLayout_Layout_layout_keyline 6
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int xml file_system_provider_paths 0x0
diff --git a/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/jars/classes.jar b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..0315a84
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/jars/classes.jar differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/res/xml/file_system_provider_paths.xml b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/res/xml/file_system_provider_paths.xml
new file mode 100644
index 0000000..959ed95
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/3570b19e156f0063fd6ed6fec8ca3ecc/transformed/out/res/xml/file_system_provider_paths.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<paths>
+    <files-path name="expo_files" path="." />
+    <cache-path name="cached_expo_files" path="." />
+</paths>
diff --git a/node_modules/expo-file-system/android/build/.transforms/463b48833e468b6a275972e97a6226e9/results.bin b/node_modules/expo-file-system/android/build/.transforms/463b48833e468b6a275972e97a6226e9/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/463b48833e468b6a275972e97a6226e9/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-file-system/android/build/.transforms/463b48833e468b6a275972e97a6226e9/transformed/classes/classes.dex b/node_modules/expo-file-system/android/build/.transforms/463b48833e468b6a275972e97a6226e9/transformed/classes/classes.dex
new file mode 100644
index 0000000..02ba2a6
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/463b48833e468b6a275972e97a6226e9/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/results.bin b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/results.bin
new file mode 100644
index 0000000..9e53e72
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/results.bin
@@ -0,0 +1 @@
+o/release
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/desugar_graph.bin b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/desugar_graph.bin differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/BuildConfig.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/BuildConfig.dex
new file mode 100644
index 0000000..5966fd3
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/BuildConfig.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FilePermissionModule.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FilePermissionModule.dex
new file mode 100644
index 0000000..2d943df
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FilePermissionModule.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemFileProvider.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemFileProvider.dex
new file mode 100644
index 0000000..9d71850
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemFileProvider.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$1.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$1.dex
new file mode 100644
index 0000000..c447c37
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$1.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$2.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$2.dex
new file mode 100644
index 0000000..2749c6b
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$2.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$3.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$3.dex
new file mode 100644
index 0000000..ff88fb7
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$3.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$4.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$4.dex
new file mode 100644
index 0000000..1aef4f0
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$4.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$DownloadResumable.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$DownloadResumable.dex
new file mode 100644
index 0000000..9b39dd5
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$DownloadResumable.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTask.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTask.dex
new file mode 100644
index 0000000..f717a02
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTask.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTaskParams.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTaskParams.dex
new file mode 100644
index 0000000..008d72b
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTaskParams.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$ProgressListener.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$ProgressListener.dex
new file mode 100644
index 0000000..9ba8a91
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$ProgressListener.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody$1.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody$1.dex
new file mode 100644
index 0000000..cd6d23e
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody$1.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody.dex
new file mode 100644
index 0000000..6545b96
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$UploadType.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$UploadType.dex
new file mode 100644
index 0000000..4a20f20
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule$UploadType.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule.dex
new file mode 100644
index 0000000..61426c3
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemModule.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemPackage.dex b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemPackage.dex
new file mode 100644
index 0000000..8ea2060
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/6395e7215b1b1c0a2f18b1e24d907ef0/transformed/release/expo/modules/filesystem/FileSystemPackage.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/682b4d53cb8e81493a05b988e6386935/results.bin b/node_modules/expo-file-system/android/build/.transforms/682b4d53cb8e81493a05b988e6386935/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/682b4d53cb8e81493a05b988e6386935/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-file-system/android/build/.transforms/682b4d53cb8e81493a05b988e6386935/transformed/classes/classes.dex b/node_modules/expo-file-system/android/build/.transforms/682b4d53cb8e81493a05b988e6386935/transformed/classes/classes.dex
new file mode 100644
index 0000000..8bd2946
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/682b4d53cb8e81493a05b988e6386935/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/results.bin b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/AndroidManifest.xml b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..a89fbe0
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/AndroidManifest.xml
@@ -0,0 +1,35 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="expo.modules.filesystem" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
+    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
+
+    <queries>
+
+        <!-- Query open documents -->
+        <intent>
+            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
+        </intent>
+    </queries>
+
+    <application>
+        <provider
+            android:name="expo.modules.filesystem.FileSystemFileProvider"
+            android:authorities="${applicationId}.FileSystemFileProvider"
+            android:exported="false"
+            android:grantUriPermissions="true"
+            tools:replace="android:authorities" >
+            <meta-data
+                android:name="android.support.FILE_PROVIDER_PATHS"
+                android:resource="@xml/file_system_provider_paths" />
+        </provider>
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..d8f70c0
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,3 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
diff --git a/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
new file mode 100644
index 0000000..dbdca7a
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=expo-file-system
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/R.txt b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/R.txt
new file mode 100644
index 0000000..77cdfa9
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/R.txt
@@ -0,0 +1,187 @@
+int attr alpha 0x0
+int attr coordinatorLayoutStyle 0x0
+int attr font 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr keylines 0x0
+int attr layout_anchor 0x0
+int attr layout_anchorGravity 0x0
+int attr layout_behavior 0x0
+int attr layout_dodgeInsetEdges 0x0
+int attr layout_insetEdge 0x0
+int attr layout_keyline 0x0
+int attr statusBarBackground 0x0
+int attr ttcIndex 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color notification_material_background_media_default_color 0x0
+int color primary_text_default_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen subtitle_corner_radius 0x0
+int dimen subtitle_outline_width 0x0
+int dimen subtitle_shadow_offset 0x0
+int dimen subtitle_shadow_radius 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int id action0 0x0
+int id action_container 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_text 0x0
+int id actions 0x0
+int id async 0x0
+int id blocking 0x0
+int id bottom 0x0
+int id cancel_action 0x0
+int id chronometer 0x0
+int id end 0x0
+int id end_padder 0x0
+int id forever 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id info 0x0
+int id italic 0x0
+int id left 0x0
+int id line1 0x0
+int id line3 0x0
+int id media_actions 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id right 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id start 0x0
+int id status_bar_latest_event_content 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id text 0x0
+int id text2 0x0
+int id time 0x0
+int id title 0x0
+int id top 0x0
+int integer cancel_button_image_alpha 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_media_action 0x0
+int layout notification_media_cancel_action 0x0
+int layout notification_template_big_media 0x0
+int layout notification_template_big_media_custom 0x0
+int layout notification_template_big_media_narrow 0x0
+int layout notification_template_big_media_narrow_custom 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_lines_media 0x0
+int layout notification_template_media 0x0
+int layout notification_template_media_custom 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int string status_bar_notification_info_overflow 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Info_Media 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Line2_Media 0x0
+int style TextAppearance_Compat_Notification_Media 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Time_Media 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Compat_Notification_Title_Media 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style Widget_Support_CoordinatorLayout 0x0
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int[] styleable CoordinatorLayout { 0x0, 0x0 }
+int styleable CoordinatorLayout_keylines 0
+int styleable CoordinatorLayout_statusBarBackground 1
+int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CoordinatorLayout_Layout_android_layout_gravity 0
+int styleable CoordinatorLayout_Layout_layout_anchor 1
+int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
+int styleable CoordinatorLayout_Layout_layout_behavior 3
+int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
+int styleable CoordinatorLayout_Layout_layout_insetEdge 5
+int styleable CoordinatorLayout_Layout_layout_keyline 6
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int xml file_system_provider_paths 0x0
diff --git a/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/jars/classes.jar b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..0315a84
Binary files /dev/null and b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/jars/classes.jar differ
diff --git a/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/res/xml/file_system_provider_paths.xml b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/res/xml/file_system_provider_paths.xml
new file mode 100644
index 0000000..959ed95
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/.transforms/bcdabcee911599656dc047b122db59a4/transformed/out/res/xml/file_system_provider_paths.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<paths>
+    <files-path name="expo_files" path="." />
+    <cache-path name="cached_expo_files" path="." />
+</paths>
diff --git a/node_modules/expo-file-system/android/build/generated/source/buildConfig/release/expo/modules/filesystem/BuildConfig.java b/node_modules/expo-file-system/android/build/generated/source/buildConfig/release/expo/modules/filesystem/BuildConfig.java
new file mode 100644
index 0000000..917429b
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/generated/source/buildConfig/release/expo/modules/filesystem/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package expo.modules.filesystem;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "expo.modules.filesystem";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/expo-file-system/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/expo-file-system/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..455c635
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,35 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="expo.modules.filesystem" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
+    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
+
+    <queries>
+
+        <!-- Query open documents -->
+        <intent>
+            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
+        </intent>
+    </queries>
+
+    <application>
+        <provider
+            android:name="expo.modules.filesystem.FileSystemFileProvider"
+            android:authorities="dollar_openBracket_applicationId_closeBracket.FileSystemFileProvider"
+            android:exported="false"
+            android:grantUriPermissions="true"
+            tools:replace="android:authorities" >
+            <meta-data
+                android:name="android.support.FILE_PROVIDER_PATHS"
+                android:resource="@xml/file_system_provider_paths" />
+        </provider>
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/expo-file-system/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..cfb1520
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules.filesystem",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/aar_main_jar/release/classes.jar b/node_modules/expo-file-system/android/build/intermediates/aar_main_jar/release/classes.jar
new file mode 100644
index 0000000..0315a84
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/aar_main_jar/release/classes.jar differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/expo-file-system/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-file-system/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/expo-file-system/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/annotations_typedef_file/release/typedefs.txt b/node_modules/expo-file-system/android/build/intermediates/annotations_typedef_file/release/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-file-system/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/expo-file-system/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..d89a239
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/compile_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/expo-file-system/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..0865611
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/compile_r_class_jar/release/R.jar differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/expo-file-system/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..77cdfa9
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/compile_symbol_list/release/R.txt
@@ -0,0 +1,187 @@
+int attr alpha 0x0
+int attr coordinatorLayoutStyle 0x0
+int attr font 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr keylines 0x0
+int attr layout_anchor 0x0
+int attr layout_anchorGravity 0x0
+int attr layout_behavior 0x0
+int attr layout_dodgeInsetEdges 0x0
+int attr layout_insetEdge 0x0
+int attr layout_keyline 0x0
+int attr statusBarBackground 0x0
+int attr ttcIndex 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color notification_material_background_media_default_color 0x0
+int color primary_text_default_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen subtitle_corner_radius 0x0
+int dimen subtitle_outline_width 0x0
+int dimen subtitle_shadow_offset 0x0
+int dimen subtitle_shadow_radius 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int id action0 0x0
+int id action_container 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_text 0x0
+int id actions 0x0
+int id async 0x0
+int id blocking 0x0
+int id bottom 0x0
+int id cancel_action 0x0
+int id chronometer 0x0
+int id end 0x0
+int id end_padder 0x0
+int id forever 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id info 0x0
+int id italic 0x0
+int id left 0x0
+int id line1 0x0
+int id line3 0x0
+int id media_actions 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id right 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id start 0x0
+int id status_bar_latest_event_content 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id text 0x0
+int id text2 0x0
+int id time 0x0
+int id title 0x0
+int id top 0x0
+int integer cancel_button_image_alpha 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_media_action 0x0
+int layout notification_media_cancel_action 0x0
+int layout notification_template_big_media 0x0
+int layout notification_template_big_media_custom 0x0
+int layout notification_template_big_media_narrow 0x0
+int layout notification_template_big_media_narrow_custom 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_lines_media 0x0
+int layout notification_template_media 0x0
+int layout notification_template_media_custom 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int string status_bar_notification_info_overflow 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Info_Media 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Line2_Media 0x0
+int style TextAppearance_Compat_Notification_Media 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Time_Media 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Compat_Notification_Title_Media 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style Widget_Support_CoordinatorLayout 0x0
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int[] styleable CoordinatorLayout { 0x0, 0x0 }
+int styleable CoordinatorLayout_keylines 0
+int styleable CoordinatorLayout_statusBarBackground 1
+int[] styleable CoordinatorLayout_Layout { 0x10100b3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable CoordinatorLayout_Layout_android_layout_gravity 0
+int styleable CoordinatorLayout_Layout_layout_anchor 1
+int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
+int styleable CoordinatorLayout_Layout_layout_behavior 3
+int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
+int styleable CoordinatorLayout_Layout_layout_insetEdge 5
+int styleable CoordinatorLayout_Layout_layout_keyline 6
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int xml file_system_provider_paths 0x0
diff --git a/node_modules/expo-file-system/android/build/intermediates/compiled_local_resources/release/out/xml_file_system_provider_paths.xml.flat b/node_modules/expo-file-system/android/build/intermediates/compiled_local_resources/release/out/xml_file_system_provider_paths.xml.flat
new file mode 100644
index 0000000..4c683e8
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/compiled_local_resources/release/out/xml_file_system_provider_paths.xml.flat differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/full_jar/release/full.jar b/node_modules/expo-file-system/android/build/intermediates/full_jar/release/full.jar
new file mode 100644
index 0000000..63e138d
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/full_jar/release/full.jar differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/expo-file-system/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..6e76883
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/expo-file-system/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..757b97a
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/expo-file-system/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..4deb0d3
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/build/intermediates/shader_assets/release/out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/release/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties b/node_modules/expo-file-system/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..f118336
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1,2 @@
+#Wed Oct 12 17:15:46 ICT 2022
+/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/res/xml/file_system_provider_paths.xml=/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/build/intermediates/packaged_res/release/xml/file_system_provider_paths.xml
diff --git a/node_modules/expo-file-system/android/build/intermediates/incremental/packageReleaseResources/merger.xml b/node_modules/expo-file-system/android/build/intermediates/incremental/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..d15206a
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/incremental/packageReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/res"><file name="file_system_provider_paths" path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/res/xml/file_system_provider_paths.xml" qualifiers="" type="xml"/></source><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/release/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/incremental/release-mergeJavaRes/merge-state b/node_modules/expo-file-system/android/build/intermediates/incremental/release-mergeJavaRes/merge-state
new file mode 100644
index 0000000..44fec92
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/incremental/release-mergeJavaRes/merge-state differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/BuildConfig.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/BuildConfig.class
new file mode 100644
index 0000000..9c485e0
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/BuildConfig.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FilePermissionModule.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FilePermissionModule.class
new file mode 100644
index 0000000..464bd69
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FilePermissionModule.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemFileProvider.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemFileProvider.class
new file mode 100644
index 0000000..a96f4ed
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemFileProvider.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$1.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$1.class
new file mode 100644
index 0000000..d66e5a1
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$1.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$2.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$2.class
new file mode 100644
index 0000000..a5fe6e3
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$2.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$3.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$3.class
new file mode 100644
index 0000000..1eb4ef7
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$3.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$4.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$4.class
new file mode 100644
index 0000000..c9e7b3e
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$4.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$DownloadResumable.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$DownloadResumable.class
new file mode 100644
index 0000000..5cbf622
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$DownloadResumable.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$DownloadResumableTask.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$DownloadResumableTask.class
new file mode 100644
index 0000000..0fbe414
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$DownloadResumableTask.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$DownloadResumableTaskParams.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$DownloadResumableTaskParams.class
new file mode 100644
index 0000000..1676c20
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$DownloadResumableTaskParams.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$ProgressListener.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$ProgressListener.class
new file mode 100644
index 0000000..7e33850
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$ProgressListener.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$ProgressResponseBody$1.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$ProgressResponseBody$1.class
new file mode 100644
index 0000000..5b0db43
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$ProgressResponseBody$1.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$ProgressResponseBody.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$ProgressResponseBody.class
new file mode 100644
index 0000000..4f054b3
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$ProgressResponseBody.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$UploadType.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$UploadType.class
new file mode 100644
index 0000000..bc1e0d7
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule$UploadType.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule.class
new file mode 100644
index 0000000..e84d611
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemModule.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemPackage.class b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemPackage.class
new file mode 100644
index 0000000..9672953
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/javac/release/classes/expo/modules/filesystem/FileSystemPackage.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties b/node_modules/expo-file-system/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
new file mode 100644
index 0000000..dbdca7a
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=expo-file-system
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/local_aar_for_lint/release/out.aar b/node_modules/expo-file-system/android/build/intermediates/local_aar_for_lint/release/out.aar
new file mode 100644
index 0000000..526b5df
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/local_aar_for_lint/release/out.aar differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/expo-file-system/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..fc80292
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,3 @@
+R_DEF: Internal format may change without notice
+local
+xml file_system_provider_paths
diff --git a/node_modules/expo-file-system/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/expo-file-system/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..fa98781
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,57 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    xmlns:tools="http://schemas.android.com/tools"
+4    package="expo.modules.filesystem" >
+5
+6    <uses-sdk
+7        android:minSdkVersion="21"
+7-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+8        android:targetSdkVersion="31" />
+8-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+9
+10    <uses-permission android:name="android.permission.INTERNET" />
+10-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:4:5-66
+10-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:4:22-64
+11    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
+11-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:5:5-80
+11-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:5:22-78
+12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
+12-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:6:5-80
+12-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:6:22-77
+13
+14    <queries>
+14-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:13:5-18:15
+15
+16        <!-- Query open documents -->
+17        <intent>
+17-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:15:9-17:18
+18            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
+18-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:16:13-79
+18-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:16:21-76
+19        </intent>
+20    </queries>
+21
+22    <application>
+22-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:7:5-11:19
+23        <provider
+23-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:9-10:20
+24            android:name="expo.modules.filesystem.FileSystemFileProvider"
+24-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:55-93
+25            android:authorities="${applicationId}.FileSystemFileProvider"
+25-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:94-155
+26            android:exported="false"
+26-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:156-180
+27            android:grantUriPermissions="true"
+27-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:181-215
+28            tools:replace="android:authorities" >
+28-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:19-54
+29            <meta-data
+29-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:9:13-128
+30                android:name="android.support.FILE_PROVIDER_PATHS"
+30-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:9:24-74
+31                android:resource="@xml/file_system_provider_paths" />
+31-->/Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:9:75-125
+32        </provider>
+33    </application>
+34
+35</manifest>
diff --git a/node_modules/expo-file-system/android/build/intermediates/merged_java_res/release/feature-expo-file-system.jar b/node_modules/expo-file-system/android/build/intermediates/merged_java_res/release/feature-expo-file-system.jar
new file mode 100644
index 0000000..15cb0ec
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/merged_java_res/release/feature-expo-file-system.jar differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/expo-file-system/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..a89fbe0
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,35 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="expo.modules.filesystem" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+    <uses-permission android:name="android.permission.INTERNET" />
+    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
+    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
+
+    <queries>
+
+        <!-- Query open documents -->
+        <intent>
+            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
+        </intent>
+    </queries>
+
+    <application>
+        <provider
+            android:name="expo.modules.filesystem.FileSystemFileProvider"
+            android:authorities="${applicationId}.FileSystemFileProvider"
+            android:exported="false"
+            android:grantUriPermissions="true"
+            tools:replace="android:authorities" >
+            <meta-data
+                android:name="android.support.FILE_PROVIDER_PATHS"
+                android:resource="@xml/file_system_provider_paths" />
+        </provider>
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/expo-file-system/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/packaged_manifests/release/output-metadata.json b/node_modules/expo-file-system/android/build/intermediates/packaged_manifests/release/output-metadata.json
new file mode 100644
index 0000000..5329e2f
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/packaged_manifests/release/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules.filesystem",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/release/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-file-system/android/build/intermediates/packaged_res/release/xml/file_system_provider_paths.xml b/node_modules/expo-file-system/android/build/intermediates/packaged_res/release/xml/file_system_provider_paths.xml
new file mode 100644
index 0000000..959ed95
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/packaged_res/release/xml/file_system_provider_paths.xml
@@ -0,0 +1,5 @@
+<?xml version="1.0" encoding="utf-8"?>
+<paths>
+    <files-path name="expo_files" path="." />
+    <cache-path name="cached_expo_files" path="." />
+</paths>
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/BuildConfig.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/BuildConfig.class
new file mode 100644
index 0000000..9c485e0
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/BuildConfig.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FilePermissionModule.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FilePermissionModule.class
new file mode 100644
index 0000000..464bd69
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FilePermissionModule.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemFileProvider.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemFileProvider.class
new file mode 100644
index 0000000..a96f4ed
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemFileProvider.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$1.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$1.class
new file mode 100644
index 0000000..d66e5a1
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$1.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$2.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$2.class
new file mode 100644
index 0000000..a5fe6e3
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$2.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$3.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$3.class
new file mode 100644
index 0000000..1eb4ef7
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$3.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$4.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$4.class
new file mode 100644
index 0000000..c9e7b3e
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$4.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$DownloadResumable.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$DownloadResumable.class
new file mode 100644
index 0000000..5cbf622
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$DownloadResumable.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTask.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTask.class
new file mode 100644
index 0000000..0fbe414
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTask.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTaskParams.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTaskParams.class
new file mode 100644
index 0000000..1676c20
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$DownloadResumableTaskParams.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$ProgressListener.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$ProgressListener.class
new file mode 100644
index 0000000..7e33850
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$ProgressListener.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody$1.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody$1.class
new file mode 100644
index 0000000..5b0db43
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody$1.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody.class
new file mode 100644
index 0000000..4f054b3
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$ProgressResponseBody.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$UploadType.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$UploadType.class
new file mode 100644
index 0000000..bc1e0d7
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule$UploadType.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule.class
new file mode 100644
index 0000000..e84d611
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemModule.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemPackage.class b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemPackage.class
new file mode 100644
index 0000000..9672953
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/filesystem/FileSystemPackage.class differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..63e138d
Binary files /dev/null and b/node_modules/expo-file-system/android/build/intermediates/runtime_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/expo-file-system/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/expo-file-system/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..c4051ed
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1,146 @@
+expo.modules.filesystem
+attr alpha
+attr coordinatorLayoutStyle
+attr font
+attr fontProviderAuthority
+attr fontProviderCerts
+attr fontProviderFetchStrategy
+attr fontProviderFetchTimeout
+attr fontProviderPackage
+attr fontProviderQuery
+attr fontStyle
+attr fontVariationSettings
+attr fontWeight
+attr keylines
+attr layout_anchor
+attr layout_anchorGravity
+attr layout_behavior
+attr layout_dodgeInsetEdges
+attr layout_insetEdge
+attr layout_keyline
+attr statusBarBackground
+attr ttcIndex
+color notification_action_color_filter
+color notification_icon_bg_color
+color notification_material_background_media_default_color
+color primary_text_default_material_dark
+color ripple_material_light
+color secondary_text_default_material_dark
+color secondary_text_default_material_light
+dimen compat_button_inset_horizontal_material
+dimen compat_button_inset_vertical_material
+dimen compat_button_padding_horizontal_material
+dimen compat_button_padding_vertical_material
+dimen compat_control_corner_material
+dimen compat_notification_large_icon_max_height
+dimen compat_notification_large_icon_max_width
+dimen notification_action_icon_size
+dimen notification_action_text_size
+dimen notification_big_circle_margin
+dimen notification_content_margin_start
+dimen notification_large_icon_height
+dimen notification_large_icon_width
+dimen notification_main_column_padding_top
+dimen notification_media_narrow_margin
+dimen notification_right_icon_size
+dimen notification_right_side_padding_top
+dimen notification_small_icon_background_padding
+dimen notification_small_icon_size_as_large
+dimen notification_subtext_size
+dimen notification_top_pad
+dimen notification_top_pad_large_text
+dimen subtitle_corner_radius
+dimen subtitle_outline_width
+dimen subtitle_shadow_offset
+dimen subtitle_shadow_radius
+drawable notification_action_background
+drawable notification_bg
+drawable notification_bg_low
+drawable notification_bg_low_normal
+drawable notification_bg_low_pressed
+drawable notification_bg_normal
+drawable notification_bg_normal_pressed
+drawable notification_icon_background
+drawable notification_template_icon_bg
+drawable notification_template_icon_low_bg
+drawable notification_tile_bg
+drawable notify_panel_notification_icon_bg
+id action0
+id action_container
+id action_divider
+id action_image
+id action_text
+id actions
+id async
+id blocking
+id bottom
+id cancel_action
+id chronometer
+id end
+id end_padder
+id forever
+id icon
+id icon_group
+id info
+id italic
+id left
+id line1
+id line3
+id media_actions
+id none
+id normal
+id notification_background
+id notification_main_column
+id notification_main_column_container
+id right
+id right_icon
+id right_side
+id start
+id status_bar_latest_event_content
+id tag_transition_group
+id tag_unhandled_key_event_manager
+id tag_unhandled_key_listeners
+id text
+id text2
+id time
+id title
+id top
+integer cancel_button_image_alpha
+integer status_bar_notification_info_maxnum
+layout notification_action
+layout notification_action_tombstone
+layout notification_media_action
+layout notification_media_cancel_action
+layout notification_template_big_media
+layout notification_template_big_media_custom
+layout notification_template_big_media_narrow
+layout notification_template_big_media_narrow_custom
+layout notification_template_custom_big
+layout notification_template_icon_group
+layout notification_template_lines_media
+layout notification_template_media
+layout notification_template_media_custom
+layout notification_template_part_chronometer
+layout notification_template_part_time
+string status_bar_notification_info_overflow
+style TextAppearance_Compat_Notification
+style TextAppearance_Compat_Notification_Info
+style TextAppearance_Compat_Notification_Info_Media
+style TextAppearance_Compat_Notification_Line2
+style TextAppearance_Compat_Notification_Line2_Media
+style TextAppearance_Compat_Notification_Media
+style TextAppearance_Compat_Notification_Time
+style TextAppearance_Compat_Notification_Time_Media
+style TextAppearance_Compat_Notification_Title
+style TextAppearance_Compat_Notification_Title_Media
+style Widget_Compat_NotificationActionContainer
+style Widget_Compat_NotificationActionText
+style Widget_Support_CoordinatorLayout
+styleable ColorStateListItem alpha android_alpha android_color
+styleable CoordinatorLayout keylines statusBarBackground
+styleable CoordinatorLayout_Layout android_layout_gravity layout_anchor layout_anchorGravity layout_behavior layout_dodgeInsetEdges layout_insetEdge layout_keyline
+styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery
+styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
+styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
+styleable GradientColorItem android_color android_offset
+xml file_system_provider_paths
diff --git a/node_modules/expo-file-system/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/expo-file-system/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..2226dfe
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,67 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:1:1-19:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:1:1-19:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:1:1-19:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:1:11-44
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+	xmlns:tools
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:3:5-51
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:2:5-63
+uses-permission#android.permission.INTERNET
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:4:5-66
+	android:name
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:4:22-64
+uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:5:5-80
+	android:name
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:5:22-78
+uses-permission#android.permission.READ_EXTERNAL_STORAGE
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:6:5-80
+	android:name
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:6:22-77
+application
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:7:5-11:19
+provider#expo.modules.filesystem.FileSystemFileProvider
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:9-10:20
+	android:grantUriPermissions
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:181-215
+	android:authorities
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:94-155
+	android:exported
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:156-180
+	tools:replace
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:19-54
+	android:name
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:8:55-93
+meta-data#android.support.FILE_PROVIDER_PATHS
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:9:13-128
+	android:resource
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:9:75-125
+	android:name
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:9:24-74
+queries
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:13:5-18:15
+intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:15:9-17:18
+action#android.intent.action.OPEN_DOCUMENT_TREE
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:16:13-79
+	android:name
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml:16:21-76
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-file-system/android/src/main/AndroidManifest.xml
diff --git a/node_modules/expo-file-system/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin b/node_modules/expo-file-system/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..1dc4abb
Binary files /dev/null and b/node_modules/expo-file-system/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/expo-file-system/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt b/node_modules/expo-file-system/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
new file mode 100644
index 0000000..2d17551
--- /dev/null
+++ b/node_modules/expo-file-system/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
@@ -0,0 +1,21 @@
+expo/modules/filesystem/FileSystemModule.java
+ expo.modules.filesystem.FileSystemModule
+ expo.modules.filesystem.FileSystemModule$1
+ expo.modules.filesystem.FileSystemModule$2
+ expo.modules.filesystem.FileSystemModule$3
+ expo.modules.filesystem.FileSystemModule$4
+ expo.modules.filesystem.FileSystemModule$DownloadResumable
+ expo.modules.filesystem.FileSystemModule$DownloadResumableTask
+ expo.modules.filesystem.FileSystemModule$DownloadResumableTaskParams
+ expo.modules.filesystem.FileSystemModule$ProgressListener
+ expo.modules.filesystem.FileSystemModule$ProgressResponseBody
+ expo.modules.filesystem.FileSystemModule$ProgressResponseBody$1
+ expo.modules.filesystem.FileSystemModule$UploadType
+expo/modules/filesystem/FilePermissionModule.java
+ expo.modules.filesystem.FilePermissionModule
+expo/modules/filesystem/FileSystemFileProvider.java
+ expo.modules.filesystem.FileSystemFileProvider
+expo/modules/filesystem/FileSystemPackage.java
+ expo.modules.filesystem.FileSystemPackage
+expo/modules/filesystem/BuildConfig.java
+ expo.modules.filesystem.BuildConfig
