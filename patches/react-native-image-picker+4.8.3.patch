diff --git a/node_modules/react-native-image-picker/ios/ImagePickerManager.m b/node_modules/react-native-image-picker/ios/ImagePickerManager.m
index 0c6138f..4b4ed50 100644
--- a/node_modules/react-native-image-picker/ios/ImagePickerManager.m
+++ b/node_modules/react-native-image-picker/ios/ImagePickerManager.m
@@ -65,36 +65,14 @@ RCT_EXPORT_METHOD(launchImageLibrary:(NSDictionary *)options callback:(RCTRespon
 
     self.options = options;
 
-#if __has_include(<PhotosUI/PHPicker.h>)
-    if (@available(iOS 14, *)) {
-        if (target == library) {
-            PHPickerConfiguration *configuration = [ImagePickerUtils makeConfigurationFromOptions:options target:target];
-            PHPickerViewController *picker = [[PHPickerViewController alloc] initWithConfiguration:configuration];
-            picker.delegate = self;
-            picker.modalPresentationStyle = [RCTConvert UIModalPresentationStyle:options[@"presentationStyle"]];
-            picker.presentationController.delegate = self;
-
-            if([self.options[@"includeExtra"] boolValue]) {
-                
-                [self checkPhotosPermissions:^(BOOL granted) {
-                    if (!granted) {
-                        self.callback(@[@{@"errorCode": errPermission}]);
-                        return;
-                    }
-                    [self showPickerViewController:picker];
-                }];
-            } else {
-                [self showPickerViewController:picker];
-            }
-            
-            return;
-        }
-    }
-#endif
     UIImagePickerController *picker = [[UIImagePickerController alloc] init];
     [ImagePickerUtils setupPickerFromOptions:picker options:self.options target:target];
     picker.delegate = self;
     
+    if([self.options[@"allowsEditing"] boolValue]) {
+        picker.allowsEditing = true;
+    }
+
     if([self.options[@"includeExtra"] boolValue]) {
         [self checkPhotosPermissions:^(BOOL granted) {
             if (!granted) {
