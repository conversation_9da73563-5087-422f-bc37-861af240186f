diff --git a/node_modules/expo-modules-core/android/build.gradle b/node_modules/expo-modules-core/android/build.gradle
index 582976f..4fc8890 100644
--- a/node_modules/expo-modules-core/android/build.gradle
+++ b/node_modules/expo-modules-core/android/build.gradle
@@ -1,6 +1,6 @@
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
-apply plugin: 'maven'
+apply plugin: 'maven-publish'
 
 group = 'host.exp.exponent'
 version = '0.2.0'
@@ -36,15 +36,6 @@ artifacts {
   archives androidSourcesJar
 }
 
-uploadArchives {
-  repositories {
-    mavenDeployer {
-      configuration = configurations.deployerJars
-      repository(url: mavenLocal().url)
-    }
-  }
-}
-
 android {
   compileSdkVersion safeExtGet("compileSdkVersion", 30)
 
diff --git a/node_modules/expo-modules-core/android/build/.transforms/106bdabeb7df0188fafd010d1c9e0b9f/results.bin b/node_modules/expo-modules-core/android/build/.transforms/106bdabeb7df0188fafd010d1c9e0b9f/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/106bdabeb7df0188fafd010d1c9e0b9f/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-modules-core/android/build/.transforms/106bdabeb7df0188fafd010d1c9e0b9f/transformed/classes/classes.dex b/node_modules/expo-modules-core/android/build/.transforms/106bdabeb7df0188fafd010d1c9e0b9f/transformed/classes/classes.dex
new file mode 100644
index 0000000..33c304e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/106bdabeb7df0188fafd010d1c9e0b9f/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/results.bin b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/results.bin
new file mode 100644
index 0000000..9e53e72
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/results.bin
@@ -0,0 +1 @@
+o/release
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/desugar_graph.bin b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/desugar_graph.bin differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/BuildConfig.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/BuildConfig.dex
new file mode 100644
index 0000000..c2dd629
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/BuildConfig.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.dex
new file mode 100644
index 0000000..27af1f9
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.dex
new file mode 100644
index 0000000..c0f7ce3
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.dex
new file mode 100644
index 0000000..88492cf
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.dex
new file mode 100644
index 0000000..9b48c49
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.dex
new file mode 100644
index 0000000..10a83d6
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/camera/CameraViewInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/camera/CameraViewInterface.dex
new file mode 100644
index 0000000..7b6394f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/camera/CameraViewInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/constants/ConstantsInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/constants/ConstantsInterface.dex
new file mode 100644
index 0000000..a8f374e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/constants/ConstantsInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectionError.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectionError.dex
new file mode 100644
index 0000000..6904ed3
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectionError.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectionSkipped.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectionSkipped.dex
new file mode 100644
index 0000000..a557475
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectionSkipped.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.dex
new file mode 100644
index 0000000..1d2a416
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectorInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectorInterface.dex
new file mode 100644
index 0000000..f603dd2
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectorInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.dex
new file mode 100644
index 0000000..491541e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FacesDetectionCompleted.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FacesDetectionCompleted.dex
new file mode 100644
index 0000000..6ac1eff
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/facedetector/FacesDetectionCompleted.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.dex
new file mode 100644
index 0000000..891d217
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/filesystem/Permission.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/filesystem/Permission.dex
new file mode 100644
index 0000000..2e76f01
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/filesystem/Permission.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/font/FontManagerInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/font/FontManagerInterface.dex
new file mode 100644
index 0000000..adc6327
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/font/FontManagerInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.dex
new file mode 100644
index 0000000..fa47950
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/imageloader/ImageLoaderInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/imageloader/ImageLoaderInterface.dex
new file mode 100644
index 0000000..0ded5af
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/imageloader/ImageLoaderInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/Permissions.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/Permissions.dex
new file mode 100644
index 0000000..e40a91f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/Permissions.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsResponse$Companion.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsResponse$Companion.dex
new file mode 100644
index 0000000..7023793
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsResponse$Companion.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsResponse.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsResponse.dex
new file mode 100644
index 0000000..a3f37cb
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsResponse.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsResponseListener.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsResponseListener.dex
new file mode 100644
index 0000000..d586b01
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsResponseListener.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsStatus.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsStatus.dex
new file mode 100644
index 0000000..b43d49f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/permissions/PermissionsStatus.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/SensorServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/SensorServiceInterface.dex
new file mode 100644
index 0000000..ea765cd
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/SensorServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.dex
new file mode 100644
index 0000000..d57cfb0
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.dex
new file mode 100644
index 0000000..a49e1cb
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/BarometerServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/BarometerServiceInterface.dex
new file mode 100644
index 0000000..14f8ad4
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/BarometerServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.dex
new file mode 100644
index 0000000..0d629b8
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.dex
new file mode 100644
index 0000000..0cce4d3
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.dex
new file mode 100644
index 0000000..b75182c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.dex
new file mode 100644
index 0000000..dd0d2dc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.dex
new file mode 100644
index 0000000..5a6bf97
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/PedometerServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/PedometerServiceInterface.dex
new file mode 100644
index 0000000..da17f2c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/PedometerServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.dex
new file mode 100644
index 0000000..327d309
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskConsumer.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskConsumer.dex
new file mode 100644
index 0000000..75264ba
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskConsumer.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskConsumerInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskConsumerInterface.dex
new file mode 100644
index 0000000..dbef04d
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskConsumerInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskExecutionCallback.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskExecutionCallback.dex
new file mode 100644
index 0000000..6828943
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskExecutionCallback.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskInterface.dex
new file mode 100644
index 0000000..9a22a43
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskManagerInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskManagerInterface.dex
new file mode 100644
index 0000000..afd0e4a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskManagerInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.dex
new file mode 100644
index 0000000..2ffbf5b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskServiceInterface.dex b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskServiceInterface.dex
new file mode 100644
index 0000000..eb6cf52
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/1a63d7eaec28fb40cd20ed200f20e279/transformed/release/expo/modules/interfaces/taskManager/TaskServiceInterface.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/results.bin b/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/AndroidManifest.xml b/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..739f242
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/R.txt b/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/jars/classes.jar b/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..bdeb8f8
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/51feeabdabc333d62947d63b2c0e5a14/transformed/out/jars/classes.jar differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/results.bin b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/AndroidManifest.xml b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..739f242
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..d8f70c0
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,3 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
diff --git a/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
new file mode 100644
index 0000000..10972e7
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=expo-modules-core
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/R.txt b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/jars/classes.jar b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..2f089f9
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/67ee60b46a3e6f29b64d375c8459b780/transformed/out/jars/classes.jar differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/9074e9763fec2ab0ce21fbbb4205c9e6/results.bin b/node_modules/expo-modules-core/android/build/.transforms/9074e9763fec2ab0ce21fbbb4205c9e6/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/9074e9763fec2ab0ce21fbbb4205c9e6/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-modules-core/android/build/.transforms/9074e9763fec2ab0ce21fbbb4205c9e6/transformed/classes/classes.dex b/node_modules/expo-modules-core/android/build/.transforms/9074e9763fec2ab0ce21fbbb4205c9e6/transformed/classes/classes.dex
new file mode 100644
index 0000000..09066e9
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/9074e9763fec2ab0ce21fbbb4205c9e6/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/b5031462edb94e66b49cd7c5041bc0af/results.bin b/node_modules/expo-modules-core/android/build/.transforms/b5031462edb94e66b49cd7c5041bc0af/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/b5031462edb94e66b49cd7c5041bc0af/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/expo-modules-core/android/build/.transforms/c003d8656f55137809e8aa3cd611c217/results.bin b/node_modules/expo-modules-core/android/build/.transforms/c003d8656f55137809e8aa3cd611c217/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/c003d8656f55137809e8aa3cd611c217/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-modules-core/android/build/.transforms/c003d8656f55137809e8aa3cd611c217/transformed/classes/classes.dex b/node_modules/expo-modules-core/android/build/.transforms/c003d8656f55137809e8aa3cd611c217/transformed/classes/classes.dex
new file mode 100644
index 0000000..980efae
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/c003d8656f55137809e8aa3cd611c217/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-modules-core/android/build/.transforms/f9bef2159fae65ce7bff89deed75fcdf/results.bin b/node_modules/expo-modules-core/android/build/.transforms/f9bef2159fae65ce7bff89deed75fcdf/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/.transforms/f9bef2159fae65ce7bff89deed75fcdf/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-modules-core/android/build/.transforms/f9bef2159fae65ce7bff89deed75fcdf/transformed/classes/classes.dex b/node_modules/expo-modules-core/android/build/.transforms/f9bef2159fae65ce7bff89deed75fcdf/transformed/classes/classes.dex
new file mode 100644
index 0000000..bcb4eb3
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/.transforms/f9bef2159fae65ce7bff89deed75fcdf/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-modules-core/android/build/generated/source/buildConfig/debug/expo/modules/BuildConfig.java b/node_modules/expo-modules-core/android/build/generated/source/buildConfig/debug/expo/modules/BuildConfig.java
new file mode 100644
index 0000000..616a551
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/generated/source/buildConfig/debug/expo/modules/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package expo.modules;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "expo.modules";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/expo-modules-core/android/build/generated/source/buildConfig/release/expo/modules/BuildConfig.java b/node_modules/expo-modules-core/android/build/generated/source/buildConfig/release/expo/modules/BuildConfig.java
new file mode 100644
index 0000000..473dacb
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/generated/source/buildConfig/release/expo/modules/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package expo.modules;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "expo.modules";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..739f242
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..abb1565
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..739f242
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..4f3a4da
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aar_main_jar/release/classes.jar b/node_modules/expo-modules-core/android/build/intermediates/aar_main_jar/release/classes.jar
new file mode 100644
index 0000000..bdeb8f8
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/aar_main_jar/release/classes.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/expo-modules-core/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-modules-core/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/expo-modules-core/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-modules-core/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/expo-modules-core/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/expo-modules-core/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/annotations_typedef_file/release/typedefs.txt b/node_modules/expo-modules-core/android/build/intermediates/annotations_typedef_file/release/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-modules-core/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/expo-modules-core/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..5933013
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/expo-modules-core/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..107761e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/compile_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/expo-modules-core/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..bf9c303
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/expo-modules-core/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..bf9c303
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/compile_r_class_jar/release/R.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/expo-modules-core/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-modules-core/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/expo-modules-core/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-modules-core/android/build/intermediates/full_jar/release/full.jar b/node_modules/expo-modules-core/android/build/intermediates/full_jar/release/full.jar
new file mode 100644
index 0000000..9d1e33b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/full_jar/release/full.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..32563ad
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..7dccac8
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..539650d
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..6e3265c
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..7d2bd6c
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/intermediates/shader_assets/debug/out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/debug/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..ed842ca
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:34:32 ICT 2022
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/packageDebugResources/merger.xml b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageDebugResources/merger.xml
new file mode 100644
index 0000000..2d150b4
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/debug/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..dbf4674
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/intermediates/shader_assets/release/out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/release/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..1142a1e
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:15:39 ICT 2022
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/packageReleaseResources/merger.xml b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..50ced36
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/incremental/packageReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/release/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/incremental/release-mergeJavaRes/merge-state b/node_modules/expo-modules-core/android/build/intermediates/incremental/release-mergeJavaRes/merge-state
new file mode 100644
index 0000000..4b5646c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/incremental/release-mergeJavaRes/merge-state differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/BuildConfig.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/BuildConfig.class
new file mode 100644
index 0000000..3510bec
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/BuildConfig.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.class
new file mode 100644
index 0000000..63721a5
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.class
new file mode 100644
index 0000000..1720480
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.class
new file mode 100644
index 0000000..2d2591b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.class
new file mode 100644
index 0000000..a456e52
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.class
new file mode 100644
index 0000000..5fabe50
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/camera/CameraViewInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/camera/CameraViewInterface.class
new file mode 100644
index 0000000..c819c54
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/camera/CameraViewInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/constants/ConstantsInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/constants/ConstantsInterface.class
new file mode 100644
index 0000000..9e0741f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/constants/ConstantsInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectionError.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectionError.class
new file mode 100644
index 0000000..75c86db
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectionError.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectionSkipped.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectionSkipped.class
new file mode 100644
index 0000000..5510bf0
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectionSkipped.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.class
new file mode 100644
index 0000000..cee4f92
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectorInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectorInterface.class
new file mode 100644
index 0000000..84f4e74
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectorInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.class
new file mode 100644
index 0000000..4c4df00
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FacesDetectionCompleted.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FacesDetectionCompleted.class
new file mode 100644
index 0000000..719e696
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/facedetector/FacesDetectionCompleted.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.class
new file mode 100644
index 0000000..568472e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/filesystem/Permission.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/filesystem/Permission.class
new file mode 100644
index 0000000..00be8e8
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/filesystem/Permission.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/font/FontManagerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/font/FontManagerInterface.class
new file mode 100644
index 0000000..ce2f8a3
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/font/FontManagerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.class
new file mode 100644
index 0000000..a426eac
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface.class
new file mode 100644
index 0000000..c69165a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/permissions/Permissions.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/permissions/Permissions.class
new file mode 100644
index 0000000..b1735e5
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/permissions/Permissions.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/permissions/PermissionsResponseListener.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/permissions/PermissionsResponseListener.class
new file mode 100644
index 0000000..0386b50
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/permissions/PermissionsResponseListener.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/permissions/PermissionsStatus.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/permissions/PermissionsStatus.class
new file mode 100644
index 0000000..3400e36
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/permissions/PermissionsStatus.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/SensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/SensorServiceInterface.class
new file mode 100644
index 0000000..9c15ecc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/SensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.class
new file mode 100644
index 0000000..819527b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.class
new file mode 100644
index 0000000..45c6798
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/BarometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/BarometerServiceInterface.class
new file mode 100644
index 0000000..5c6d7d6
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/BarometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.class
new file mode 100644
index 0000000..403221d
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.class
new file mode 100644
index 0000000..a9d0deb
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.class
new file mode 100644
index 0000000..4c53966
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.class
new file mode 100644
index 0000000..c406486
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.class
new file mode 100644
index 0000000..ebde4c7
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/PedometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/PedometerServiceInterface.class
new file mode 100644
index 0000000..6f5c557
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/PedometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.class
new file mode 100644
index 0000000..0e9e320
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskConsumer.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskConsumer.class
new file mode 100644
index 0000000..09502e7
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskConsumer.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskConsumerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskConsumerInterface.class
new file mode 100644
index 0000000..bf3042f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskConsumerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskExecutionCallback.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskExecutionCallback.class
new file mode 100644
index 0000000..a22e7e2
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskExecutionCallback.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskInterface.class
new file mode 100644
index 0000000..e70436d
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskManagerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskManagerInterface.class
new file mode 100644
index 0000000..b90c5db
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskManagerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.class
new file mode 100644
index 0000000..17dd2ac
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskServiceInterface.class
new file mode 100644
index 0000000..e58df7e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/debug/classes/expo/modules/interfaces/taskManager/TaskServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/BuildConfig.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/BuildConfig.class
new file mode 100644
index 0000000..025ddc4
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/BuildConfig.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.class
new file mode 100644
index 0000000..63721a5
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.class
new file mode 100644
index 0000000..1720480
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.class
new file mode 100644
index 0000000..2d2591b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.class
new file mode 100644
index 0000000..a456e52
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.class
new file mode 100644
index 0000000..5fabe50
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/camera/CameraViewInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/camera/CameraViewInterface.class
new file mode 100644
index 0000000..c819c54
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/camera/CameraViewInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/constants/ConstantsInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/constants/ConstantsInterface.class
new file mode 100644
index 0000000..9e0741f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/constants/ConstantsInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectionError.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectionError.class
new file mode 100644
index 0000000..75c86db
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectionError.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectionSkipped.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectionSkipped.class
new file mode 100644
index 0000000..5510bf0
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectionSkipped.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.class
new file mode 100644
index 0000000..cee4f92
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectorInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectorInterface.class
new file mode 100644
index 0000000..84f4e74
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectorInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.class
new file mode 100644
index 0000000..4c4df00
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FacesDetectionCompleted.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FacesDetectionCompleted.class
new file mode 100644
index 0000000..719e696
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/facedetector/FacesDetectionCompleted.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.class
new file mode 100644
index 0000000..568472e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/filesystem/Permission.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/filesystem/Permission.class
new file mode 100644
index 0000000..00be8e8
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/filesystem/Permission.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/font/FontManagerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/font/FontManagerInterface.class
new file mode 100644
index 0000000..ce2f8a3
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/font/FontManagerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.class
new file mode 100644
index 0000000..a426eac
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface.class
new file mode 100644
index 0000000..c69165a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/imageloader/ImageLoaderInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/permissions/Permissions.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/permissions/Permissions.class
new file mode 100644
index 0000000..b1735e5
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/permissions/Permissions.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/permissions/PermissionsResponseListener.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/permissions/PermissionsResponseListener.class
new file mode 100644
index 0000000..0386b50
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/permissions/PermissionsResponseListener.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/permissions/PermissionsStatus.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/permissions/PermissionsStatus.class
new file mode 100644
index 0000000..3400e36
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/permissions/PermissionsStatus.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/SensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/SensorServiceInterface.class
new file mode 100644
index 0000000..9c15ecc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/SensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.class
new file mode 100644
index 0000000..819527b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.class
new file mode 100644
index 0000000..45c6798
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/BarometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/BarometerServiceInterface.class
new file mode 100644
index 0000000..5c6d7d6
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/BarometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.class
new file mode 100644
index 0000000..403221d
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.class
new file mode 100644
index 0000000..a9d0deb
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.class
new file mode 100644
index 0000000..4c53966
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.class
new file mode 100644
index 0000000..c406486
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.class
new file mode 100644
index 0000000..ebde4c7
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/PedometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/PedometerServiceInterface.class
new file mode 100644
index 0000000..6f5c557
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/PedometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.class
new file mode 100644
index 0000000..0e9e320
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskConsumer.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskConsumer.class
new file mode 100644
index 0000000..09502e7
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskConsumer.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskConsumerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskConsumerInterface.class
new file mode 100644
index 0000000..bf3042f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskConsumerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskExecutionCallback.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskExecutionCallback.class
new file mode 100644
index 0000000..a22e7e2
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskExecutionCallback.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskInterface.class
new file mode 100644
index 0000000..e70436d
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskManagerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskManagerInterface.class
new file mode 100644
index 0000000..b90c5db
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskManagerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.class
new file mode 100644
index 0000000..17dd2ac
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskServiceInterface.class
new file mode 100644
index 0000000..e58df7e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/javac/release/classes/expo/modules/interfaces/taskManager/TaskServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/library_java_res/debug/res.jar b/node_modules/expo-modules-core/android/build/intermediates/library_java_res/debug/res.jar
new file mode 100644
index 0000000..d756063
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/library_java_res/debug/res.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/library_java_res/release/res.jar b/node_modules/expo-modules-core/android/build/intermediates/library_java_res/release/res.jar
new file mode 100644
index 0000000..e08fb5a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/library_java_res/release/res.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties b/node_modules/expo-modules-core/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
new file mode 100644
index 0000000..10972e7
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=expo-modules-core
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/local_aar_for_lint/release/out.aar b/node_modules/expo-modules-core/android/build/intermediates/local_aar_for_lint/release/out.aar
new file mode 100644
index 0000000..04f7b87
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/local_aar_for_lint/release/out.aar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/expo-modules-core/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/expo-modules-core/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/expo-modules-core/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/expo-modules-core/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/expo-modules-core/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..49bf418
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="expo.modules" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/expo-modules-core/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/expo-modules-core/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..49bf418
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="expo.modules" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/expo-modules-core/android/build/intermediates/merged_java_res/release/feature-expo-modules-core.jar b/node_modules/expo-modules-core/android/build/intermediates/merged_java_res/release/feature-expo-modules-core.jar
new file mode 100644
index 0000000..d43264e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/merged_java_res/release/feature-expo-modules-core.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..739f242
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..739f242
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/expo-modules-core/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/expo-modules-core/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/expo-modules-core/android/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..00e1ce3
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/packaged_manifests/release/output-metadata.json b/node_modules/expo-modules-core/android/build/intermediates/packaged_manifests/release/output-metadata.json
new file mode 100644
index 0000000..64bd400
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/packaged_manifests/release/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/release/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/META-INF/expo-modules-core_release.kotlin_module b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/META-INF/expo-modules-core_release.kotlin_module
new file mode 100644
index 0000000..3a4e3bf
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/META-INF/expo-modules-core_release.kotlin_module differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/BuildConfig.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/BuildConfig.class
new file mode 100644
index 0000000..bfe334b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/BuildConfig.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.class
new file mode 100644
index 0000000..787582a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.class
new file mode 100644
index 0000000..ca100c5
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.class
new file mode 100644
index 0000000..00f07b2
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerResult.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.class
new file mode 100644
index 0000000..21db7b4
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.class
new file mode 100644
index 0000000..f8f330e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/camera/CameraViewInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/camera/CameraViewInterface.class
new file mode 100644
index 0000000..a73e992
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/camera/CameraViewInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/constants/ConstantsInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/constants/ConstantsInterface.class
new file mode 100644
index 0000000..15c8371
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/constants/ConstantsInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectionError.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectionError.class
new file mode 100644
index 0000000..9d98490
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectionError.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectionSkipped.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectionSkipped.class
new file mode 100644
index 0000000..c86191c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectionSkipped.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.class
new file mode 100644
index 0000000..7919c6b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectorInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectorInterface.class
new file mode 100644
index 0000000..e9b837c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectorInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.class
new file mode 100644
index 0000000..8abfc58
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FacesDetectionCompleted.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FacesDetectionCompleted.class
new file mode 100644
index 0000000..9e30323
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/facedetector/FacesDetectionCompleted.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.class
new file mode 100644
index 0000000..c3939ab
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/filesystem/FilePermissionModuleInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/filesystem/Permission.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/filesystem/Permission.class
new file mode 100644
index 0000000..889b66f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/filesystem/Permission.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/font/FontManagerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/font/FontManagerInterface.class
new file mode 100644
index 0000000..8930d85
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/font/FontManagerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.class
new file mode 100644
index 0000000..528a8f2
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/imageloader/ImageLoaderInterface$ResultListener.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/imageloader/ImageLoaderInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/imageloader/ImageLoaderInterface.class
new file mode 100644
index 0000000..494003c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/imageloader/ImageLoaderInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/Permissions.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/Permissions.class
new file mode 100644
index 0000000..0a9a6e5
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/Permissions.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsResponse$Companion.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsResponse$Companion.class
new file mode 100644
index 0000000..fa84d19
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsResponse$Companion.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsResponse.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsResponse.class
new file mode 100644
index 0000000..8b0e288
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsResponse.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsResponseListener.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsResponseListener.class
new file mode 100644
index 0000000..bf507f3
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsResponseListener.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsStatus.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsStatus.class
new file mode 100644
index 0000000..95bd61f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/permissions/PermissionsStatus.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/SensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/SensorServiceInterface.class
new file mode 100644
index 0000000..f445422
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/SensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.class
new file mode 100644
index 0000000..7e46457
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.class
new file mode 100644
index 0000000..0f8f900
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/BarometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/BarometerServiceInterface.class
new file mode 100644
index 0000000..abbab12
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/BarometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.class
new file mode 100644
index 0000000..8e73583
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.class
new file mode 100644
index 0000000..37abef8
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.class
new file mode 100644
index 0000000..f74c51a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.class
new file mode 100644
index 0000000..089378f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.class
new file mode 100644
index 0000000..c830759
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/PedometerServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/PedometerServiceInterface.class
new file mode 100644
index 0000000..a255de6
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/PedometerServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.class
new file mode 100644
index 0000000..38d8a1b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskConsumer.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskConsumer.class
new file mode 100644
index 0000000..8ce0132
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskConsumer.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskConsumerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskConsumerInterface.class
new file mode 100644
index 0000000..2d24b38
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskConsumerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskExecutionCallback.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskExecutionCallback.class
new file mode 100644
index 0000000..e1c08a6
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskExecutionCallback.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskInterface.class
new file mode 100644
index 0000000..0b0ab37
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskManagerInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskManagerInterface.class
new file mode 100644
index 0000000..eb3d144
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskManagerInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.class
new file mode 100644
index 0000000..77ed295
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskServiceInterface.class b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskServiceInterface.class
new file mode 100644
index 0000000..32bc118
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/interfaces/taskManager/TaskServiceInterface.class differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..9e5e849
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..663301f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/intermediates/runtime_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/expo-modules-core/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/expo-modules-core/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..eeeb776
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1 @@
+expo.modules
diff --git a/node_modules/expo-modules-core/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/expo-modules-core/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..eeeb776
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1 @@
+expo.modules
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/build-history.bin b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/build-history.bin
new file mode 100644
index 0000000..07f1010
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/build-history.bin differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream
new file mode 100644
index 0000000..cb12809
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len
new file mode 100644
index 0000000..762b014
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.values.at
new file mode 100644
index 0000000..99ab39a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i
new file mode 100644
index 0000000..78cec6a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab
new file mode 100644
index 0000000..44aa9f1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
new file mode 100644
index 0000000..2a310bc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
new file mode 100644
index 0000000..faaff93
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
new file mode 100644
index 0000000..fc0e221
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i
new file mode 100644
index 0000000..85434cc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
new file mode 100644
index 0000000..ec34a63
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
new file mode 100644
index 0000000..2a310bc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..faaff93
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
new file mode 100644
index 0000000..747b4ec
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
new file mode 100644
index 0000000..85434cc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab
new file mode 100644
index 0000000..ce95448
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream
new file mode 100644
index 0000000..94c362f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream.len
new file mode 100644
index 0000000..748b0e7
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.values.at
new file mode 100644
index 0000000..18caf9e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab_i
new file mode 100644
index 0000000..8b5f8b0
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/constants.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab
new file mode 100644
index 0000000..ec34a63
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
new file mode 100644
index 0000000..bc2a19c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..faaff93
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
new file mode 100644
index 0000000..747b4ec
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
new file mode 100644
index 0000000..6053f08
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab
new file mode 100644
index 0000000..1511e4b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream
new file mode 100644
index 0000000..eb3a5cd
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len
new file mode 100644
index 0000000..701a27c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at
new file mode 100644
index 0000000..58660ea
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i
new file mode 100644
index 0000000..4193bbf
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
new file mode 100644
index 0000000..cb12809
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
new file mode 100644
index 0000000..762b014
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
new file mode 100644
index 0000000..3b37e11
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i
new file mode 100644
index 0000000..78cec6a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/counters.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/counters.tab
new file mode 100644
index 0000000..166c057
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/counters.tab
@@ -0,0 +1,2 @@
+1
+0
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream
new file mode 100644
index 0000000..cb12809
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len
new file mode 100644
index 0000000..762b014
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.values.at
new file mode 100644
index 0000000..5875372
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i
new file mode 100644
index 0000000..dd66c37
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab
new file mode 100644
index 0000000..8aad32b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream
new file mode 100644
index 0000000..08e7df1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len
new file mode 100644
index 0000000..b7da01d
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.values.at
new file mode 100644
index 0000000..929716e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab
new file mode 100644
index 0000000..cf860fa
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream
new file mode 100644
index 0000000..b626246
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream.len
new file mode 100644
index 0000000..8143849
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.len
new file mode 100644
index 0000000..eb0b8a0
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.values.at
new file mode 100644
index 0000000..886e38e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i
new file mode 100644
index 0000000..bca4b47
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/last-build.bin b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/last-build.bin
new file mode 100644
index 0000000..bc1ba54
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileDebugKotlin/last-build.bin differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/build-history.bin b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/build-history.bin
new file mode 100644
index 0000000..bf20056
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/build-history.bin differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream
new file mode 100644
index 0000000..cb12809
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len
new file mode 100644
index 0000000..762b014
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.values.at
new file mode 100644
index 0000000..1b31a6b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i
new file mode 100644
index 0000000..78cec6a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab
new file mode 100644
index 0000000..44aa9f1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
new file mode 100644
index 0000000..2a310bc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
new file mode 100644
index 0000000..faaff93
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
new file mode 100644
index 0000000..fc0e221
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i
new file mode 100644
index 0000000..85434cc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
new file mode 100644
index 0000000..ec34a63
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
new file mode 100644
index 0000000..2a310bc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..faaff93
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
new file mode 100644
index 0000000..747b4ec
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
new file mode 100644
index 0000000..85434cc
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab
new file mode 100644
index 0000000..ce95448
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream
new file mode 100644
index 0000000..94c362f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream.len
new file mode 100644
index 0000000..748b0e7
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.values.at
new file mode 100644
index 0000000..18caf9e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab_i
new file mode 100644
index 0000000..8b5f8b0
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/constants.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab
new file mode 100644
index 0000000..ec34a63
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
new file mode 100644
index 0000000..bc2a19c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..faaff93
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
new file mode 100644
index 0000000..747b4ec
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
new file mode 100644
index 0000000..6053f08
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab
new file mode 100644
index 0000000..11b8630
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream
new file mode 100644
index 0000000..eb3a5cd
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len
new file mode 100644
index 0000000..701a27c
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at
new file mode 100644
index 0000000..eeb3f75
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i
new file mode 100644
index 0000000..4193bbf
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
new file mode 100644
index 0000000..cb12809
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
new file mode 100644
index 0000000..762b014
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
new file mode 100644
index 0000000..3b37e11
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i
new file mode 100644
index 0000000..78cec6a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/counters.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/counters.tab
new file mode 100644
index 0000000..166c057
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/counters.tab
@@ -0,0 +1,2 @@
+1
+0
\ No newline at end of file
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream
new file mode 100644
index 0000000..cb12809
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len
new file mode 100644
index 0000000..762b014
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.values.at
new file mode 100644
index 0000000..5875372
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i
new file mode 100644
index 0000000..dd66c37
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab
new file mode 100644
index 0000000..8aad32b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream
new file mode 100644
index 0000000..08e7df1
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len
new file mode 100644
index 0000000..b7da01d
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.values.at
new file mode 100644
index 0000000..929716e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab
new file mode 100644
index 0000000..cf860fa
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream
new file mode 100644
index 0000000..b626246
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream.len
new file mode 100644
index 0000000..8143849
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.len
new file mode 100644
index 0000000..eb0b8a0
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.values.at b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.values.at
new file mode 100644
index 0000000..886e38e
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.values.at differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i
new file mode 100644
index 0000000..bca4b47
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i.len b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i.len differ
diff --git a/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/last-build.bin b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/last-build.bin
new file mode 100644
index 0000000..7f582e0
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/kotlin/compileReleaseKotlin/last-build.bin differ
diff --git a/node_modules/expo-modules-core/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/expo-modules-core/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..eaab481
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:1-3:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:1-3:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:1-3:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:11-33
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:1-3:12
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
diff --git a/node_modules/expo-modules-core/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/expo-modules-core/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..eaab481
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:1-3:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:1-3:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:1-3:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:11-33
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml:1:1-3:12
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-modules-core/android/src/main/AndroidManifest.xml
diff --git a/node_modules/expo-modules-core/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/expo-modules-core/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..f57247f
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/expo-modules-core/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin b/node_modules/expo-modules-core/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..6eca410
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/expo-modules-core/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt b/node_modules/expo-modules-core/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
new file mode 100644
index 0000000..8859964
--- /dev/null
+++ b/node_modules/expo-modules-core/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
@@ -0,0 +1,79 @@
+expo/modules/interfaces/taskManager/TaskServiceInterface.java
+ expo.modules.interfaces.taskManager.TaskServiceInterface
+expo/modules/interfaces/constants/ConstantsInterface.java
+ expo.modules.interfaces.constants.ConstantsInterface
+expo/modules/interfaces/sensors/services/AccelerometerServiceInterface.java
+ expo.modules.interfaces.sensors.services.AccelerometerServiceInterface
+expo/modules/interfaces/facedetector/FaceDetectionSkipped.java
+ expo.modules.interfaces.facedetector.FaceDetectionSkipped
+expo/modules/interfaces/barcodescanner/BarCodeScannerProviderInterface.java
+ expo.modules.interfaces.barcodescanner.BarCodeScannerProviderInterface
+expo/modules/interfaces/facedetector/FaceDetectionUnspecifiedError.java
+ expo.modules.interfaces.facedetector.FaceDetectionUnspecifiedError
+expo/modules/interfaces/facedetector/FaceDetectionError.java
+ expo.modules.interfaces.facedetector.FaceDetectionError
+expo/modules/interfaces/taskManager/TaskInterface.java
+ expo.modules.interfaces.taskManager.TaskInterface
+expo/modules/interfaces/imageloader/ImageLoaderInterface.java
+ expo.modules.interfaces.imageloader.ImageLoaderInterface
+ expo.modules.interfaces.imageloader.ImageLoaderInterface$ResultListener
+expo/modules/interfaces/permissions/PermissionsResponseListener.java
+ expo.modules.interfaces.permissions.PermissionsResponseListener
+expo/modules/interfaces/taskManager/TaskManagerInterface.java
+ expo.modules.interfaces.taskManager.TaskManagerInterface
+expo/modules/interfaces/sensors/SensorServiceSubscriptionInterface.java
+ expo.modules.interfaces.sensors.SensorServiceSubscriptionInterface
+expo/modules/interfaces/barcodescanner/BarCodeScannerResult.java
+ expo.modules.interfaces.barcodescanner.BarCodeScannerResult
+expo/modules/interfaces/taskManager/TaskManagerUtilsInterface.java
+ expo.modules.interfaces.taskManager.TaskManagerUtilsInterface
+expo/modules/interfaces/sensors/services/GravitySensorServiceInterface.java
+ expo.modules.interfaces.sensors.services.GravitySensorServiceInterface
+expo/modules/interfaces/sensors/SensorServiceInterface.java
+ expo.modules.interfaces.sensors.SensorServiceInterface
+expo/modules/interfaces/facedetector/FaceDetectorProviderInterface.java
+ expo.modules.interfaces.facedetector.FaceDetectorProviderInterface
+expo/modules/interfaces/taskManager/TaskConsumerInterface.java
+ expo.modules.interfaces.taskManager.TaskConsumerInterface
+expo/modules/interfaces/facedetector/FacesDetectionCompleted.java
+ expo.modules.interfaces.facedetector.FacesDetectionCompleted
+expo/modules/interfaces/barcodescanner/BarCodeScannerInterface.java
+ expo.modules.interfaces.barcodescanner.BarCodeScannerInterface
+expo/modules/interfaces/sensors/services/PedometerServiceInterface.java
+ expo.modules.interfaces.sensors.services.PedometerServiceInterface
+expo/modules/interfaces/filesystem/Permission.java
+ expo.modules.interfaces.filesystem.Permission
+expo/modules/interfaces/barcodescanner/BarCodeScannerSettingsKey.java
+ expo.modules.interfaces.barcodescanner.BarCodeScannerSettingsKey
+expo/modules/interfaces/sensors/services/BarometerServiceInterface.java
+ expo.modules.interfaces.sensors.services.BarometerServiceInterface
+expo/modules/interfaces/camera/CameraViewInterface.java
+ expo.modules.interfaces.camera.CameraViewInterface
+expo/modules/interfaces/barcodescanner/BarCodeScannerSettings.java
+ expo.modules.interfaces.barcodescanner.BarCodeScannerSettings
+expo/modules/interfaces/taskManager/TaskConsumer.java
+ expo.modules.interfaces.taskManager.TaskConsumer
+expo/modules/interfaces/sensors/services/GyroscopeServiceInterface.java
+ expo.modules.interfaces.sensors.services.GyroscopeServiceInterface
+expo/modules/interfaces/font/FontManagerInterface.java
+ expo.modules.interfaces.font.FontManagerInterface
+expo/modules/interfaces/filesystem/FilePermissionModuleInterface.java
+ expo.modules.interfaces.filesystem.FilePermissionModuleInterface
+expo/modules/interfaces/facedetector/FaceDetectorInterface.java
+ expo.modules.interfaces.facedetector.FaceDetectorInterface
+expo/modules/interfaces/sensors/services/RotationVectorSensorServiceInterface.java
+ expo.modules.interfaces.sensors.services.RotationVectorSensorServiceInterface
+expo/modules/interfaces/taskManager/TaskExecutionCallback.java
+ expo.modules.interfaces.taskManager.TaskExecutionCallback
+expo/modules/interfaces/permissions/Permissions.java
+ expo.modules.interfaces.permissions.Permissions
+expo/modules/interfaces/sensors/services/MagnetometerUncalibratedServiceInterface.java
+ expo.modules.interfaces.sensors.services.MagnetometerUncalibratedServiceInterface
+expo/modules/interfaces/permissions/PermissionsStatus.java
+ expo.modules.interfaces.permissions.PermissionsStatus
+expo/modules/BuildConfig.java
+ expo.modules.BuildConfig
+expo/modules/interfaces/sensors/services/MagnetometerServiceInterface.java
+ expo.modules.interfaces.sensors.services.MagnetometerServiceInterface
+expo/modules/interfaces/sensors/services/LinearAccelerationSensorServiceInterface.java
+ expo.modules.interfaces.sensors.services.LinearAccelerationSensorServiceInterface
diff --git a/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/debug/META-INF/expo-modules-core_debug.kotlin_module b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/debug/META-INF/expo-modules-core_debug.kotlin_module
new file mode 100644
index 0000000..3a4e3bf
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/debug/META-INF/expo-modules-core_debug.kotlin_module differ
diff --git a/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/debug/expo/modules/interfaces/permissions/PermissionsResponse$Companion.class b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/debug/expo/modules/interfaces/permissions/PermissionsResponse$Companion.class
new file mode 100644
index 0000000..25855d2
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/debug/expo/modules/interfaces/permissions/PermissionsResponse$Companion.class differ
diff --git a/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/debug/expo/modules/interfaces/permissions/PermissionsResponse.class b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/debug/expo/modules/interfaces/permissions/PermissionsResponse.class
new file mode 100644
index 0000000..3baaa3b
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/debug/expo/modules/interfaces/permissions/PermissionsResponse.class differ
diff --git a/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/release/META-INF/expo-modules-core_release.kotlin_module b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/release/META-INF/expo-modules-core_release.kotlin_module
new file mode 100644
index 0000000..3a4e3bf
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/release/META-INF/expo-modules-core_release.kotlin_module differ
diff --git a/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/release/expo/modules/interfaces/permissions/PermissionsResponse$Companion.class b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/release/expo/modules/interfaces/permissions/PermissionsResponse$Companion.class
new file mode 100644
index 0000000..fa84d19
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/release/expo/modules/interfaces/permissions/PermissionsResponse$Companion.class differ
diff --git a/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/release/expo/modules/interfaces/permissions/PermissionsResponse.class b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/release/expo/modules/interfaces/permissions/PermissionsResponse.class
new file mode 100644
index 0000000..8b0e288
Binary files /dev/null and b/node_modules/expo-modules-core/android/build/tmp/kotlin-classes/release/expo/modules/interfaces/permissions/PermissionsResponse.class differ
