diff --git a/node_modules/@gregfrench/react-native-wheel-picker/WheelCurvedPicker.android.js b/node_modules/@gregfrench/react-native-wheel-picker/WheelCurvedPicker.android.js
index c4d3f46..1ebbef4 100644
--- a/node_modules/@gregfrench/react-native-wheel-picker/WheelCurvedPicker.android.js
+++ b/node_modules/@gregfrench/react-native-wheel-picker/WheelCurvedPicker.android.js
@@ -3,9 +3,9 @@
 import React from 'react';
 import PropTypes from 'prop-types'
 import {
-	ColorPropType,
 	requireNativeComponent,
 } from 'react-native';
+import {ColorPropType} from 'deprecated-react-native-prop-types'
 
 const WheelCurvedPickerNativeInterface = {
 	name: 'WheelCurvedPicker',
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/.DS_Store b/node_modules/@gregfrench/react-native-wheel-picker/android/.DS_Store
new file mode 100644
index 0000000..98dccb0
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/.DS_Store differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build.gradle b/node_modules/@gregfrench/react-native-wheel-picker/android/build.gradle
index b8e969a..baf1640 100644
--- a/node_modules/@gregfrench/react-native-wheel-picker/android/build.gradle
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build.gradle
@@ -16,6 +16,6 @@ android {
 
 dependencies {
     implementation fileTree(dir: 'libs', include: ['*.jar'])
-    implementation "cn.aigestudio.wheelpicker:WheelPicker:1.0.3"
+//    implementation "cn.aigestudio.wheelpicker:WheelPicker:1.0.3"
     implementation 'com.facebook.react:react-native:+'
 }
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/.transforms/05f306d0b5f2b30fe5f150d49caadb23/results.bin b/node_modules/@gregfrench/react-native-wheel-picker/android/build/.transforms/05f306d0b5f2b30fe5f150d49caadb23/results.bin
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/generated/source/buildConfig/release/com/zyu/BuildConfig.java b/node_modules/@gregfrench/react-native-wheel-picker/android/build/generated/source/buildConfig/release/com/zyu/BuildConfig.java
new file mode 100644
index 0000000..9bf934e
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/generated/source/buildConfig/release/com/zyu/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.zyu;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "com.zyu";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..2d9a640
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.zyu" >
+
+    <uses-sdk
+        android:minSdkVersion="25"
+        android:targetSdkVersion="28" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..7bd0885
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.zyu",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..ef6afcf
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compile_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..2bdaeb2
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compile_r_class_jar/release/R.jar differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..2d790cf
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compile_symbol_list/release/R.txt
@@ -0,0 +1,1994 @@
+int anim abc_fade_in 0x0
+int anim abc_fade_out 0x0
+int anim abc_grow_fade_in_from_bottom 0x0
+int anim abc_popup_enter 0x0
+int anim abc_popup_exit 0x0
+int anim abc_shrink_fade_out_from_bottom 0x0
+int anim abc_slide_in_bottom 0x0
+int anim abc_slide_in_top 0x0
+int anim abc_slide_out_bottom 0x0
+int anim abc_slide_out_top 0x0
+int anim abc_tooltip_enter 0x0
+int anim abc_tooltip_exit 0x0
+int anim btn_checkbox_to_checked_box_inner_merged_animation 0x0
+int anim btn_checkbox_to_checked_box_outer_merged_animation 0x0
+int anim btn_checkbox_to_checked_icon_null_animation 0x0
+int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x0
+int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x0
+int anim btn_checkbox_to_unchecked_icon_null_animation 0x0
+int anim btn_radio_to_off_mtrl_dot_group_animation 0x0
+int anim btn_radio_to_off_mtrl_ring_outer_animation 0x0
+int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x0
+int anim btn_radio_to_on_mtrl_dot_group_animation 0x0
+int anim btn_radio_to_on_mtrl_ring_outer_animation 0x0
+int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x0
+int anim catalyst_fade_in 0x0
+int anim catalyst_fade_out 0x0
+int anim catalyst_push_up_in 0x0
+int anim catalyst_push_up_out 0x0
+int anim catalyst_slide_down 0x0
+int anim catalyst_slide_up 0x0
+int anim fragment_fast_out_extra_slow_in 0x0
+int animator fragment_close_enter 0x0
+int animator fragment_close_exit 0x0
+int animator fragment_fade_enter 0x0
+int animator fragment_fade_exit 0x0
+int animator fragment_open_enter 0x0
+int animator fragment_open_exit 0x0
+int array WheelArrayConstellation 0x0
+int array WheelArrayDefault 0x0
+int array WheelArrayZodiac 0x0
+int attr actionBarDivider 0x0
+int attr actionBarItemBackground 0x0
+int attr actionBarPopupTheme 0x0
+int attr actionBarSize 0x0
+int attr actionBarSplitStyle 0x0
+int attr actionBarStyle 0x0
+int attr actionBarTabBarStyle 0x0
+int attr actionBarTabStyle 0x0
+int attr actionBarTabTextStyle 0x0
+int attr actionBarTheme 0x0
+int attr actionBarWidgetTheme 0x0
+int attr actionButtonStyle 0x0
+int attr actionDropDownStyle 0x0
+int attr actionLayout 0x0
+int attr actionMenuTextAppearance 0x0
+int attr actionMenuTextColor 0x0
+int attr actionModeBackground 0x0
+int attr actionModeCloseButtonStyle 0x0
+int attr actionModeCloseContentDescription 0x0
+int attr actionModeCloseDrawable 0x0
+int attr actionModeCopyDrawable 0x0
+int attr actionModeCutDrawable 0x0
+int attr actionModeFindDrawable 0x0
+int attr actionModePasteDrawable 0x0
+int attr actionModePopupWindowStyle 0x0
+int attr actionModeSelectAllDrawable 0x0
+int attr actionModeShareDrawable 0x0
+int attr actionModeSplitBackground 0x0
+int attr actionModeStyle 0x0
+int attr actionModeTheme 0x0
+int attr actionModeWebSearchDrawable 0x0
+int attr actionOverflowButtonStyle 0x0
+int attr actionOverflowMenuStyle 0x0
+int attr actionProviderClass 0x0
+int attr actionViewClass 0x0
+int attr activityChooserViewStyle 0x0
+int attr actualImageResource 0x0
+int attr actualImageScaleType 0x0
+int attr actualImageUri 0x0
+int attr alertDialogButtonGroupStyle 0x0
+int attr alertDialogCenterButtons 0x0
+int attr alertDialogStyle 0x0
+int attr alertDialogTheme 0x0
+int attr allowStacking 0x0
+int attr alpha 0x0
+int attr alphabeticModifiers 0x0
+int attr arrowHeadLength 0x0
+int attr arrowShaftLength 0x0
+int attr autoCompleteTextViewStyle 0x0
+int attr autoSizeMaxTextSize 0x0
+int attr autoSizeMinTextSize 0x0
+int attr autoSizePresetSizes 0x0
+int attr autoSizeStepGranularity 0x0
+int attr autoSizeTextType 0x0
+int attr autofillInlineSuggestionChip 0x0
+int attr autofillInlineSuggestionEndIconStyle 0x0
+int attr autofillInlineSuggestionStartIconStyle 0x0
+int attr autofillInlineSuggestionSubtitle 0x0
+int attr autofillInlineSuggestionTitle 0x0
+int attr background 0x0
+int attr backgroundImage 0x0
+int attr backgroundSplit 0x0
+int attr backgroundStacked 0x0
+int attr backgroundTint 0x0
+int attr backgroundTintMode 0x0
+int attr barLength 0x0
+int attr borderlessButtonStyle 0x0
+int attr buttonBarButtonStyle 0x0
+int attr buttonBarNegativeButtonStyle 0x0
+int attr buttonBarNeutralButtonStyle 0x0
+int attr buttonBarPositiveButtonStyle 0x0
+int attr buttonBarStyle 0x0
+int attr buttonCompat 0x0
+int attr buttonGravity 0x0
+int attr buttonIconDimen 0x0
+int attr buttonPanelSideLayout 0x0
+int attr buttonStyle 0x0
+int attr buttonStyleSmall 0x0
+int attr buttonTint 0x0
+int attr buttonTintMode 0x0
+int attr checkMarkCompat 0x0
+int attr checkMarkTint 0x0
+int attr checkMarkTintMode 0x0
+int attr checkboxStyle 0x0
+int attr checkedTextViewStyle 0x0
+int attr closeIcon 0x0
+int attr closeItemLayout 0x0
+int attr collapseContentDescription 0x0
+int attr collapseIcon 0x0
+int attr color 0x0
+int attr colorAccent 0x0
+int attr colorBackgroundFloating 0x0
+int attr colorButtonNormal 0x0
+int attr colorControlActivated 0x0
+int attr colorControlHighlight 0x0
+int attr colorControlNormal 0x0
+int attr colorError 0x0
+int attr colorPrimary 0x0
+int attr colorPrimaryDark 0x0
+int attr colorSwitchThumbNormal 0x0
+int attr commitIcon 0x0
+int attr contentDescription 0x0
+int attr contentInsetEnd 0x0
+int attr contentInsetEndWithActions 0x0
+int attr contentInsetLeft 0x0
+int attr contentInsetRight 0x0
+int attr contentInsetStart 0x0
+int attr contentInsetStartWithNavigation 0x0
+int attr controlBackground 0x0
+int attr customNavigationLayout 0x0
+int attr defaultQueryHint 0x0
+int attr dialogCornerRadius 0x0
+int attr dialogPreferredPadding 0x0
+int attr dialogTheme 0x0
+int attr displayOptions 0x0
+int attr divider 0x0
+int attr dividerHorizontal 0x0
+int attr dividerPadding 0x0
+int attr dividerVertical 0x0
+int attr drawableBottomCompat 0x0
+int attr drawableEndCompat 0x0
+int attr drawableLeftCompat 0x0
+int attr drawableRightCompat 0x0
+int attr drawableSize 0x0
+int attr drawableStartCompat 0x0
+int attr drawableTint 0x0
+int attr drawableTintMode 0x0
+int attr drawableTopCompat 0x0
+int attr drawerArrowStyle 0x0
+int attr dropDownListViewStyle 0x0
+int attr dropdownListPreferredItemHeight 0x0
+int attr editTextBackground 0x0
+int attr editTextColor 0x0
+int attr editTextStyle 0x0
+int attr elevation 0x0
+int attr emojiCompatEnabled 0x0
+int attr expandActivityOverflowButtonDrawable 0x0
+int attr fadeDuration 0x0
+int attr failureImage 0x0
+int attr failureImageScaleType 0x0
+int attr firstBaselineToTopHeight 0x0
+int attr font 0x0
+int attr fontFamily 0x0
+int attr fontProviderAuthority 0x0
+int attr fontProviderCerts 0x0
+int attr fontProviderFetchStrategy 0x0
+int attr fontProviderFetchTimeout 0x0
+int attr fontProviderPackage 0x0
+int attr fontProviderQuery 0x0
+int attr fontProviderSystemFontFamily 0x0
+int attr fontStyle 0x0
+int attr fontVariationSettings 0x0
+int attr fontWeight 0x0
+int attr gapBetweenBars 0x0
+int attr goIcon 0x0
+int attr height 0x0
+int attr hideOnContentScroll 0x0
+int attr homeAsUpIndicator 0x0
+int attr homeLayout 0x0
+int attr icon 0x0
+int attr iconTint 0x0
+int attr iconTintMode 0x0
+int attr iconifiedByDefault 0x0
+int attr imageButtonStyle 0x0
+int attr indeterminateProgressStyle 0x0
+int attr initialActivityCount 0x0
+int attr isAutofillInlineSuggestionTheme 0x0
+int attr isLightTheme 0x0
+int attr itemPadding 0x0
+int attr lStar 0x0
+int attr lastBaselineToBottomHeight 0x0
+int attr layout 0x0
+int attr lineHeight 0x0
+int attr listChoiceBackgroundIndicator 0x0
+int attr listChoiceIndicatorMultipleAnimated 0x0
+int attr listChoiceIndicatorSingleAnimated 0x0
+int attr listDividerAlertDialog 0x0
+int attr listItemLayout 0x0
+int attr listLayout 0x0
+int attr listMenuViewStyle 0x0
+int attr listPopupWindowStyle 0x0
+int attr listPreferredItemHeight 0x0
+int attr listPreferredItemHeightLarge 0x0
+int attr listPreferredItemHeightSmall 0x0
+int attr listPreferredItemPaddingEnd 0x0
+int attr listPreferredItemPaddingLeft 0x0
+int attr listPreferredItemPaddingRight 0x0
+int attr listPreferredItemPaddingStart 0x0
+int attr logo 0x0
+int attr logoDescription 0x0
+int attr maxButtonHeight 0x0
+int attr measureWithLargestChild 0x0
+int attr menu 0x0
+int attr multiChoiceItemLayout 0x0
+int attr navigationContentDescription 0x0
+int attr navigationIcon 0x0
+int attr navigationMode 0x0
+int attr nestedScrollViewStyle 0x0
+int attr numericModifiers 0x0
+int attr overlapAnchor 0x0
+int attr overlayImage 0x0
+int attr paddingBottomNoButtons 0x0
+int attr paddingEnd 0x0
+int attr paddingStart 0x0
+int attr paddingTopNoTitle 0x0
+int attr panelBackground 0x0
+int attr panelMenuListTheme 0x0
+int attr panelMenuListWidth 0x0
+int attr placeholderImage 0x0
+int attr placeholderImageScaleType 0x0
+int attr popupMenuStyle 0x0
+int attr popupTheme 0x0
+int attr popupWindowStyle 0x0
+int attr preserveIconSpacing 0x0
+int attr pressedStateOverlayImage 0x0
+int attr progressBarAutoRotateInterval 0x0
+int attr progressBarImage 0x0
+int attr progressBarImageScaleType 0x0
+int attr progressBarPadding 0x0
+int attr progressBarStyle 0x0
+int attr queryBackground 0x0
+int attr queryHint 0x0
+int attr queryPatterns 0x0
+int attr radioButtonStyle 0x0
+int attr ratingBarStyle 0x0
+int attr ratingBarStyleIndicator 0x0
+int attr ratingBarStyleSmall 0x0
+int attr retryImage 0x0
+int attr retryImageScaleType 0x0
+int attr roundAsCircle 0x0
+int attr roundBottomEnd 0x0
+int attr roundBottomLeft 0x0
+int attr roundBottomRight 0x0
+int attr roundBottomStart 0x0
+int attr roundTopEnd 0x0
+int attr roundTopLeft 0x0
+int attr roundTopRight 0x0
+int attr roundTopStart 0x0
+int attr roundWithOverlayColor 0x0
+int attr roundedCornerRadius 0x0
+int attr roundingBorderColor 0x0
+int attr roundingBorderPadding 0x0
+int attr roundingBorderWidth 0x0
+int attr searchHintIcon 0x0
+int attr searchIcon 0x0
+int attr searchViewStyle 0x0
+int attr seekBarStyle 0x0
+int attr selectableItemBackground 0x0
+int attr selectableItemBackgroundBorderless 0x0
+int attr shortcutMatchRequired 0x0
+int attr showAsAction 0x0
+int attr showDividers 0x0
+int attr showText 0x0
+int attr showTitle 0x0
+int attr singleChoiceItemLayout 0x0
+int attr spinBars 0x0
+int attr spinnerDropDownItemStyle 0x0
+int attr spinnerStyle 0x0
+int attr splitTrack 0x0
+int attr srcCompat 0x0
+int attr state_above_anchor 0x0
+int attr subMenuArrow 0x0
+int attr submitBackground 0x0
+int attr subtitle 0x0
+int attr subtitleTextAppearance 0x0
+int attr subtitleTextColor 0x0
+int attr subtitleTextStyle 0x0
+int attr suggestionRowLayout 0x0
+int attr switchMinWidth 0x0
+int attr switchPadding 0x0
+int attr switchStyle 0x0
+int attr switchTextAppearance 0x0
+int attr textAllCaps 0x0
+int attr textAppearanceLargePopupMenu 0x0
+int attr textAppearanceListItem 0x0
+int attr textAppearanceListItemSecondary 0x0
+int attr textAppearanceListItemSmall 0x0
+int attr textAppearancePopupMenuHeader 0x0
+int attr textAppearanceSearchResultSubtitle 0x0
+int attr textAppearanceSearchResultTitle 0x0
+int attr textAppearanceSmallPopupMenu 0x0
+int attr textColorAlertDialogListItem 0x0
+int attr textColorSearchUrl 0x0
+int attr textLocale 0x0
+int attr theme 0x0
+int attr thickness 0x0
+int attr thumbTextPadding 0x0
+int attr thumbTint 0x0
+int attr thumbTintMode 0x0
+int attr tickMark 0x0
+int attr tickMarkTint 0x0
+int attr tickMarkTintMode 0x0
+int attr tint 0x0
+int attr tintMode 0x0
+int attr title 0x0
+int attr titleMargin 0x0
+int attr titleMarginBottom 0x0
+int attr titleMarginEnd 0x0
+int attr titleMarginStart 0x0
+int attr titleMarginTop 0x0
+int attr titleMargins 0x0
+int attr titleTextAppearance 0x0
+int attr titleTextColor 0x0
+int attr titleTextStyle 0x0
+int attr toolbarNavigationButtonStyle 0x0
+int attr toolbarStyle 0x0
+int attr tooltipForegroundColor 0x0
+int attr tooltipFrameBackground 0x0
+int attr tooltipText 0x0
+int attr track 0x0
+int attr trackTint 0x0
+int attr trackTintMode 0x0
+int attr ttcIndex 0x0
+int attr viewAspectRatio 0x0
+int attr viewInflaterClass 0x0
+int attr voiceIcon 0x0
+int attr wheel_data 0x0
+int attr wheel_direction 0x0
+int attr wheel_item_count 0x0
+int attr wheel_item_index 0x0
+int attr wheel_item_same_size 0x0
+int attr wheel_item_space 0x0
+int attr wheel_style 0x0
+int attr wheel_text_color 0x0
+int attr wheel_text_color_current 0x0
+int attr wheel_text_size 0x0
+int attr windowActionBar 0x0
+int attr windowActionBarOverlay 0x0
+int attr windowActionModeOverlay 0x0
+int attr windowFixedHeightMajor 0x0
+int attr windowFixedHeightMinor 0x0
+int attr windowFixedWidthMajor 0x0
+int attr windowFixedWidthMinor 0x0
+int attr windowMinWidthMajor 0x0
+int attr windowMinWidthMinor 0x0
+int attr windowNoTitle 0x0
+int bool abc_action_bar_embed_tabs 0x0
+int bool abc_config_actionMenuItemAllCaps 0x0
+int color ColorButtonDisable 0x0
+int color ColorButtonPressed 0x0
+int color ColorButtonRelease 0x0
+int color abc_background_cache_hint_selector_material_dark 0x0
+int color abc_background_cache_hint_selector_material_light 0x0
+int color abc_btn_colored_borderless_text_material 0x0
+int color abc_btn_colored_text_material 0x0
+int color abc_color_highlight_material 0x0
+int color abc_decor_view_status_guard 0x0
+int color abc_decor_view_status_guard_light 0x0
+int color abc_hint_foreground_material_dark 0x0
+int color abc_hint_foreground_material_light 0x0
+int color abc_primary_text_disable_only_material_dark 0x0
+int color abc_primary_text_disable_only_material_light 0x0
+int color abc_primary_text_material_dark 0x0
+int color abc_primary_text_material_light 0x0
+int color abc_search_url_text 0x0
+int color abc_search_url_text_normal 0x0
+int color abc_search_url_text_pressed 0x0
+int color abc_search_url_text_selected 0x0
+int color abc_secondary_text_material_dark 0x0
+int color abc_secondary_text_material_light 0x0
+int color abc_tint_btn_checkable 0x0
+int color abc_tint_default 0x0
+int color abc_tint_edittext 0x0
+int color abc_tint_seek_thumb 0x0
+int color abc_tint_spinner 0x0
+int color abc_tint_switch_track 0x0
+int color accent_material_dark 0x0
+int color accent_material_light 0x0
+int color androidx_core_ripple_material_light 0x0
+int color androidx_core_secondary_text_default_material_light 0x0
+int color background_floating_material_dark 0x0
+int color background_floating_material_light 0x0
+int color background_material_dark 0x0
+int color background_material_light 0x0
+int color bright_foreground_disabled_material_dark 0x0
+int color bright_foreground_disabled_material_light 0x0
+int color bright_foreground_inverse_material_dark 0x0
+int color bright_foreground_inverse_material_light 0x0
+int color bright_foreground_material_dark 0x0
+int color bright_foreground_material_light 0x0
+int color button_material_dark 0x0
+int color button_material_light 0x0
+int color catalyst_logbox_background 0x0
+int color catalyst_redbox_background 0x0
+int color dim_foreground_disabled_material_dark 0x0
+int color dim_foreground_disabled_material_light 0x0
+int color dim_foreground_material_dark 0x0
+int color dim_foreground_material_light 0x0
+int color error_color_material_dark 0x0
+int color error_color_material_light 0x0
+int color foreground_material_dark 0x0
+int color foreground_material_light 0x0
+int color highlighted_text_material_dark 0x0
+int color highlighted_text_material_light 0x0
+int color material_blue_grey_800 0x0
+int color material_blue_grey_900 0x0
+int color material_blue_grey_950 0x0
+int color material_deep_teal_200 0x0
+int color material_deep_teal_500 0x0
+int color material_grey_100 0x0
+int color material_grey_300 0x0
+int color material_grey_50 0x0
+int color material_grey_600 0x0
+int color material_grey_800 0x0
+int color material_grey_850 0x0
+int color material_grey_900 0x0
+int color notification_action_color_filter 0x0
+int color notification_icon_bg_color 0x0
+int color primary_dark_material_dark 0x0
+int color primary_dark_material_light 0x0
+int color primary_material_dark 0x0
+int color primary_material_light 0x0
+int color primary_text_default_material_dark 0x0
+int color primary_text_default_material_light 0x0
+int color primary_text_disabled_material_dark 0x0
+int color primary_text_disabled_material_light 0x0
+int color ripple_material_dark 0x0
+int color ripple_material_light 0x0
+int color secondary_text_default_material_dark 0x0
+int color secondary_text_default_material_light 0x0
+int color secondary_text_disabled_material_dark 0x0
+int color secondary_text_disabled_material_light 0x0
+int color switch_thumb_disabled_material_dark 0x0
+int color switch_thumb_disabled_material_light 0x0
+int color switch_thumb_material_dark 0x0
+int color switch_thumb_material_light 0x0
+int color switch_thumb_normal_material_dark 0x0
+int color switch_thumb_normal_material_light 0x0
+int color tooltip_background_dark 0x0
+int color tooltip_background_light 0x0
+int dimen WheelItemSpace 0x0
+int dimen WheelLabelTextSize 0x0
+int dimen WheelPadding 0x0
+int dimen WheelTextSize 0x0
+int dimen abc_action_bar_content_inset_material 0x0
+int dimen abc_action_bar_content_inset_with_nav 0x0
+int dimen abc_action_bar_default_height_material 0x0
+int dimen abc_action_bar_default_padding_end_material 0x0
+int dimen abc_action_bar_default_padding_start_material 0x0
+int dimen abc_action_bar_elevation_material 0x0
+int dimen abc_action_bar_icon_vertical_padding_material 0x0
+int dimen abc_action_bar_overflow_padding_end_material 0x0
+int dimen abc_action_bar_overflow_padding_start_material 0x0
+int dimen abc_action_bar_stacked_max_height 0x0
+int dimen abc_action_bar_stacked_tab_max_width 0x0
+int dimen abc_action_bar_subtitle_bottom_margin_material 0x0
+int dimen abc_action_bar_subtitle_top_margin_material 0x0
+int dimen abc_action_button_min_height_material 0x0
+int dimen abc_action_button_min_width_material 0x0
+int dimen abc_action_button_min_width_overflow_material 0x0
+int dimen abc_alert_dialog_button_bar_height 0x0
+int dimen abc_alert_dialog_button_dimen 0x0
+int dimen abc_button_inset_horizontal_material 0x0
+int dimen abc_button_inset_vertical_material 0x0
+int dimen abc_button_padding_horizontal_material 0x0
+int dimen abc_button_padding_vertical_material 0x0
+int dimen abc_cascading_menus_min_smallest_width 0x0
+int dimen abc_config_prefDialogWidth 0x0
+int dimen abc_control_corner_material 0x0
+int dimen abc_control_inset_material 0x0
+int dimen abc_control_padding_material 0x0
+int dimen abc_dialog_corner_radius_material 0x0
+int dimen abc_dialog_fixed_height_major 0x0
+int dimen abc_dialog_fixed_height_minor 0x0
+int dimen abc_dialog_fixed_width_major 0x0
+int dimen abc_dialog_fixed_width_minor 0x0
+int dimen abc_dialog_list_padding_bottom_no_buttons 0x0
+int dimen abc_dialog_list_padding_top_no_title 0x0
+int dimen abc_dialog_min_width_major 0x0
+int dimen abc_dialog_min_width_minor 0x0
+int dimen abc_dialog_padding_material 0x0
+int dimen abc_dialog_padding_top_material 0x0
+int dimen abc_dialog_title_divider_material 0x0
+int dimen abc_disabled_alpha_material_dark 0x0
+int dimen abc_disabled_alpha_material_light 0x0
+int dimen abc_dropdownitem_icon_width 0x0
+int dimen abc_dropdownitem_text_padding_left 0x0
+int dimen abc_dropdownitem_text_padding_right 0x0
+int dimen abc_edit_text_inset_bottom_material 0x0
+int dimen abc_edit_text_inset_horizontal_material 0x0
+int dimen abc_edit_text_inset_top_material 0x0
+int dimen abc_floating_window_z 0x0
+int dimen abc_list_item_height_large_material 0x0
+int dimen abc_list_item_height_material 0x0
+int dimen abc_list_item_height_small_material 0x0
+int dimen abc_list_item_padding_horizontal_material 0x0
+int dimen abc_panel_menu_list_width 0x0
+int dimen abc_progress_bar_height_material 0x0
+int dimen abc_search_view_preferred_height 0x0
+int dimen abc_search_view_preferred_width 0x0
+int dimen abc_seekbar_track_background_height_material 0x0
+int dimen abc_seekbar_track_progress_height_material 0x0
+int dimen abc_select_dialog_padding_start_material 0x0
+int dimen abc_star_big 0x0
+int dimen abc_star_medium 0x0
+int dimen abc_star_small 0x0
+int dimen abc_switch_padding 0x0
+int dimen abc_text_size_body_1_material 0x0
+int dimen abc_text_size_body_2_material 0x0
+int dimen abc_text_size_button_material 0x0
+int dimen abc_text_size_caption_material 0x0
+int dimen abc_text_size_display_1_material 0x0
+int dimen abc_text_size_display_2_material 0x0
+int dimen abc_text_size_display_3_material 0x0
+int dimen abc_text_size_display_4_material 0x0
+int dimen abc_text_size_headline_material 0x0
+int dimen abc_text_size_large_material 0x0
+int dimen abc_text_size_medium_material 0x0
+int dimen abc_text_size_menu_header_material 0x0
+int dimen abc_text_size_menu_material 0x0
+int dimen abc_text_size_small_material 0x0
+int dimen abc_text_size_subhead_material 0x0
+int dimen abc_text_size_subtitle_material_toolbar 0x0
+int dimen abc_text_size_title_material 0x0
+int dimen abc_text_size_title_material_toolbar 0x0
+int dimen autofill_inline_suggestion_icon_size 0x0
+int dimen compat_button_inset_horizontal_material 0x0
+int dimen compat_button_inset_vertical_material 0x0
+int dimen compat_button_padding_horizontal_material 0x0
+int dimen compat_button_padding_vertical_material 0x0
+int dimen compat_control_corner_material 0x0
+int dimen compat_notification_large_icon_max_height 0x0
+int dimen compat_notification_large_icon_max_width 0x0
+int dimen disabled_alpha_material_dark 0x0
+int dimen disabled_alpha_material_light 0x0
+int dimen highlight_alpha_material_colored 0x0
+int dimen highlight_alpha_material_dark 0x0
+int dimen highlight_alpha_material_light 0x0
+int dimen hint_alpha_material_dark 0x0
+int dimen hint_alpha_material_light 0x0
+int dimen hint_pressed_alpha_material_dark 0x0
+int dimen hint_pressed_alpha_material_light 0x0
+int dimen notification_action_icon_size 0x0
+int dimen notification_action_text_size 0x0
+int dimen notification_big_circle_margin 0x0
+int dimen notification_content_margin_start 0x0
+int dimen notification_large_icon_height 0x0
+int dimen notification_large_icon_width 0x0
+int dimen notification_main_column_padding_top 0x0
+int dimen notification_media_narrow_margin 0x0
+int dimen notification_right_icon_size 0x0
+int dimen notification_right_side_padding_top 0x0
+int dimen notification_small_icon_background_padding 0x0
+int dimen notification_small_icon_size_as_large 0x0
+int dimen notification_subtext_size 0x0
+int dimen notification_top_pad 0x0
+int dimen notification_top_pad_large_text 0x0
+int dimen tooltip_corner_radius 0x0
+int dimen tooltip_horizontal_padding 0x0
+int dimen tooltip_margin 0x0
+int dimen tooltip_precise_anchor_extra_offset 0x0
+int dimen tooltip_precise_anchor_threshold 0x0
+int dimen tooltip_vertical_padding 0x0
+int dimen tooltip_y_offset_non_touch 0x0
+int dimen tooltip_y_offset_touch 0x0
+int drawable abc_ab_share_pack_mtrl_alpha 0x0
+int drawable abc_action_bar_item_background_material 0x0
+int drawable abc_btn_borderless_material 0x0
+int drawable abc_btn_check_material 0x0
+int drawable abc_btn_check_material_anim 0x0
+int drawable abc_btn_check_to_on_mtrl_000 0x0
+int drawable abc_btn_check_to_on_mtrl_015 0x0
+int drawable abc_btn_colored_material 0x0
+int drawable abc_btn_default_mtrl_shape 0x0
+int drawable abc_btn_radio_material 0x0
+int drawable abc_btn_radio_material_anim 0x0
+int drawable abc_btn_radio_to_on_mtrl_000 0x0
+int drawable abc_btn_radio_to_on_mtrl_015 0x0
+int drawable abc_btn_switch_to_on_mtrl_00001 0x0
+int drawable abc_btn_switch_to_on_mtrl_00012 0x0
+int drawable abc_cab_background_internal_bg 0x0
+int drawable abc_cab_background_top_material 0x0
+int drawable abc_cab_background_top_mtrl_alpha 0x0
+int drawable abc_control_background_material 0x0
+int drawable abc_dialog_material_background 0x0
+int drawable abc_edit_text_material 0x0
+int drawable abc_ic_ab_back_material 0x0
+int drawable abc_ic_arrow_drop_right_black_24dp 0x0
+int drawable abc_ic_clear_material 0x0
+int drawable abc_ic_commit_search_api_mtrl_alpha 0x0
+int drawable abc_ic_go_search_api_material 0x0
+int drawable abc_ic_menu_copy_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_cut_mtrl_alpha 0x0
+int drawable abc_ic_menu_overflow_material 0x0
+int drawable abc_ic_menu_paste_mtrl_am_alpha 0x0
+int drawable abc_ic_menu_selectall_mtrl_alpha 0x0
+int drawable abc_ic_menu_share_mtrl_alpha 0x0
+int drawable abc_ic_search_api_material 0x0
+int drawable abc_ic_voice_search_api_material 0x0
+int drawable abc_item_background_holo_dark 0x0
+int drawable abc_item_background_holo_light 0x0
+int drawable abc_list_divider_material 0x0
+int drawable abc_list_divider_mtrl_alpha 0x0
+int drawable abc_list_focused_holo 0x0
+int drawable abc_list_longpressed_holo 0x0
+int drawable abc_list_pressed_holo_dark 0x0
+int drawable abc_list_pressed_holo_light 0x0
+int drawable abc_list_selector_background_transition_holo_dark 0x0
+int drawable abc_list_selector_background_transition_holo_light 0x0
+int drawable abc_list_selector_disabled_holo_dark 0x0
+int drawable abc_list_selector_disabled_holo_light 0x0
+int drawable abc_list_selector_holo_dark 0x0
+int drawable abc_list_selector_holo_light 0x0
+int drawable abc_menu_hardkey_panel_mtrl_mult 0x0
+int drawable abc_popup_background_mtrl_mult 0x0
+int drawable abc_ratingbar_indicator_material 0x0
+int drawable abc_ratingbar_material 0x0
+int drawable abc_ratingbar_small_material 0x0
+int drawable abc_scrubber_control_off_mtrl_alpha 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_000 0x0
+int drawable abc_scrubber_control_to_pressed_mtrl_005 0x0
+int drawable abc_scrubber_primary_mtrl_alpha 0x0
+int drawable abc_scrubber_track_mtrl_alpha 0x0
+int drawable abc_seekbar_thumb_material 0x0
+int drawable abc_seekbar_tick_mark_material 0x0
+int drawable abc_seekbar_track_material 0x0
+int drawable abc_spinner_mtrl_am_alpha 0x0
+int drawable abc_spinner_textfield_background_material 0x0
+int drawable abc_star_black_48dp 0x0
+int drawable abc_star_half_black_48dp 0x0
+int drawable abc_switch_thumb_material 0x0
+int drawable abc_switch_track_mtrl_alpha 0x0
+int drawable abc_tab_indicator_material 0x0
+int drawable abc_tab_indicator_mtrl_alpha 0x0
+int drawable abc_text_cursor_material 0x0
+int drawable abc_text_select_handle_left_mtrl 0x0
+int drawable abc_text_select_handle_middle_mtrl 0x0
+int drawable abc_text_select_handle_right_mtrl 0x0
+int drawable abc_textfield_activated_mtrl_alpha 0x0
+int drawable abc_textfield_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_activated_mtrl_alpha 0x0
+int drawable abc_textfield_search_default_mtrl_alpha 0x0
+int drawable abc_textfield_search_material 0x0
+int drawable abc_vector_test 0x0
+int drawable autofill_inline_suggestion_chip_background 0x0
+int drawable bg_btn 0x0
+int drawable btn_checkbox_checked_mtrl 0x0
+int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x0
+int drawable btn_checkbox_unchecked_mtrl 0x0
+int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x0
+int drawable btn_radio_off_mtrl 0x0
+int drawable btn_radio_off_to_on_mtrl_animation 0x0
+int drawable btn_radio_on_mtrl 0x0
+int drawable btn_radio_on_to_off_mtrl_animation 0x0
+int drawable notification_action_background 0x0
+int drawable notification_bg 0x0
+int drawable notification_bg_low 0x0
+int drawable notification_bg_low_normal 0x0
+int drawable notification_bg_low_pressed 0x0
+int drawable notification_bg_normal 0x0
+int drawable notification_bg_normal_pressed 0x0
+int drawable notification_icon_background 0x0
+int drawable notification_template_icon_bg 0x0
+int drawable notification_template_icon_low_bg 0x0
+int drawable notification_tile_bg 0x0
+int drawable notify_panel_notification_icon_bg 0x0
+int drawable redbox_top_border_background 0x0
+int drawable test_level_drawable 0x0
+int drawable tooltip_frame_dark 0x0
+int drawable tooltip_frame_light 0x0
+int id accessibility_action_clickable_span 0x0
+int id accessibility_actions 0x0
+int id accessibility_collection 0x0
+int id accessibility_collection_item 0x0
+int id accessibility_custom_action_0 0x0
+int id accessibility_custom_action_1 0x0
+int id accessibility_custom_action_10 0x0
+int id accessibility_custom_action_11 0x0
+int id accessibility_custom_action_12 0x0
+int id accessibility_custom_action_13 0x0
+int id accessibility_custom_action_14 0x0
+int id accessibility_custom_action_15 0x0
+int id accessibility_custom_action_16 0x0
+int id accessibility_custom_action_17 0x0
+int id accessibility_custom_action_18 0x0
+int id accessibility_custom_action_19 0x0
+int id accessibility_custom_action_2 0x0
+int id accessibility_custom_action_20 0x0
+int id accessibility_custom_action_21 0x0
+int id accessibility_custom_action_22 0x0
+int id accessibility_custom_action_23 0x0
+int id accessibility_custom_action_24 0x0
+int id accessibility_custom_action_25 0x0
+int id accessibility_custom_action_26 0x0
+int id accessibility_custom_action_27 0x0
+int id accessibility_custom_action_28 0x0
+int id accessibility_custom_action_29 0x0
+int id accessibility_custom_action_3 0x0
+int id accessibility_custom_action_30 0x0
+int id accessibility_custom_action_31 0x0
+int id accessibility_custom_action_4 0x0
+int id accessibility_custom_action_5 0x0
+int id accessibility_custom_action_6 0x0
+int id accessibility_custom_action_7 0x0
+int id accessibility_custom_action_8 0x0
+int id accessibility_custom_action_9 0x0
+int id accessibility_hint 0x0
+int id accessibility_label 0x0
+int id accessibility_links 0x0
+int id accessibility_role 0x0
+int id accessibility_state 0x0
+int id accessibility_value 0x0
+int id action_bar 0x0
+int id action_bar_activity_content 0x0
+int id action_bar_container 0x0
+int id action_bar_root 0x0
+int id action_bar_spinner 0x0
+int id action_bar_subtitle 0x0
+int id action_bar_title 0x0
+int id action_container 0x0
+int id action_context_bar 0x0
+int id action_divider 0x0
+int id action_image 0x0
+int id action_menu_divider 0x0
+int id action_menu_presenter 0x0
+int id action_mode_bar 0x0
+int id action_mode_bar_stub 0x0
+int id action_mode_close_button 0x0
+int id action_text 0x0
+int id actions 0x0
+int id activity_chooser_view_content 0x0
+int id add 0x0
+int id alertTitle 0x0
+int id async 0x0
+int id autofill_inline_suggestion_end_icon 0x0
+int id autofill_inline_suggestion_start_icon 0x0
+int id autofill_inline_suggestion_subtitle 0x0
+int id autofill_inline_suggestion_title 0x0
+int id blocking 0x0
+int id buttonPanel 0x0
+int id catalyst_redbox_title 0x0
+int id center 0x0
+int id centerCrop 0x0
+int id centerInside 0x0
+int id checkbox 0x0
+int id checked 0x0
+int id chronometer 0x0
+int id content 0x0
+int id contentPanel 0x0
+int id curved 0x0
+int id custom 0x0
+int id customPanel 0x0
+int id decor_content_parent 0x0
+int id default_activity_button 0x0
+int id dialog_button 0x0
+int id edit_query 0x0
+int id expand_activities_button 0x0
+int id expanded_menu 0x0
+int id fitBottomStart 0x0
+int id fitCenter 0x0
+int id fitEnd 0x0
+int id fitStart 0x0
+int id fitXY 0x0
+int id focusCrop 0x0
+int id forever 0x0
+int id fps_text 0x0
+int id fragment_container_view_tag 0x0
+int id group_divider 0x0
+int id home 0x0
+int id horizontal 0x0
+int id icon 0x0
+int id icon_group 0x0
+int id image 0x0
+int id info 0x0
+int id italic 0x0
+int id item1 0x0
+int id item2 0x0
+int id item3 0x0
+int id item4 0x0
+int id labelled_by 0x0
+int id line1 0x0
+int id line3 0x0
+int id listMode 0x0
+int id list_item 0x0
+int id message 0x0
+int id multiply 0x0
+int id none 0x0
+int id normal 0x0
+int id notification_background 0x0
+int id notification_main_column 0x0
+int id notification_main_column_container 0x0
+int id off 0x0
+int id on 0x0
+int id parentPanel 0x0
+int id pointer_enter 0x0
+int id pointer_enter_capture 0x0
+int id pointer_leave 0x0
+int id pointer_leave_capture 0x0
+int id pointer_move 0x0
+int id pointer_move_capture 0x0
+int id progress_circular 0x0
+int id progress_horizontal 0x0
+int id radio 0x0
+int id react_test_id 0x0
+int id right_icon 0x0
+int id right_side 0x0
+int id rn_frame_file 0x0
+int id rn_frame_method 0x0
+int id rn_redbox_dismiss_button 0x0
+int id rn_redbox_line_separator 0x0
+int id rn_redbox_loading_indicator 0x0
+int id rn_redbox_reload_button 0x0
+int id rn_redbox_report_button 0x0
+int id rn_redbox_report_label 0x0
+int id rn_redbox_stack 0x0
+int id screen 0x0
+int id scrollIndicatorDown 0x0
+int id scrollIndicatorUp 0x0
+int id scrollView 0x0
+int id search_badge 0x0
+int id search_bar 0x0
+int id search_button 0x0
+int id search_close_btn 0x0
+int id search_edit_frame 0x0
+int id search_go_btn 0x0
+int id search_mag_icon 0x0
+int id search_plate 0x0
+int id search_src_text 0x0
+int id search_voice_btn 0x0
+int id select_dialog_listview 0x0
+int id shortcut 0x0
+int id spacer 0x0
+int id special_effects_controller_view_tag 0x0
+int id split_action_bar 0x0
+int id src_atop 0x0
+int id src_in 0x0
+int id src_over 0x0
+int id straight 0x0
+int id submenuarrow 0x0
+int id submit_area 0x0
+int id tabMode 0x0
+int id tag_accessibility_actions 0x0
+int id tag_accessibility_clickable_spans 0x0
+int id tag_accessibility_heading 0x0
+int id tag_accessibility_pane_title 0x0
+int id tag_on_apply_window_listener 0x0
+int id tag_on_receive_content_listener 0x0
+int id tag_on_receive_content_mime_types 0x0
+int id tag_screen_reader_focusable 0x0
+int id tag_state_description 0x0
+int id tag_transition_group 0x0
+int id tag_unhandled_key_event_manager 0x0
+int id tag_unhandled_key_listeners 0x0
+int id tag_window_insets_animation_callback 0x0
+int id text 0x0
+int id text2 0x0
+int id textSpacerNoButtons 0x0
+int id textSpacerNoTitle 0x0
+int id time 0x0
+int id title 0x0
+int id titleDividerNoCustom 0x0
+int id title_template 0x0
+int id topPanel 0x0
+int id unchecked 0x0
+int id uniform 0x0
+int id up 0x0
+int id vertical 0x0
+int id view_tag_instance_handle 0x0
+int id view_tag_native_id 0x0
+int id view_tree_lifecycle_owner 0x0
+int id view_tree_saved_state_registry_owner 0x0
+int id view_tree_view_model_store_owner 0x0
+int id visible_removing_fragment_view_tag 0x0
+int id wrap_content 0x0
+int integer abc_config_activityDefaultDur 0x0
+int integer abc_config_activityShortDur 0x0
+int integer cancel_button_image_alpha 0x0
+int integer config_tooltipAnimTime 0x0
+int integer react_native_dev_server_port 0x0
+int integer react_native_inspector_proxy_port 0x0
+int integer status_bar_notification_info_maxnum 0x0
+int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x0
+int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x0
+int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x0
+int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x0
+int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x0
+int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x0
+int interpolator fast_out_slow_in 0x0
+int layout abc_action_bar_title_item 0x0
+int layout abc_action_bar_up_container 0x0
+int layout abc_action_menu_item_layout 0x0
+int layout abc_action_menu_layout 0x0
+int layout abc_action_mode_bar 0x0
+int layout abc_action_mode_close_item_material 0x0
+int layout abc_activity_chooser_view 0x0
+int layout abc_activity_chooser_view_list_item 0x0
+int layout abc_alert_dialog_button_bar_material 0x0
+int layout abc_alert_dialog_material 0x0
+int layout abc_alert_dialog_title_material 0x0
+int layout abc_cascading_menu_item_layout 0x0
+int layout abc_dialog_title_material 0x0
+int layout abc_expanded_menu_layout 0x0
+int layout abc_list_menu_item_checkbox 0x0
+int layout abc_list_menu_item_icon 0x0
+int layout abc_list_menu_item_layout 0x0
+int layout abc_list_menu_item_radio 0x0
+int layout abc_popup_menu_header_item_layout 0x0
+int layout abc_popup_menu_item_layout 0x0
+int layout abc_screen_content_include 0x0
+int layout abc_screen_simple 0x0
+int layout abc_screen_simple_overlay_action_mode 0x0
+int layout abc_screen_toolbar 0x0
+int layout abc_search_dropdown_item_icons_2line 0x0
+int layout abc_search_view 0x0
+int layout abc_select_dialog_material 0x0
+int layout abc_tooltip 0x0
+int layout autofill_inline_suggestion 0x0
+int layout custom_dialog 0x0
+int layout dev_loading_view 0x0
+int layout fps_view 0x0
+int layout notification_action 0x0
+int layout notification_action_tombstone 0x0
+int layout notification_template_custom_big 0x0
+int layout notification_template_icon_group 0x0
+int layout notification_template_part_chronometer 0x0
+int layout notification_template_part_time 0x0
+int layout redbox_item_frame 0x0
+int layout redbox_item_title 0x0
+int layout redbox_view 0x0
+int layout select_dialog_item_material 0x0
+int layout select_dialog_multichoice_material 0x0
+int layout select_dialog_singlechoice_material 0x0
+int layout support_simple_spinner_dropdown_item 0x0
+int menu example_menu 0x0
+int menu example_menu2 0x0
+int string abc_action_bar_home_description 0x0
+int string abc_action_bar_up_description 0x0
+int string abc_action_menu_overflow_description 0x0
+int string abc_action_mode_done 0x0
+int string abc_activity_chooser_view_see_all 0x0
+int string abc_activitychooserview_choose_application 0x0
+int string abc_capital_off 0x0
+int string abc_capital_on 0x0
+int string abc_menu_alt_shortcut_label 0x0
+int string abc_menu_ctrl_shortcut_label 0x0
+int string abc_menu_delete_shortcut_label 0x0
+int string abc_menu_enter_shortcut_label 0x0
+int string abc_menu_function_shortcut_label 0x0
+int string abc_menu_meta_shortcut_label 0x0
+int string abc_menu_shift_shortcut_label 0x0
+int string abc_menu_space_shortcut_label 0x0
+int string abc_menu_sym_shortcut_label 0x0
+int string abc_prepend_shortcut_label 0x0
+int string abc_search_hint 0x0
+int string abc_searchview_description_clear 0x0
+int string abc_searchview_description_query 0x0
+int string abc_searchview_description_search 0x0
+int string abc_searchview_description_submit 0x0
+int string abc_searchview_description_voice 0x0
+int string abc_shareactionprovider_share_with 0x0
+int string abc_shareactionprovider_share_with_application 0x0
+int string abc_toolbar_collapse_description 0x0
+int string alert_description 0x0
+int string catalyst_change_bundle_location 0x0
+int string catalyst_copy_button 0x0
+int string catalyst_debug 0x0
+int string catalyst_debug_chrome 0x0
+int string catalyst_debug_chrome_stop 0x0
+int string catalyst_debug_connecting 0x0
+int string catalyst_debug_error 0x0
+int string catalyst_debug_open 0x0
+int string catalyst_debug_stop 0x0
+int string catalyst_devtools_open 0x0
+int string catalyst_dismiss_button 0x0
+int string catalyst_heap_capture 0x0
+int string catalyst_hot_reloading 0x0
+int string catalyst_hot_reloading_auto_disable 0x0
+int string catalyst_hot_reloading_auto_enable 0x0
+int string catalyst_hot_reloading_stop 0x0
+int string catalyst_inspector 0x0
+int string catalyst_inspector_stop 0x0
+int string catalyst_loading_from_url 0x0
+int string catalyst_open_flipper_error 0x0
+int string catalyst_perf_monitor 0x0
+int string catalyst_perf_monitor_stop 0x0
+int string catalyst_reload 0x0
+int string catalyst_reload_button 0x0
+int string catalyst_reload_error 0x0
+int string catalyst_report_button 0x0
+int string catalyst_sample_profiler_disable 0x0
+int string catalyst_sample_profiler_enable 0x0
+int string catalyst_settings 0x0
+int string catalyst_settings_title 0x0
+int string combobox_description 0x0
+int string header_description 0x0
+int string image_description 0x0
+int string imagebutton_description 0x0
+int string link_description 0x0
+int string menu_description 0x0
+int string menubar_description 0x0
+int string menuitem_description 0x0
+int string progressbar_description 0x0
+int string radiogroup_description 0x0
+int string rn_tab_description 0x0
+int string scrollbar_description 0x0
+int string search_menu_title 0x0
+int string spinbutton_description 0x0
+int string state_busy_description 0x0
+int string state_collapsed_description 0x0
+int string state_expanded_description 0x0
+int string state_mixed_description 0x0
+int string state_off_description 0x0
+int string state_on_description 0x0
+int string state_unselected_description 0x0
+int string status_bar_notification_info_overflow 0x0
+int string summary_description 0x0
+int string tablist_description 0x0
+int string timer_description 0x0
+int string toolbar_description 0x0
+int style AlertDialog_AppCompat 0x0
+int style AlertDialog_AppCompat_Light 0x0
+int style Animation_AppCompat_Dialog 0x0
+int style Animation_AppCompat_DropDownUp 0x0
+int style Animation_AppCompat_Tooltip 0x0
+int style Animation_Catalyst_LogBox 0x0
+int style Animation_Catalyst_RedBox 0x0
+int style Base_AlertDialog_AppCompat 0x0
+int style Base_AlertDialog_AppCompat_Light 0x0
+int style Base_Animation_AppCompat_Dialog 0x0
+int style Base_Animation_AppCompat_DropDownUp 0x0
+int style Base_Animation_AppCompat_Tooltip 0x0
+int style Base_DialogWindowTitleBackground_AppCompat 0x0
+int style Base_DialogWindowTitle_AppCompat 0x0
+int style Base_TextAppearance_AppCompat 0x0
+int style Base_TextAppearance_AppCompat_Body1 0x0
+int style Base_TextAppearance_AppCompat_Body2 0x0
+int style Base_TextAppearance_AppCompat_Button 0x0
+int style Base_TextAppearance_AppCompat_Caption 0x0
+int style Base_TextAppearance_AppCompat_Display1 0x0
+int style Base_TextAppearance_AppCompat_Display2 0x0
+int style Base_TextAppearance_AppCompat_Display3 0x0
+int style Base_TextAppearance_AppCompat_Display4 0x0
+int style Base_TextAppearance_AppCompat_Headline 0x0
+int style Base_TextAppearance_AppCompat_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Large 0x0
+int style Base_TextAppearance_AppCompat_Large_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Medium 0x0
+int style Base_TextAppearance_AppCompat_Medium_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Menu 0x0
+int style Base_TextAppearance_AppCompat_SearchResult 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_SearchResult_Title 0x0
+int style Base_TextAppearance_AppCompat_Small 0x0
+int style Base_TextAppearance_AppCompat_Small_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Subhead 0x0
+int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Title 0x0
+int style Base_TextAppearance_AppCompat_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Tooltip 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style Base_TextAppearance_AppCompat_Widget_Switch 0x0
+int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Base_ThemeOverlay_AppCompat 0x0
+int style Base_ThemeOverlay_AppCompat_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dark 0x0
+int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style Base_ThemeOverlay_AppCompat_Light 0x0
+int style Base_Theme_AppCompat 0x0
+int style Base_Theme_AppCompat_CompactMenu 0x0
+int style Base_Theme_AppCompat_Dialog 0x0
+int style Base_Theme_AppCompat_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Dialog_MinWidth 0x0
+int style Base_Theme_AppCompat_Light 0x0
+int style Base_Theme_AppCompat_Light_DarkActionBar 0x0
+int style Base_Theme_AppCompat_Light_Dialog 0x0
+int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Base_Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x0
+int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat 0x0
+int style Base_V21_Theme_AppCompat_Dialog 0x0
+int style Base_V21_Theme_AppCompat_Light 0x0
+int style Base_V21_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V22_Theme_AppCompat 0x0
+int style Base_V22_Theme_AppCompat_Light 0x0
+int style Base_V23_Theme_AppCompat 0x0
+int style Base_V23_Theme_AppCompat_Light 0x0
+int style Base_V26_Theme_AppCompat 0x0
+int style Base_V26_Theme_AppCompat_Light 0x0
+int style Base_V26_Widget_AppCompat_Toolbar 0x0
+int style Base_V28_Theme_AppCompat 0x0
+int style Base_V28_Theme_AppCompat_Light 0x0
+int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat 0x0
+int style Base_V7_Theme_AppCompat_Dialog 0x0
+int style Base_V7_Theme_AppCompat_Light 0x0
+int style Base_V7_Theme_AppCompat_Light_Dialog 0x0
+int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_V7_Widget_AppCompat_EditText 0x0
+int style Base_V7_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_ActionBar 0x0
+int style Base_Widget_AppCompat_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_ActionButton 0x0
+int style Base_Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Base_Widget_AppCompat_ActionButton_Overflow 0x0
+int style Base_Widget_AppCompat_ActionMode 0x0
+int style Base_Widget_AppCompat_ActivityChooserView 0x0
+int style Base_Widget_AppCompat_AutoCompleteTextView 0x0
+int style Base_Widget_AppCompat_Button 0x0
+int style Base_Widget_AppCompat_ButtonBar 0x0
+int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Borderless 0x0
+int style Base_Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Base_Widget_AppCompat_Button_Colored 0x0
+int style Base_Widget_AppCompat_Button_Small 0x0
+int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Base_Widget_AppCompat_CompoundButton_Switch 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle 0x0
+int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x0
+int style Base_Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Base_Widget_AppCompat_EditText 0x0
+int style Base_Widget_AppCompat_ImageButton 0x0
+int style Base_Widget_AppCompat_Light_ActionBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu 0x0
+int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_ListMenuView 0x0
+int style Base_Widget_AppCompat_ListPopupWindow 0x0
+int style Base_Widget_AppCompat_ListView 0x0
+int style Base_Widget_AppCompat_ListView_DropDown 0x0
+int style Base_Widget_AppCompat_ListView_Menu 0x0
+int style Base_Widget_AppCompat_PopupMenu 0x0
+int style Base_Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Base_Widget_AppCompat_PopupWindow 0x0
+int style Base_Widget_AppCompat_ProgressBar 0x0
+int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Base_Widget_AppCompat_RatingBar 0x0
+int style Base_Widget_AppCompat_RatingBar_Indicator 0x0
+int style Base_Widget_AppCompat_RatingBar_Small 0x0
+int style Base_Widget_AppCompat_SearchView 0x0
+int style Base_Widget_AppCompat_SearchView_ActionBar 0x0
+int style Base_Widget_AppCompat_SeekBar 0x0
+int style Base_Widget_AppCompat_SeekBar_Discrete 0x0
+int style Base_Widget_AppCompat_Spinner 0x0
+int style Base_Widget_AppCompat_Spinner_Underlined 0x0
+int style Base_Widget_AppCompat_TextView 0x0
+int style Base_Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Base_Widget_AppCompat_Toolbar 0x0
+int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style CalendarDatePickerDialog 0x0
+int style CalendarDatePickerStyle 0x0
+int style DialogAnimationFade 0x0
+int style DialogAnimationSlide 0x0
+int style Platform_AppCompat 0x0
+int style Platform_AppCompat_Light 0x0
+int style Platform_ThemeOverlay_AppCompat 0x0
+int style Platform_ThemeOverlay_AppCompat_Dark 0x0
+int style Platform_ThemeOverlay_AppCompat_Light 0x0
+int style Platform_V21_AppCompat 0x0
+int style Platform_V21_AppCompat_Light 0x0
+int style Platform_V25_AppCompat 0x0
+int style Platform_V25_AppCompat_Light 0x0
+int style Platform_Widget_AppCompat_Spinner 0x0
+int style RtlOverlay_DialogWindowTitle_AppCompat 0x0
+int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x0
+int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x0
+int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x0
+int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x0
+int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton 0x0
+int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x0
+int style SpinnerDatePickerDialog 0x0
+int style SpinnerDatePickerStyle 0x0
+int style TextAppearance_AppCompat 0x0
+int style TextAppearance_AppCompat_Body1 0x0
+int style TextAppearance_AppCompat_Body2 0x0
+int style TextAppearance_AppCompat_Button 0x0
+int style TextAppearance_AppCompat_Caption 0x0
+int style TextAppearance_AppCompat_Display1 0x0
+int style TextAppearance_AppCompat_Display2 0x0
+int style TextAppearance_AppCompat_Display3 0x0
+int style TextAppearance_AppCompat_Display4 0x0
+int style TextAppearance_AppCompat_Headline 0x0
+int style TextAppearance_AppCompat_Inverse 0x0
+int style TextAppearance_AppCompat_Large 0x0
+int style TextAppearance_AppCompat_Large_Inverse 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_Light_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Medium 0x0
+int style TextAppearance_AppCompat_Medium_Inverse 0x0
+int style TextAppearance_AppCompat_Menu 0x0
+int style TextAppearance_AppCompat_SearchResult_Subtitle 0x0
+int style TextAppearance_AppCompat_SearchResult_Title 0x0
+int style TextAppearance_AppCompat_Small 0x0
+int style TextAppearance_AppCompat_Small_Inverse 0x0
+int style TextAppearance_AppCompat_Subhead 0x0
+int style TextAppearance_AppCompat_Subhead_Inverse 0x0
+int style TextAppearance_AppCompat_Title 0x0
+int style TextAppearance_AppCompat_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Tooltip 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x0
+int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_Button 0x0
+int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Colored 0x0
+int style TextAppearance_AppCompat_Widget_Button_Inverse 0x0
+int style TextAppearance_AppCompat_Widget_DropDownItem 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x0
+int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x0
+int style TextAppearance_AppCompat_Widget_Switch 0x0
+int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x0
+int style TextAppearance_Compat_Notification 0x0
+int style TextAppearance_Compat_Notification_Info 0x0
+int style TextAppearance_Compat_Notification_Line2 0x0
+int style TextAppearance_Compat_Notification_Time 0x0
+int style TextAppearance_Compat_Notification_Title 0x0
+int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x0
+int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x0
+int style Theme 0x0
+int style ThemeOverlay_AppCompat 0x0
+int style ThemeOverlay_AppCompat_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dark 0x0
+int style ThemeOverlay_AppCompat_Dark_ActionBar 0x0
+int style ThemeOverlay_AppCompat_DayNight 0x0
+int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x0
+int style ThemeOverlay_AppCompat_Dialog 0x0
+int style ThemeOverlay_AppCompat_Dialog_Alert 0x0
+int style ThemeOverlay_AppCompat_Light 0x0
+int style Theme_AppCompat 0x0
+int style Theme_AppCompat_CompactMenu 0x0
+int style Theme_AppCompat_DayNight 0x0
+int style Theme_AppCompat_DayNight_DarkActionBar 0x0
+int style Theme_AppCompat_DayNight_Dialog 0x0
+int style Theme_AppCompat_DayNight_DialogWhenLarge 0x0
+int style Theme_AppCompat_DayNight_Dialog_Alert 0x0
+int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x0
+int style Theme_AppCompat_DayNight_NoActionBar 0x0
+int style Theme_AppCompat_Dialog 0x0
+int style Theme_AppCompat_DialogWhenLarge 0x0
+int style Theme_AppCompat_Dialog_Alert 0x0
+int style Theme_AppCompat_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Empty 0x0
+int style Theme_AppCompat_Light 0x0
+int style Theme_AppCompat_Light_DarkActionBar 0x0
+int style Theme_AppCompat_Light_Dialog 0x0
+int style Theme_AppCompat_Light_DialogWhenLarge 0x0
+int style Theme_AppCompat_Light_Dialog_Alert 0x0
+int style Theme_AppCompat_Light_Dialog_MinWidth 0x0
+int style Theme_AppCompat_Light_NoActionBar 0x0
+int style Theme_AppCompat_NoActionBar 0x0
+int style Theme_AutofillInlineSuggestion 0x0
+int style Theme_Catalyst 0x0
+int style Theme_Catalyst_LogBox 0x0
+int style Theme_Catalyst_RedBox 0x0
+int style Theme_FullScreenDialog 0x0
+int style Theme_FullScreenDialogAnimatedFade 0x0
+int style Theme_FullScreenDialogAnimatedSlide 0x0
+int style Theme_ReactNative_AppCompat_Light 0x0
+int style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen 0x0
+int style Widget_AppCompat_ActionBar 0x0
+int style Widget_AppCompat_ActionBar_Solid 0x0
+int style Widget_AppCompat_ActionBar_TabBar 0x0
+int style Widget_AppCompat_ActionBar_TabText 0x0
+int style Widget_AppCompat_ActionBar_TabView 0x0
+int style Widget_AppCompat_ActionButton 0x0
+int style Widget_AppCompat_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_ActionButton_Overflow 0x0
+int style Widget_AppCompat_ActionMode 0x0
+int style Widget_AppCompat_ActivityChooserView 0x0
+int style Widget_AppCompat_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Button 0x0
+int style Widget_AppCompat_ButtonBar 0x0
+int style Widget_AppCompat_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Borderless 0x0
+int style Widget_AppCompat_Button_Borderless_Colored 0x0
+int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x0
+int style Widget_AppCompat_Button_Colored 0x0
+int style Widget_AppCompat_Button_Small 0x0
+int style Widget_AppCompat_CompoundButton_CheckBox 0x0
+int style Widget_AppCompat_CompoundButton_RadioButton 0x0
+int style Widget_AppCompat_CompoundButton_Switch 0x0
+int style Widget_AppCompat_DrawerArrowToggle 0x0
+int style Widget_AppCompat_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_EditText 0x0
+int style Widget_AppCompat_ImageButton 0x0
+int style Widget_AppCompat_Light_ActionBar 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid 0x0
+int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar 0x0
+int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText 0x0
+int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView 0x0
+int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x0
+int style Widget_AppCompat_Light_ActionButton 0x0
+int style Widget_AppCompat_Light_ActionButton_CloseMode 0x0
+int style Widget_AppCompat_Light_ActionButton_Overflow 0x0
+int style Widget_AppCompat_Light_ActionMode_Inverse 0x0
+int style Widget_AppCompat_Light_ActivityChooserView 0x0
+int style Widget_AppCompat_Light_AutoCompleteTextView 0x0
+int style Widget_AppCompat_Light_DropDownItem_Spinner 0x0
+int style Widget_AppCompat_Light_ListPopupWindow 0x0
+int style Widget_AppCompat_Light_ListView_DropDown 0x0
+int style Widget_AppCompat_Light_PopupMenu 0x0
+int style Widget_AppCompat_Light_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_Light_SearchView 0x0
+int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_ListMenuView 0x0
+int style Widget_AppCompat_ListPopupWindow 0x0
+int style Widget_AppCompat_ListView 0x0
+int style Widget_AppCompat_ListView_DropDown 0x0
+int style Widget_AppCompat_ListView_Menu 0x0
+int style Widget_AppCompat_PopupMenu 0x0
+int style Widget_AppCompat_PopupMenu_Overflow 0x0
+int style Widget_AppCompat_PopupWindow 0x0
+int style Widget_AppCompat_ProgressBar 0x0
+int style Widget_AppCompat_ProgressBar_Horizontal 0x0
+int style Widget_AppCompat_RatingBar 0x0
+int style Widget_AppCompat_RatingBar_Indicator 0x0
+int style Widget_AppCompat_RatingBar_Small 0x0
+int style Widget_AppCompat_SearchView 0x0
+int style Widget_AppCompat_SearchView_ActionBar 0x0
+int style Widget_AppCompat_SeekBar 0x0
+int style Widget_AppCompat_SeekBar_Discrete 0x0
+int style Widget_AppCompat_Spinner 0x0
+int style Widget_AppCompat_Spinner_DropDown 0x0
+int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x0
+int style Widget_AppCompat_Spinner_Underlined 0x0
+int style Widget_AppCompat_TextView 0x0
+int style Widget_AppCompat_TextView_SpinnerItem 0x0
+int style Widget_AppCompat_Toolbar 0x0
+int style Widget_AppCompat_Toolbar_Button_Navigation 0x0
+int style Widget_Autofill 0x0
+int style Widget_Autofill_InlineSuggestionChip 0x0
+int style Widget_Autofill_InlineSuggestionEndIconStyle 0x0
+int style Widget_Autofill_InlineSuggestionStartIconStyle 0x0
+int style Widget_Autofill_InlineSuggestionSubtitle 0x0
+int style Widget_Autofill_InlineSuggestionTitle 0x0
+int style Widget_Compat_NotificationActionContainer 0x0
+int style Widget_Compat_NotificationActionText 0x0
+int style redboxButton 0x0
+int[] styleable AbstractWheelPicker { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AbstractWheelPicker_wheel_data 0
+int styleable AbstractWheelPicker_wheel_direction 1
+int styleable AbstractWheelPicker_wheel_item_count 2
+int styleable AbstractWheelPicker_wheel_item_index 3
+int styleable AbstractWheelPicker_wheel_item_same_size 4
+int styleable AbstractWheelPicker_wheel_item_space 5
+int styleable AbstractWheelPicker_wheel_style 6
+int styleable AbstractWheelPicker_wheel_text_color 7
+int styleable AbstractWheelPicker_wheel_text_color_current 8
+int styleable AbstractWheelPicker_wheel_text_size 9
+int[] styleable ActionBar { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionBar_background 0
+int styleable ActionBar_backgroundSplit 1
+int styleable ActionBar_backgroundStacked 2
+int styleable ActionBar_contentInsetEnd 3
+int styleable ActionBar_contentInsetEndWithActions 4
+int styleable ActionBar_contentInsetLeft 5
+int styleable ActionBar_contentInsetRight 6
+int styleable ActionBar_contentInsetStart 7
+int styleable ActionBar_contentInsetStartWithNavigation 8
+int styleable ActionBar_customNavigationLayout 9
+int styleable ActionBar_displayOptions 10
+int styleable ActionBar_divider 11
+int styleable ActionBar_elevation 12
+int styleable ActionBar_height 13
+int styleable ActionBar_hideOnContentScroll 14
+int styleable ActionBar_homeAsUpIndicator 15
+int styleable ActionBar_homeLayout 16
+int styleable ActionBar_icon 17
+int styleable ActionBar_indeterminateProgressStyle 18
+int styleable ActionBar_itemPadding 19
+int styleable ActionBar_logo 20
+int styleable ActionBar_navigationMode 21
+int styleable ActionBar_popupTheme 22
+int styleable ActionBar_progressBarPadding 23
+int styleable ActionBar_progressBarStyle 24
+int styleable ActionBar_subtitle 25
+int styleable ActionBar_subtitleTextStyle 26
+int styleable ActionBar_title 27
+int styleable ActionBar_titleTextStyle 28
+int[] styleable ActionBarLayout { 0x10100b3 }
+int styleable ActionBarLayout_android_layout_gravity 0
+int[] styleable ActionMenuItemView { 0x101013f }
+int styleable ActionMenuItemView_android_minWidth 0
+int[] styleable ActionMenuView {  }
+int[] styleable ActionMode { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable ActionMode_background 0
+int styleable ActionMode_backgroundSplit 1
+int styleable ActionMode_closeItemLayout 2
+int styleable ActionMode_height 3
+int styleable ActionMode_subtitleTextStyle 4
+int styleable ActionMode_titleTextStyle 5
+int[] styleable ActivityChooserView { 0x0, 0x0 }
+int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
+int styleable ActivityChooserView_initialActivityCount 1
+int[] styleable AlertDialog { 0x10100f2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AlertDialog_android_layout 0
+int styleable AlertDialog_buttonIconDimen 1
+int styleable AlertDialog_buttonPanelSideLayout 2
+int styleable AlertDialog_listItemLayout 3
+int styleable AlertDialog_listLayout 4
+int styleable AlertDialog_multiChoiceItemLayout 5
+int styleable AlertDialog_showTitle 6
+int styleable AlertDialog_singleChoiceItemLayout 7
+int[] styleable AnimatedStateListDrawableCompat { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable AnimatedStateListDrawableCompat_android_constantSize 0
+int styleable AnimatedStateListDrawableCompat_android_dither 1
+int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 2
+int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 3
+int styleable AnimatedStateListDrawableCompat_android_variablePadding 4
+int styleable AnimatedStateListDrawableCompat_android_visible 5
+int[] styleable AnimatedStateListDrawableItem { 0x1010199, 0x10100d0 }
+int styleable AnimatedStateListDrawableItem_android_drawable 0
+int styleable AnimatedStateListDrawableItem_android_id 1
+int[] styleable AnimatedStateListDrawableTransition { 0x1010199, 0x101044a, 0x101044b, 0x1010449 }
+int styleable AnimatedStateListDrawableTransition_android_drawable 0
+int styleable AnimatedStateListDrawableTransition_android_fromId 1
+int styleable AnimatedStateListDrawableTransition_android_reversible 2
+int styleable AnimatedStateListDrawableTransition_android_toId 3
+int[] styleable AppCompatEmojiHelper {  }
+int[] styleable AppCompatImageView { 0x1010119, 0x0, 0x0, 0x0 }
+int styleable AppCompatImageView_android_src 0
+int styleable AppCompatImageView_srcCompat 1
+int styleable AppCompatImageView_tint 2
+int styleable AppCompatImageView_tintMode 3
+int[] styleable AppCompatSeekBar { 0x1010142, 0x0, 0x0, 0x0 }
+int styleable AppCompatSeekBar_android_thumb 0
+int styleable AppCompatSeekBar_tickMark 1
+int styleable AppCompatSeekBar_tickMarkTint 2
+int styleable AppCompatSeekBar_tickMarkTintMode 3
+int[] styleable AppCompatTextHelper { 0x101016e, 0x1010393, 0x101016f, 0x1010170, 0x1010392, 0x101016d, 0x1010034 }
+int styleable AppCompatTextHelper_android_drawableBottom 0
+int styleable AppCompatTextHelper_android_drawableEnd 1
+int styleable AppCompatTextHelper_android_drawableLeft 2
+int styleable AppCompatTextHelper_android_drawableRight 3
+int styleable AppCompatTextHelper_android_drawableStart 4
+int styleable AppCompatTextHelper_android_drawableTop 5
+int styleable AppCompatTextHelper_android_textAppearance 6
+int[] styleable AppCompatTextView { 0x1010034, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTextView_android_textAppearance 0
+int styleable AppCompatTextView_autoSizeMaxTextSize 1
+int styleable AppCompatTextView_autoSizeMinTextSize 2
+int styleable AppCompatTextView_autoSizePresetSizes 3
+int styleable AppCompatTextView_autoSizeStepGranularity 4
+int styleable AppCompatTextView_autoSizeTextType 5
+int styleable AppCompatTextView_drawableBottomCompat 6
+int styleable AppCompatTextView_drawableEndCompat 7
+int styleable AppCompatTextView_drawableLeftCompat 8
+int styleable AppCompatTextView_drawableRightCompat 9
+int styleable AppCompatTextView_drawableStartCompat 10
+int styleable AppCompatTextView_drawableTint 11
+int styleable AppCompatTextView_drawableTintMode 12
+int styleable AppCompatTextView_drawableTopCompat 13
+int styleable AppCompatTextView_emojiCompatEnabled 14
+int styleable AppCompatTextView_firstBaselineToTopHeight 15
+int styleable AppCompatTextView_fontFamily 16
+int styleable AppCompatTextView_fontVariationSettings 17
+int styleable AppCompatTextView_lastBaselineToBottomHeight 18
+int styleable AppCompatTextView_lineHeight 19
+int styleable AppCompatTextView_textAllCaps 20
+int styleable AppCompatTextView_textLocale 21
+int[] styleable AppCompatTheme { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10100ae, 0x1010057, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable AppCompatTheme_actionBarDivider 0
+int styleable AppCompatTheme_actionBarItemBackground 1
+int styleable AppCompatTheme_actionBarPopupTheme 2
+int styleable AppCompatTheme_actionBarSize 3
+int styleable AppCompatTheme_actionBarSplitStyle 4
+int styleable AppCompatTheme_actionBarStyle 5
+int styleable AppCompatTheme_actionBarTabBarStyle 6
+int styleable AppCompatTheme_actionBarTabStyle 7
+int styleable AppCompatTheme_actionBarTabTextStyle 8
+int styleable AppCompatTheme_actionBarTheme 9
+int styleable AppCompatTheme_actionBarWidgetTheme 10
+int styleable AppCompatTheme_actionButtonStyle 11
+int styleable AppCompatTheme_actionDropDownStyle 12
+int styleable AppCompatTheme_actionMenuTextAppearance 13
+int styleable AppCompatTheme_actionMenuTextColor 14
+int styleable AppCompatTheme_actionModeBackground 15
+int styleable AppCompatTheme_actionModeCloseButtonStyle 16
+int styleable AppCompatTheme_actionModeCloseContentDescription 17
+int styleable AppCompatTheme_actionModeCloseDrawable 18
+int styleable AppCompatTheme_actionModeCopyDrawable 19
+int styleable AppCompatTheme_actionModeCutDrawable 20
+int styleable AppCompatTheme_actionModeFindDrawable 21
+int styleable AppCompatTheme_actionModePasteDrawable 22
+int styleable AppCompatTheme_actionModePopupWindowStyle 23
+int styleable AppCompatTheme_actionModeSelectAllDrawable 24
+int styleable AppCompatTheme_actionModeShareDrawable 25
+int styleable AppCompatTheme_actionModeSplitBackground 26
+int styleable AppCompatTheme_actionModeStyle 27
+int styleable AppCompatTheme_actionModeTheme 28
+int styleable AppCompatTheme_actionModeWebSearchDrawable 29
+int styleable AppCompatTheme_actionOverflowButtonStyle 30
+int styleable AppCompatTheme_actionOverflowMenuStyle 31
+int styleable AppCompatTheme_activityChooserViewStyle 32
+int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
+int styleable AppCompatTheme_alertDialogCenterButtons 34
+int styleable AppCompatTheme_alertDialogStyle 35
+int styleable AppCompatTheme_alertDialogTheme 36
+int styleable AppCompatTheme_android_windowAnimationStyle 37
+int styleable AppCompatTheme_android_windowIsFloating 38
+int styleable AppCompatTheme_autoCompleteTextViewStyle 39
+int styleable AppCompatTheme_borderlessButtonStyle 40
+int styleable AppCompatTheme_buttonBarButtonStyle 41
+int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
+int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
+int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
+int styleable AppCompatTheme_buttonBarStyle 45
+int styleable AppCompatTheme_buttonStyle 46
+int styleable AppCompatTheme_buttonStyleSmall 47
+int styleable AppCompatTheme_checkboxStyle 48
+int styleable AppCompatTheme_checkedTextViewStyle 49
+int styleable AppCompatTheme_colorAccent 50
+int styleable AppCompatTheme_colorBackgroundFloating 51
+int styleable AppCompatTheme_colorButtonNormal 52
+int styleable AppCompatTheme_colorControlActivated 53
+int styleable AppCompatTheme_colorControlHighlight 54
+int styleable AppCompatTheme_colorControlNormal 55
+int styleable AppCompatTheme_colorError 56
+int styleable AppCompatTheme_colorPrimary 57
+int styleable AppCompatTheme_colorPrimaryDark 58
+int styleable AppCompatTheme_colorSwitchThumbNormal 59
+int styleable AppCompatTheme_controlBackground 60
+int styleable AppCompatTheme_dialogCornerRadius 61
+int styleable AppCompatTheme_dialogPreferredPadding 62
+int styleable AppCompatTheme_dialogTheme 63
+int styleable AppCompatTheme_dividerHorizontal 64
+int styleable AppCompatTheme_dividerVertical 65
+int styleable AppCompatTheme_dropDownListViewStyle 66
+int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
+int styleable AppCompatTheme_editTextBackground 68
+int styleable AppCompatTheme_editTextColor 69
+int styleable AppCompatTheme_editTextStyle 70
+int styleable AppCompatTheme_homeAsUpIndicator 71
+int styleable AppCompatTheme_imageButtonStyle 72
+int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
+int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
+int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
+int styleable AppCompatTheme_listDividerAlertDialog 76
+int styleable AppCompatTheme_listMenuViewStyle 77
+int styleable AppCompatTheme_listPopupWindowStyle 78
+int styleable AppCompatTheme_listPreferredItemHeight 79
+int styleable AppCompatTheme_listPreferredItemHeightLarge 80
+int styleable AppCompatTheme_listPreferredItemHeightSmall 81
+int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
+int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
+int styleable AppCompatTheme_listPreferredItemPaddingRight 84
+int styleable AppCompatTheme_listPreferredItemPaddingStart 85
+int styleable AppCompatTheme_panelBackground 86
+int styleable AppCompatTheme_panelMenuListTheme 87
+int styleable AppCompatTheme_panelMenuListWidth 88
+int styleable AppCompatTheme_popupMenuStyle 89
+int styleable AppCompatTheme_popupWindowStyle 90
+int styleable AppCompatTheme_radioButtonStyle 91
+int styleable AppCompatTheme_ratingBarStyle 92
+int styleable AppCompatTheme_ratingBarStyleIndicator 93
+int styleable AppCompatTheme_ratingBarStyleSmall 94
+int styleable AppCompatTheme_searchViewStyle 95
+int styleable AppCompatTheme_seekBarStyle 96
+int styleable AppCompatTheme_selectableItemBackground 97
+int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
+int styleable AppCompatTheme_spinnerDropDownItemStyle 99
+int styleable AppCompatTheme_spinnerStyle 100
+int styleable AppCompatTheme_switchStyle 101
+int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
+int styleable AppCompatTheme_textAppearanceListItem 103
+int styleable AppCompatTheme_textAppearanceListItemSecondary 104
+int styleable AppCompatTheme_textAppearanceListItemSmall 105
+int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
+int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
+int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
+int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
+int styleable AppCompatTheme_textColorAlertDialogListItem 110
+int styleable AppCompatTheme_textColorSearchUrl 111
+int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
+int styleable AppCompatTheme_toolbarStyle 113
+int styleable AppCompatTheme_tooltipForegroundColor 114
+int styleable AppCompatTheme_tooltipFrameBackground 115
+int styleable AppCompatTheme_viewInflaterClass 116
+int styleable AppCompatTheme_windowActionBar 117
+int styleable AppCompatTheme_windowActionBarOverlay 118
+int styleable AppCompatTheme_windowActionModeOverlay 119
+int styleable AppCompatTheme_windowFixedHeightMajor 120
+int styleable AppCompatTheme_windowFixedHeightMinor 121
+int styleable AppCompatTheme_windowFixedWidthMajor 122
+int styleable AppCompatTheme_windowFixedWidthMinor 123
+int styleable AppCompatTheme_windowMinWidthMajor 124
+int styleable AppCompatTheme_windowMinWidthMinor 125
+int styleable AppCompatTheme_windowNoTitle 126
+int[] styleable Autofill_InlineSuggestion { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionChip 0
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionEndIconStyle 1
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionStartIconStyle 2
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionSubtitle 3
+int styleable Autofill_InlineSuggestion_autofillInlineSuggestionTitle 4
+int styleable Autofill_InlineSuggestion_isAutofillInlineSuggestionTheme 5
+int[] styleable ButtonBarLayout { 0x0 }
+int styleable ButtonBarLayout_allowStacking 0
+int[] styleable Capability { 0x0, 0x0 }
+int styleable Capability_queryPatterns 0
+int styleable Capability_shortcutMatchRequired 1
+int[] styleable CheckedTextView { 0x1010108, 0x0, 0x0, 0x0 }
+int styleable CheckedTextView_android_checkMark 0
+int styleable CheckedTextView_checkMarkCompat 1
+int styleable CheckedTextView_checkMarkTint 2
+int styleable CheckedTextView_checkMarkTintMode 3
+int[] styleable ColorStateListItem { 0x0, 0x101031f, 0x10101a5, 0x0, 0x0 }
+int styleable ColorStateListItem_alpha 0
+int styleable ColorStateListItem_android_alpha 1
+int styleable ColorStateListItem_android_color 2
+int styleable ColorStateListItem_android_lStar 3
+int styleable ColorStateListItem_lStar 4
+int[] styleable CompoundButton { 0x1010107, 0x0, 0x0, 0x0 }
+int styleable CompoundButton_android_button 0
+int styleable CompoundButton_buttonCompat 1
+int styleable CompoundButton_buttonTint 2
+int styleable CompoundButton_buttonTintMode 3
+int[] styleable DrawerArrowToggle { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable DrawerArrowToggle_arrowHeadLength 0
+int styleable DrawerArrowToggle_arrowShaftLength 1
+int styleable DrawerArrowToggle_barLength 2
+int styleable DrawerArrowToggle_color 3
+int styleable DrawerArrowToggle_drawableSize 4
+int styleable DrawerArrowToggle_gapBetweenBars 5
+int styleable DrawerArrowToggle_spinBars 6
+int styleable DrawerArrowToggle_thickness 7
+int[] styleable FontFamily { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamily_fontProviderAuthority 0
+int styleable FontFamily_fontProviderCerts 1
+int styleable FontFamily_fontProviderFetchStrategy 2
+int styleable FontFamily_fontProviderFetchTimeout 3
+int styleable FontFamily_fontProviderPackage 4
+int styleable FontFamily_fontProviderQuery 5
+int styleable FontFamily_fontProviderSystemFontFamily 6
+int[] styleable FontFamilyFont { 0x1010532, 0x101053f, 0x1010570, 0x1010533, 0x101056f, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable FontFamilyFont_android_font 0
+int styleable FontFamilyFont_android_fontStyle 1
+int styleable FontFamilyFont_android_fontVariationSettings 2
+int styleable FontFamilyFont_android_fontWeight 3
+int styleable FontFamilyFont_android_ttcIndex 4
+int styleable FontFamilyFont_font 5
+int styleable FontFamilyFont_fontStyle 6
+int styleable FontFamilyFont_fontVariationSettings 7
+int styleable FontFamilyFont_fontWeight 8
+int styleable FontFamilyFont_ttcIndex 9
+int[] styleable Fragment { 0x10100d0, 0x1010003, 0x10100d1 }
+int styleable Fragment_android_id 0
+int styleable Fragment_android_name 1
+int styleable Fragment_android_tag 2
+int[] styleable FragmentContainerView { 0x1010003, 0x10100d1 }
+int styleable FragmentContainerView_android_name 0
+int styleable FragmentContainerView_android_tag 1
+int[] styleable GenericDraweeHierarchy { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable GenericDraweeHierarchy_actualImageScaleType 0
+int styleable GenericDraweeHierarchy_backgroundImage 1
+int styleable GenericDraweeHierarchy_fadeDuration 2
+int styleable GenericDraweeHierarchy_failureImage 3
+int styleable GenericDraweeHierarchy_failureImageScaleType 4
+int styleable GenericDraweeHierarchy_overlayImage 5
+int styleable GenericDraweeHierarchy_placeholderImage 6
+int styleable GenericDraweeHierarchy_placeholderImageScaleType 7
+int styleable GenericDraweeHierarchy_pressedStateOverlayImage 8
+int styleable GenericDraweeHierarchy_progressBarAutoRotateInterval 9
+int styleable GenericDraweeHierarchy_progressBarImage 10
+int styleable GenericDraweeHierarchy_progressBarImageScaleType 11
+int styleable GenericDraweeHierarchy_retryImage 12
+int styleable GenericDraweeHierarchy_retryImageScaleType 13
+int styleable GenericDraweeHierarchy_roundAsCircle 14
+int styleable GenericDraweeHierarchy_roundBottomEnd 15
+int styleable GenericDraweeHierarchy_roundBottomLeft 16
+int styleable GenericDraweeHierarchy_roundBottomRight 17
+int styleable GenericDraweeHierarchy_roundBottomStart 18
+int styleable GenericDraweeHierarchy_roundTopEnd 19
+int styleable GenericDraweeHierarchy_roundTopLeft 20
+int styleable GenericDraweeHierarchy_roundTopRight 21
+int styleable GenericDraweeHierarchy_roundTopStart 22
+int styleable GenericDraweeHierarchy_roundWithOverlayColor 23
+int styleable GenericDraweeHierarchy_roundedCornerRadius 24
+int styleable GenericDraweeHierarchy_roundingBorderColor 25
+int styleable GenericDraweeHierarchy_roundingBorderPadding 26
+int styleable GenericDraweeHierarchy_roundingBorderWidth 27
+int styleable GenericDraweeHierarchy_viewAspectRatio 28
+int[] styleable GradientColor { 0x101020b, 0x10101a2, 0x10101a3, 0x101019e, 0x1010512, 0x1010513, 0x10101a4, 0x101019d, 0x1010510, 0x1010511, 0x1010201, 0x10101a1 }
+int styleable GradientColor_android_centerColor 0
+int styleable GradientColor_android_centerX 1
+int styleable GradientColor_android_centerY 2
+int styleable GradientColor_android_endColor 3
+int styleable GradientColor_android_endX 4
+int styleable GradientColor_android_endY 5
+int styleable GradientColor_android_gradientRadius 6
+int styleable GradientColor_android_startColor 7
+int styleable GradientColor_android_startX 8
+int styleable GradientColor_android_startY 9
+int styleable GradientColor_android_tileMode 10
+int styleable GradientColor_android_type 11
+int[] styleable GradientColorItem { 0x10101a5, 0x1010514 }
+int styleable GradientColorItem_android_color 0
+int styleable GradientColorItem_android_offset 1
+int[] styleable LinearLayoutCompat { 0x1010126, 0x1010127, 0x10100af, 0x10100c4, 0x1010128, 0x0, 0x0, 0x0, 0x0 }
+int styleable LinearLayoutCompat_android_baselineAligned 0
+int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 1
+int styleable LinearLayoutCompat_android_gravity 2
+int styleable LinearLayoutCompat_android_orientation 3
+int styleable LinearLayoutCompat_android_weightSum 4
+int styleable LinearLayoutCompat_divider 5
+int styleable LinearLayoutCompat_dividerPadding 6
+int styleable LinearLayoutCompat_measureWithLargestChild 7
+int styleable LinearLayoutCompat_showDividers 8
+int[] styleable LinearLayoutCompat_Layout { 0x10100b3, 0x10100f5, 0x1010181, 0x10100f4 }
+int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
+int styleable LinearLayoutCompat_Layout_android_layout_height 1
+int styleable LinearLayoutCompat_Layout_android_layout_weight 2
+int styleable LinearLayoutCompat_Layout_android_layout_width 3
+int[] styleable ListPopupWindow { 0x10102ac, 0x10102ad }
+int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
+int styleable ListPopupWindow_android_dropDownVerticalOffset 1
+int[] styleable MenuGroup { 0x10101e0, 0x101000e, 0x10100d0, 0x10101de, 0x10101df, 0x1010194 }
+int styleable MenuGroup_android_checkableBehavior 0
+int styleable MenuGroup_android_enabled 1
+int styleable MenuGroup_android_id 2
+int styleable MenuGroup_android_menuCategory 3
+int styleable MenuGroup_android_orderInCategory 4
+int styleable MenuGroup_android_visible 5
+int[] styleable MenuItem { 0x0, 0x0, 0x0, 0x0, 0x10101e3, 0x10101e5, 0x1010106, 0x101000e, 0x1010002, 0x10100d0, 0x10101de, 0x10101e4, 0x101026f, 0x10101df, 0x10101e1, 0x10101e2, 0x1010194, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable MenuItem_actionLayout 0
+int styleable MenuItem_actionProviderClass 1
+int styleable MenuItem_actionViewClass 2
+int styleable MenuItem_alphabeticModifiers 3
+int styleable MenuItem_android_alphabeticShortcut 4
+int styleable MenuItem_android_checkable 5
+int styleable MenuItem_android_checked 6
+int styleable MenuItem_android_enabled 7
+int styleable MenuItem_android_icon 8
+int styleable MenuItem_android_id 9
+int styleable MenuItem_android_menuCategory 10
+int styleable MenuItem_android_numericShortcut 11
+int styleable MenuItem_android_onClick 12
+int styleable MenuItem_android_orderInCategory 13
+int styleable MenuItem_android_title 14
+int styleable MenuItem_android_titleCondensed 15
+int styleable MenuItem_android_visible 16
+int styleable MenuItem_contentDescription 17
+int styleable MenuItem_iconTint 18
+int styleable MenuItem_iconTintMode 19
+int styleable MenuItem_numericModifiers 20
+int styleable MenuItem_showAsAction 21
+int styleable MenuItem_tooltipText 22
+int[] styleable MenuView { 0x101012f, 0x101012d, 0x1010130, 0x1010131, 0x101012c, 0x101012e, 0x10100ae, 0x0, 0x0 }
+int styleable MenuView_android_headerBackground 0
+int styleable MenuView_android_horizontalDivider 1
+int styleable MenuView_android_itemBackground 2
+int styleable MenuView_android_itemIconDisabledAlpha 3
+int styleable MenuView_android_itemTextAppearance 4
+int styleable MenuView_android_verticalDivider 5
+int styleable MenuView_android_windowAnimationStyle 6
+int styleable MenuView_preserveIconSpacing 7
+int styleable MenuView_subMenuArrow 8
+int[] styleable PopupWindow { 0x10102c9, 0x1010176, 0x0 }
+int styleable PopupWindow_android_popupAnimationStyle 0
+int styleable PopupWindow_android_popupBackground 1
+int styleable PopupWindow_overlapAnchor 2
+int[] styleable PopupWindowBackgroundState { 0x0 }
+int styleable PopupWindowBackgroundState_state_above_anchor 0
+int[] styleable RecycleListView { 0x0, 0x0 }
+int styleable RecycleListView_paddingBottomNoButtons 0
+int styleable RecycleListView_paddingTopNoTitle 1
+int[] styleable SearchView { 0x10100da, 0x1010264, 0x1010220, 0x101011f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SearchView_android_focusable 0
+int styleable SearchView_android_imeOptions 1
+int styleable SearchView_android_inputType 2
+int styleable SearchView_android_maxWidth 3
+int styleable SearchView_closeIcon 4
+int styleable SearchView_commitIcon 5
+int styleable SearchView_defaultQueryHint 6
+int styleable SearchView_goIcon 7
+int styleable SearchView_iconifiedByDefault 8
+int styleable SearchView_layout 9
+int styleable SearchView_queryBackground 10
+int styleable SearchView_queryHint 11
+int styleable SearchView_searchHintIcon 12
+int styleable SearchView_searchIcon 13
+int styleable SearchView_submitBackground 14
+int styleable SearchView_suggestionRowLayout 15
+int styleable SearchView_voiceIcon 16
+int[] styleable SimpleDraweeView { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SimpleDraweeView_actualImageResource 0
+int styleable SimpleDraweeView_actualImageScaleType 1
+int styleable SimpleDraweeView_actualImageUri 2
+int styleable SimpleDraweeView_backgroundImage 3
+int styleable SimpleDraweeView_fadeDuration 4
+int styleable SimpleDraweeView_failureImage 5
+int styleable SimpleDraweeView_failureImageScaleType 6
+int styleable SimpleDraweeView_overlayImage 7
+int styleable SimpleDraweeView_placeholderImage 8
+int styleable SimpleDraweeView_placeholderImageScaleType 9
+int styleable SimpleDraweeView_pressedStateOverlayImage 10
+int styleable SimpleDraweeView_progressBarAutoRotateInterval 11
+int styleable SimpleDraweeView_progressBarImage 12
+int styleable SimpleDraweeView_progressBarImageScaleType 13
+int styleable SimpleDraweeView_retryImage 14
+int styleable SimpleDraweeView_retryImageScaleType 15
+int styleable SimpleDraweeView_roundAsCircle 16
+int styleable SimpleDraweeView_roundBottomEnd 17
+int styleable SimpleDraweeView_roundBottomLeft 18
+int styleable SimpleDraweeView_roundBottomRight 19
+int styleable SimpleDraweeView_roundBottomStart 20
+int styleable SimpleDraweeView_roundTopEnd 21
+int styleable SimpleDraweeView_roundTopLeft 22
+int styleable SimpleDraweeView_roundTopRight 23
+int styleable SimpleDraweeView_roundTopStart 24
+int styleable SimpleDraweeView_roundWithOverlayColor 25
+int styleable SimpleDraweeView_roundedCornerRadius 26
+int styleable SimpleDraweeView_roundingBorderColor 27
+int styleable SimpleDraweeView_roundingBorderPadding 28
+int styleable SimpleDraweeView_roundingBorderWidth 29
+int styleable SimpleDraweeView_viewAspectRatio 30
+int[] styleable Spinner { 0x1010262, 0x10100b2, 0x1010176, 0x101017b, 0x0 }
+int styleable Spinner_android_dropDownWidth 0
+int styleable Spinner_android_entries 1
+int styleable Spinner_android_popupBackground 2
+int styleable Spinner_android_prompt 3
+int styleable Spinner_popupTheme 4
+int[] styleable StateListDrawable { 0x1010196, 0x101011c, 0x101030c, 0x101030d, 0x1010195, 0x1010194 }
+int styleable StateListDrawable_android_constantSize 0
+int styleable StateListDrawable_android_dither 1
+int styleable StateListDrawable_android_enterFadeDuration 2
+int styleable StateListDrawable_android_exitFadeDuration 3
+int styleable StateListDrawable_android_variablePadding 4
+int styleable StateListDrawable_android_visible 5
+int[] styleable StateListDrawableItem { 0x1010199 }
+int styleable StateListDrawableItem_android_drawable 0
+int[] styleable SwitchCompat { 0x1010125, 0x1010124, 0x1010142, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable SwitchCompat_android_textOff 0
+int styleable SwitchCompat_android_textOn 1
+int styleable SwitchCompat_android_thumb 2
+int styleable SwitchCompat_showText 3
+int styleable SwitchCompat_splitTrack 4
+int styleable SwitchCompat_switchMinWidth 5
+int styleable SwitchCompat_switchPadding 6
+int styleable SwitchCompat_switchTextAppearance 7
+int styleable SwitchCompat_thumbTextPadding 8
+int styleable SwitchCompat_thumbTint 9
+int styleable SwitchCompat_thumbTintMode 10
+int styleable SwitchCompat_track 11
+int styleable SwitchCompat_trackTint 12
+int styleable SwitchCompat_trackTintMode 13
+int[] styleable TextAppearance { 0x10103ac, 0x1010161, 0x1010162, 0x1010163, 0x1010164, 0x1010098, 0x101009a, 0x101009b, 0x1010585, 0x1010095, 0x1010097, 0x1010096, 0x0, 0x0, 0x0, 0x0 }
+int styleable TextAppearance_android_fontFamily 0
+int styleable TextAppearance_android_shadowColor 1
+int styleable TextAppearance_android_shadowDx 2
+int styleable TextAppearance_android_shadowDy 3
+int styleable TextAppearance_android_shadowRadius 4
+int styleable TextAppearance_android_textColor 5
+int styleable TextAppearance_android_textColorHint 6
+int styleable TextAppearance_android_textColorLink 7
+int styleable TextAppearance_android_textFontWeight 8
+int styleable TextAppearance_android_textSize 9
+int styleable TextAppearance_android_textStyle 10
+int styleable TextAppearance_android_typeface 11
+int styleable TextAppearance_fontFamily 12
+int styleable TextAppearance_fontVariationSettings 13
+int styleable TextAppearance_textAllCaps 14
+int styleable TextAppearance_textLocale 15
+int[] styleable Toolbar { 0x10100af, 0x1010140, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
+int styleable Toolbar_android_gravity 0
+int styleable Toolbar_android_minHeight 1
+int styleable Toolbar_buttonGravity 2
+int styleable Toolbar_collapseContentDescription 3
+int styleable Toolbar_collapseIcon 4
+int styleable Toolbar_contentInsetEnd 5
+int styleable Toolbar_contentInsetEndWithActions 6
+int styleable Toolbar_contentInsetLeft 7
+int styleable Toolbar_contentInsetRight 8
+int styleable Toolbar_contentInsetStart 9
+int styleable Toolbar_contentInsetStartWithNavigation 10
+int styleable Toolbar_logo 11
+int styleable Toolbar_logoDescription 12
+int styleable Toolbar_maxButtonHeight 13
+int styleable Toolbar_menu 14
+int styleable Toolbar_navigationContentDescription 15
+int styleable Toolbar_navigationIcon 16
+int styleable Toolbar_popupTheme 17
+int styleable Toolbar_subtitle 18
+int styleable Toolbar_subtitleTextAppearance 19
+int styleable Toolbar_subtitleTextColor 20
+int styleable Toolbar_title 21
+int styleable Toolbar_titleMargin 22
+int styleable Toolbar_titleMarginBottom 23
+int styleable Toolbar_titleMarginEnd 24
+int styleable Toolbar_titleMarginStart 25
+int styleable Toolbar_titleMarginTop 26
+int styleable Toolbar_titleMargins 27
+int styleable Toolbar_titleTextAppearance 28
+int styleable Toolbar_titleTextColor 29
+int[] styleable View { 0x10100da, 0x1010000, 0x0, 0x0, 0x0 }
+int styleable View_android_focusable 0
+int styleable View_android_theme 1
+int styleable View_paddingEnd 2
+int styleable View_paddingStart 3
+int styleable View_theme 4
+int[] styleable ViewBackgroundHelper { 0x10100d4, 0x0, 0x0 }
+int styleable ViewBackgroundHelper_android_background 0
+int styleable ViewBackgroundHelper_backgroundTint 1
+int styleable ViewBackgroundHelper_backgroundTintMode 2
+int[] styleable ViewStubCompat { 0x10100d0, 0x10100f3, 0x10100f2 }
+int styleable ViewStubCompat_android_id 0
+int styleable ViewStubCompat_android_inflatedId 1
+int styleable ViewStubCompat_android_layout 2
+int xml rn_dev_preferences 0x0
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compiled_local_resources/release/out/drawable_bg_btn.xml.flat b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compiled_local_resources/release/out/drawable_bg_btn.xml.flat
new file mode 100644
index 0000000..72b491a
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/compiled_local_resources/release/out/drawable_bg_btn.xml.flat differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..f0d3400
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..2aace10
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..c5fe90a
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/shader_assets/release/out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/release/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..fa0b2b1
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1,2 @@
+#Thu Jul 31 12:31:36 ICT 2025
+/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/drawable/bg_btn.xml=/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/drawable/bg_btn.xml
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/merged.dir/values-zh/values-zh.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/merged.dir/values-zh/values-zh.xml
new file mode 100644
index 0000000..290e56a
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/merged.dir/values-zh/values-zh.xml
@@ -0,0 +1,31 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string-array name="WheelArrayConstellation">
+        <item>水瓶座</item>
+        <item>双鱼座</item>
+        <item>白羊座</item>
+        <item>金牛座</item>
+        <item>双子座</item>
+        <item>巨蟹座</item>
+        <item>狮子座</item>
+        <item>处女座</item>
+        <item>天秤座</item>
+        <item>天蝎座</item>
+        <item>射手座</item>
+        <item>摩羯座</item>
+    </string-array>
+    <string-array name="WheelArrayZodiac">
+        <item>鼠</item>
+        <item>牛</item>
+        <item>虎</item>
+        <item>兔</item>
+        <item>龙</item>
+        <item>蛇</item>
+        <item>马</item>
+        <item>羊</item>
+        <item>猴</item>
+        <item>鸡</item>
+        <item>狗</item>
+        <item>猪</item>
+    </string-array>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/merged.dir/values/values.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/merged.dir/values/values.xml
new file mode 100644
index 0000000..38de0c4
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/merged.dir/values/values.xml
@@ -0,0 +1,82 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string-array name="WheelArrayConstellation">
+        <item>Aquarius</item>
+        <item>Pisces</item>
+        <item>Aries</item>
+        <item>Taurus</item>
+        <item>Gemini</item>
+        <item>Cancer</item>
+        <item>Leo</item>
+        <item>Virgo</item>
+        <item>Libra</item>
+        <item>Scorpio</item>
+        <item>Sagittarius</item>
+        <item>Capricorn</item>
+    </string-array>
+    <string-array name="WheelArrayDefault">
+        <item>AigeStudio</item>
+        <item>Aige</item>
+        <item>爱哥</item>
+        <item>الحب  اخي</item>
+        <item>jeg elsker</item>
+        <item>사랑해요 형</item>
+        <item>Amor de irmão</item>
+        <item>armastan</item>
+        <item>愛の兄</item>
+        <item>обичам те</item>
+        <item>любовь - братa</item>
+        <item>miłość bracie</item>
+        <item>Liebe</item>
+        <item>Lamour</item>
+        <item>rakastan sinua</item>
+        <item>láska..</item>
+        <item>dragostea.</item>
+        <item>jag älskar</item>
+        <item>ljubezen, brat.</item>
+        <item>愛哥</item>
+        <item>ชอบพี่</item>
+        <item>αγάπη μου</item>
+        <item>a szerelem.</item>
+        <item>Amore, fratello.</item>
+    </string-array>
+    <string-array name="WheelArrayZodiac">
+        <item>Rat</item>
+        <item>Ox</item>
+        <item>Tiger</item>
+        <item>Hare</item>
+        <item>Dragon</item>
+        <item>Snake</item>
+        <item>Horse</item>
+        <item>Sheep</item>
+        <item>Monkey</item>
+        <item>Cock</item>
+        <item>Dog</item>
+        <item>Boar</item>
+    </string-array>
+    <color name="ColorButtonDisable">#EEE</color>
+    <color name="ColorButtonPressed">#FF7438</color>
+    <color name="ColorButtonRelease">#FCC689</color>
+    <dimen name="WheelItemSpace">8dp</dimen>
+    <dimen name="WheelLabelTextSize">12sp</dimen>
+    <dimen name="WheelPadding">8dp</dimen>
+    <dimen name="WheelTextSize">20sp</dimen>
+    <declare-styleable name="AbstractWheelPicker">
+        <attr format="reference" name="wheel_data"/>
+        <attr format="enum" name="wheel_style">
+            <enum name="straight" value="0"/>
+            <enum name="curved" value="1"/>
+        </attr>
+        <attr format="enum" name="wheel_direction">
+            <enum name="horizontal" value="0"/>
+            <enum name="vertical" value="1"/>
+        </attr>
+        <attr format="integer" name="wheel_item_index"/>
+        <attr format="boolean" name="wheel_item_same_size"/>
+        <attr format="integer" name="wheel_item_count"/>
+        <attr format="dimension" name="wheel_item_space"/>
+        <attr format="dimension" name="wheel_text_size"/>
+        <attr format="color" name="wheel_text_color"/>
+        <attr format="color" name="wheel_text_color_current"/>
+    </declare-styleable>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/merger.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..dea37b4
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/incremental/packageReleaseResources/merger.xml
@@ -0,0 +1,113 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res"><file name="bg_btn" path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/drawable/bg_btn.xml" qualifiers="" type="drawable"/><file path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/colors.xml" qualifiers=""><color name="ColorButtonRelease">#FCC689</color><color name="ColorButtonPressed">#FF7438</color><color name="ColorButtonDisable">#EEE</color></file><file path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/dimens.xml" qualifiers=""><dimen name="WheelItemSpace">8dp</dimen><dimen name="WheelPadding">8dp</dimen><dimen name="WheelTextSize">20sp</dimen><dimen name="WheelLabelTextSize">12sp</dimen></file><file path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/arrays.xml" qualifiers=""><string-array name="WheelArrayDefault">
+        <item>AigeStudio</item>
+        <item>Aige</item>
+        <item>爱哥</item>
+        <item>الحب  اخي</item>
+        <item>jeg elsker</item>
+        <item>사랑해요 형</item>
+        <item>Amor de irmão</item>
+        <item>armastan</item>
+        <item>愛の兄</item>
+        <item>обичам те</item>
+        <item>любовь - братa</item>
+        <item>miłość bracie</item>
+        <item>Liebe</item>
+        <item>Lamour</item>
+        <item>rakastan sinua</item>
+        <item>láska..</item>
+        <item>dragostea.</item>
+        <item>jag älskar</item>
+        <item>ljubezen, brat.</item>
+        <item>愛哥</item>
+        <item>ชอบพี่</item>
+        <item>αγάπη μου</item>
+        <item>a szerelem.</item>
+        <item>Amore, fratello.</item>
+    </string-array><string-array name="WheelArrayConstellation">
+        <item>Aquarius</item>
+        <item>Pisces</item>
+        <item>Aries</item>
+        <item>Taurus</item>
+        <item>Gemini</item>
+        <item>Cancer</item>
+        <item>Leo</item>
+        <item>Virgo</item>
+        <item>Libra</item>
+        <item>Scorpio</item>
+        <item>Sagittarius</item>
+        <item>Capricorn</item>
+    </string-array><string-array name="WheelArrayZodiac">
+        <item>Rat</item>
+        <item>Ox</item>
+        <item>Tiger</item>
+        <item>Hare</item>
+        <item>Dragon</item>
+        <item>Snake</item>
+        <item>Horse</item>
+        <item>Sheep</item>
+        <item>Monkey</item>
+        <item>Cock</item>
+        <item>Dog</item>
+        <item>Boar</item>
+    </string-array></file><file path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/attrs.xml" qualifiers=""><declare-styleable name="AbstractWheelPicker">
+        <attr format="reference" name="wheel_data"/>
+        <attr format="enum" name="wheel_style">
+            <enum name="straight" value="0"/>
+            <enum name="curved" value="1"/>
+        </attr>
+        <attr format="enum" name="wheel_direction">
+            <enum name="horizontal" value="0"/>
+            <enum name="vertical" value="1"/>
+        </attr>
+        <attr format="integer" name="wheel_item_index"/>
+        <attr format="boolean" name="wheel_item_same_size"/>
+        <attr format="integer" name="wheel_item_count"/>
+        <attr format="dimension" name="wheel_item_space"/>
+        <attr format="dimension" name="wheel_text_size"/>
+        <attr format="color" name="wheel_text_color"/>
+        <attr format="color" name="wheel_text_color_current"/>
+    </declare-styleable></file><file path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values-zh/arrays.xml" qualifiers="zh"><string-array name="WheelArrayConstellation">
+        <item>水瓶座</item>
+        <item>双鱼座</item>
+        <item>白羊座</item>
+        <item>金牛座</item>
+        <item>双子座</item>
+        <item>巨蟹座</item>
+        <item>狮子座</item>
+        <item>处女座</item>
+        <item>天秤座</item>
+        <item>天蝎座</item>
+        <item>射手座</item>
+        <item>摩羯座</item>
+    </string-array><string-array name="WheelArrayZodiac">
+        <item>鼠</item>
+        <item>牛</item>
+        <item>虎</item>
+        <item>兔</item>
+        <item>龙</item>
+        <item>蛇</item>
+        <item>马</item>
+        <item>羊</item>
+        <item>猴</item>
+        <item>鸡</item>
+        <item>狗</item>
+        <item>猪</item>
+    </string-array></file></source><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/release/res"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="AbstractWheelPicker">
+        <attr format="reference" name="wheel_data"/>
+        <attr format="enum" name="wheel_style">
+            <enum name="straight" value="0"/>
+            <enum name="curved" value="1"/>
+        </attr>
+        <attr format="enum" name="wheel_direction">
+            <enum name="horizontal" value="0"/>
+            <enum name="vertical" value="1"/>
+        </attr>
+        <attr format="integer" name="wheel_item_index"/>
+        <attr format="boolean" name="wheel_item_same_size"/>
+        <attr format="integer" name="wheel_item_count"/>
+        <attr format="dimension" name="wheel_item_space"/>
+        <attr format="dimension" name="wheel_text_size"/>
+        <attr format="color" name="wheel_text_color"/>
+        <attr format="color" name="wheel_text_color_current"/>
+    </declare-styleable></configuration></mergedItems></merger>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelDecor.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelDecor.class
new file mode 100644
index 0000000..9dd061d
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelDecor.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelPicker$OnWheelChangeListener.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelPicker$OnWheelChangeListener.class
new file mode 100644
index 0000000..57b65b4
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelPicker$OnWheelChangeListener.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelPicker$SimpleWheelChangeListener.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelPicker$SimpleWheelChangeListener.class
new file mode 100644
index 0000000..44f2644
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelPicker$SimpleWheelChangeListener.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelPicker.class
new file mode 100644
index 0000000..f79f161
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/AbstractWheelPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/IWheelPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/IWheelPicker.class
new file mode 100644
index 0000000..0dba99c
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/IWheelPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/OverScrollerCompat.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/OverScrollerCompat.class
new file mode 100644
index 0000000..8857ba1
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/OverScrollerCompat.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/ScrollerCompat.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/ScrollerCompat.class
new file mode 100644
index 0000000..1cab171
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/ScrollerCompat.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/WheelScroller.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/WheelScroller.class
new file mode 100644
index 0000000..0aa7111
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/core/WheelScroller.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/CrossHorImpl.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/CrossHorImpl.class
new file mode 100644
index 0000000..0a56687
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/CrossHorImpl.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/CrossVerImpl.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/CrossVerImpl.class
new file mode 100644
index 0000000..3966a9f
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/CrossVerImpl.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/ICrossOrientation.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/ICrossOrientation.class
new file mode 100644
index 0000000..dafe61c
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/ICrossOrientation.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/IWheelCrossPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/IWheelCrossPicker.class
new file mode 100644
index 0000000..e2748f7
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/IWheelCrossPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelCircularPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelCircularPicker.class
new file mode 100644
index 0000000..3892e61
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelCircularPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelCrossPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelCrossPicker.class
new file mode 100644
index 0000000..79be3fc
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelCrossPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelCurvedPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelCurvedPicker.class
new file mode 100644
index 0000000..221a1b5
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelCurvedPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelStraightPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelStraightPicker.class
new file mode 100644
index 0000000..0936116
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/view/WheelStraightPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/IDigital.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/IDigital.class
new file mode 100644
index 0000000..043e070
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/IDigital.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker$1.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker$1.class
new file mode 100644
index 0000000..f19ebd9
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker$1.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker$2.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker$2.class
new file mode 100644
index 0000000..dd719f7
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker$2.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker.class
new file mode 100644
index 0000000..3a7565a
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDayPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDayPicker.class
new file mode 100644
index 0000000..b716bfa
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelDayPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelHourPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelHourPicker.class
new file mode 100644
index 0000000..cf98985
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelHourPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelMinutePicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelMinutePicker.class
new file mode 100644
index 0000000..889e201
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelMinutePicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelMonthPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelMonthPicker.class
new file mode 100644
index 0000000..2a6809f
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelMonthPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker$1.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker$1.class
new file mode 100644
index 0000000..d349d0b
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker$1.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker$2.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker$2.class
new file mode 100644
index 0000000..2529683
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker$2.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker.class
new file mode 100644
index 0000000..6589106
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelYearPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelYearPicker.class
new file mode 100644
index 0000000..b3841c1
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/aigestudio/wheelpicker/widget/curved/WheelYearPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/BuildConfig.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/BuildConfig.class
new file mode 100644
index 0000000..affa6e9
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/BuildConfig.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ItemSelectedEvent.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ItemSelectedEvent.class
new file mode 100644
index 0000000..daa5a91
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ItemSelectedEvent.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactNativeWheelPickerPackage.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactNativeWheelPickerPackage.class
new file mode 100644
index 0000000..79157ac
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactNativeWheelPickerPackage.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactWheelCurvedPicker$1.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactWheelCurvedPicker$1.class
new file mode 100644
index 0000000..0d51021
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactWheelCurvedPicker$1.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactWheelCurvedPicker.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactWheelCurvedPicker.class
new file mode 100644
index 0000000..d4c38cf
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactWheelCurvedPicker.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactWheelCurvedPickerManager.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactWheelCurvedPickerManager.class
new file mode 100644
index 0000000..5718f2f
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/ReactWheelCurvedPickerManager.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/Utils.class b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/Utils.class
new file mode 100644
index 0000000..0840e92
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/javac/release/classes/com/zyu/Utils.class differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..e8f19bc
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,28 @@
+R_DEF: Internal format may change without notice
+local
+array WheelArrayConstellation
+array WheelArrayDefault
+array WheelArrayZodiac
+attr? wheel_data
+attr? wheel_direction
+attr? wheel_item_count
+attr? wheel_item_index
+attr? wheel_item_same_size
+attr? wheel_item_space
+attr? wheel_style
+attr? wheel_text_color
+attr? wheel_text_color_current
+attr? wheel_text_size
+color ColorButtonDisable
+color ColorButtonPressed
+color ColorButtonRelease
+dimen WheelItemSpace
+dimen WheelLabelTextSize
+dimen WheelPadding
+dimen WheelTextSize
+drawable bg_btn
+id curved
+id horizontal
+id straight
+id vertical
+styleable AbstractWheelPicker wheel_data wheel_style wheel_direction wheel_item_index wheel_item_same_size wheel_item_count wheel_item_space wheel_text_size wheel_text_color wheel_text_color_current
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..12fa1e4
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.zyu" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="25"
+6-->/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="28" />
+7-->/Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..2d9a640
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.zyu" >
+
+    <uses-sdk
+        android:minSdkVersion="25"
+        android:targetSdkVersion="28" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_manifests/release/output-metadata.json b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_manifests/release/output-metadata.json
new file mode 100644
index 0000000..ae8745e
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_manifests/release/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.zyu",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/release/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/drawable/bg_btn.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/drawable/bg_btn.xml
new file mode 100644
index 0000000..06fa126
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/drawable/bg_btn.xml
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="utf-8"?>
+<selector xmlns:android="http://schemas.android.com/apk/res/android">
+    <item android:drawable="@color/ColorButtonPressed" android:state_pressed="true" />
+    <item android:drawable="@color/ColorButtonDisable" android:state_enabled="false" />
+    <item android:drawable="@color/ColorButtonRelease" />
+</selector>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/values-zh/values-zh.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/values-zh/values-zh.xml
new file mode 100644
index 0000000..290e56a
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/values-zh/values-zh.xml
@@ -0,0 +1,31 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string-array name="WheelArrayConstellation">
+        <item>水瓶座</item>
+        <item>双鱼座</item>
+        <item>白羊座</item>
+        <item>金牛座</item>
+        <item>双子座</item>
+        <item>巨蟹座</item>
+        <item>狮子座</item>
+        <item>处女座</item>
+        <item>天秤座</item>
+        <item>天蝎座</item>
+        <item>射手座</item>
+        <item>摩羯座</item>
+    </string-array>
+    <string-array name="WheelArrayZodiac">
+        <item>鼠</item>
+        <item>牛</item>
+        <item>虎</item>
+        <item>兔</item>
+        <item>龙</item>
+        <item>蛇</item>
+        <item>马</item>
+        <item>羊</item>
+        <item>猴</item>
+        <item>鸡</item>
+        <item>狗</item>
+        <item>猪</item>
+    </string-array>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/values/values.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/values/values.xml
new file mode 100644
index 0000000..38de0c4
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/packaged_res/release/values/values.xml
@@ -0,0 +1,82 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string-array name="WheelArrayConstellation">
+        <item>Aquarius</item>
+        <item>Pisces</item>
+        <item>Aries</item>
+        <item>Taurus</item>
+        <item>Gemini</item>
+        <item>Cancer</item>
+        <item>Leo</item>
+        <item>Virgo</item>
+        <item>Libra</item>
+        <item>Scorpio</item>
+        <item>Sagittarius</item>
+        <item>Capricorn</item>
+    </string-array>
+    <string-array name="WheelArrayDefault">
+        <item>AigeStudio</item>
+        <item>Aige</item>
+        <item>爱哥</item>
+        <item>الحب  اخي</item>
+        <item>jeg elsker</item>
+        <item>사랑해요 형</item>
+        <item>Amor de irmão</item>
+        <item>armastan</item>
+        <item>愛の兄</item>
+        <item>обичам те</item>
+        <item>любовь - братa</item>
+        <item>miłość bracie</item>
+        <item>Liebe</item>
+        <item>Lamour</item>
+        <item>rakastan sinua</item>
+        <item>láska..</item>
+        <item>dragostea.</item>
+        <item>jag älskar</item>
+        <item>ljubezen, brat.</item>
+        <item>愛哥</item>
+        <item>ชอบพี่</item>
+        <item>αγάπη μου</item>
+        <item>a szerelem.</item>
+        <item>Amore, fratello.</item>
+    </string-array>
+    <string-array name="WheelArrayZodiac">
+        <item>Rat</item>
+        <item>Ox</item>
+        <item>Tiger</item>
+        <item>Hare</item>
+        <item>Dragon</item>
+        <item>Snake</item>
+        <item>Horse</item>
+        <item>Sheep</item>
+        <item>Monkey</item>
+        <item>Cock</item>
+        <item>Dog</item>
+        <item>Boar</item>
+    </string-array>
+    <color name="ColorButtonDisable">#EEE</color>
+    <color name="ColorButtonPressed">#FF7438</color>
+    <color name="ColorButtonRelease">#FCC689</color>
+    <dimen name="WheelItemSpace">8dp</dimen>
+    <dimen name="WheelLabelTextSize">12sp</dimen>
+    <dimen name="WheelPadding">8dp</dimen>
+    <dimen name="WheelTextSize">20sp</dimen>
+    <declare-styleable name="AbstractWheelPicker">
+        <attr format="reference" name="wheel_data"/>
+        <attr format="enum" name="wheel_style">
+            <enum name="straight" value="0"/>
+            <enum name="curved" value="1"/>
+        </attr>
+        <attr format="enum" name="wheel_direction">
+            <enum name="horizontal" value="0"/>
+            <enum name="vertical" value="1"/>
+        </attr>
+        <attr format="integer" name="wheel_item_index"/>
+        <attr format="boolean" name="wheel_item_same_size"/>
+        <attr format="integer" name="wheel_item_count"/>
+        <attr format="dimension" name="wheel_item_space"/>
+        <attr format="dimension" name="wheel_text_size"/>
+        <attr format="color" name="wheel_text_color"/>
+        <attr format="color" name="wheel_text_color_current"/>
+    </declare-styleable>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..8733bef
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/runtime_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..76267a4
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1,1477 @@
+com.zyu
+anim abc_fade_in
+anim abc_fade_out
+anim abc_grow_fade_in_from_bottom
+anim abc_popup_enter
+anim abc_popup_exit
+anim abc_shrink_fade_out_from_bottom
+anim abc_slide_in_bottom
+anim abc_slide_in_top
+anim abc_slide_out_bottom
+anim abc_slide_out_top
+anim abc_tooltip_enter
+anim abc_tooltip_exit
+anim btn_checkbox_to_checked_box_inner_merged_animation
+anim btn_checkbox_to_checked_box_outer_merged_animation
+anim btn_checkbox_to_checked_icon_null_animation
+anim btn_checkbox_to_unchecked_box_inner_merged_animation
+anim btn_checkbox_to_unchecked_check_path_merged_animation
+anim btn_checkbox_to_unchecked_icon_null_animation
+anim btn_radio_to_off_mtrl_dot_group_animation
+anim btn_radio_to_off_mtrl_ring_outer_animation
+anim btn_radio_to_off_mtrl_ring_outer_path_animation
+anim btn_radio_to_on_mtrl_dot_group_animation
+anim btn_radio_to_on_mtrl_ring_outer_animation
+anim btn_radio_to_on_mtrl_ring_outer_path_animation
+anim catalyst_fade_in
+anim catalyst_fade_out
+anim catalyst_push_up_in
+anim catalyst_push_up_out
+anim catalyst_slide_down
+anim catalyst_slide_up
+anim fragment_fast_out_extra_slow_in
+animator fragment_close_enter
+animator fragment_close_exit
+animator fragment_fade_enter
+animator fragment_fade_exit
+animator fragment_open_enter
+animator fragment_open_exit
+array WheelArrayConstellation
+array WheelArrayDefault
+array WheelArrayZodiac
+attr actionBarDivider
+attr actionBarItemBackground
+attr actionBarPopupTheme
+attr actionBarSize
+attr actionBarSplitStyle
+attr actionBarStyle
+attr actionBarTabBarStyle
+attr actionBarTabStyle
+attr actionBarTabTextStyle
+attr actionBarTheme
+attr actionBarWidgetTheme
+attr actionButtonStyle
+attr actionDropDownStyle
+attr actionLayout
+attr actionMenuTextAppearance
+attr actionMenuTextColor
+attr actionModeBackground
+attr actionModeCloseButtonStyle
+attr actionModeCloseContentDescription
+attr actionModeCloseDrawable
+attr actionModeCopyDrawable
+attr actionModeCutDrawable
+attr actionModeFindDrawable
+attr actionModePasteDrawable
+attr actionModePopupWindowStyle
+attr actionModeSelectAllDrawable
+attr actionModeShareDrawable
+attr actionModeSplitBackground
+attr actionModeStyle
+attr actionModeTheme
+attr actionModeWebSearchDrawable
+attr actionOverflowButtonStyle
+attr actionOverflowMenuStyle
+attr actionProviderClass
+attr actionViewClass
+attr activityChooserViewStyle
+attr actualImageResource
+attr actualImageScaleType
+attr actualImageUri
+attr alertDialogButtonGroupStyle
+attr alertDialogCenterButtons
+attr alertDialogStyle
+attr alertDialogTheme
+attr allowStacking
+attr alpha
+attr alphabeticModifiers
+attr arrowHeadLength
+attr arrowShaftLength
+attr autoCompleteTextViewStyle
+attr autoSizeMaxTextSize
+attr autoSizeMinTextSize
+attr autoSizePresetSizes
+attr autoSizeStepGranularity
+attr autoSizeTextType
+attr autofillInlineSuggestionChip
+attr autofillInlineSuggestionEndIconStyle
+attr autofillInlineSuggestionStartIconStyle
+attr autofillInlineSuggestionSubtitle
+attr autofillInlineSuggestionTitle
+attr background
+attr backgroundImage
+attr backgroundSplit
+attr backgroundStacked
+attr backgroundTint
+attr backgroundTintMode
+attr barLength
+attr borderlessButtonStyle
+attr buttonBarButtonStyle
+attr buttonBarNegativeButtonStyle
+attr buttonBarNeutralButtonStyle
+attr buttonBarPositiveButtonStyle
+attr buttonBarStyle
+attr buttonCompat
+attr buttonGravity
+attr buttonIconDimen
+attr buttonPanelSideLayout
+attr buttonStyle
+attr buttonStyleSmall
+attr buttonTint
+attr buttonTintMode
+attr checkMarkCompat
+attr checkMarkTint
+attr checkMarkTintMode
+attr checkboxStyle
+attr checkedTextViewStyle
+attr closeIcon
+attr closeItemLayout
+attr collapseContentDescription
+attr collapseIcon
+attr color
+attr colorAccent
+attr colorBackgroundFloating
+attr colorButtonNormal
+attr colorControlActivated
+attr colorControlHighlight
+attr colorControlNormal
+attr colorError
+attr colorPrimary
+attr colorPrimaryDark
+attr colorSwitchThumbNormal
+attr commitIcon
+attr contentDescription
+attr contentInsetEnd
+attr contentInsetEndWithActions
+attr contentInsetLeft
+attr contentInsetRight
+attr contentInsetStart
+attr contentInsetStartWithNavigation
+attr controlBackground
+attr customNavigationLayout
+attr defaultQueryHint
+attr dialogCornerRadius
+attr dialogPreferredPadding
+attr dialogTheme
+attr displayOptions
+attr divider
+attr dividerHorizontal
+attr dividerPadding
+attr dividerVertical
+attr drawableBottomCompat
+attr drawableEndCompat
+attr drawableLeftCompat
+attr drawableRightCompat
+attr drawableSize
+attr drawableStartCompat
+attr drawableTint
+attr drawableTintMode
+attr drawableTopCompat
+attr drawerArrowStyle
+attr dropDownListViewStyle
+attr dropdownListPreferredItemHeight
+attr editTextBackground
+attr editTextColor
+attr editTextStyle
+attr elevation
+attr emojiCompatEnabled
+attr expandActivityOverflowButtonDrawable
+attr fadeDuration
+attr failureImage
+attr failureImageScaleType
+attr firstBaselineToTopHeight
+attr font
+attr fontFamily
+attr fontProviderAuthority
+attr fontProviderCerts
+attr fontProviderFetchStrategy
+attr fontProviderFetchTimeout
+attr fontProviderPackage
+attr fontProviderQuery
+attr fontProviderSystemFontFamily
+attr fontStyle
+attr fontVariationSettings
+attr fontWeight
+attr gapBetweenBars
+attr goIcon
+attr height
+attr hideOnContentScroll
+attr homeAsUpIndicator
+attr homeLayout
+attr icon
+attr iconTint
+attr iconTintMode
+attr iconifiedByDefault
+attr imageButtonStyle
+attr indeterminateProgressStyle
+attr initialActivityCount
+attr isAutofillInlineSuggestionTheme
+attr isLightTheme
+attr itemPadding
+attr lStar
+attr lastBaselineToBottomHeight
+attr layout
+attr lineHeight
+attr listChoiceBackgroundIndicator
+attr listChoiceIndicatorMultipleAnimated
+attr listChoiceIndicatorSingleAnimated
+attr listDividerAlertDialog
+attr listItemLayout
+attr listLayout
+attr listMenuViewStyle
+attr listPopupWindowStyle
+attr listPreferredItemHeight
+attr listPreferredItemHeightLarge
+attr listPreferredItemHeightSmall
+attr listPreferredItemPaddingEnd
+attr listPreferredItemPaddingLeft
+attr listPreferredItemPaddingRight
+attr listPreferredItemPaddingStart
+attr logo
+attr logoDescription
+attr maxButtonHeight
+attr measureWithLargestChild
+attr menu
+attr multiChoiceItemLayout
+attr navigationContentDescription
+attr navigationIcon
+attr navigationMode
+attr nestedScrollViewStyle
+attr numericModifiers
+attr overlapAnchor
+attr overlayImage
+attr paddingBottomNoButtons
+attr paddingEnd
+attr paddingStart
+attr paddingTopNoTitle
+attr panelBackground
+attr panelMenuListTheme
+attr panelMenuListWidth
+attr placeholderImage
+attr placeholderImageScaleType
+attr popupMenuStyle
+attr popupTheme
+attr popupWindowStyle
+attr preserveIconSpacing
+attr pressedStateOverlayImage
+attr progressBarAutoRotateInterval
+attr progressBarImage
+attr progressBarImageScaleType
+attr progressBarPadding
+attr progressBarStyle
+attr queryBackground
+attr queryHint
+attr queryPatterns
+attr radioButtonStyle
+attr ratingBarStyle
+attr ratingBarStyleIndicator
+attr ratingBarStyleSmall
+attr retryImage
+attr retryImageScaleType
+attr roundAsCircle
+attr roundBottomEnd
+attr roundBottomLeft
+attr roundBottomRight
+attr roundBottomStart
+attr roundTopEnd
+attr roundTopLeft
+attr roundTopRight
+attr roundTopStart
+attr roundWithOverlayColor
+attr roundedCornerRadius
+attr roundingBorderColor
+attr roundingBorderPadding
+attr roundingBorderWidth
+attr searchHintIcon
+attr searchIcon
+attr searchViewStyle
+attr seekBarStyle
+attr selectableItemBackground
+attr selectableItemBackgroundBorderless
+attr shortcutMatchRequired
+attr showAsAction
+attr showDividers
+attr showText
+attr showTitle
+attr singleChoiceItemLayout
+attr spinBars
+attr spinnerDropDownItemStyle
+attr spinnerStyle
+attr splitTrack
+attr srcCompat
+attr state_above_anchor
+attr subMenuArrow
+attr submitBackground
+attr subtitle
+attr subtitleTextAppearance
+attr subtitleTextColor
+attr subtitleTextStyle
+attr suggestionRowLayout
+attr switchMinWidth
+attr switchPadding
+attr switchStyle
+attr switchTextAppearance
+attr textAllCaps
+attr textAppearanceLargePopupMenu
+attr textAppearanceListItem
+attr textAppearanceListItemSecondary
+attr textAppearanceListItemSmall
+attr textAppearancePopupMenuHeader
+attr textAppearanceSearchResultSubtitle
+attr textAppearanceSearchResultTitle
+attr textAppearanceSmallPopupMenu
+attr textColorAlertDialogListItem
+attr textColorSearchUrl
+attr textLocale
+attr theme
+attr thickness
+attr thumbTextPadding
+attr thumbTint
+attr thumbTintMode
+attr tickMark
+attr tickMarkTint
+attr tickMarkTintMode
+attr tint
+attr tintMode
+attr title
+attr titleMargin
+attr titleMarginBottom
+attr titleMarginEnd
+attr titleMarginStart
+attr titleMarginTop
+attr titleMargins
+attr titleTextAppearance
+attr titleTextColor
+attr titleTextStyle
+attr toolbarNavigationButtonStyle
+attr toolbarStyle
+attr tooltipForegroundColor
+attr tooltipFrameBackground
+attr tooltipText
+attr track
+attr trackTint
+attr trackTintMode
+attr ttcIndex
+attr viewAspectRatio
+attr viewInflaterClass
+attr voiceIcon
+attr wheel_data
+attr wheel_direction
+attr wheel_item_count
+attr wheel_item_index
+attr wheel_item_same_size
+attr wheel_item_space
+attr wheel_style
+attr wheel_text_color
+attr wheel_text_color_current
+attr wheel_text_size
+attr windowActionBar
+attr windowActionBarOverlay
+attr windowActionModeOverlay
+attr windowFixedHeightMajor
+attr windowFixedHeightMinor
+attr windowFixedWidthMajor
+attr windowFixedWidthMinor
+attr windowMinWidthMajor
+attr windowMinWidthMinor
+attr windowNoTitle
+bool abc_action_bar_embed_tabs
+bool abc_config_actionMenuItemAllCaps
+color ColorButtonDisable
+color ColorButtonPressed
+color ColorButtonRelease
+color abc_background_cache_hint_selector_material_dark
+color abc_background_cache_hint_selector_material_light
+color abc_btn_colored_borderless_text_material
+color abc_btn_colored_text_material
+color abc_color_highlight_material
+color abc_decor_view_status_guard
+color abc_decor_view_status_guard_light
+color abc_hint_foreground_material_dark
+color abc_hint_foreground_material_light
+color abc_primary_text_disable_only_material_dark
+color abc_primary_text_disable_only_material_light
+color abc_primary_text_material_dark
+color abc_primary_text_material_light
+color abc_search_url_text
+color abc_search_url_text_normal
+color abc_search_url_text_pressed
+color abc_search_url_text_selected
+color abc_secondary_text_material_dark
+color abc_secondary_text_material_light
+color abc_tint_btn_checkable
+color abc_tint_default
+color abc_tint_edittext
+color abc_tint_seek_thumb
+color abc_tint_spinner
+color abc_tint_switch_track
+color accent_material_dark
+color accent_material_light
+color androidx_core_ripple_material_light
+color androidx_core_secondary_text_default_material_light
+color background_floating_material_dark
+color background_floating_material_light
+color background_material_dark
+color background_material_light
+color bright_foreground_disabled_material_dark
+color bright_foreground_disabled_material_light
+color bright_foreground_inverse_material_dark
+color bright_foreground_inverse_material_light
+color bright_foreground_material_dark
+color bright_foreground_material_light
+color button_material_dark
+color button_material_light
+color catalyst_logbox_background
+color catalyst_redbox_background
+color dim_foreground_disabled_material_dark
+color dim_foreground_disabled_material_light
+color dim_foreground_material_dark
+color dim_foreground_material_light
+color error_color_material_dark
+color error_color_material_light
+color foreground_material_dark
+color foreground_material_light
+color highlighted_text_material_dark
+color highlighted_text_material_light
+color material_blue_grey_800
+color material_blue_grey_900
+color material_blue_grey_950
+color material_deep_teal_200
+color material_deep_teal_500
+color material_grey_100
+color material_grey_300
+color material_grey_50
+color material_grey_600
+color material_grey_800
+color material_grey_850
+color material_grey_900
+color notification_action_color_filter
+color notification_icon_bg_color
+color primary_dark_material_dark
+color primary_dark_material_light
+color primary_material_dark
+color primary_material_light
+color primary_text_default_material_dark
+color primary_text_default_material_light
+color primary_text_disabled_material_dark
+color primary_text_disabled_material_light
+color ripple_material_dark
+color ripple_material_light
+color secondary_text_default_material_dark
+color secondary_text_default_material_light
+color secondary_text_disabled_material_dark
+color secondary_text_disabled_material_light
+color switch_thumb_disabled_material_dark
+color switch_thumb_disabled_material_light
+color switch_thumb_material_dark
+color switch_thumb_material_light
+color switch_thumb_normal_material_dark
+color switch_thumb_normal_material_light
+color tooltip_background_dark
+color tooltip_background_light
+dimen WheelItemSpace
+dimen WheelLabelTextSize
+dimen WheelPadding
+dimen WheelTextSize
+dimen abc_action_bar_content_inset_material
+dimen abc_action_bar_content_inset_with_nav
+dimen abc_action_bar_default_height_material
+dimen abc_action_bar_default_padding_end_material
+dimen abc_action_bar_default_padding_start_material
+dimen abc_action_bar_elevation_material
+dimen abc_action_bar_icon_vertical_padding_material
+dimen abc_action_bar_overflow_padding_end_material
+dimen abc_action_bar_overflow_padding_start_material
+dimen abc_action_bar_stacked_max_height
+dimen abc_action_bar_stacked_tab_max_width
+dimen abc_action_bar_subtitle_bottom_margin_material
+dimen abc_action_bar_subtitle_top_margin_material
+dimen abc_action_button_min_height_material
+dimen abc_action_button_min_width_material
+dimen abc_action_button_min_width_overflow_material
+dimen abc_alert_dialog_button_bar_height
+dimen abc_alert_dialog_button_dimen
+dimen abc_button_inset_horizontal_material
+dimen abc_button_inset_vertical_material
+dimen abc_button_padding_horizontal_material
+dimen abc_button_padding_vertical_material
+dimen abc_cascading_menus_min_smallest_width
+dimen abc_config_prefDialogWidth
+dimen abc_control_corner_material
+dimen abc_control_inset_material
+dimen abc_control_padding_material
+dimen abc_dialog_corner_radius_material
+dimen abc_dialog_fixed_height_major
+dimen abc_dialog_fixed_height_minor
+dimen abc_dialog_fixed_width_major
+dimen abc_dialog_fixed_width_minor
+dimen abc_dialog_list_padding_bottom_no_buttons
+dimen abc_dialog_list_padding_top_no_title
+dimen abc_dialog_min_width_major
+dimen abc_dialog_min_width_minor
+dimen abc_dialog_padding_material
+dimen abc_dialog_padding_top_material
+dimen abc_dialog_title_divider_material
+dimen abc_disabled_alpha_material_dark
+dimen abc_disabled_alpha_material_light
+dimen abc_dropdownitem_icon_width
+dimen abc_dropdownitem_text_padding_left
+dimen abc_dropdownitem_text_padding_right
+dimen abc_edit_text_inset_bottom_material
+dimen abc_edit_text_inset_horizontal_material
+dimen abc_edit_text_inset_top_material
+dimen abc_floating_window_z
+dimen abc_list_item_height_large_material
+dimen abc_list_item_height_material
+dimen abc_list_item_height_small_material
+dimen abc_list_item_padding_horizontal_material
+dimen abc_panel_menu_list_width
+dimen abc_progress_bar_height_material
+dimen abc_search_view_preferred_height
+dimen abc_search_view_preferred_width
+dimen abc_seekbar_track_background_height_material
+dimen abc_seekbar_track_progress_height_material
+dimen abc_select_dialog_padding_start_material
+dimen abc_star_big
+dimen abc_star_medium
+dimen abc_star_small
+dimen abc_switch_padding
+dimen abc_text_size_body_1_material
+dimen abc_text_size_body_2_material
+dimen abc_text_size_button_material
+dimen abc_text_size_caption_material
+dimen abc_text_size_display_1_material
+dimen abc_text_size_display_2_material
+dimen abc_text_size_display_3_material
+dimen abc_text_size_display_4_material
+dimen abc_text_size_headline_material
+dimen abc_text_size_large_material
+dimen abc_text_size_medium_material
+dimen abc_text_size_menu_header_material
+dimen abc_text_size_menu_material
+dimen abc_text_size_small_material
+dimen abc_text_size_subhead_material
+dimen abc_text_size_subtitle_material_toolbar
+dimen abc_text_size_title_material
+dimen abc_text_size_title_material_toolbar
+dimen autofill_inline_suggestion_icon_size
+dimen compat_button_inset_horizontal_material
+dimen compat_button_inset_vertical_material
+dimen compat_button_padding_horizontal_material
+dimen compat_button_padding_vertical_material
+dimen compat_control_corner_material
+dimen compat_notification_large_icon_max_height
+dimen compat_notification_large_icon_max_width
+dimen disabled_alpha_material_dark
+dimen disabled_alpha_material_light
+dimen highlight_alpha_material_colored
+dimen highlight_alpha_material_dark
+dimen highlight_alpha_material_light
+dimen hint_alpha_material_dark
+dimen hint_alpha_material_light
+dimen hint_pressed_alpha_material_dark
+dimen hint_pressed_alpha_material_light
+dimen notification_action_icon_size
+dimen notification_action_text_size
+dimen notification_big_circle_margin
+dimen notification_content_margin_start
+dimen notification_large_icon_height
+dimen notification_large_icon_width
+dimen notification_main_column_padding_top
+dimen notification_media_narrow_margin
+dimen notification_right_icon_size
+dimen notification_right_side_padding_top
+dimen notification_small_icon_background_padding
+dimen notification_small_icon_size_as_large
+dimen notification_subtext_size
+dimen notification_top_pad
+dimen notification_top_pad_large_text
+dimen tooltip_corner_radius
+dimen tooltip_horizontal_padding
+dimen tooltip_margin
+dimen tooltip_precise_anchor_extra_offset
+dimen tooltip_precise_anchor_threshold
+dimen tooltip_vertical_padding
+dimen tooltip_y_offset_non_touch
+dimen tooltip_y_offset_touch
+drawable abc_ab_share_pack_mtrl_alpha
+drawable abc_action_bar_item_background_material
+drawable abc_btn_borderless_material
+drawable abc_btn_check_material
+drawable abc_btn_check_material_anim
+drawable abc_btn_check_to_on_mtrl_000
+drawable abc_btn_check_to_on_mtrl_015
+drawable abc_btn_colored_material
+drawable abc_btn_default_mtrl_shape
+drawable abc_btn_radio_material
+drawable abc_btn_radio_material_anim
+drawable abc_btn_radio_to_on_mtrl_000
+drawable abc_btn_radio_to_on_mtrl_015
+drawable abc_btn_switch_to_on_mtrl_00001
+drawable abc_btn_switch_to_on_mtrl_00012
+drawable abc_cab_background_internal_bg
+drawable abc_cab_background_top_material
+drawable abc_cab_background_top_mtrl_alpha
+drawable abc_control_background_material
+drawable abc_dialog_material_background
+drawable abc_edit_text_material
+drawable abc_ic_ab_back_material
+drawable abc_ic_arrow_drop_right_black_24dp
+drawable abc_ic_clear_material
+drawable abc_ic_commit_search_api_mtrl_alpha
+drawable abc_ic_go_search_api_material
+drawable abc_ic_menu_copy_mtrl_am_alpha
+drawable abc_ic_menu_cut_mtrl_alpha
+drawable abc_ic_menu_overflow_material
+drawable abc_ic_menu_paste_mtrl_am_alpha
+drawable abc_ic_menu_selectall_mtrl_alpha
+drawable abc_ic_menu_share_mtrl_alpha
+drawable abc_ic_search_api_material
+drawable abc_ic_voice_search_api_material
+drawable abc_item_background_holo_dark
+drawable abc_item_background_holo_light
+drawable abc_list_divider_material
+drawable abc_list_divider_mtrl_alpha
+drawable abc_list_focused_holo
+drawable abc_list_longpressed_holo
+drawable abc_list_pressed_holo_dark
+drawable abc_list_pressed_holo_light
+drawable abc_list_selector_background_transition_holo_dark
+drawable abc_list_selector_background_transition_holo_light
+drawable abc_list_selector_disabled_holo_dark
+drawable abc_list_selector_disabled_holo_light
+drawable abc_list_selector_holo_dark
+drawable abc_list_selector_holo_light
+drawable abc_menu_hardkey_panel_mtrl_mult
+drawable abc_popup_background_mtrl_mult
+drawable abc_ratingbar_indicator_material
+drawable abc_ratingbar_material
+drawable abc_ratingbar_small_material
+drawable abc_scrubber_control_off_mtrl_alpha
+drawable abc_scrubber_control_to_pressed_mtrl_000
+drawable abc_scrubber_control_to_pressed_mtrl_005
+drawable abc_scrubber_primary_mtrl_alpha
+drawable abc_scrubber_track_mtrl_alpha
+drawable abc_seekbar_thumb_material
+drawable abc_seekbar_tick_mark_material
+drawable abc_seekbar_track_material
+drawable abc_spinner_mtrl_am_alpha
+drawable abc_spinner_textfield_background_material
+drawable abc_star_black_48dp
+drawable abc_star_half_black_48dp
+drawable abc_switch_thumb_material
+drawable abc_switch_track_mtrl_alpha
+drawable abc_tab_indicator_material
+drawable abc_tab_indicator_mtrl_alpha
+drawable abc_text_cursor_material
+drawable abc_text_select_handle_left_mtrl
+drawable abc_text_select_handle_middle_mtrl
+drawable abc_text_select_handle_right_mtrl
+drawable abc_textfield_activated_mtrl_alpha
+drawable abc_textfield_default_mtrl_alpha
+drawable abc_textfield_search_activated_mtrl_alpha
+drawable abc_textfield_search_default_mtrl_alpha
+drawable abc_textfield_search_material
+drawable abc_vector_test
+drawable autofill_inline_suggestion_chip_background
+drawable bg_btn
+drawable btn_checkbox_checked_mtrl
+drawable btn_checkbox_checked_to_unchecked_mtrl_animation
+drawable btn_checkbox_unchecked_mtrl
+drawable btn_checkbox_unchecked_to_checked_mtrl_animation
+drawable btn_radio_off_mtrl
+drawable btn_radio_off_to_on_mtrl_animation
+drawable btn_radio_on_mtrl
+drawable btn_radio_on_to_off_mtrl_animation
+drawable notification_action_background
+drawable notification_bg
+drawable notification_bg_low
+drawable notification_bg_low_normal
+drawable notification_bg_low_pressed
+drawable notification_bg_normal
+drawable notification_bg_normal_pressed
+drawable notification_icon_background
+drawable notification_template_icon_bg
+drawable notification_template_icon_low_bg
+drawable notification_tile_bg
+drawable notify_panel_notification_icon_bg
+drawable redbox_top_border_background
+drawable test_level_drawable
+drawable tooltip_frame_dark
+drawable tooltip_frame_light
+id accessibility_action_clickable_span
+id accessibility_actions
+id accessibility_collection
+id accessibility_collection_item
+id accessibility_custom_action_0
+id accessibility_custom_action_1
+id accessibility_custom_action_10
+id accessibility_custom_action_11
+id accessibility_custom_action_12
+id accessibility_custom_action_13
+id accessibility_custom_action_14
+id accessibility_custom_action_15
+id accessibility_custom_action_16
+id accessibility_custom_action_17
+id accessibility_custom_action_18
+id accessibility_custom_action_19
+id accessibility_custom_action_2
+id accessibility_custom_action_20
+id accessibility_custom_action_21
+id accessibility_custom_action_22
+id accessibility_custom_action_23
+id accessibility_custom_action_24
+id accessibility_custom_action_25
+id accessibility_custom_action_26
+id accessibility_custom_action_27
+id accessibility_custom_action_28
+id accessibility_custom_action_29
+id accessibility_custom_action_3
+id accessibility_custom_action_30
+id accessibility_custom_action_31
+id accessibility_custom_action_4
+id accessibility_custom_action_5
+id accessibility_custom_action_6
+id accessibility_custom_action_7
+id accessibility_custom_action_8
+id accessibility_custom_action_9
+id accessibility_hint
+id accessibility_label
+id accessibility_links
+id accessibility_role
+id accessibility_state
+id accessibility_value
+id action_bar
+id action_bar_activity_content
+id action_bar_container
+id action_bar_root
+id action_bar_spinner
+id action_bar_subtitle
+id action_bar_title
+id action_container
+id action_context_bar
+id action_divider
+id action_image
+id action_menu_divider
+id action_menu_presenter
+id action_mode_bar
+id action_mode_bar_stub
+id action_mode_close_button
+id action_text
+id actions
+id activity_chooser_view_content
+id add
+id alertTitle
+id async
+id autofill_inline_suggestion_end_icon
+id autofill_inline_suggestion_start_icon
+id autofill_inline_suggestion_subtitle
+id autofill_inline_suggestion_title
+id blocking
+id buttonPanel
+id catalyst_redbox_title
+id center
+id centerCrop
+id centerInside
+id checkbox
+id checked
+id chronometer
+id content
+id contentPanel
+id curved
+id custom
+id customPanel
+id decor_content_parent
+id default_activity_button
+id dialog_button
+id edit_query
+id expand_activities_button
+id expanded_menu
+id fitBottomStart
+id fitCenter
+id fitEnd
+id fitStart
+id fitXY
+id focusCrop
+id forever
+id fps_text
+id fragment_container_view_tag
+id group_divider
+id home
+id horizontal
+id icon
+id icon_group
+id image
+id info
+id italic
+id item1
+id item2
+id item3
+id item4
+id labelled_by
+id line1
+id line3
+id listMode
+id list_item
+id message
+id multiply
+id none
+id normal
+id notification_background
+id notification_main_column
+id notification_main_column_container
+id off
+id on
+id parentPanel
+id pointer_enter
+id pointer_enter_capture
+id pointer_leave
+id pointer_leave_capture
+id pointer_move
+id pointer_move_capture
+id progress_circular
+id progress_horizontal
+id radio
+id react_test_id
+id right_icon
+id right_side
+id rn_frame_file
+id rn_frame_method
+id rn_redbox_dismiss_button
+id rn_redbox_line_separator
+id rn_redbox_loading_indicator
+id rn_redbox_reload_button
+id rn_redbox_report_button
+id rn_redbox_report_label
+id rn_redbox_stack
+id screen
+id scrollIndicatorDown
+id scrollIndicatorUp
+id scrollView
+id search_badge
+id search_bar
+id search_button
+id search_close_btn
+id search_edit_frame
+id search_go_btn
+id search_mag_icon
+id search_plate
+id search_src_text
+id search_voice_btn
+id select_dialog_listview
+id shortcut
+id spacer
+id special_effects_controller_view_tag
+id split_action_bar
+id src_atop
+id src_in
+id src_over
+id straight
+id submenuarrow
+id submit_area
+id tabMode
+id tag_accessibility_actions
+id tag_accessibility_clickable_spans
+id tag_accessibility_heading
+id tag_accessibility_pane_title
+id tag_on_apply_window_listener
+id tag_on_receive_content_listener
+id tag_on_receive_content_mime_types
+id tag_screen_reader_focusable
+id tag_state_description
+id tag_transition_group
+id tag_unhandled_key_event_manager
+id tag_unhandled_key_listeners
+id tag_window_insets_animation_callback
+id text
+id text2
+id textSpacerNoButtons
+id textSpacerNoTitle
+id time
+id title
+id titleDividerNoCustom
+id title_template
+id topPanel
+id unchecked
+id uniform
+id up
+id vertical
+id view_tag_instance_handle
+id view_tag_native_id
+id view_tree_lifecycle_owner
+id view_tree_saved_state_registry_owner
+id view_tree_view_model_store_owner
+id visible_removing_fragment_view_tag
+id wrap_content
+integer abc_config_activityDefaultDur
+integer abc_config_activityShortDur
+integer cancel_button_image_alpha
+integer config_tooltipAnimTime
+integer react_native_dev_server_port
+integer react_native_inspector_proxy_port
+integer status_bar_notification_info_maxnum
+interpolator btn_checkbox_checked_mtrl_animation_interpolator_0
+interpolator btn_checkbox_checked_mtrl_animation_interpolator_1
+interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0
+interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1
+interpolator btn_radio_to_off_mtrl_animation_interpolator_0
+interpolator btn_radio_to_on_mtrl_animation_interpolator_0
+interpolator fast_out_slow_in
+layout abc_action_bar_title_item
+layout abc_action_bar_up_container
+layout abc_action_menu_item_layout
+layout abc_action_menu_layout
+layout abc_action_mode_bar
+layout abc_action_mode_close_item_material
+layout abc_activity_chooser_view
+layout abc_activity_chooser_view_list_item
+layout abc_alert_dialog_button_bar_material
+layout abc_alert_dialog_material
+layout abc_alert_dialog_title_material
+layout abc_cascading_menu_item_layout
+layout abc_dialog_title_material
+layout abc_expanded_menu_layout
+layout abc_list_menu_item_checkbox
+layout abc_list_menu_item_icon
+layout abc_list_menu_item_layout
+layout abc_list_menu_item_radio
+layout abc_popup_menu_header_item_layout
+layout abc_popup_menu_item_layout
+layout abc_screen_content_include
+layout abc_screen_simple
+layout abc_screen_simple_overlay_action_mode
+layout abc_screen_toolbar
+layout abc_search_dropdown_item_icons_2line
+layout abc_search_view
+layout abc_select_dialog_material
+layout abc_tooltip
+layout autofill_inline_suggestion
+layout custom_dialog
+layout dev_loading_view
+layout fps_view
+layout notification_action
+layout notification_action_tombstone
+layout notification_template_custom_big
+layout notification_template_icon_group
+layout notification_template_part_chronometer
+layout notification_template_part_time
+layout redbox_item_frame
+layout redbox_item_title
+layout redbox_view
+layout select_dialog_item_material
+layout select_dialog_multichoice_material
+layout select_dialog_singlechoice_material
+layout support_simple_spinner_dropdown_item
+menu example_menu
+menu example_menu2
+string abc_action_bar_home_description
+string abc_action_bar_up_description
+string abc_action_menu_overflow_description
+string abc_action_mode_done
+string abc_activity_chooser_view_see_all
+string abc_activitychooserview_choose_application
+string abc_capital_off
+string abc_capital_on
+string abc_menu_alt_shortcut_label
+string abc_menu_ctrl_shortcut_label
+string abc_menu_delete_shortcut_label
+string abc_menu_enter_shortcut_label
+string abc_menu_function_shortcut_label
+string abc_menu_meta_shortcut_label
+string abc_menu_shift_shortcut_label
+string abc_menu_space_shortcut_label
+string abc_menu_sym_shortcut_label
+string abc_prepend_shortcut_label
+string abc_search_hint
+string abc_searchview_description_clear
+string abc_searchview_description_query
+string abc_searchview_description_search
+string abc_searchview_description_submit
+string abc_searchview_description_voice
+string abc_shareactionprovider_share_with
+string abc_shareactionprovider_share_with_application
+string abc_toolbar_collapse_description
+string alert_description
+string catalyst_change_bundle_location
+string catalyst_copy_button
+string catalyst_debug
+string catalyst_debug_chrome
+string catalyst_debug_chrome_stop
+string catalyst_debug_connecting
+string catalyst_debug_error
+string catalyst_debug_open
+string catalyst_debug_stop
+string catalyst_devtools_open
+string catalyst_dismiss_button
+string catalyst_heap_capture
+string catalyst_hot_reloading
+string catalyst_hot_reloading_auto_disable
+string catalyst_hot_reloading_auto_enable
+string catalyst_hot_reloading_stop
+string catalyst_inspector
+string catalyst_inspector_stop
+string catalyst_loading_from_url
+string catalyst_open_flipper_error
+string catalyst_perf_monitor
+string catalyst_perf_monitor_stop
+string catalyst_reload
+string catalyst_reload_button
+string catalyst_reload_error
+string catalyst_report_button
+string catalyst_sample_profiler_disable
+string catalyst_sample_profiler_enable
+string catalyst_settings
+string catalyst_settings_title
+string combobox_description
+string header_description
+string image_description
+string imagebutton_description
+string link_description
+string menu_description
+string menubar_description
+string menuitem_description
+string progressbar_description
+string radiogroup_description
+string rn_tab_description
+string scrollbar_description
+string search_menu_title
+string spinbutton_description
+string state_busy_description
+string state_collapsed_description
+string state_expanded_description
+string state_mixed_description
+string state_off_description
+string state_on_description
+string state_unselected_description
+string status_bar_notification_info_overflow
+string summary_description
+string tablist_description
+string timer_description
+string toolbar_description
+style AlertDialog_AppCompat
+style AlertDialog_AppCompat_Light
+style Animation_AppCompat_Dialog
+style Animation_AppCompat_DropDownUp
+style Animation_AppCompat_Tooltip
+style Animation_Catalyst_LogBox
+style Animation_Catalyst_RedBox
+style Base_AlertDialog_AppCompat
+style Base_AlertDialog_AppCompat_Light
+style Base_Animation_AppCompat_Dialog
+style Base_Animation_AppCompat_DropDownUp
+style Base_Animation_AppCompat_Tooltip
+style Base_DialogWindowTitleBackground_AppCompat
+style Base_DialogWindowTitle_AppCompat
+style Base_TextAppearance_AppCompat
+style Base_TextAppearance_AppCompat_Body1
+style Base_TextAppearance_AppCompat_Body2
+style Base_TextAppearance_AppCompat_Button
+style Base_TextAppearance_AppCompat_Caption
+style Base_TextAppearance_AppCompat_Display1
+style Base_TextAppearance_AppCompat_Display2
+style Base_TextAppearance_AppCompat_Display3
+style Base_TextAppearance_AppCompat_Display4
+style Base_TextAppearance_AppCompat_Headline
+style Base_TextAppearance_AppCompat_Inverse
+style Base_TextAppearance_AppCompat_Large
+style Base_TextAppearance_AppCompat_Large_Inverse
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Medium
+style Base_TextAppearance_AppCompat_Medium_Inverse
+style Base_TextAppearance_AppCompat_Menu
+style Base_TextAppearance_AppCompat_SearchResult
+style Base_TextAppearance_AppCompat_SearchResult_Subtitle
+style Base_TextAppearance_AppCompat_SearchResult_Title
+style Base_TextAppearance_AppCompat_Small
+style Base_TextAppearance_AppCompat_Small_Inverse
+style Base_TextAppearance_AppCompat_Subhead
+style Base_TextAppearance_AppCompat_Subhead_Inverse
+style Base_TextAppearance_AppCompat_Title
+style Base_TextAppearance_AppCompat_Title_Inverse
+style Base_TextAppearance_AppCompat_Tooltip
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title
+style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style Base_TextAppearance_AppCompat_Widget_ActionMode_Title
+style Base_TextAppearance_AppCompat_Widget_Button
+style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Colored
+style Base_TextAppearance_AppCompat_Widget_Button_Inverse
+style Base_TextAppearance_AppCompat_Widget_DropDownItem
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large
+style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small
+style Base_TextAppearance_AppCompat_Widget_Switch
+style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style Base_TextAppearance_Widget_AppCompat_Toolbar_Title
+style Base_ThemeOverlay_AppCompat
+style Base_ThemeOverlay_AppCompat_ActionBar
+style Base_ThemeOverlay_AppCompat_Dark
+style Base_ThemeOverlay_AppCompat_Dark_ActionBar
+style Base_ThemeOverlay_AppCompat_Dialog
+style Base_ThemeOverlay_AppCompat_Dialog_Alert
+style Base_ThemeOverlay_AppCompat_Light
+style Base_Theme_AppCompat
+style Base_Theme_AppCompat_CompactMenu
+style Base_Theme_AppCompat_Dialog
+style Base_Theme_AppCompat_DialogWhenLarge
+style Base_Theme_AppCompat_Dialog_Alert
+style Base_Theme_AppCompat_Dialog_FixedSize
+style Base_Theme_AppCompat_Dialog_MinWidth
+style Base_Theme_AppCompat_Light
+style Base_Theme_AppCompat_Light_DarkActionBar
+style Base_Theme_AppCompat_Light_Dialog
+style Base_Theme_AppCompat_Light_DialogWhenLarge
+style Base_Theme_AppCompat_Light_Dialog_Alert
+style Base_Theme_AppCompat_Light_Dialog_FixedSize
+style Base_Theme_AppCompat_Light_Dialog_MinWidth
+style Base_V21_ThemeOverlay_AppCompat_Dialog
+style Base_V21_Theme_AppCompat
+style Base_V21_Theme_AppCompat_Dialog
+style Base_V21_Theme_AppCompat_Light
+style Base_V21_Theme_AppCompat_Light_Dialog
+style Base_V22_Theme_AppCompat
+style Base_V22_Theme_AppCompat_Light
+style Base_V23_Theme_AppCompat
+style Base_V23_Theme_AppCompat_Light
+style Base_V26_Theme_AppCompat
+style Base_V26_Theme_AppCompat_Light
+style Base_V26_Widget_AppCompat_Toolbar
+style Base_V28_Theme_AppCompat
+style Base_V28_Theme_AppCompat_Light
+style Base_V7_ThemeOverlay_AppCompat_Dialog
+style Base_V7_Theme_AppCompat
+style Base_V7_Theme_AppCompat_Dialog
+style Base_V7_Theme_AppCompat_Light
+style Base_V7_Theme_AppCompat_Light_Dialog
+style Base_V7_Widget_AppCompat_AutoCompleteTextView
+style Base_V7_Widget_AppCompat_EditText
+style Base_V7_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_ActionBar
+style Base_Widget_AppCompat_ActionBar_Solid
+style Base_Widget_AppCompat_ActionBar_TabBar
+style Base_Widget_AppCompat_ActionBar_TabText
+style Base_Widget_AppCompat_ActionBar_TabView
+style Base_Widget_AppCompat_ActionButton
+style Base_Widget_AppCompat_ActionButton_CloseMode
+style Base_Widget_AppCompat_ActionButton_Overflow
+style Base_Widget_AppCompat_ActionMode
+style Base_Widget_AppCompat_ActivityChooserView
+style Base_Widget_AppCompat_AutoCompleteTextView
+style Base_Widget_AppCompat_Button
+style Base_Widget_AppCompat_ButtonBar
+style Base_Widget_AppCompat_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Borderless
+style Base_Widget_AppCompat_Button_Borderless_Colored
+style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Base_Widget_AppCompat_Button_Colored
+style Base_Widget_AppCompat_Button_Small
+style Base_Widget_AppCompat_CompoundButton_CheckBox
+style Base_Widget_AppCompat_CompoundButton_RadioButton
+style Base_Widget_AppCompat_CompoundButton_Switch
+style Base_Widget_AppCompat_DrawerArrowToggle
+style Base_Widget_AppCompat_DrawerArrowToggle_Common
+style Base_Widget_AppCompat_DropDownItem_Spinner
+style Base_Widget_AppCompat_EditText
+style Base_Widget_AppCompat_ImageButton
+style Base_Widget_AppCompat_Light_ActionBar
+style Base_Widget_AppCompat_Light_ActionBar_Solid
+style Base_Widget_AppCompat_Light_ActionBar_TabBar
+style Base_Widget_AppCompat_Light_ActionBar_TabText
+style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Base_Widget_AppCompat_Light_ActionBar_TabView
+style Base_Widget_AppCompat_Light_PopupMenu
+style Base_Widget_AppCompat_Light_PopupMenu_Overflow
+style Base_Widget_AppCompat_ListMenuView
+style Base_Widget_AppCompat_ListPopupWindow
+style Base_Widget_AppCompat_ListView
+style Base_Widget_AppCompat_ListView_DropDown
+style Base_Widget_AppCompat_ListView_Menu
+style Base_Widget_AppCompat_PopupMenu
+style Base_Widget_AppCompat_PopupMenu_Overflow
+style Base_Widget_AppCompat_PopupWindow
+style Base_Widget_AppCompat_ProgressBar
+style Base_Widget_AppCompat_ProgressBar_Horizontal
+style Base_Widget_AppCompat_RatingBar
+style Base_Widget_AppCompat_RatingBar_Indicator
+style Base_Widget_AppCompat_RatingBar_Small
+style Base_Widget_AppCompat_SearchView
+style Base_Widget_AppCompat_SearchView_ActionBar
+style Base_Widget_AppCompat_SeekBar
+style Base_Widget_AppCompat_SeekBar_Discrete
+style Base_Widget_AppCompat_Spinner
+style Base_Widget_AppCompat_Spinner_Underlined
+style Base_Widget_AppCompat_TextView
+style Base_Widget_AppCompat_TextView_SpinnerItem
+style Base_Widget_AppCompat_Toolbar
+style Base_Widget_AppCompat_Toolbar_Button_Navigation
+style CalendarDatePickerDialog
+style CalendarDatePickerStyle
+style DialogAnimationFade
+style DialogAnimationSlide
+style Platform_AppCompat
+style Platform_AppCompat_Light
+style Platform_ThemeOverlay_AppCompat
+style Platform_ThemeOverlay_AppCompat_Dark
+style Platform_ThemeOverlay_AppCompat_Light
+style Platform_V21_AppCompat
+style Platform_V21_AppCompat_Light
+style Platform_V25_AppCompat
+style Platform_V25_AppCompat_Light
+style Platform_Widget_AppCompat_Spinner
+style RtlOverlay_DialogWindowTitle_AppCompat
+style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem
+style RtlOverlay_Widget_AppCompat_DialogTitle_Icon
+style RtlOverlay_Widget_AppCompat_PopupMenuItem
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text
+style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title
+style RtlOverlay_Widget_AppCompat_SearchView_MagIcon
+style RtlOverlay_Widget_AppCompat_Search_DropDown
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Query
+style RtlOverlay_Widget_AppCompat_Search_DropDown_Text
+style RtlUnderlay_Widget_AppCompat_ActionButton
+style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow
+style SpinnerDatePickerDialog
+style SpinnerDatePickerStyle
+style TextAppearance_AppCompat
+style TextAppearance_AppCompat_Body1
+style TextAppearance_AppCompat_Body2
+style TextAppearance_AppCompat_Button
+style TextAppearance_AppCompat_Caption
+style TextAppearance_AppCompat_Display1
+style TextAppearance_AppCompat_Display2
+style TextAppearance_AppCompat_Display3
+style TextAppearance_AppCompat_Display4
+style TextAppearance_AppCompat_Headline
+style TextAppearance_AppCompat_Inverse
+style TextAppearance_AppCompat_Large
+style TextAppearance_AppCompat_Large_Inverse
+style TextAppearance_AppCompat_Light_SearchResult_Subtitle
+style TextAppearance_AppCompat_Light_SearchResult_Title
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Medium
+style TextAppearance_AppCompat_Medium_Inverse
+style TextAppearance_AppCompat_Menu
+style TextAppearance_AppCompat_SearchResult_Subtitle
+style TextAppearance_AppCompat_SearchResult_Title
+style TextAppearance_AppCompat_Small
+style TextAppearance_AppCompat_Small_Inverse
+style TextAppearance_AppCompat_Subhead
+style TextAppearance_AppCompat_Subhead_Inverse
+style TextAppearance_AppCompat_Title
+style TextAppearance_AppCompat_Title_Inverse
+style TextAppearance_AppCompat_Tooltip
+style TextAppearance_AppCompat_Widget_ActionBar_Menu
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle
+style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionBar_Title
+style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle
+style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse
+style TextAppearance_AppCompat_Widget_ActionMode_Title
+style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse
+style TextAppearance_AppCompat_Widget_Button
+style TextAppearance_AppCompat_Widget_Button_Borderless_Colored
+style TextAppearance_AppCompat_Widget_Button_Colored
+style TextAppearance_AppCompat_Widget_Button_Inverse
+style TextAppearance_AppCompat_Widget_DropDownItem
+style TextAppearance_AppCompat_Widget_PopupMenu_Header
+style TextAppearance_AppCompat_Widget_PopupMenu_Large
+style TextAppearance_AppCompat_Widget_PopupMenu_Small
+style TextAppearance_AppCompat_Widget_Switch
+style TextAppearance_AppCompat_Widget_TextView_SpinnerItem
+style TextAppearance_Compat_Notification
+style TextAppearance_Compat_Notification_Info
+style TextAppearance_Compat_Notification_Line2
+style TextAppearance_Compat_Notification_Time
+style TextAppearance_Compat_Notification_Title
+style TextAppearance_Widget_AppCompat_ExpandedMenu_Item
+style TextAppearance_Widget_AppCompat_Toolbar_Subtitle
+style TextAppearance_Widget_AppCompat_Toolbar_Title
+style Theme
+style ThemeOverlay_AppCompat
+style ThemeOverlay_AppCompat_ActionBar
+style ThemeOverlay_AppCompat_Dark
+style ThemeOverlay_AppCompat_Dark_ActionBar
+style ThemeOverlay_AppCompat_DayNight
+style ThemeOverlay_AppCompat_DayNight_ActionBar
+style ThemeOverlay_AppCompat_Dialog
+style ThemeOverlay_AppCompat_Dialog_Alert
+style ThemeOverlay_AppCompat_Light
+style Theme_AppCompat
+style Theme_AppCompat_CompactMenu
+style Theme_AppCompat_DayNight
+style Theme_AppCompat_DayNight_DarkActionBar
+style Theme_AppCompat_DayNight_Dialog
+style Theme_AppCompat_DayNight_DialogWhenLarge
+style Theme_AppCompat_DayNight_Dialog_Alert
+style Theme_AppCompat_DayNight_Dialog_MinWidth
+style Theme_AppCompat_DayNight_NoActionBar
+style Theme_AppCompat_Dialog
+style Theme_AppCompat_DialogWhenLarge
+style Theme_AppCompat_Dialog_Alert
+style Theme_AppCompat_Dialog_MinWidth
+style Theme_AppCompat_Empty
+style Theme_AppCompat_Light
+style Theme_AppCompat_Light_DarkActionBar
+style Theme_AppCompat_Light_Dialog
+style Theme_AppCompat_Light_DialogWhenLarge
+style Theme_AppCompat_Light_Dialog_Alert
+style Theme_AppCompat_Light_Dialog_MinWidth
+style Theme_AppCompat_Light_NoActionBar
+style Theme_AppCompat_NoActionBar
+style Theme_AutofillInlineSuggestion
+style Theme_Catalyst
+style Theme_Catalyst_LogBox
+style Theme_Catalyst_RedBox
+style Theme_FullScreenDialog
+style Theme_FullScreenDialogAnimatedFade
+style Theme_FullScreenDialogAnimatedSlide
+style Theme_ReactNative_AppCompat_Light
+style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen
+style Widget_AppCompat_ActionBar
+style Widget_AppCompat_ActionBar_Solid
+style Widget_AppCompat_ActionBar_TabBar
+style Widget_AppCompat_ActionBar_TabText
+style Widget_AppCompat_ActionBar_TabView
+style Widget_AppCompat_ActionButton
+style Widget_AppCompat_ActionButton_CloseMode
+style Widget_AppCompat_ActionButton_Overflow
+style Widget_AppCompat_ActionMode
+style Widget_AppCompat_ActivityChooserView
+style Widget_AppCompat_AutoCompleteTextView
+style Widget_AppCompat_Button
+style Widget_AppCompat_ButtonBar
+style Widget_AppCompat_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Borderless
+style Widget_AppCompat_Button_Borderless_Colored
+style Widget_AppCompat_Button_ButtonBar_AlertDialog
+style Widget_AppCompat_Button_Colored
+style Widget_AppCompat_Button_Small
+style Widget_AppCompat_CompoundButton_CheckBox
+style Widget_AppCompat_CompoundButton_RadioButton
+style Widget_AppCompat_CompoundButton_Switch
+style Widget_AppCompat_DrawerArrowToggle
+style Widget_AppCompat_DropDownItem_Spinner
+style Widget_AppCompat_EditText
+style Widget_AppCompat_ImageButton
+style Widget_AppCompat_Light_ActionBar
+style Widget_AppCompat_Light_ActionBar_Solid
+style Widget_AppCompat_Light_ActionBar_Solid_Inverse
+style Widget_AppCompat_Light_ActionBar_TabBar
+style Widget_AppCompat_Light_ActionBar_TabBar_Inverse
+style Widget_AppCompat_Light_ActionBar_TabText
+style Widget_AppCompat_Light_ActionBar_TabText_Inverse
+style Widget_AppCompat_Light_ActionBar_TabView
+style Widget_AppCompat_Light_ActionBar_TabView_Inverse
+style Widget_AppCompat_Light_ActionButton
+style Widget_AppCompat_Light_ActionButton_CloseMode
+style Widget_AppCompat_Light_ActionButton_Overflow
+style Widget_AppCompat_Light_ActionMode_Inverse
+style Widget_AppCompat_Light_ActivityChooserView
+style Widget_AppCompat_Light_AutoCompleteTextView
+style Widget_AppCompat_Light_DropDownItem_Spinner
+style Widget_AppCompat_Light_ListPopupWindow
+style Widget_AppCompat_Light_ListView_DropDown
+style Widget_AppCompat_Light_PopupMenu
+style Widget_AppCompat_Light_PopupMenu_Overflow
+style Widget_AppCompat_Light_SearchView
+style Widget_AppCompat_Light_Spinner_DropDown_ActionBar
+style Widget_AppCompat_ListMenuView
+style Widget_AppCompat_ListPopupWindow
+style Widget_AppCompat_ListView
+style Widget_AppCompat_ListView_DropDown
+style Widget_AppCompat_ListView_Menu
+style Widget_AppCompat_PopupMenu
+style Widget_AppCompat_PopupMenu_Overflow
+style Widget_AppCompat_PopupWindow
+style Widget_AppCompat_ProgressBar
+style Widget_AppCompat_ProgressBar_Horizontal
+style Widget_AppCompat_RatingBar
+style Widget_AppCompat_RatingBar_Indicator
+style Widget_AppCompat_RatingBar_Small
+style Widget_AppCompat_SearchView
+style Widget_AppCompat_SearchView_ActionBar
+style Widget_AppCompat_SeekBar
+style Widget_AppCompat_SeekBar_Discrete
+style Widget_AppCompat_Spinner
+style Widget_AppCompat_Spinner_DropDown
+style Widget_AppCompat_Spinner_DropDown_ActionBar
+style Widget_AppCompat_Spinner_Underlined
+style Widget_AppCompat_TextView
+style Widget_AppCompat_TextView_SpinnerItem
+style Widget_AppCompat_Toolbar
+style Widget_AppCompat_Toolbar_Button_Navigation
+style Widget_Autofill
+style Widget_Autofill_InlineSuggestionChip
+style Widget_Autofill_InlineSuggestionEndIconStyle
+style Widget_Autofill_InlineSuggestionStartIconStyle
+style Widget_Autofill_InlineSuggestionSubtitle
+style Widget_Autofill_InlineSuggestionTitle
+style Widget_Compat_NotificationActionContainer
+style Widget_Compat_NotificationActionText
+style redboxButton
+styleable AbstractWheelPicker wheel_data wheel_direction wheel_item_count wheel_item_index wheel_item_same_size wheel_item_space wheel_style wheel_text_color wheel_text_color_current wheel_text_size
+styleable ActionBar background backgroundSplit backgroundStacked contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation customNavigationLayout displayOptions divider elevation height hideOnContentScroll homeAsUpIndicator homeLayout icon indeterminateProgressStyle itemPadding logo navigationMode popupTheme progressBarPadding progressBarStyle subtitle subtitleTextStyle title titleTextStyle
+styleable ActionBarLayout android_layout_gravity
+styleable ActionMenuItemView android_minWidth
+styleable ActionMenuView
+styleable ActionMode background backgroundSplit closeItemLayout height subtitleTextStyle titleTextStyle
+styleable ActivityChooserView expandActivityOverflowButtonDrawable initialActivityCount
+styleable AlertDialog android_layout buttonIconDimen buttonPanelSideLayout listItemLayout listLayout multiChoiceItemLayout showTitle singleChoiceItemLayout
+styleable AnimatedStateListDrawableCompat android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable AnimatedStateListDrawableItem android_drawable android_id
+styleable AnimatedStateListDrawableTransition android_drawable android_fromId android_reversible android_toId
+styleable AppCompatEmojiHelper
+styleable AppCompatImageView android_src srcCompat tint tintMode
+styleable AppCompatSeekBar android_thumb tickMark tickMarkTint tickMarkTintMode
+styleable AppCompatTextHelper android_drawableBottom android_drawableEnd android_drawableLeft android_drawableRight android_drawableStart android_drawableTop android_textAppearance
+styleable AppCompatTextView android_textAppearance autoSizeMaxTextSize autoSizeMinTextSize autoSizePresetSizes autoSizeStepGranularity autoSizeTextType drawableBottomCompat drawableEndCompat drawableLeftCompat drawableRightCompat drawableStartCompat drawableTint drawableTintMode drawableTopCompat emojiCompatEnabled firstBaselineToTopHeight fontFamily fontVariationSettings lastBaselineToBottomHeight lineHeight textAllCaps textLocale
+styleable AppCompatTheme actionBarDivider actionBarItemBackground actionBarPopupTheme actionBarSize actionBarSplitStyle actionBarStyle actionBarTabBarStyle actionBarTabStyle actionBarTabTextStyle actionBarTheme actionBarWidgetTheme actionButtonStyle actionDropDownStyle actionMenuTextAppearance actionMenuTextColor actionModeBackground actionModeCloseButtonStyle actionModeCloseContentDescription actionModeCloseDrawable actionModeCopyDrawable actionModeCutDrawable actionModeFindDrawable actionModePasteDrawable actionModePopupWindowStyle actionModeSelectAllDrawable actionModeShareDrawable actionModeSplitBackground actionModeStyle actionModeTheme actionModeWebSearchDrawable actionOverflowButtonStyle actionOverflowMenuStyle activityChooserViewStyle alertDialogButtonGroupStyle alertDialogCenterButtons alertDialogStyle alertDialogTheme android_windowAnimationStyle android_windowIsFloating autoCompleteTextViewStyle borderlessButtonStyle buttonBarButtonStyle buttonBarNegativeButtonStyle buttonBarNeutralButtonStyle buttonBarPositiveButtonStyle buttonBarStyle buttonStyle buttonStyleSmall checkboxStyle checkedTextViewStyle colorAccent colorBackgroundFloating colorButtonNormal colorControlActivated colorControlHighlight colorControlNormal colorError colorPrimary colorPrimaryDark colorSwitchThumbNormal controlBackground dialogCornerRadius dialogPreferredPadding dialogTheme dividerHorizontal dividerVertical dropDownListViewStyle dropdownListPreferredItemHeight editTextBackground editTextColor editTextStyle homeAsUpIndicator imageButtonStyle listChoiceBackgroundIndicator listChoiceIndicatorMultipleAnimated listChoiceIndicatorSingleAnimated listDividerAlertDialog listMenuViewStyle listPopupWindowStyle listPreferredItemHeight listPreferredItemHeightLarge listPreferredItemHeightSmall listPreferredItemPaddingEnd listPreferredItemPaddingLeft listPreferredItemPaddingRight listPreferredItemPaddingStart panelBackground panelMenuListTheme panelMenuListWidth popupMenuStyle popupWindowStyle radioButtonStyle ratingBarStyle ratingBarStyleIndicator ratingBarStyleSmall searchViewStyle seekBarStyle selectableItemBackground selectableItemBackgroundBorderless spinnerDropDownItemStyle spinnerStyle switchStyle textAppearanceLargePopupMenu textAppearanceListItem textAppearanceListItemSecondary textAppearanceListItemSmall textAppearancePopupMenuHeader textAppearanceSearchResultSubtitle textAppearanceSearchResultTitle textAppearanceSmallPopupMenu textColorAlertDialogListItem textColorSearchUrl toolbarNavigationButtonStyle toolbarStyle tooltipForegroundColor tooltipFrameBackground viewInflaterClass windowActionBar windowActionBarOverlay windowActionModeOverlay windowFixedHeightMajor windowFixedHeightMinor windowFixedWidthMajor windowFixedWidthMinor windowMinWidthMajor windowMinWidthMinor windowNoTitle
+styleable Autofill_InlineSuggestion autofillInlineSuggestionChip autofillInlineSuggestionEndIconStyle autofillInlineSuggestionStartIconStyle autofillInlineSuggestionSubtitle autofillInlineSuggestionTitle isAutofillInlineSuggestionTheme
+styleable ButtonBarLayout allowStacking
+styleable Capability queryPatterns shortcutMatchRequired
+styleable CheckedTextView android_checkMark checkMarkCompat checkMarkTint checkMarkTintMode
+styleable ColorStateListItem alpha android_alpha android_color android_lStar lStar
+styleable CompoundButton android_button buttonCompat buttonTint buttonTintMode
+styleable DrawerArrowToggle arrowHeadLength arrowShaftLength barLength color drawableSize gapBetweenBars spinBars thickness
+styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
+styleable FontFamilyFont android_font android_fontStyle android_fontVariationSettings android_fontWeight android_ttcIndex font fontStyle fontVariationSettings fontWeight ttcIndex
+styleable Fragment android_id android_name android_tag
+styleable FragmentContainerView android_name android_tag
+styleable GenericDraweeHierarchy actualImageScaleType backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable GradientColor android_centerColor android_centerX android_centerY android_endColor android_endX android_endY android_gradientRadius android_startColor android_startX android_startY android_tileMode android_type
+styleable GradientColorItem android_color android_offset
+styleable LinearLayoutCompat android_baselineAligned android_baselineAlignedChildIndex android_gravity android_orientation android_weightSum divider dividerPadding measureWithLargestChild showDividers
+styleable LinearLayoutCompat_Layout android_layout_gravity android_layout_height android_layout_weight android_layout_width
+styleable ListPopupWindow android_dropDownHorizontalOffset android_dropDownVerticalOffset
+styleable MenuGroup android_checkableBehavior android_enabled android_id android_menuCategory android_orderInCategory android_visible
+styleable MenuItem actionLayout actionProviderClass actionViewClass alphabeticModifiers android_alphabeticShortcut android_checkable android_checked android_enabled android_icon android_id android_menuCategory android_numericShortcut android_onClick android_orderInCategory android_title android_titleCondensed android_visible contentDescription iconTint iconTintMode numericModifiers showAsAction tooltipText
+styleable MenuView android_headerBackground android_horizontalDivider android_itemBackground android_itemIconDisabledAlpha android_itemTextAppearance android_verticalDivider android_windowAnimationStyle preserveIconSpacing subMenuArrow
+styleable PopupWindow android_popupAnimationStyle android_popupBackground overlapAnchor
+styleable PopupWindowBackgroundState state_above_anchor
+styleable RecycleListView paddingBottomNoButtons paddingTopNoTitle
+styleable SearchView android_focusable android_imeOptions android_inputType android_maxWidth closeIcon commitIcon defaultQueryHint goIcon iconifiedByDefault layout queryBackground queryHint searchHintIcon searchIcon submitBackground suggestionRowLayout voiceIcon
+styleable SimpleDraweeView actualImageResource actualImageScaleType actualImageUri backgroundImage fadeDuration failureImage failureImageScaleType overlayImage placeholderImage placeholderImageScaleType pressedStateOverlayImage progressBarAutoRotateInterval progressBarImage progressBarImageScaleType retryImage retryImageScaleType roundAsCircle roundBottomEnd roundBottomLeft roundBottomRight roundBottomStart roundTopEnd roundTopLeft roundTopRight roundTopStart roundWithOverlayColor roundedCornerRadius roundingBorderColor roundingBorderPadding roundingBorderWidth viewAspectRatio
+styleable Spinner android_dropDownWidth android_entries android_popupBackground android_prompt popupTheme
+styleable StateListDrawable android_constantSize android_dither android_enterFadeDuration android_exitFadeDuration android_variablePadding android_visible
+styleable StateListDrawableItem android_drawable
+styleable SwitchCompat android_textOff android_textOn android_thumb showText splitTrack switchMinWidth switchPadding switchTextAppearance thumbTextPadding thumbTint thumbTintMode track trackTint trackTintMode
+styleable TextAppearance android_fontFamily android_shadowColor android_shadowDx android_shadowDy android_shadowRadius android_textColor android_textColorHint android_textColorLink android_textFontWeight android_textSize android_textStyle android_typeface fontFamily fontVariationSettings textAllCaps textLocale
+styleable Toolbar android_gravity android_minHeight buttonGravity collapseContentDescription collapseIcon contentInsetEnd contentInsetEndWithActions contentInsetLeft contentInsetRight contentInsetStart contentInsetStartWithNavigation logo logoDescription maxButtonHeight menu navigationContentDescription navigationIcon popupTheme subtitle subtitleTextAppearance subtitleTextColor title titleMargin titleMarginBottom titleMarginEnd titleMarginStart titleMarginTop titleMargins titleTextAppearance titleTextColor
+styleable View android_focusable android_theme paddingEnd paddingStart theme
+styleable ViewBackgroundHelper android_background backgroundTint backgroundTintMode
+styleable ViewStubCompat android_id android_inflatedId android_layout
+xml rn_dev_preferences
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/@gregfrench/react-native-wheel-picker/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..914cb0e
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml:1:1-3:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml:1:1-3:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml:1:1-3:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml:2:5-22
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml:1:11-69
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/AndroidManifest.xml
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin b/node_modules/@gregfrench/react-native-wheel-picker/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..872b3d5
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/.DS_Store b/node_modules/@gregfrench/react-native-wheel-picker/android/src/.DS_Store
new file mode 100644
index 0000000..9c3de19
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/src/.DS_Store differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/.DS_Store b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/.DS_Store
new file mode 100644
index 0000000..1785cb9
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/.DS_Store differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/.DS_Store b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/.DS_Store
new file mode 100644
index 0000000..66b0bca
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/.DS_Store differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/.DS_Store b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/.DS_Store
new file mode 100644
index 0000000..7bdf813
Binary files /dev/null and b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/.DS_Store differ
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/AbstractWheelDecor.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/AbstractWheelDecor.java
new file mode 100644
index 0000000..d8665af
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/AbstractWheelDecor.java
@@ -0,0 +1,9 @@
+package com.aigestudio.wheelpicker.core;
+
+import android.graphics.Canvas;
+import android.graphics.Paint;
+import android.graphics.Rect;
+
+public abstract class AbstractWheelDecor {
+    public abstract void drawDecor(Canvas canvas, Rect rectLast, Rect rectNext, Paint paint);
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/AbstractWheelPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/AbstractWheelPicker.java
new file mode 100644
index 0000000..1502e37
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/AbstractWheelPicker.java
@@ -0,0 +1,401 @@
+package com.aigestudio.wheelpicker.core;
+
+import android.content.Context;
+import android.content.res.TypedArray;
+import android.graphics.Canvas;
+import android.graphics.Color;
+import android.graphics.Paint;
+import android.graphics.Rect;
+import android.os.Build;
+import android.os.Handler;
+import android.text.TextPaint;
+import android.util.AttributeSet;
+import android.view.MotionEvent;
+import android.view.VelocityTracker;
+import android.view.View;
+import android.view.ViewConfiguration;
+import android.view.animation.DecelerateInterpolator;
+
+import com.zyu.R;
+
+import java.util.Arrays;
+import java.util.List;
+
+/**
+ * <AUTHOR> 2015-12-12
+ */
+public abstract class AbstractWheelPicker extends View implements IWheelPicker {
+    private static final int TOUCH_DISTANCE_MINIMUM = 8;
+    private static final int VELOCITY_TRACKER_UNITS = 150;
+
+    protected VelocityTracker mTracker;
+    protected WheelScroller mScroller;
+    protected TextPaint mTextPaint;
+    protected Paint mPaint;
+    protected Rect mTextBound;
+    protected Rect mDrawBound;
+    protected Handler mHandler;
+    protected OnWheelChangeListener mListener;
+    protected AbstractWheelDecor mWheelDecor;
+
+    protected List<String> data;
+
+    protected String curData;
+
+    protected int state = SCROLL_STATE_IDLE;
+    protected int itemCount;
+    protected int itemIndex;
+    protected int itemSpace;
+    protected int textSize;
+    protected int textColor;
+    protected int curTextColor;
+    protected int maxTextWidth, maxTextHeight;
+    protected int wheelContentWidth, wheelContentHeight;
+    protected int wheelCenterX, wheelCenterY, wheelCenterTextY;
+
+    protected int lastX, lastY;
+    protected int diSingleMoveX, diSingleMoveY;
+    protected int disTotalMoveX, disTotalMoveY;
+
+    protected boolean ignorePadding;
+    protected boolean hasSameSize;
+
+    public static final int SCROLL_STATE_IDLE = 0;
+    public static final int SCROLL_STATE_DRAGGING = 1;
+    public static final int SCROLL_STATE_SCROLLING = 2;
+
+    public interface OnWheelChangeListener {
+        void onWheelScrolling(float deltaX, float deltaY);
+
+        void onWheelSelected(int index, String data);
+
+        void onWheelScrollStateChanged(int state);
+    }
+
+    public static class SimpleWheelChangeListener implements OnWheelChangeListener {
+        @Override
+        public void onWheelScrolling(float deltaX, float deltaY) {
+
+        }
+
+        @Override
+        public void onWheelSelected(int index, String data) {
+
+        }
+
+        @Override
+        public void onWheelScrollStateChanged(int state) {
+
+        }
+    }
+
+    public AbstractWheelPicker(Context context) {
+        super(context);
+        init(null);
+    }
+
+    public AbstractWheelPicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+        init(attrs);
+    }
+
+    private void init(AttributeSet attrs) {
+        obtainAttrs(attrs);
+        instantiation();
+        assignment();
+        computeWheelSizes();
+    }
+
+    protected void obtainAttrs(AttributeSet attrs) {
+        int defDataID = R.array.WheelArrayDefault;
+
+        int defItemIndex = 0;
+        int defItemCount = 7;
+        int defItemSpace = getResources().getDimensionPixelSize(R.dimen.WheelItemSpace);
+
+        int defTextSize = getResources().getDimensionPixelSize(R.dimen.WheelTextSize);
+        int defTextColor = 0xFF000000;
+
+        int defCurTextColor = 0xFF000000;
+        if (null != attrs) {
+            TypedArray a = getContext()
+                    .obtainStyledAttributes(attrs, R.styleable.AbstractWheelPicker);
+
+            int idData = a.getResourceId(R.styleable.AbstractWheelPicker_wheel_data, 0);
+            if (idData == 0) idData = defDataID;
+            data = Arrays.asList(getContext().getResources().getStringArray(idData));
+
+            itemIndex = a.getInt(R.styleable.AbstractWheelPicker_wheel_item_index, defItemIndex);
+            itemCount = a.getInt(R.styleable.AbstractWheelPicker_wheel_item_count, defItemCount);
+            itemSpace = a.getDimensionPixelSize(
+                    R.styleable.AbstractWheelPicker_wheel_item_space, defItemSpace);
+
+            textSize = a.getDimensionPixelSize(
+                    R.styleable.AbstractWheelPicker_wheel_text_size, defTextSize);
+            textColor = a.getColor(R.styleable.AbstractWheelPicker_wheel_text_color, defTextColor);
+
+            curTextColor = a.getColor(
+                    R.styleable.AbstractWheelPicker_wheel_text_color_current, defCurTextColor);
+
+            hasSameSize = a.getBoolean(R.styleable.AbstractWheelPicker_wheel_item_same_size, false);
+
+            a.recycle();
+        } else {
+            data = Arrays.asList(getContext().getResources().getStringArray(defDataID));
+
+            itemIndex = defItemIndex;
+            itemCount = defItemCount;
+            itemSpace = defItemSpace;
+
+            textSize = defTextSize;
+
+            curTextColor = defCurTextColor;
+        }
+    }
+
+    protected void instantiation() {
+        mTextPaint = new TextPaint(
+                Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG | Paint.LINEAR_TEXT_FLAG);
+        mTextPaint.setTextAlign(Paint.Align.CENTER);
+        mTextPaint.setTextSize(textSize);
+        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);
+
+        mTextBound = new Rect();
+        mDrawBound = new Rect();
+
+        mHandler = new Handler();
+
+        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.GINGERBREAD) {
+            mScroller = new OverScrollerCompat(getContext(), new DecelerateInterpolator());
+        } else {
+            mScroller = new ScrollerCompat(getContext(), new DecelerateInterpolator());
+        }
+        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
+            mScroller.setFriction(ViewConfiguration.getScrollFriction() / 25);
+        }
+    }
+
+    protected void assignment() {
+        curData = "";
+    }
+
+    protected void computeWheelSizes() {
+        disTotalMoveX = 0;
+        disTotalMoveY = 0;
+        maxTextWidth = 0;
+        maxTextHeight = 0;
+        if (hasSameSize) {
+            String text = data.get(0);
+            mTextPaint.getTextBounds(text, 0, text.length(), mTextBound);
+            maxTextWidth = Math.max(maxTextWidth, mTextBound.width());
+            maxTextHeight = Math.max(maxTextHeight, mTextBound.height());
+        } else {
+            for (String text : data) {
+                mTextPaint.getTextBounds(text, 0, text.length(), mTextBound);
+                maxTextWidth = Math.max(maxTextWidth, mTextBound.width());
+                maxTextHeight = Math.max(maxTextHeight, mTextBound.height());
+            }
+        }
+    }
+
+    @Override
+    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
+        int modeWidth = MeasureSpec.getMode(widthMeasureSpec);
+        int modeHeight = MeasureSpec.getMode(heightMeasureSpec);
+
+        int sizeWidth = MeasureSpec.getSize(widthMeasureSpec);
+        int sizeHeight = MeasureSpec.getSize(heightMeasureSpec);
+
+        int resultWidth = wheelContentWidth, resultHeight = wheelContentHeight;
+        resultWidth += (getPaddingLeft() + getPaddingRight());
+        resultHeight += (getPaddingTop() + getPaddingBottom());
+
+        resultWidth = measureSize(modeWidth, sizeWidth, resultWidth);
+        resultHeight = measureSize(modeHeight, sizeHeight, resultHeight);
+
+        setMeasuredDimension(resultWidth, resultHeight);
+    }
+
+    private int measureSize(int mode, int sizeExpect, int sizeActual) {
+        int realSize;
+        if (mode == MeasureSpec.EXACTLY) {
+            realSize = sizeExpect;
+        } else {
+            realSize = sizeActual;
+            if (mode == MeasureSpec.AT_MOST) {
+                realSize = Math.min(realSize, sizeExpect);
+            }
+        }
+        return realSize;
+    }
+
+    @Override
+    protected void onSizeChanged(int w, int h, int oldW, int oldH) {
+        onWheelSelected(itemIndex, data.get(itemIndex));
+
+        mDrawBound.set(getPaddingLeft(), getPaddingTop(), w - getPaddingRight(),
+                h - getPaddingBottom());
+
+        wheelCenterX = mDrawBound.centerX();
+        wheelCenterY = mDrawBound.centerY();
+        wheelCenterTextY = (int) (wheelCenterY - (mTextPaint.ascent() +
+                mTextPaint.descent()) / 2);
+    }
+
+    @Override
+    protected void onDraw(Canvas canvas) {
+        drawBackground(canvas);
+
+        canvas.save();
+        canvas.clipRect(mDrawBound);
+        drawItems(canvas);
+        canvas.restore();
+
+        drawForeground(canvas);
+    }
+
+    protected abstract void drawBackground(Canvas canvas);
+
+    protected abstract void drawItems(Canvas canvas);
+
+    protected abstract void drawForeground(Canvas canvas);
+
+    @Override
+    public boolean onTouchEvent(MotionEvent event) {
+        if (null == mTracker) {
+            mTracker = VelocityTracker.obtain();
+        }
+        mTracker.addMovement(event);
+        switch (event.getAction()) {
+            case MotionEvent.ACTION_DOWN:
+                getParent().requestDisallowInterceptTouchEvent(true);
+                if (!mScroller.isFinished()) mScroller.abortAnimation();
+                lastX = (int) event.getX();
+                lastY = (int) event.getY();
+                onTouchDown(event);
+                break;
+            case MotionEvent.ACTION_MOVE:
+                diSingleMoveX += (event.getX() - lastX);
+                diSingleMoveY += (event.getY() - lastY);
+                lastX = (int) event.getX();
+                lastY = (int) event.getY();
+                onTouchMove(event);
+                break;
+            case MotionEvent.ACTION_UP:
+                disTotalMoveX += diSingleMoveX;
+                disTotalMoveY += diSingleMoveY;
+                diSingleMoveX = 0;
+                diSingleMoveY = 0;
+                mTracker.computeCurrentVelocity(VELOCITY_TRACKER_UNITS);
+                onTouchUp(event);
+                getParent().requestDisallowInterceptTouchEvent(false);
+                mTracker.recycle();
+                mTracker = null;
+                break;
+            case MotionEvent.ACTION_CANCEL:
+                getParent().requestDisallowInterceptTouchEvent(false);
+                mScroller.abortAnimation();
+                mTracker.recycle();
+                mTracker = null;
+                break;
+        }
+        return true;
+    }
+
+    protected abstract void onTouchDown(MotionEvent event);
+
+    protected abstract void onTouchMove(MotionEvent event);
+
+    protected abstract void onTouchUp(MotionEvent event);
+
+    protected boolean isEventValid() {
+        return isEventValidVer() || isEventValidHor();
+    }
+
+    protected boolean isEventValidHor() {
+        return Math.abs(diSingleMoveX) > TOUCH_DISTANCE_MINIMUM;
+    }
+
+    protected boolean isEventValidVer() {
+        return Math.abs(diSingleMoveY) > TOUCH_DISTANCE_MINIMUM;
+    }
+
+    protected void onWheelScrolling(float deltaX, float deltaY) {
+        if (null != mListener) {
+            mListener.onWheelScrolling(deltaX, deltaY);
+        }
+    }
+
+    protected void onWheelSelected(int index, String data) {
+        if (null != mListener) {
+            mListener.onWheelSelected(index, data);
+        }
+    }
+
+    protected void onWheelScrollStateChanged(int state) {
+        if (this.state != state) {
+            this.state = state;
+            if (null != mListener) mListener.onWheelScrollStateChanged(state);
+        }
+    }
+
+    @Override
+    public void setData(List<String> data) {
+        this.data = data;
+        computeWheelSizes();
+        requestLayout();
+    }
+
+    @Override
+    public void setOnWheelChangeListener(OnWheelChangeListener listener) {
+        this.mListener = listener;
+    }
+
+    @Override
+    public void setItemIndex(int index) {
+        itemIndex = index;
+        computeWheelSizes();
+        requestLayout();
+    }
+
+    @Override
+    public void setItemSpace(int space) {
+        itemSpace = space;
+        computeWheelSizes();
+        requestLayout();
+    }
+
+    @Override
+    public void setItemCount(int count) {
+        itemCount = count;
+        computeWheelSizes();
+        requestLayout();
+    }
+
+    @Override
+    public void setTextColor(int color) {
+        textColor = color;
+        invalidate();
+    }
+
+    @Override
+    public void setTextSize(int size) {
+        textSize = size;
+        mTextPaint.setTextSize(size);
+        computeWheelSizes();
+        requestLayout();
+    }
+
+    @Override
+    public void setCurrentTextColor(int color) {
+        curTextColor = color;
+        // May be you don't need to invalidate all of view area.
+    }
+
+    @Override
+    public void setWheelDecor(boolean ignorePadding, AbstractWheelDecor decor) {
+        this.ignorePadding = ignorePadding;
+        mWheelDecor = decor;
+        // May be you don't need to invalidate all of view area.
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/IWheelPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/IWheelPicker.java
new file mode 100644
index 0000000..895c4a4
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/IWheelPicker.java
@@ -0,0 +1,118 @@
+package com.aigestudio.wheelpicker.core;
+
+import java.util.List;
+
+/**
+ * 滚轮控件对外功能接口
+ * Interface of WheelView's function you can use
+ *
+ * <AUTHOR> 2015-12-03
+ * <AUTHOR> 2015-12-08
+ * <AUTHOR> 2015-12-12
+ * @version 1.0.0 beta
+ */
+public interface IWheelPicker {
+    /**
+     * 设置显示数据
+     * 目前为止WheelPicker仅仅支持字符串列表类型的数据源
+     * Set data to display in WheelView
+     * So far, WheelPicker only support string list for data set
+     *
+     * @param data 显示数据
+     *             Display data set
+     */
+    void setData(List<String> data);
+
+    /**
+     * 设置滚动监听
+     * Set mListener
+     *
+     * @param listener 滚动监听器
+     */
+    void setOnWheelChangeListener(AbstractWheelPicker.OnWheelChangeListener listener);
+
+    /**
+     * 设置当前居中显示的文本在数据列表中的下标值
+     * Set index of data in list will display in WheelView center
+     *
+     * @param index 下标值
+     *              Index of data in list will display in WheelView center
+     */
+    void setItemIndex(int index);
+
+    /**
+     * 设置Item间距
+     * Set space of items
+     *
+     * @param space Item间距大小
+     *              Space of items
+     */
+    void setItemSpace(int space);
+
+    /**
+     * 设置显示的Item个数
+     * Set count of item display
+     *
+     * @param count Item显示的个数
+     *              Count of item display
+     */
+    void setItemCount(int count);
+
+    /**
+     * 设置文本颜色
+     * Set item text color
+     *
+     * @param color 文本颜色
+     *              Item text color
+     */
+    void setTextColor(int color);
+
+    /**
+     * 设置文本大小
+     * Set size of text
+     *
+     * @param size 文本大小
+     *             Text size
+     */
+    void setTextSize(int size);
+
+    /**
+     * 清除缓存
+     * 在某些子类实现中为了加速绘制减少性能损耗会将一些计算结果进行缓存，当影响这些计算结果的参数发生改变时需要清除这些缓存并重建
+     * Clear calculate cache
+     * WheelPicker will cache calculate result in some specific implementation, when the parameter influences the calculated result changed, we need to clean these cache and rebuild them.
+     * This method will help to how to clean the cache.
+     */
+    void clearCache();
+
+    /**
+     * 设置当前Item文本的颜色
+     * Set color of text in current item
+     *
+     * @param color 16进制的颜色值 例如：0xFF456789
+     *              Color in hex value like 0xFF456789
+     */
+    void setCurrentTextColor(int color);
+
+    /**
+     * 设置当前Item项的装饰物
+     * Set decorate of current item
+     *
+     * @param ignorePadding 是否忽略WheelView的内边距
+     *                      Is ignore padding of WheelView
+     * @param decor         装饰物对象
+     *                      Subclass of AbstractWheelDecor{@link AbstractWheelDecor}
+     */
+    void setWheelDecor(boolean ignorePadding, AbstractWheelDecor decor);
+
+//    /**
+//     * 设置WheelPicker每个Item中的文本宽高是否都一致
+//     * 默认情况下WheelPicker会遍历数据集中的每一个Item项来计算确定自身的宽高尺寸，当每个Item项的宽高都一致时（比如1900-2100这样的年份数据）对整个数据集遍历显然没必要，此时就可以通过该方法传入true来表示每一个Item项都应该有相同的尺寸大小，这样可以在数据较多的情况下减轻计算损耗
+//     * Set is every item of WheelPicker has same width and height
+//     *
+//     * @param hasSameSize true表示每个Item中的文本宽高都一样，否则反之
+//     *                    true means every item of WheelPicker has same width and height, otherwise.
+//     */
+//    @Deprecated
+//    void setHasSameSize(boolean hasSameSize);
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/OverScrollerCompat.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/OverScrollerCompat.java
new file mode 100644
index 0000000..1f43769
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/OverScrollerCompat.java
@@ -0,0 +1,157 @@
+package com.aigestudio.wheelpicker.core;
+
+import android.annotation.TargetApi;
+import android.content.Context;
+import android.os.Build;
+import android.view.animation.Interpolator;
+import android.widget.OverScroller;
+
+@TargetApi(Build.VERSION_CODES.GINGERBREAD)
+class OverScrollerCompat implements WheelScroller {
+    private OverScroller mOverScroller;
+
+    OverScrollerCompat(Context context) {
+        mOverScroller = new OverScroller(context);
+    }
+
+    OverScrollerCompat(Context context, Interpolator interpolator) {
+        mOverScroller = new OverScroller(context, interpolator);
+    }
+
+    OverScrollerCompat(Context context, Interpolator interpolator, float bounceCoefficientX, float bounceCoefficientY) {
+        mOverScroller = new OverScroller(context, interpolator, bounceCoefficientX, bounceCoefficientY);
+    }
+
+    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
+    OverScrollerCompat(Context context, Interpolator interpolator, float bounceCoefficientX, float bounceCoefficientY, boolean flywheel) {
+        mOverScroller = new OverScroller(context, interpolator, bounceCoefficientX, bounceCoefficientY, flywheel);
+    }
+
+    @Override
+    public void abortAnimation() {
+        mOverScroller.abortAnimation();
+    }
+
+    @Override
+    public boolean computeScrollOffset() {
+        return mOverScroller.computeScrollOffset();
+    }
+
+    @Override
+    public void extendDuration(int extend) {
+        throw new RuntimeException("OverScrollerCompat not support this method.");
+    }
+
+    @Override
+    public void fling(int startX, int startY, int velocityX, int velocityY, int minX, int maxX, int minY, int maxY) {
+        mOverScroller.fling(startX, startY, velocityX, velocityY, minX, maxX, minY, maxY);
+    }
+
+    @Override
+    public void fling(int startX, int startY, int velocityX, int velocityY, int minX, int maxX, int minY, int maxY, int overX, int overY) {
+        mOverScroller.fling(startX, startY, velocityX, velocityY, minX, maxX, minY, maxY, overX, overY);
+    }
+
+    @Override
+    public void forceFinished(boolean finished) {
+        mOverScroller.forceFinished(finished);
+    }
+
+    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
+    @Override
+    public float getCurrVelocity() {
+        return mOverScroller.getCurrVelocity();
+    }
+
+    @Override
+    public int getCurrX() {
+        return mOverScroller.getCurrX();
+    }
+
+    @Override
+    public int getCurrY() {
+        return mOverScroller.getCurrY();
+    }
+
+    @Override
+    public int getDuration() {
+        throw new RuntimeException("OverScrollerCompat not support this method.");
+    }
+
+    @Override
+    public int getFinalX() {
+        return mOverScroller.getFinalX();
+    }
+
+    @Override
+    public int getFinalY() {
+        return mOverScroller.getFinalY();
+    }
+
+    @Override
+    public int getStartX() {
+        return mOverScroller.getStartX();
+    }
+
+    @Override
+    public int getStartY() {
+        return mOverScroller.getStartY();
+    }
+
+    @Override
+    public boolean isFinished() {
+        return mOverScroller.isFinished();
+    }
+
+    @Override
+    public void setFinalX(int newX) {
+        throw new RuntimeException("OverScrollerCompat not support this method.");
+    }
+
+    @Override
+    public void setFinalY(int newY) {
+        throw new RuntimeException("OverScrollerCompat not support this method.");
+    }
+
+    @Override
+    public boolean isOverScrolled() {
+        return mOverScroller.isOverScrolled();
+    }
+
+    @Override
+    public void notifyHorizontalEdgeReached(int startX, int finalX, int overX) {
+        mOverScroller.notifyHorizontalEdgeReached(startX, finalX, overX);
+    }
+
+    @Override
+    public void notifyVerticalEdgeReached(int startY, int finalY, int overY) {
+        mOverScroller.notifyVerticalEdgeReached(startY, finalY, overY);
+
+    }
+
+    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
+    @Override
+    public void setFriction(float friction) {
+        mOverScroller.setFriction(friction);
+    }
+
+    @Override
+    public boolean springBack(int startX, int startY, int minX, int maxX, int minY, int maxY) {
+        return mOverScroller.springBack(startX, startY, minX, maxX, minY, maxY);
+    }
+
+    @Override
+    public void startScroll(int startX, int startY, int dx, int dy) {
+        mOverScroller.startScroll(startX, startY, dx, dy);
+    }
+
+    @Override
+    public void startScroll(int startX, int startY, int dx, int dy, int duration) {
+        mOverScroller.startScroll(startX, startY, dx, dy, duration);
+    }
+
+    @Override
+    public int timePassed() {
+        throw new RuntimeException("OverScrollerCompat not support this method.");
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/ScrollerCompat.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/ScrollerCompat.java
new file mode 100644
index 0000000..834b2f2
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/ScrollerCompat.java
@@ -0,0 +1,153 @@
+package com.aigestudio.wheelpicker.core;
+
+import android.annotation.TargetApi;
+import android.content.Context;
+import android.os.Build;
+import android.view.animation.Interpolator;
+import android.widget.Scroller;
+
+class ScrollerCompat implements WheelScroller {
+    private Scroller mScroller;
+
+    ScrollerCompat(Context context) {
+        mScroller = new Scroller(context);
+    }
+
+    ScrollerCompat(Context context, Interpolator interpolator) {
+        mScroller = new Scroller(context, interpolator);
+    }
+
+    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
+    ScrollerCompat(Context context, Interpolator interpolator, boolean flywheel) {
+        mScroller = new Scroller(context, interpolator, flywheel);
+    }
+
+    @Override
+    public void abortAnimation() {
+        mScroller.abortAnimation();
+    }
+
+    @Override
+    public boolean computeScrollOffset() {
+        return mScroller.computeScrollOffset();
+    }
+
+    @Override
+    public void extendDuration(int extend) {
+        mScroller.extendDuration(extend);
+    }
+
+    @Override
+    public void fling(int startX, int startY, int velocityX, int velocityY, int minX, int maxX, int minY, int maxY) {
+        mScroller.fling(startX, startY, velocityX, velocityY, minX, maxX, minY, maxY);
+    }
+
+    @Override
+    public void fling(int startX, int startY, int velocityX, int velocityY, int minX, int maxX, int minY, int maxY, int overX, int overY) {
+        mScroller.fling(startX, startY, velocityX, velocityY, minX, maxX, minY, maxY);
+    }
+
+    @Override
+    public void forceFinished(boolean finished) {
+        mScroller.forceFinished(finished);
+    }
+
+    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH)
+    @Override
+    public float getCurrVelocity() {
+        return mScroller.getCurrVelocity();
+    }
+
+    @Override
+    public int getCurrX() {
+        return mScroller.getCurrX();
+    }
+
+    @Override
+    public int getCurrY() {
+        return mScroller.getCurrY();
+    }
+
+    @Override
+    public int getDuration() {
+        return mScroller.getDuration();
+    }
+
+    @Override
+    public int getFinalX() {
+        return mScroller.getFinalX();
+    }
+
+    @Override
+    public int getFinalY() {
+        return mScroller.getFinalY();
+    }
+
+    @TargetApi(Build.VERSION_CODES.CUPCAKE)
+    @Override
+    public int getStartX() {
+        return mScroller.getStartX();
+    }
+
+    @TargetApi(Build.VERSION_CODES.CUPCAKE)
+    @Override
+    public int getStartY() {
+        return mScroller.getStartY();
+    }
+
+    @Override
+    public boolean isFinished() {
+        return mScroller.isFinished();
+    }
+
+    @Override
+    public void setFinalX(int newX) {
+        mScroller.setFinalX(newX);
+    }
+
+    @Override
+    public void setFinalY(int newY) {
+        mScroller.setFinalY(newY);
+    }
+
+    @Override
+    public boolean isOverScrolled() {
+        throw new RuntimeException("ScrollerCompat not support this method.");
+    }
+
+    @Override
+    public void notifyHorizontalEdgeReached(int startX, int finalX, int overX) {
+        throw new RuntimeException("ScrollerCompat not support this method.");
+    }
+
+    @Override
+    public void notifyVerticalEdgeReached(int startY, int finalY, int overY) {
+        throw new RuntimeException("ScrollerCompat not support this method.");
+    }
+
+    @TargetApi(Build.VERSION_CODES.HONEYCOMB)
+    @Override
+    public void setFriction(float friction) {
+        mScroller.setFriction(friction);
+    }
+
+    @Override
+    public boolean springBack(int startX, int startY, int minX, int maxX, int minY, int maxY) {
+        throw new RuntimeException("ScrollerCompat not support this method.");
+    }
+
+    @Override
+    public void startScroll(int startX, int startY, int dx, int dy) {
+        mScroller.startScroll(startX, startY, dx, dy);
+    }
+
+    @Override
+    public void startScroll(int startX, int startY, int dx, int dy, int duration) {
+        mScroller.startScroll(startX, startY, dx, dy, duration);
+    }
+
+    @Override
+    public int timePassed() {
+        return mScroller.timePassed();
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/WheelScroller.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/WheelScroller.java
new file mode 100644
index 0000000..6b79ec7
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/core/WheelScroller.java
@@ -0,0 +1,53 @@
+package com.aigestudio.wheelpicker.core;
+
+public interface WheelScroller {
+    void abortAnimation();
+
+    boolean computeScrollOffset();
+
+    void extendDuration(int extend);
+
+    void fling(int startX, int startY, int velocityX, int velocityY, int minX, int maxX, int minY, int maxY);
+
+    void fling(int startX, int startY, int velocityX, int velocityY, int minX, int maxX, int minY, int maxY, int overX, int overY);
+
+    void forceFinished(boolean finished);
+
+    float getCurrVelocity();
+
+    int getCurrX();
+
+    int getCurrY();
+
+    int getDuration();
+
+    int getFinalX();
+
+    int getFinalY();
+
+    int getStartX();
+
+    int getStartY();
+
+    boolean isFinished();
+
+    void setFinalX(int newX);
+
+    void setFinalY(int newY);
+
+    boolean isOverScrolled();
+
+    void notifyHorizontalEdgeReached(int startX, int finalX, int overX);
+
+    void notifyVerticalEdgeReached(int startY, int finalY, int overY);
+
+    void setFriction(float friction);
+
+    boolean springBack(int startX, int startY, int minX, int maxX, int minY, int maxY);
+
+    void startScroll(int startX, int startY, int dx, int dy);
+
+    void startScroll(int startX, int startY, int dx, int dy, int duration);
+
+    int timePassed();
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/CrossHorImpl.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/CrossHorImpl.java
new file mode 100644
index 0000000..40c739e
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/CrossHorImpl.java
@@ -0,0 +1,131 @@
+package com.aigestudio.wheelpicker.view;
+
+import android.graphics.Camera;
+import android.graphics.Canvas;
+import android.graphics.Matrix;
+import android.graphics.Rect;
+import android.text.TextPaint;
+import android.view.VelocityTracker;
+
+import com.aigestudio.wheelpicker.core.WheelScroller;
+
+import java.util.HashMap;
+
+class CrossHorImpl implements ICrossOrientation {
+    private final HashMap<Integer, Integer> DEGREE = new HashMap<>();
+
+    @Override
+    public int getUnitDeltaTotal(WheelScroller scroller) {
+        return scroller.getCurrX();
+    }
+
+    @Override
+    public void startScroll(WheelScroller scroller, int start, int distance) {
+        scroller.startScroll(start, 0, distance, 0, 300);
+    }
+
+    @Override
+    public int computeRadius(int count, int space, int width, int height) {
+        return (int) (((count + 1) * width + (count - 1) * space) / Math.PI);
+    }
+
+    @Override
+    public int getCurvedWidth(int radius, int width, int height) {
+        return radius * 2;
+    }
+
+    @Override
+    public int getCurvedHeight(int radius, int width, int height) {
+        return height;
+    }
+
+    @Override
+    public int getStraightWidth(int count, int space, int width, int height) {
+        return width * count + (count - 1) * space;
+    }
+
+    @Override
+    public int getStraightHeight(int count, int space, int width, int height) {
+        return height;
+    }
+
+    @Override
+    public int getStraightUnit(int space, int width, int height) {
+        return width + space;
+    }
+
+    @Override
+    public int getDisplay(int count, int space, int width, int height) {
+        return (count / 2 + 1) * (width + space);
+    }
+
+    @Override
+    public void rotateCamera(Camera camera, int degree) {
+        camera.rotateY(degree);
+    }
+
+    @Override
+    public void matrixToCenter(Matrix matrix, int space, int x, int y) {
+        matrix.preTranslate(-(x + space), -y);
+        matrix.postTranslate(x + space, y);
+    }
+
+    @Override
+    public void draw(Canvas canvas, TextPaint paint, String data, int space, int x, int y) {
+        canvas.drawText(data, x + space, y, paint);
+    }
+
+    @Override
+    public int computeDegreeSingleDelta(int moveX, int moveY, int radius) {
+        int degree;
+        if (DEGREE.containsKey(moveX)) {
+            degree = DEGREE.get(moveX);
+        } else {
+            degree = (int) Math.toDegrees(Math.asin(moveX * 1.0 / radius));
+            DEGREE.put(moveX, degree);
+        }
+        return degree;
+    }
+
+    @Override
+    public void fling(WheelScroller scroller, VelocityTracker tracker, int from, int disMin, int disMax, int over) {
+        scroller.fling(from, 0, (int) tracker.getXVelocity(), 0, disMin, disMax, 0, 0, over, 0);
+    }
+
+    @Override
+    public int computeStraightUnit(int unit, int index, int totalMoveX, int totalMoveY, int singleMoveX, int singleMoveY) {
+        return unit * index + totalMoveX + singleMoveX;
+    }
+
+    @Override
+    public int getUnitDeltaTotal(int totalMoveX, int totalMoveY) {
+        return totalMoveX;
+    }
+
+    @Override
+    public void computeCurItemRect(Rect rect, int space, int width, int height, int textWidth, int textHeight, int x, int y, int left, int top, int right, int bottom) {
+        int half = (textWidth + space) / 2;
+        rect.set(x - half, 0, x + half, height);
+    }
+
+    @Override
+    public void clearCache() {
+        DEGREE.clear();
+    }
+
+    @Override
+    public void removePadding(Rect rect, int left, int top, int right, int bottom) {
+        rect.set(rect.left, rect.top + top, rect.right, rect.bottom - bottom);
+    }
+
+    @Override
+    public void computeRectPadding(Rect rectLast, Rect rectNext, Rect rect, int left, int top, int right, int bottom) {
+        rectLast.set(rect.left, 0, rect.right, top);
+        rectNext.set(rect.left, rect.bottom - bottom, rect.right, rect.bottom);
+    }
+
+    @Override
+    public int obtainCurrentDis(int moveX, int moveY) {
+        return moveX;
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/CrossVerImpl.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/CrossVerImpl.java
new file mode 100644
index 0000000..f788754
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/CrossVerImpl.java
@@ -0,0 +1,131 @@
+package com.aigestudio.wheelpicker.view;
+
+import android.graphics.Camera;
+import android.graphics.Canvas;
+import android.graphics.Matrix;
+import android.graphics.Rect;
+import android.text.TextPaint;
+import android.view.VelocityTracker;
+
+import com.aigestudio.wheelpicker.core.WheelScroller;
+
+import java.util.HashMap;
+
+class CrossVerImpl implements ICrossOrientation {
+    private final HashMap<Integer, Integer> DEGREE = new HashMap<>();
+
+    @Override
+    public int getUnitDeltaTotal(WheelScroller scroller) {
+        return scroller.getCurrY();
+    }
+
+    @Override
+    public void startScroll(WheelScroller scroller, int start, int distance) {
+        scroller.startScroll(0, start, 0, distance, 300);
+    }
+
+    @Override
+    public int computeRadius(int count, int space, int width, int height) {
+        return (int) (((count + 1) * height + (count - 1) * space) / Math.PI);
+    }
+
+    @Override
+    public int getCurvedWidth(int radius, int width, int height) {
+        return width;
+    }
+
+    @Override
+    public int getCurvedHeight(int radius, int width, int height) {
+        return radius * 2;
+    }
+
+    @Override
+    public int getStraightWidth(int count, int space, int width, int height) {
+        return width;
+    }
+
+    @Override
+    public int getStraightHeight(int count, int space, int width, int height) {
+        return height * count + (count - 1) * space;
+    }
+
+    @Override
+    public int getStraightUnit(int space, int width, int height) {
+        return height + space;
+    }
+
+    @Override
+    public int getDisplay(int count, int space, int width, int height) {
+        return (count / 2 + 1) * (height + space);
+    }
+
+    @Override
+    public void rotateCamera(Camera camera, int degree) {
+        camera.rotateX(-degree);
+    }
+
+    @Override
+    public void matrixToCenter(Matrix matrix, int space, int x, int y) {
+        matrix.preTranslate(-x, -(y + space));
+        matrix.postTranslate(x, y + space);
+    }
+
+    @Override
+    public void draw(Canvas canvas, TextPaint paint, String data, int space, int x, int y) {
+        canvas.drawText(data, x, y + space, paint);
+    }
+
+    @Override
+    public int computeDegreeSingleDelta(int moveX, int moveY, int radius) {
+        int degree;
+        if (DEGREE.containsKey(moveY)) {
+            degree = DEGREE.get(moveY);
+        } else {
+            degree = (int) Math.toDegrees(Math.asin(moveY * 1.0 / radius));
+            DEGREE.put(moveY, degree);
+        }
+        return degree;
+    }
+
+    @Override
+    public void fling(WheelScroller scroller, VelocityTracker tracker, int from, int disMin, int disMax, int over) {
+        scroller.fling(0, from, 0, (int) tracker.getYVelocity(), 0, 0, disMin, disMax, 0, over);
+    }
+
+    @Override
+    public int computeStraightUnit(int unit, int index, int totalMoveX, int totalMoveY, int singleMoveX, int singleMoveY) {
+        return unit * index + totalMoveY + singleMoveY;
+    }
+
+    @Override
+    public int getUnitDeltaTotal(int totalMoveX, int totalMoveY) {
+        return totalMoveY;
+    }
+
+    @Override
+    public void computeCurItemRect(Rect rect, int space, int width, int height, int textWidth, int textHeight, int x, int y, int left, int top, int right, int bottom) {
+        int half = (textHeight + space) / 2;
+        rect.set(0, y - half, width, y + half);
+    }
+
+    @Override
+    public void clearCache() {
+        DEGREE.clear();
+    }
+
+    @Override
+    public void removePadding(Rect rect, int left, int top, int right, int bottom) {
+        rect.set(rect.left + left, rect.top, rect.right - right, rect.bottom);
+    }
+
+    @Override
+    public void computeRectPadding(Rect rectLast, Rect rectNext, Rect rect, int left, int top, int right, int bottom) {
+        rectLast.set(0, rect.top, left, rect.bottom);
+        rectNext.set(rect.right - right, rect.top, rect.right, rect.bottom);
+    }
+
+    @Override
+    public int obtainCurrentDis(int moveX, int moveY) {
+        return moveY;
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/ICrossOrientation.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/ICrossOrientation.java
new file mode 100644
index 0000000..dc7adba
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/ICrossOrientation.java
@@ -0,0 +1,55 @@
+package com.aigestudio.wheelpicker.view;
+
+import android.graphics.Camera;
+import android.graphics.Canvas;
+import android.graphics.Matrix;
+import android.graphics.Rect;
+import android.text.TextPaint;
+import android.view.VelocityTracker;
+
+import com.aigestudio.wheelpicker.core.WheelScroller;
+
+interface ICrossOrientation {
+
+    int getUnitDeltaTotal(WheelScroller scroller);
+
+    void startScroll(WheelScroller scroller, int start, int distance);
+
+    int computeRadius(int count, int space, int width, int height);
+
+    int getCurvedWidth(int radius, int width, int height);
+
+    int getCurvedHeight(int radius, int width, int height);
+
+    int getStraightWidth(int count, int space, int width, int height);
+
+    int getStraightHeight(int count, int space, int width, int height);
+
+    int getStraightUnit(int space, int width, int height);
+
+    int getDisplay(int count, int space, int width, int height);
+
+    void rotateCamera(Camera camera, int degree);
+
+    void matrixToCenter(Matrix matrix, int space, int x, int y);
+
+    void draw(Canvas canvas, TextPaint paint, String data, int space, int x, int y);
+
+    int computeDegreeSingleDelta(int moveX, int moveY, int radius);
+
+    void fling(WheelScroller scroller, VelocityTracker tracker, int from, int disMin, int disMax, int over);
+
+    int computeStraightUnit(int unit, int index, int totalMoveX, int totalMoveY, int singleMoveX, int singleMoveY);
+
+    int getUnitDeltaTotal(int totalMoveX, int totalMoveY);
+
+    void computeCurItemRect(Rect rect, int space, int width, int height, int textWidth, int textHeight, int x, int y, int left, int top, int right, int bottom);
+
+    void clearCache();
+
+    void removePadding(Rect rect, int left, int top, int right, int bottom);
+
+    void computeRectPadding(Rect rectLast, Rect rectNext, Rect rect, int left, int top, int right, int bottom);
+
+    int obtainCurrentDis(int moveX, int moveY);
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/IWheelCrossPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/IWheelCrossPicker.java
new file mode 100644
index 0000000..5cfec9c
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/IWheelCrossPicker.java
@@ -0,0 +1,5 @@
+package com.aigestudio.wheelpicker.view;
+
+interface IWheelCrossPicker {
+    void setOrientation(int orientation);
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelCircularPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelCircularPicker.java
new file mode 100644
index 0000000..614ad6c
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelCircularPicker.java
@@ -0,0 +1,64 @@
+package com.aigestudio.wheelpicker.view;
+
+import android.content.Context;
+import android.graphics.Canvas;
+import android.util.AttributeSet;
+import android.view.MotionEvent;
+
+import com.aigestudio.wheelpicker.core.AbstractWheelPicker;
+
+/**
+ * 圆形滚动选择器
+ * 该View将会在以后的版本中启用
+ * CircularPicker
+ * This view will be available in later versions
+ */
+class WheelCircularPicker extends AbstractWheelPicker {
+    public WheelCircularPicker(Context context) {
+        super(context);
+    }
+
+    public WheelCircularPicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+    }
+
+    @Override
+    protected void computeWheelSizes() {
+
+    }
+
+    @Override
+    protected void drawBackground(Canvas canvas) {
+
+    }
+
+    @Override
+    protected void drawItems(Canvas canvas) {
+
+    }
+
+    @Override
+    protected void drawForeground(Canvas canvas) {
+
+    }
+
+    @Override
+    protected void onTouchDown(MotionEvent event) {
+
+    }
+
+    @Override
+    protected void onTouchMove(MotionEvent event) {
+
+    }
+
+    @Override
+    protected void onTouchUp(MotionEvent event) {
+
+    }
+
+    @Override
+    public void clearCache() {
+
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelCrossPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelCrossPicker.java
new file mode 100644
index 0000000..f8817a9
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelCrossPicker.java
@@ -0,0 +1,223 @@
+package com.aigestudio.wheelpicker.view;
+
+import android.content.Context;
+import android.graphics.Canvas;
+import android.graphics.Color;
+import android.graphics.Rect;
+import android.util.AttributeSet;
+import android.view.MotionEvent;
+
+import com.aigestudio.wheelpicker.core.AbstractWheelDecor;
+import com.aigestudio.wheelpicker.core.AbstractWheelPicker;
+
+import java.util.HashMap;
+import java.util.List;
+
+/**
+ * <AUTHOR> 2015-12-13
+ */
+public abstract class WheelCrossPicker extends AbstractWheelPicker
+        implements IWheelCrossPicker, Runnable {
+    private static final int TIME_REFRESH = 16;
+
+    /**
+     * 滚轮方向常量值
+     * Constant of WheelView's direction
+     */
+    public static final int HORIZONTAL = 0;
+    public static final int VERTICAL = 1;
+
+    protected ICrossOrientation mOrientation;
+    protected Rect rectCurDecor;
+    protected Rect rectCurItem;
+    protected Rect rectLast, rectNext;
+
+    protected int unit;
+    protected int unitDeltaTotal;
+    protected int unitDeltaMin, unitDeltaMax;
+    protected int unitDisplayMin, unitDisplayMax;
+
+    public WheelCrossPicker(Context context) {
+        super(context);
+    }
+
+    public WheelCrossPicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+    }
+
+    @Override
+    protected void instantiation() {
+        super.instantiation();
+        mOrientation = new CrossVerImpl();
+
+        rectCurDecor = new Rect();
+        rectCurItem = new Rect();
+
+        rectLast = new Rect();
+        rectNext = new Rect();
+    }
+
+    @Override
+    public void setOrientation(int orientation) {
+        mOrientation = orientation == HORIZONTAL ? new CrossHorImpl() : new CrossVerImpl();
+        computeWheelSizes();
+        requestLayout();
+    }
+
+    @Override
+    protected void onSizeChanged(int w, int h, int oldW, int oldH) {
+        super.onSizeChanged(w, h, oldW, oldH);
+
+        mOrientation.computeCurItemRect(rectCurItem, itemSpace, w, h, maxTextWidth, maxTextHeight,
+                wheelCenterX, wheelCenterY, getPaddingLeft(), getPaddingTop(), getPaddingRight(),
+                getPaddingBottom());
+        mOrientation.computeRectPadding(rectLast, rectNext, rectCurItem, getPaddingLeft(),
+                getPaddingTop(), getPaddingRight(), getPaddingBottom());
+        rectCurDecor.set(rectCurItem);
+        if (!ignorePadding) {
+            mOrientation.removePadding(rectCurDecor, getPaddingLeft(), getPaddingTop(),
+                    getPaddingRight(), getPaddingBottom());
+        }
+    }
+
+    @Override
+    protected void drawBackground(Canvas canvas) {
+//        mPaint.setColor(Color.RED);
+//        rectCurItem.set(rectCurItem.left,rectCurItem.top,rectCurItem.right-10,rectCurItem.bottom);
+//        canvas.drawRect(rectCurItem,mPaint);
+    }
+
+    @Override
+    protected void drawForeground(Canvas canvas) {
+        if (null != mWheelDecor) {
+            canvas.save();
+            canvas.clipRect(rectCurDecor);
+            mWheelDecor.drawDecor(canvas, rectLast, rectNext, mPaint);
+            canvas.restore();
+        }
+    }
+
+    @Override
+    protected void onTouchDown(MotionEvent event) {
+
+    }
+
+    @Override
+    protected void onTouchMove(MotionEvent event) {
+        onWheelScrollStateChanged(SCROLL_STATE_DRAGGING);
+        onWheelScrolling(disTotalMoveX + diSingleMoveX, disTotalMoveY + diSingleMoveY);
+        invalidate();
+    }
+
+    @Override
+    protected void onTouchUp(MotionEvent event) {
+        mOrientation.fling(mScroller, mTracker, unitDeltaTotal, unitDeltaMin, unitDeltaMax,
+                unitDisplayMax);
+        onWheelScrollStateChanged(SCROLL_STATE_SCROLLING);
+        mHandler.post(this);
+    }
+
+    @Override
+    public void run() {
+        if (mScroller.isFinished()) {
+            onWheelScrollStateChanged(SCROLL_STATE_IDLE);
+            correctLocation();
+            confirmData();
+        }
+        if (mScroller.computeScrollOffset()) {
+            disTotalMoveX = mScroller.getCurrX();
+            disTotalMoveY = mScroller.getCurrY();
+            unitDeltaTotal = mOrientation.getUnitDeltaTotal(mScroller);
+            onWheelScrolling(disTotalMoveX, disTotalMoveY);
+            postInvalidate();
+            mHandler.postDelayed(this, TIME_REFRESH);
+        }
+    }
+
+    private void confirmData() {
+        if (state != SCROLL_STATE_IDLE) {
+            return;
+        }
+        int curIndex = itemIndex - unitDeltaTotal / unit;
+        curIndex = Math.max(0, curIndex);
+        curIndex = Math.min(data.size() - 1, curIndex);
+        String curData = data.get(curIndex);
+        if (!this.curData.equals(curData)) {
+            this.curData = curData;
+            onWheelSelected(curIndex, curData);
+        }
+    }
+
+    private void correctLocation() {
+        int remainder = Math.abs(unitDeltaTotal % unit);
+        if (remainder != 0) {
+            if (remainder >= unit / 2.0F) {
+                correctScroll(remainder - unit, unit - remainder);
+            } else {
+                correctScroll(remainder, -remainder);
+            }
+            postInvalidate();
+            mHandler.postDelayed(this, TIME_REFRESH);
+        }
+    }
+
+    private void correctScroll(int endBack, int endForward) {
+        if (unitDeltaTotal < 0) {
+            mOrientation.startScroll(mScroller, unitDeltaTotal, endBack);
+        } else {
+            mOrientation.startScroll(mScroller, unitDeltaTotal, endForward);
+        }
+        onWheelScrollStateChanged(SCROLL_STATE_SCROLLING);
+    }
+
+    public void checkScrollState() {
+        if (unitDeltaTotal > unitDeltaMax) {
+            mOrientation.startScroll(mScroller, unitDeltaTotal, unitDeltaMax - unitDeltaTotal);
+        }
+        if (unitDeltaTotal < unitDeltaMin) {
+            mOrientation.startScroll(mScroller, unitDeltaTotal, unitDeltaMin - unitDeltaTotal);
+        }
+        mHandler.post(this);
+    }
+
+    @Override
+    public void setCurrentTextColor(int color) {
+        super.setCurrentTextColor(color);
+        invalidate(rectCurItem);
+    }
+
+    @Override
+    public void setWheelDecor(boolean ignorePadding, AbstractWheelDecor decor) {
+        super.setWheelDecor(ignorePadding, decor);
+        invalidate(rectCurItem);
+    }
+
+    @Override
+    public void setTextSize(int size) {
+        super.setTextSize(size);
+        clearCache();
+    }
+
+    @Override
+    public void setItemSpace(int space) {
+        super.setItemSpace(space);
+        clearCache();
+    }
+
+    @Override
+    public void setItemCount(int count) {
+        super.setItemCount(count);
+        clearCache();
+    }
+
+    @Override
+    public void setData(List<String> data) {
+        super.setData(data);
+        clearCache();
+    }
+
+    @Override
+    public void clearCache() {
+
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelCurvedPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelCurvedPicker.java
new file mode 100644
index 0000000..ac2c4bc
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelCurvedPicker.java
@@ -0,0 +1,147 @@
+package com.aigestudio.wheelpicker.view;
+
+import android.content.Context;
+import android.graphics.Camera;
+import android.graphics.Canvas;
+import android.graphics.Matrix;
+import android.graphics.Region;
+import android.util.AttributeSet;
+import android.view.MotionEvent;
+
+import java.util.HashMap;
+
+public class WheelCurvedPicker extends WheelCrossPicker {
+    private final HashMap<Integer, Integer> SPACE = new HashMap<>();
+    private final HashMap<Integer, Integer> DEPTH = new HashMap<>();
+
+    private final Camera camera = new Camera();
+    private final Matrix matrixRotate = new Matrix(), matrixDepth = new Matrix();
+
+    private int radius;
+    private int degreeSingleDelta;
+    private int degreeIndex, degreeUnitDelta;
+
+    public WheelCurvedPicker(Context context) {
+        super(context);
+    }
+
+    public WheelCurvedPicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+    }
+
+    @Override
+    protected void computeWheelSizes() {
+        super.computeWheelSizes();
+
+        radius = mOrientation.computeRadius(itemCount, itemSpace, maxTextWidth, maxTextHeight);
+
+        unit = (int) (180 * 1.0F / (itemCount + 1));
+
+        wheelContentWidth = mOrientation.getCurvedWidth(radius, maxTextWidth, maxTextHeight);
+        wheelContentHeight = mOrientation.getCurvedHeight(radius, maxTextWidth, maxTextHeight);
+
+        unitDisplayMin = -90;
+        unitDisplayMax = 90;
+
+        unitDeltaMin = -unit * (data.size() - itemIndex - 1);
+        unitDeltaMax = unit * itemIndex;
+    }
+
+    @Override
+    protected void drawItems(Canvas canvas) {
+        for (int i = -itemIndex; i < data.size() - itemIndex; i++) {
+            int curUnit = unit * i;
+            curUnit += (unitDeltaTotal + degreeSingleDelta);
+            if (curUnit > unitDisplayMax || curUnit < unitDisplayMin) continue;
+
+            int space = computeSpace(curUnit);
+            if (space == 0) curUnit = 1;
+            int depth = computeDepth(curUnit);
+
+            camera.save();
+            mOrientation.rotateCamera(camera, curUnit);
+            camera.getMatrix(matrixRotate);
+            camera.restore();
+            mOrientation.matrixToCenter(matrixRotate, space, wheelCenterX, wheelCenterY);
+            camera.save();
+            camera.translate(0, 0, depth);
+            camera.getMatrix(matrixDepth);
+            camera.restore();
+            mOrientation.matrixToCenter(matrixDepth, space, wheelCenterX, wheelCenterY);
+            matrixRotate.postConcat(matrixDepth);
+
+            canvas.save();
+            canvas.concat(matrixRotate);
+            canvas.clipRect(rectCurItem, Region.Op.DIFFERENCE);
+            mTextPaint.setColor(textColor);
+            mTextPaint.setAlpha(255 - 255 * Math.abs(curUnit) / unitDisplayMax);
+            mOrientation.draw(canvas, mTextPaint, data.get(i + itemIndex), space, wheelCenterX,
+                    wheelCenterTextY);
+            canvas.restore();
+
+            canvas.save();
+            canvas.clipRect(rectCurItem);
+            mTextPaint.setColor(curTextColor);
+            mOrientation.draw(canvas, mTextPaint, data.get(i + itemIndex), space, wheelCenterX,
+                    wheelCenterTextY);
+            canvas.restore();
+        }
+    }
+
+    private int computeSpace(int degree) {
+        int space;
+        if (SPACE.containsKey(degree)) {
+            space = SPACE.get(degree);
+        } else {
+            space = (int) (Math.sin(Math.toRadians(degree)) * radius);
+            SPACE.put(degree, space);
+        }
+        return space;
+    }
+
+    private int computeDepth(int degree) {
+        int depth;
+        if (DEPTH.containsKey(degree)) {
+            depth = DEPTH.get(degree);
+        } else {
+            depth = (int) (radius - Math.cos(Math.toRadians(degree)) * radius);
+            DEPTH.put(degree, depth);
+        }
+        return depth;
+    }
+
+    @Override
+    protected void onTouchMove(MotionEvent event) {
+        degreeUnitDelta = mOrientation.computeDegreeSingleDelta(diSingleMoveX, diSingleMoveY, radius);
+        int curDis = mOrientation.obtainCurrentDis(diSingleMoveX, diSingleMoveY);
+        if (Math.abs(curDis) >= radius) {
+            if (curDis >= 0)
+                degreeIndex++;
+            else
+                degreeIndex--;
+            diSingleMoveX = 0;
+            diSingleMoveY = 0;
+            degreeUnitDelta = 0;
+        }
+        degreeSingleDelta = (degreeIndex * 80) + degreeUnitDelta;
+//        Log.d("AigeStudio", degreeSingleDelta + ":" + degreeUnitDelta + ":" + degreeIndex + ":" + diSingleMoveY + ":" + radius);
+//        degreeSingleDelta = mOrientation.computeDegreeSingleDelta(diSingleMoveX, diSingleMoveY, radius);
+        super.onTouchMove(event);
+    }
+
+    @Override
+    protected void onTouchUp(MotionEvent event) {
+        unitDeltaTotal += degreeSingleDelta;
+        degreeSingleDelta = 0;
+        degreeUnitDelta = 0;
+        degreeIndex = 0;
+        super.onTouchUp(event);
+    }
+
+    @Override
+    public void clearCache() {
+        SPACE.clear();
+        DEPTH.clear();
+        mOrientation.clearCache();
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelStraightPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelStraightPicker.java
new file mode 100644
index 0000000..388c2a5
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/view/WheelStraightPicker.java
@@ -0,0 +1,76 @@
+package com.aigestudio.wheelpicker.view;
+
+import android.content.Context;
+import android.graphics.Canvas;
+import android.graphics.Region;
+import android.util.AttributeSet;
+import android.view.MotionEvent;
+
+/**
+ * <AUTHOR> 2015-12-12
+ */
+public class WheelStraightPicker extends WheelCrossPicker {
+    public WheelStraightPicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+    }
+
+    public WheelStraightPicker(Context context) {
+        super(context);
+    }
+
+    @Override
+    public void computeWheelSizes() {
+        super.computeWheelSizes();
+
+        wheelContentWidth = mOrientation
+                .getStraightWidth(itemCount, itemSpace, maxTextWidth, maxTextHeight);
+        wheelContentHeight = mOrientation
+                .getStraightHeight(itemCount, itemSpace, maxTextWidth, maxTextHeight);
+
+        unit = mOrientation.getStraightUnit(itemSpace, maxTextWidth, maxTextHeight);
+
+        int disDisplay = mOrientation.getDisplay(itemCount, itemSpace, maxTextWidth, maxTextHeight);
+
+        unitDisplayMin = -disDisplay;
+        unitDisplayMax = disDisplay;
+
+        unitDeltaMin = -unit * (data.size() - itemIndex - 1);
+        unitDeltaMax = unit * itemIndex;
+    }
+
+    @Override
+    protected void drawItems(Canvas canvas) {
+        for (int i = -itemIndex; i < data.size() - itemIndex; i++) {
+            int curDis = mOrientation.computeStraightUnit(unit, i, disTotalMoveX, disTotalMoveY,
+                    diSingleMoveX, diSingleMoveY);
+            if (curDis > unitDisplayMax || curDis < unitDisplayMin) {
+                continue;
+            }
+            canvas.save();
+            canvas.clipRect(rectCurItem, Region.Op.DIFFERENCE);
+            mTextPaint.setColor(textColor);
+            mTextPaint.setAlpha(255 - 255 * Math.abs(curDis) / unitDisplayMax);
+            mOrientation.draw(canvas, mTextPaint, data.get(i + itemIndex), curDis, wheelCenterX,
+                    wheelCenterTextY);
+            canvas.restore();
+
+            canvas.save();
+            canvas.clipRect(rectCurItem);
+            mTextPaint.setColor(curTextColor);
+            mOrientation.draw(canvas, mTextPaint, data.get(i + itemIndex), curDis, wheelCenterX,
+                    wheelCenterTextY);
+            canvas.restore();
+        }
+    }
+
+    @Override
+    protected void onTouchMove(MotionEvent event) {
+        super.onTouchMove(event);
+    }
+
+    @Override
+    protected void onTouchUp(MotionEvent event) {
+        unitDeltaTotal = mOrientation.getUnitDeltaTotal(disTotalMoveX, disTotalMoveY);
+        super.onTouchUp(event);
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/IDigital.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/IDigital.java
new file mode 100644
index 0000000..69c9b57
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/IDigital.java
@@ -0,0 +1,8 @@
+package com.aigestudio.wheelpicker.widget;
+
+/**
+ * <AUTHOR> 2015-12-20
+ */
+public interface IDigital {
+    void setDigitType(int type);
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker.java
new file mode 100644
index 0000000..cb30db0
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelDatePicker.java
@@ -0,0 +1,231 @@
+package com.aigestudio.wheelpicker.widget.curved;
+
+import android.content.Context;
+import android.graphics.Canvas;
+import android.graphics.Color;
+import android.graphics.Paint;
+import android.graphics.Rect;
+import android.text.TextUtils;
+import android.util.AttributeSet;
+import android.view.Gravity;
+import android.widget.LinearLayout;
+
+import com.zyu.R;
+import com.aigestudio.wheelpicker.core.AbstractWheelDecor;
+import com.aigestudio.wheelpicker.core.AbstractWheelPicker;
+import com.aigestudio.wheelpicker.core.IWheelPicker;
+import com.aigestudio.wheelpicker.view.WheelCrossPicker;
+
+import java.util.List;
+
+/**
+ * 基于WheelPicker的日期选择控件
+ * DatePicker base on WheelPicker
+ *
+ * <AUTHOR> 2015-12-03
+ * <AUTHOR> 2015-12-08
+ *         Init初始化
+ * <AUTHOR> 2015-12-12
+ *         实现变更
+ * @version 1.0.0 beta
+ */
+public class WheelDatePicker extends LinearLayout implements IWheelPicker {
+    protected WheelYearPicker pickerYear;
+    protected WheelMonthPicker pickerMonth;
+    protected WheelDayPicker pickerDay;
+
+    protected AbstractWheelPicker.OnWheelChangeListener listener;
+
+    protected String year, month, day;
+    protected int labelColor = 0xFF000000;
+    protected int stateYear, stateMonth, stateDay;
+
+    protected float labelTextSize;
+
+    public WheelDatePicker(Context context) {
+        super(context);
+        init();
+    }
+
+    public WheelDatePicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+        init();
+    }
+
+    private void init() {
+        setGravity(Gravity.CENTER);
+        setOrientation(HORIZONTAL);
+
+        int padding = getResources().getDimensionPixelSize(R.dimen.WheelPadding);
+        int padding2x = padding * 2;
+
+        labelTextSize = padding;
+
+        LayoutParams llParams = new LayoutParams(-2, -2);
+
+        pickerYear = new WheelYearPicker(getContext());
+        pickerMonth = new WheelMonthPicker(getContext());
+        pickerDay = new WheelDayPicker(getContext());
+        pickerYear.setPadding(0, padding, padding2x, padding);
+        pickerMonth.setPadding(0, padding, padding2x, padding);
+        pickerDay.setPadding(0, padding, padding2x, padding);
+        addLabel(pickerYear, "年");
+        addLabel(pickerMonth, "月");
+        addLabel(pickerDay, "日");
+
+        addView(pickerYear, llParams);
+        addView(pickerMonth, llParams);
+        addView(pickerDay, llParams);
+
+        initListener(pickerYear, 0);
+        initListener(pickerMonth, 1);
+        initListener(pickerDay, 2);
+    }
+
+    private void addLabel(WheelCrossPicker picker, final String label) {
+        picker.setWheelDecor(true, new AbstractWheelDecor() {
+            @Override
+            public void drawDecor(Canvas canvas, Rect rectLast, Rect rectNext, Paint paint) {
+                paint.setColor(labelColor);
+                paint.setTextAlign(Paint.Align.CENTER);
+                paint.setTextSize(labelTextSize * 1.5F);
+                canvas.drawText(label, rectNext.centerX(),
+                        rectNext.centerY() - (paint.ascent() + paint.descent()) / 2.0F, paint);
+            }
+        });
+    }
+
+    private void initListener(final WheelCrossPicker picker, final int type) {
+        picker.setOnWheelChangeListener(new AbstractWheelPicker.OnWheelChangeListener() {
+            @Override
+            public void onWheelScrolling(float deltaX, float deltaY) {
+                if (null != listener) listener.onWheelScrolling(deltaX, deltaY);
+            }
+
+            @Override
+            public void onWheelSelected(int index, String data) {
+                if (type == 0) year = data;
+                if (type == 1) month = data;
+                if (type == 2) day = data;
+                if (isValidDate()) {
+                    if (type == 0 || type == 1)
+                        pickerDay.setCurrentYearAndMonth(Integer.valueOf(year),
+                                Integer.valueOf(month));
+                    if (null != listener)
+                        listener.onWheelSelected(-1, year + "-" + month + "-" + day);
+                }
+            }
+
+            @Override
+            public void onWheelScrollStateChanged(int state) {
+                if (type == 0) stateYear = state;
+                if (type == 1) stateMonth = state;
+                if (type == 2) stateDay = state;
+                if (null != listener) checkState(listener);
+            }
+        });
+    }
+
+    public void setLabelColor(int labelColor) {
+        this.labelColor = labelColor;
+        invalidate();
+    }
+
+    public void setLabelTextSize(float labelTextSize) {
+        this.labelTextSize = labelTextSize;
+        invalidate();
+    }
+
+    @Override
+    public void setData(List<String> data) {
+        throw new RuntimeException("Set data will not allow here!");
+    }
+
+    public void setCurrentDate(int year, int month, int day) {
+        pickerYear.setCurrentYear(year);
+        pickerMonth.setCurrentMonth(month);
+        pickerDay.setCurrentYearAndMonth(year, month);
+        pickerDay.setCurrentDay(day);
+    }
+
+    @Override
+    public void setOnWheelChangeListener(AbstractWheelPicker.OnWheelChangeListener listener) {
+        this.listener = listener;
+    }
+
+    private void checkState(AbstractWheelPicker.OnWheelChangeListener listener) {
+        if (stateYear == AbstractWheelPicker.SCROLL_STATE_IDLE &&
+                stateMonth == AbstractWheelPicker.SCROLL_STATE_IDLE &&
+                stateDay == AbstractWheelPicker.SCROLL_STATE_IDLE) {
+            listener.onWheelScrollStateChanged(AbstractWheelPicker.SCROLL_STATE_IDLE);
+        }
+        if (stateYear == AbstractWheelPicker.SCROLL_STATE_SCROLLING ||
+                stateMonth == AbstractWheelPicker.SCROLL_STATE_SCROLLING ||
+                stateDay == AbstractWheelPicker.SCROLL_STATE_SCROLLING) {
+            listener.onWheelScrollStateChanged(AbstractWheelPicker.SCROLL_STATE_SCROLLING);
+        }
+        if (stateYear + stateMonth + stateDay == 1) {
+            listener.onWheelScrollStateChanged(AbstractWheelPicker.SCROLL_STATE_DRAGGING);
+        }
+    }
+
+    private boolean isValidDate() {
+        return !TextUtils.isEmpty(year) && !TextUtils.isEmpty(month) && !TextUtils.isEmpty(day);
+    }
+
+    @Override
+    public void setItemIndex(int index) {
+        pickerYear.setItemIndex(index);
+        pickerMonth.setItemIndex(index);
+        pickerDay.setItemIndex(index);
+    }
+
+    @Override
+    public void setItemSpace(int space) {
+        pickerYear.setItemSpace(space);
+        pickerMonth.setItemSpace(space);
+        pickerDay.setItemSpace(space);
+    }
+
+    @Override
+    public void setItemCount(int count) {
+        pickerYear.setItemCount(count);
+        pickerMonth.setItemCount(count);
+        pickerDay.setItemCount(count);
+    }
+
+    @Override
+    public void setTextColor(int color) {
+        pickerYear.setTextColor(color);
+        pickerMonth.setTextColor(color);
+        pickerDay.setTextColor(color);
+    }
+
+    @Override
+    public void setTextSize(int size) {
+        pickerYear.setTextSize(size);
+        pickerMonth.setTextSize(size);
+        pickerDay.setTextSize(size);
+    }
+
+    @Override
+    public void clearCache() {
+        pickerYear.clearCache();
+        pickerMonth.clearCache();
+        pickerDay.clearCache();
+    }
+
+    @Override
+    public void setCurrentTextColor(int color) {
+        pickerYear.setCurrentTextColor(color);
+        pickerMonth.setCurrentTextColor(color);
+        pickerDay.setCurrentTextColor(color);
+    }
+
+    @Override
+    public void setWheelDecor(boolean ignorePadding, AbstractWheelDecor decor) {
+        pickerYear.setWheelDecor(ignorePadding, decor);
+        pickerMonth.setWheelDecor(ignorePadding, decor);
+        pickerDay.setWheelDecor(ignorePadding, decor);
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelDayPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelDayPicker.java
new file mode 100644
index 0000000..8ea583c
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelDayPicker.java
@@ -0,0 +1,110 @@
+package com.aigestudio.wheelpicker.widget.curved;
+
+import android.content.Context;
+import android.util.AttributeSet;
+
+import com.aigestudio.wheelpicker.view.WheelCurvedPicker;
+
+import java.util.ArrayList;
+import java.util.Calendar;
+import java.util.HashMap;
+import java.util.List;
+
+/**
+ * 基于WheelPicker的月份选择控件
+ * MonthPicker base on WheelPicker
+ *
+ * <AUTHOR> 2015-12-03
+ * <AUTHOR> 2015-12-08
+ * <AUTHOR> 2015-12-12
+ * @version 1.0.0 beta
+ */
+public class WheelDayPicker extends WheelCurvedPicker {
+    private static final HashMap<Integer, List<String>> DAYS = new HashMap<>();
+
+    private static final Calendar C = Calendar.getInstance();
+
+    private List<String> days = new ArrayList<>();
+
+    private int day = C.get(Calendar.DAY_OF_MONTH), month = C.get(Calendar.MONTH) + 1,
+            year = C.get(Calendar.YEAR);
+    private int maxDay;
+
+    public WheelDayPicker(Context context) {
+        super(context);
+        init();
+    }
+
+    public WheelDayPicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+        init();
+    }
+
+    private void init() {
+        initData();
+        setCurrentDay();
+    }
+
+    private void initData() {
+        int maxDay = C.getActualMaximum(Calendar.DATE);
+        if (maxDay == this.maxDay) return;
+        this.maxDay = maxDay;
+        List<String> days;
+        if (DAYS.containsKey(maxDay)) {
+            days = DAYS.get(maxDay);
+        } else {
+            days = new ArrayList<>();
+            for (int i = 1; i <= maxDay; i++) days.add(String.valueOf(i));
+            DAYS.put(maxDay, days);
+        }
+        this.days = days;
+        super.setData(days);
+    }
+
+    @Override
+    public void setData(List<String> data) {
+        throw new RuntimeException("Set data will not allow here!");
+    }
+
+    public void setCurrentDay(int day) {
+        day = Math.max(day, 1);
+        day = Math.min(day, maxDay);
+        this.day = day;
+        setCurrentDay();
+    }
+
+    private void setCurrentDay() {
+        setItemIndex(day - 1);
+    }
+
+    public void setCurrentMonth(int month) {
+        setMonth(month);
+        initData();
+    }
+
+    private void setMonth(int month) {
+        month = Math.max(month, 1);
+        month = Math.min(month, 12);
+        this.month = month;
+        C.set(Calendar.MONTH, month - 1);
+    }
+
+    public void setCurrentYear(int year) {
+        setYear(year);
+        initData();
+    }
+
+    private void setYear(int year) {
+        year = Math.max(year, 1);
+        year = Math.min(year, Integer.MAX_VALUE - 1);
+        this.year = year;
+        C.set(Calendar.YEAR, year);
+    }
+
+    public void setCurrentYearAndMonth(int year, int month) {
+        setYear(year);
+        setMonth(month);
+        initData();
+        checkScrollState();
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelHourPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelHourPicker.java
new file mode 100644
index 0000000..eb0b972
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelHourPicker.java
@@ -0,0 +1,68 @@
+package com.aigestudio.wheelpicker.widget.curved;
+
+import android.content.Context;
+import android.util.AttributeSet;
+
+import com.aigestudio.wheelpicker.view.WheelCurvedPicker;
+import com.aigestudio.wheelpicker.widget.IDigital;
+
+import java.util.ArrayList;
+import java.util.Calendar;
+import java.util.List;
+
+public class WheelHourPicker extends WheelCurvedPicker implements IDigital {
+    private static final List<String> HOURS_DIGITAL_SINGLE = new ArrayList<>();
+    private static final List<String> HOURS_DIGITAL_DOUBLE = new ArrayList<>();
+
+    static {
+        for (int i = 0; i < 24; i++) HOURS_DIGITAL_SINGLE.add(String.valueOf(i));
+        for (int i = 0; i < 24; i++) {
+            String num = String.valueOf(i);
+            if (num.length() == 1) {
+                num = "0" + num;
+            }
+            HOURS_DIGITAL_DOUBLE.add(num);
+        }
+    }
+
+    private List<String> hours = HOURS_DIGITAL_SINGLE;
+
+    private int hour;
+
+    public WheelHourPicker(Context context) {
+        super(context);
+        init();
+    }
+
+    public WheelHourPicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+        init();
+    }
+
+    private void init() {
+        super.setData(hours);
+        setCurrentHour(Calendar.getInstance().get(Calendar.HOUR_OF_DAY));
+    }
+
+    @Override
+    public void setData(List<String> data) {
+        throw new RuntimeException("Set data will not allow here!");
+    }
+
+    public void setCurrentHour(int hour) {
+        hour = Math.max(hour, 0);
+        hour = Math.min(hour, 23);
+        this.hour = hour;
+        setItemIndex(hour);
+    }
+
+    @Override
+    public void setDigitType(int type) {
+        if (type == 1) {
+            hours = HOURS_DIGITAL_SINGLE;
+        } else {
+            hours = HOURS_DIGITAL_DOUBLE;
+        }
+        super.setData(hours);
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelMinutePicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelMinutePicker.java
new file mode 100644
index 0000000..2e6a991
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelMinutePicker.java
@@ -0,0 +1,68 @@
+package com.aigestudio.wheelpicker.widget.curved;
+
+import android.content.Context;
+import android.util.AttributeSet;
+
+import com.aigestudio.wheelpicker.view.WheelCurvedPicker;
+import com.aigestudio.wheelpicker.widget.IDigital;
+
+import java.util.ArrayList;
+import java.util.Calendar;
+import java.util.List;
+
+public class WheelMinutePicker extends WheelCurvedPicker implements IDigital {
+    private static final List<String> MINUTES_DIGITAL_SINGLE = new ArrayList<>();
+    private static final List<String> MINUTES_DIGITAL_DOUBLE = new ArrayList<>();
+
+    static {
+        for (int i = 0; i < 60; i++) MINUTES_DIGITAL_SINGLE.add(String.valueOf(i));
+        for (int i = 0; i < 60; i++) {
+            String num = String.valueOf(i);
+            if (num.length() == 1) {
+                num = "0" + num;
+            }
+            MINUTES_DIGITAL_DOUBLE.add(num);
+        }
+    }
+
+    private List<String> minutes = MINUTES_DIGITAL_SINGLE;
+
+    private int minute;
+
+    public WheelMinutePicker(Context context) {
+        super(context);
+        init();
+    }
+
+    public WheelMinutePicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+        init();
+    }
+
+    private void init() {
+        super.setData(minutes);
+        setCurrentMinute(Calendar.getInstance().get(Calendar.MINUTE));
+    }
+
+    @Override
+    public void setData(List<String> data) {
+        throw new RuntimeException("Set data will not allow here!");
+    }
+
+    public void setCurrentMinute(int minute) {
+        minute = Math.max(minute, 0);
+        minute = Math.min(minute, 59);
+        this.minute = minute;
+        setItemIndex(minute);
+    }
+
+    @Override
+    public void setDigitType(int type) {
+        if (type == 1) {
+            minutes = MINUTES_DIGITAL_SINGLE;
+        } else {
+            minutes = MINUTES_DIGITAL_DOUBLE;
+        }
+        super.setData(minutes);
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelMonthPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelMonthPicker.java
new file mode 100644
index 0000000..cae1112
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelMonthPicker.java
@@ -0,0 +1,58 @@
+package com.aigestudio.wheelpicker.widget.curved;
+
+import android.content.Context;
+import android.util.AttributeSet;
+
+import com.aigestudio.wheelpicker.view.WheelCurvedPicker;
+
+import java.util.ArrayList;
+import java.util.Calendar;
+import java.util.List;
+
+/**
+ * 基于WheelPicker的月份选择控件
+ * MonthPicker base on WheelPicker
+ *
+ * <AUTHOR> 2015-12-03
+ * <AUTHOR> 2015-12-08
+ * <AUTHOR> 2015-12-12
+ * @version 1.0.0 beta
+ */
+public class WheelMonthPicker extends WheelCurvedPicker {
+    private static final List<String> MONTHS = new ArrayList<>();
+
+    static {
+        for (int i = 1; i <= 12; i++) MONTHS.add(String.valueOf(i));
+    }
+
+    private List<String> months = MONTHS;
+
+    private int month;
+
+    public WheelMonthPicker(Context context) {
+        super(context);
+        init();
+    }
+
+    public WheelMonthPicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+        init();
+    }
+
+    private void init() {
+        super.setData(months);
+        setCurrentMonth(Calendar.getInstance().get(Calendar.MONTH) + 1);
+    }
+
+    @Override
+    public void setData(List<String> data) {
+        throw new RuntimeException("Set data will not allow here!");
+    }
+
+    public void setCurrentMonth(int month) {
+        month = Math.max(month, 1);
+        month = Math.min(month, 12);
+        this.month = month;
+        setItemIndex(month - 1);
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker.java
new file mode 100644
index 0000000..d2935bf
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelTimePicker.java
@@ -0,0 +1,210 @@
+package com.aigestudio.wheelpicker.widget.curved;
+
+import android.content.Context;
+import android.graphics.Canvas;
+import android.graphics.Paint;
+import android.graphics.Rect;
+import android.text.TextUtils;
+import android.util.AttributeSet;
+import android.view.Gravity;
+import android.widget.LinearLayout;
+
+import com.zyu.R;
+import com.aigestudio.wheelpicker.core.AbstractWheelDecor;
+import com.aigestudio.wheelpicker.core.AbstractWheelPicker;
+import com.aigestudio.wheelpicker.core.IWheelPicker;
+import com.aigestudio.wheelpicker.view.WheelCrossPicker;
+import com.aigestudio.wheelpicker.widget.IDigital;
+
+import java.util.List;
+
+/**
+ * 基于WheelPicker的时间选择控件
+ * TimePicker base on WheelPicker
+ *
+ * <AUTHOR> 2015-12-12
+ * @version 1.0.0 beta
+ */
+public class WheelTimePicker extends LinearLayout implements IWheelPicker, IDigital {
+    protected WheelHourPicker pickerHour;
+    protected WheelMinutePicker pickerMinute;
+
+    protected AbstractWheelPicker.OnWheelChangeListener listener;
+
+    protected String hour, minute;
+    protected int labelColor = 0xFF000000;
+    protected int stateHour, stateMinute;
+
+    protected float labelTextSize;
+
+    public WheelTimePicker(Context context) {
+        super(context);
+        init();
+    }
+
+    public WheelTimePicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+        init();
+    }
+
+    private void init() {
+        setGravity(Gravity.CENTER);
+        setOrientation(HORIZONTAL);
+
+        int padding = getResources().getDimensionPixelSize(R.dimen.WheelPadding);
+        int padding2x = padding * 2;
+
+        labelTextSize = padding;
+
+        LayoutParams llParams = new LayoutParams(-2, -2);
+
+        pickerHour = new WheelHourPicker(getContext());
+        pickerMinute = new WheelMinutePicker(getContext());
+        pickerHour.setPadding(0, padding, padding2x, padding);
+        pickerMinute.setPadding(0, padding, padding2x, padding);
+        addLabel(pickerHour, "时");
+        addLabel(pickerMinute, "分");
+
+        addView(pickerHour, llParams);
+        addView(pickerMinute, llParams);
+
+        initListener(pickerHour, 0);
+        initListener(pickerMinute, 1);
+    }
+
+    private void addLabel(WheelCrossPicker picker, final String label) {
+        picker.setWheelDecor(true, new AbstractWheelDecor() {
+            @Override
+            public void drawDecor(Canvas canvas, Rect rectLast, Rect rectNext, Paint paint) {
+                paint.setColor(labelColor);
+                paint.setTextAlign(Paint.Align.CENTER);
+                paint.setTextSize(labelTextSize * 1.5F);
+                canvas.drawText(label, rectNext.centerX(),
+                        rectNext.centerY() - (paint.ascent() + paint.descent()) / 2.0F, paint);
+            }
+        });
+    }
+
+    private void initListener(final WheelCrossPicker picker, final int type) {
+        picker.setOnWheelChangeListener(new AbstractWheelPicker.OnWheelChangeListener() {
+            @Override
+            public void onWheelScrolling(float deltaX, float deltaY) {
+                if (null != listener) listener.onWheelScrolling(deltaX, deltaY);
+            }
+
+            @Override
+            public void onWheelSelected(int index, String data) {
+                if (type == 0) hour = data;
+                if (type == 1) minute = data;
+                if (isValidDate()) {
+                    if (null != listener)
+                        listener.onWheelSelected(-1, hour + ":" + minute);
+                }
+            }
+
+            @Override
+            public void onWheelScrollStateChanged(int state) {
+                if (type == 0) stateHour = state;
+                if (type == 1) stateMinute = state;
+                if (null != listener) checkState(listener);
+            }
+        });
+    }
+
+    public void setLabelColor(int labelColor) {
+        this.labelColor = labelColor;
+        invalidate();
+    }
+
+    public void setLabelTextSize(float labelTextSize) {
+        this.labelTextSize = labelTextSize;
+        invalidate();
+    }
+
+    @Override
+    public void setData(List<String> data) {
+        throw new RuntimeException("Set data will not allow here!");
+    }
+
+    public void setCurrentTime(int hour, int minute) {
+        pickerHour.setCurrentHour(hour);
+        pickerMinute.setCurrentMinute(minute);
+    }
+
+    @Override
+    public void setOnWheelChangeListener(AbstractWheelPicker.OnWheelChangeListener listener) {
+        this.listener = listener;
+    }
+
+    private void checkState(AbstractWheelPicker.OnWheelChangeListener listener) {
+        if (stateHour == AbstractWheelPicker.SCROLL_STATE_IDLE &&
+                stateMinute == AbstractWheelPicker.SCROLL_STATE_IDLE) {
+            listener.onWheelScrollStateChanged(AbstractWheelPicker.SCROLL_STATE_IDLE);
+        }
+        if (stateHour == AbstractWheelPicker.SCROLL_STATE_SCROLLING ||
+                stateMinute == AbstractWheelPicker.SCROLL_STATE_SCROLLING) {
+            listener.onWheelScrollStateChanged(AbstractWheelPicker.SCROLL_STATE_SCROLLING);
+        }
+        if (stateHour + stateMinute == 1) {
+            listener.onWheelScrollStateChanged(AbstractWheelPicker.SCROLL_STATE_DRAGGING);
+        }
+    }
+
+    private boolean isValidDate() {
+        return !TextUtils.isEmpty(hour) && !TextUtils.isEmpty(minute);
+    }
+
+    @Override
+    public void setItemIndex(int index) {
+        pickerHour.setItemIndex(index);
+        pickerMinute.setItemIndex(index);
+    }
+
+    @Override
+    public void setItemSpace(int space) {
+        pickerHour.setItemSpace(space);
+        pickerMinute.setItemSpace(space);
+    }
+
+    @Override
+    public void setItemCount(int count) {
+        pickerHour.setItemCount(count);
+        pickerMinute.setItemCount(count);
+    }
+
+    @Override
+    public void setTextColor(int color) {
+        pickerHour.setTextColor(color);
+        pickerMinute.setTextColor(color);
+    }
+
+    @Override
+    public void setTextSize(int size) {
+        pickerHour.setTextSize(size);
+        pickerMinute.setTextSize(size);
+    }
+
+    @Override
+    public void clearCache() {
+        pickerHour.clearCache();
+        pickerMinute.clearCache();
+    }
+
+    @Override
+    public void setCurrentTextColor(int color) {
+        pickerHour.setCurrentTextColor(color);
+        pickerMinute.setCurrentTextColor(color);
+    }
+
+    @Override
+    public void setWheelDecor(boolean ignorePadding, AbstractWheelDecor decor) {
+        pickerHour.setWheelDecor(ignorePadding, decor);
+        pickerMinute.setWheelDecor(ignorePadding, decor);
+    }
+
+    @Override
+    public void setDigitType(int type) {
+        pickerHour.setDigitType(type);
+        pickerMinute.setDigitType(type);
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelYearPicker.java b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelYearPicker.java
new file mode 100644
index 0000000..dee7f7f
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/java/com/aigestudio/wheelpicker/widget/curved/WheelYearPicker.java
@@ -0,0 +1,69 @@
+package com.aigestudio.wheelpicker.widget.curved;
+
+import android.content.Context;
+import android.util.AttributeSet;
+
+import com.aigestudio.wheelpicker.view.WheelCurvedPicker;
+
+import java.util.ArrayList;
+import java.util.Calendar;
+import java.util.List;
+
+/**
+ * 基于WheelPicker的年份选择控件
+ * YearPicker base on WheelPicker
+ *
+ * <AUTHOR> 2015-12-03
+ * <AUTHOR> 2015-12-08
+ * <AUTHOR> 2015-12-12
+ * @version 1.0.0 beta
+ */
+public class WheelYearPicker extends WheelCurvedPicker {
+    private static final List<String> YEARS = new ArrayList<>();
+    private static final int FROM = 1900, TO = 2100;
+
+    static {
+        for (int i = 1900; i <= 2100; i++) YEARS.add(String.valueOf(i));
+    }
+
+    private List<String> years = YEARS;
+
+    private int from = FROM, to = TO;
+    private int year;
+
+    public WheelYearPicker(Context context) {
+        super(context);
+        init();
+    }
+
+    public WheelYearPicker(Context context, AttributeSet attrs) {
+        super(context, attrs);
+        init();
+    }
+
+    private void init() {
+        super.setData(years);
+        setCurrentYear(Calendar.getInstance().get(Calendar.YEAR));
+    }
+
+    @Override
+    public void setData(List<String> data) {
+        throw new RuntimeException("Set data will not allow here!");
+    }
+
+    public void setYearRange(int yearFrom, int yearTo) {
+        from = yearFrom;
+        to = yearTo;
+        years.clear();
+        for (int i = yearFrom; i <= yearTo; i++) years.add(String.valueOf(i));
+        super.setData(years);
+    }
+
+    public void setCurrentYear(int year) {
+        year = Math.max(year, from);
+        year = Math.min(year, to);
+        this.year = year;
+        int d = year - from;
+        setItemIndex(d);
+    }
+}
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/drawable/bg_btn.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/drawable/bg_btn.xml
new file mode 100644
index 0000000..06fa126
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/drawable/bg_btn.xml
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="utf-8"?>
+<selector xmlns:android="http://schemas.android.com/apk/res/android">
+    <item android:drawable="@color/ColorButtonPressed" android:state_pressed="true" />
+    <item android:drawable="@color/ColorButtonDisable" android:state_enabled="false" />
+    <item android:drawable="@color/ColorButtonRelease" />
+</selector>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values-zh/arrays.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values-zh/arrays.xml
new file mode 100644
index 0000000..290e56a
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values-zh/arrays.xml
@@ -0,0 +1,31 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string-array name="WheelArrayConstellation">
+        <item>水瓶座</item>
+        <item>双鱼座</item>
+        <item>白羊座</item>
+        <item>金牛座</item>
+        <item>双子座</item>
+        <item>巨蟹座</item>
+        <item>狮子座</item>
+        <item>处女座</item>
+        <item>天秤座</item>
+        <item>天蝎座</item>
+        <item>射手座</item>
+        <item>摩羯座</item>
+    </string-array>
+    <string-array name="WheelArrayZodiac">
+        <item>鼠</item>
+        <item>牛</item>
+        <item>虎</item>
+        <item>兔</item>
+        <item>龙</item>
+        <item>蛇</item>
+        <item>马</item>
+        <item>羊</item>
+        <item>猴</item>
+        <item>鸡</item>
+        <item>狗</item>
+        <item>猪</item>
+    </string-array>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/arrays.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/arrays.xml
new file mode 100644
index 0000000..fba3efb
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/arrays.xml
@@ -0,0 +1,57 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string-array name="WheelArrayDefault">
+        <item>AigeStudio</item>
+        <item>Aige</item>
+        <item>爱哥</item>
+        <item>الحب  اخي</item>
+        <item>jeg elsker</item>
+        <item>사랑해요 형</item>
+        <item>Amor de irmão</item>
+        <item>armastan</item>
+        <item>愛の兄</item>
+        <item>обичам те</item>
+        <item>любовь - братa</item>
+        <item>miłość bracie</item>
+        <item>Liebe</item>
+        <item>Lamour</item>
+        <item>rakastan sinua</item>
+        <item>láska..</item>
+        <item>dragostea.</item>
+        <item>jag älskar</item>
+        <item>ljubezen, brat.</item>
+        <item>愛哥</item>
+        <item>ชอบพี่</item>
+        <item>αγάπη μου</item>
+        <item>a szerelem.</item>
+        <item>Amore, fratello.</item>
+    </string-array>
+    <string-array name="WheelArrayConstellation">
+        <item>Aquarius</item>
+        <item>Pisces</item>
+        <item>Aries</item>
+        <item>Taurus</item>
+        <item>Gemini</item>
+        <item>Cancer</item>
+        <item>Leo</item>
+        <item>Virgo</item>
+        <item>Libra</item>
+        <item>Scorpio</item>
+        <item>Sagittarius</item>
+        <item>Capricorn</item>
+    </string-array>
+    <string-array name="WheelArrayZodiac">
+        <item>Rat</item>
+        <item>Ox</item>
+        <item>Tiger</item>
+        <item>Hare</item>
+        <item>Dragon</item>
+        <item>Snake</item>
+        <item>Horse</item>
+        <item>Sheep</item>
+        <item>Monkey</item>
+        <item>Cock</item>
+        <item>Dog</item>
+        <item>Boar</item>
+    </string-array>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/attrs.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/attrs.xml
new file mode 100644
index 0000000..fa35625
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/attrs.xml
@@ -0,0 +1,21 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <declare-styleable name="AbstractWheelPicker">
+        <attr name="wheel_data" format="reference" />
+        <attr name="wheel_style" format="enum">
+            <enum name="straight" value="0" />
+            <enum name="curved" value="1" />
+        </attr>
+        <attr name="wheel_direction" format="enum">
+            <enum name="horizontal" value="0" />
+            <enum name="vertical" value="1" />
+        </attr>
+        <attr name="wheel_item_index" format="integer" />
+        <attr name="wheel_item_same_size" format="boolean" />
+        <attr name="wheel_item_count" format="integer" />
+        <attr name="wheel_item_space" format="dimension" />
+        <attr name="wheel_text_size" format="dimension" />
+        <attr name="wheel_text_color" format="color" />
+        <attr name="wheel_text_color_current" format="color" />
+    </declare-styleable>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/colors.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/colors.xml
new file mode 100644
index 0000000..470bb44
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/colors.xml
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <color name="ColorButtonRelease">#FCC689</color>
+    <color name="ColorButtonPressed">#FF7438</color>
+    <color name="ColorButtonDisable">#EEE</color>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/dimens.xml b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/dimens.xml
new file mode 100644
index 0000000..773d507
--- /dev/null
+++ b/node_modules/@gregfrench/react-native-wheel-picker/android/src/main/res/values/dimens.xml
@@ -0,0 +1,10 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <!--通用尺寸-->
+    <!--General sizes-->
+    <dimen name="WheelItemSpace">8dp</dimen>
+    <dimen name="WheelPadding">8dp</dimen>
+    <dimen name="WheelTextSize">20sp</dimen>
+
+    <dimen name="WheelLabelTextSize">12sp</dimen>
+</resources>
\ No newline at end of file
