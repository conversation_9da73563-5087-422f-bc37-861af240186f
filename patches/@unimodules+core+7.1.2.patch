diff --git a/node_modules/@unimodules/core/android/build.gradle b/node_modules/@unimodules/core/android/build.gradle
index e12c9bb..1ed2179 100644
--- a/node_modules/@unimodules/core/android/build.gradle
+++ b/node_modules/@unimodules/core/android/build.gradle
@@ -1,6 +1,6 @@
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
-apply plugin: 'maven'
+apply plugin: 'maven-publish'
 
 group = 'org.unimodules'
 version = '7.1.2'
@@ -36,15 +36,6 @@ artifacts {
   archives androidSourcesJar
 }
 
-uploadArchives {
-  repositories {
-    mavenDeployer {
-      configuration = configurations.deployerJars
-      repository(url: mavenLocal().url)
-    }
-  }
-}
-
 android {
   compileSdkVersion safeExtGet("compileSdkVersion", 30)
 
diff --git a/node_modules/@unimodules/core/android/build/.transforms/1db3918d2966aa5afa79f6a52ea1e6a6/results.bin b/node_modules/@unimodules/core/android/build/.transforms/1db3918d2966aa5afa79f6a52ea1e6a6/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/1db3918d2966aa5afa79f6a52ea1e6a6/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/@unimodules/core/android/build/.transforms/39ac41aea3baf5fff47b48a4e1ecab32/results.bin b/node_modules/@unimodules/core/android/build/.transforms/39ac41aea3baf5fff47b48a4e1ecab32/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/39ac41aea3baf5fff47b48a4e1ecab32/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@unimodules/core/android/build/.transforms/39ac41aea3baf5fff47b48a4e1ecab32/transformed/classes/classes.dex b/node_modules/@unimodules/core/android/build/.transforms/39ac41aea3baf5fff47b48a4e1ecab32/transformed/classes/classes.dex
new file mode 100644
index 0000000..c834b7a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/39ac41aea3baf5fff47b48a4e1ecab32/transformed/classes/classes.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/results.bin b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/results.bin
new file mode 100644
index 0000000..9e53e72
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/results.bin
@@ -0,0 +1 @@
+o/release
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/desugar_graph.bin b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/desugar_graph.bin differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ArgumentsHelper.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ArgumentsHelper.dex
new file mode 100644
index 0000000..7df39d4
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ArgumentsHelper.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/BasePackage.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/BasePackage.dex
new file mode 100644
index 0000000..2e76a0c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/BasePackage.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/BuildConfig.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/BuildConfig.dex
new file mode 100644
index 0000000..2dd7a82
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/BuildConfig.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ExportedModule$MethodInfo.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ExportedModule$MethodInfo.dex
new file mode 100644
index 0000000..dac9285
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ExportedModule$MethodInfo.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ExportedModule.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ExportedModule.dex
new file mode 100644
index 0000000..47608a9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ExportedModule.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/MapHelper.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/MapHelper.dex
new file mode 100644
index 0000000..fe58516
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/MapHelper.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistry.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistry.dex
new file mode 100644
index 0000000..c9547dd
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistry.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.dex
new file mode 100644
index 0000000..5cd929c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistryDelegate.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistryDelegate.dex
new file mode 100644
index 0000000..4f4f03e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistryDelegate.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistryProvider.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistryProvider.dex
new file mode 100644
index 0000000..72d4fde
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ModuleRegistryProvider.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/Promise.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/Promise.dex
new file mode 100644
index 0000000..6941d66
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/Promise.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ViewManager$PropSetterInfo.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ViewManager$PropSetterInfo.dex
new file mode 100644
index 0000000..ef755e5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ViewManager$PropSetterInfo.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ViewManager$ViewManagerType.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ViewManager$ViewManagerType.dex
new file mode 100644
index 0000000..ce2bd42
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ViewManager$ViewManagerType.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ViewManager.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ViewManager.dex
new file mode 100644
index 0000000..bfb41f8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/ViewManager.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/arguments/MapArguments.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/arguments/MapArguments.dex
new file mode 100644
index 0000000..82249ee
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/arguments/MapArguments.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/arguments/ReadableArguments.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/arguments/ReadableArguments.dex
new file mode 100644
index 0000000..358e4b0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/arguments/ReadableArguments.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/CodedException.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/CodedException.dex
new file mode 100644
index 0000000..e5ff759
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/CodedException.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/CodedRuntimeException.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/CodedRuntimeException.dex
new file mode 100644
index 0000000..fc6a228
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/CodedRuntimeException.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/CurrentActivityNotFoundException.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/CurrentActivityNotFoundException.dex
new file mode 100644
index 0000000..e3b36ba
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/CurrentActivityNotFoundException.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/InvalidArgumentException.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/InvalidArgumentException.dex
new file mode 100644
index 0000000..a9ce807
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/InvalidArgumentException.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/ModuleNotFoundException.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/ModuleNotFoundException.dex
new file mode 100644
index 0000000..899f2d9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/errors/ModuleNotFoundException.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ActivityEventListener.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ActivityEventListener.dex
new file mode 100644
index 0000000..f1e8672
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ActivityEventListener.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ActivityProvider.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ActivityProvider.dex
new file mode 100644
index 0000000..7ee82f4
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ActivityProvider.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Arguments.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Arguments.dex
new file mode 100644
index 0000000..dd4fc24
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Arguments.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/CodedThrowable.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/CodedThrowable.dex
new file mode 100644
index 0000000..36fa145
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/CodedThrowable.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Consumer.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Consumer.dex
new file mode 100644
index 0000000..9cd14e9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Consumer.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/DoNotStrip.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/DoNotStrip.dex
new file mode 100644
index 0000000..27d5d76
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/DoNotStrip.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ExpoMethod.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ExpoMethod.dex
new file mode 100644
index 0000000..33ef508
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ExpoMethod.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ExpoProp.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ExpoProp.dex
new file mode 100644
index 0000000..a95033e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/ExpoProp.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Function.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Function.dex
new file mode 100644
index 0000000..5137379
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Function.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/InternalModule.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/InternalModule.dex
new file mode 100644
index 0000000..7d1aef3
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/InternalModule.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/JavaScriptContextProvider.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/JavaScriptContextProvider.dex
new file mode 100644
index 0000000..e0e1f6d
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/JavaScriptContextProvider.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/LifecycleEventListener.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/LifecycleEventListener.dex
new file mode 100644
index 0000000..d234353
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/LifecycleEventListener.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Package.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Package.dex
new file mode 100644
index 0000000..e051eb0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/Package.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/RegistryLifecycleListener.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/RegistryLifecycleListener.dex
new file mode 100644
index 0000000..f8f537d
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/RegistryLifecycleListener.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.dex
new file mode 100644
index 0000000..0833751
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.dex
new file mode 100644
index 0000000..374ae1e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/SingletonModule.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/SingletonModule.dex
new file mode 100644
index 0000000..16551e8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/SingletonModule.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.dex
new file mode 100644
index 0000000..e607e35
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/EventEmitter$Event.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/EventEmitter$Event.dex
new file mode 100644
index 0000000..0684d71
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/EventEmitter$Event.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/EventEmitter.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/EventEmitter.dex
new file mode 100644
index 0000000..aa75ddb
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/EventEmitter.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/KeepAwakeManager.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/KeepAwakeManager.dex
new file mode 100644
index 0000000..508bd8b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/KeepAwakeManager.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.dex
new file mode 100644
index 0000000..de90b14
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager$UIBlock.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager$UIBlock.dex
new file mode 100644
index 0000000..a7abe88
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager$UIBlock.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager$ViewHolder.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager$ViewHolder.dex
new file mode 100644
index 0000000..421baad
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager$ViewHolder.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager.dex
new file mode 100644
index 0000000..d60e469
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/interfaces/services/UIManager.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/utilities/FileUtilities.dex b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/utilities/FileUtilities.dex
new file mode 100644
index 0000000..cc2995c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/5c0c5c56ca239c2c27710d23fed8e283/transformed/release/org/unimodules/core/utilities/FileUtilities.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/71e1de4d5801ef27b9ae80575d969ab2/results.bin b/node_modules/@unimodules/core/android/build/.transforms/71e1de4d5801ef27b9ae80575d969ab2/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/71e1de4d5801ef27b9ae80575d969ab2/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@unimodules/core/android/build/.transforms/71e1de4d5801ef27b9ae80575d969ab2/transformed/classes/classes.dex b/node_modules/@unimodules/core/android/build/.transforms/71e1de4d5801ef27b9ae80575d969ab2/transformed/classes/classes.dex
new file mode 100644
index 0000000..2f05c8f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/71e1de4d5801ef27b9ae80575d969ab2/transformed/classes/classes.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/results.bin b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/AndroidManifest.xml b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..f5c3691
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.core" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/R.txt b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/jars/classes.jar b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..f278ce7
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/jars/classes.jar differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/proguard.txt b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/proguard.txt
new file mode 100644
index 0000000..bd9417b
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/8416ea0443d7b72f49bd7b4a6e271871/transformed/out/proguard.txt
@@ -0,0 +1,13 @@
+
+-keepclassmembers class * {
+  @org.unimodules.core.interfaces.ExpoProp *;
+}
+
+-keepclassmembers class * {
+  @org.unimodules.core.interfaces.ExpoMethod *;
+}
+
+-keep @org.unimodules.core.interfaces.DoNotStrip class *
+-keepclassmembers class * {
+  @org.unimodules.core.interfaces.DoNotStrip *;
+}
diff --git a/node_modules/@unimodules/core/android/build/.transforms/98ea55a8ac567578e6f6eaa994d3ab00/results.bin b/node_modules/@unimodules/core/android/build/.transforms/98ea55a8ac567578e6f6eaa994d3ab00/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/98ea55a8ac567578e6f6eaa994d3ab00/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@unimodules/core/android/build/.transforms/98ea55a8ac567578e6f6eaa994d3ab00/transformed/classes/classes.dex b/node_modules/@unimodules/core/android/build/.transforms/98ea55a8ac567578e6f6eaa994d3ab00/transformed/classes/classes.dex
new file mode 100644
index 0000000..1644dbe
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/98ea55a8ac567578e6f6eaa994d3ab00/transformed/classes/classes.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/bf1396440276f55ea5ad60fbbc99bc4c/results.bin b/node_modules/@unimodules/core/android/build/.transforms/bf1396440276f55ea5ad60fbbc99bc4c/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/bf1396440276f55ea5ad60fbbc99bc4c/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@unimodules/core/android/build/.transforms/bf1396440276f55ea5ad60fbbc99bc4c/transformed/classes/classes.dex b/node_modules/@unimodules/core/android/build/.transforms/bf1396440276f55ea5ad60fbbc99bc4c/transformed/classes/classes.dex
new file mode 100644
index 0000000..93041ce
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/bf1396440276f55ea5ad60fbbc99bc4c/transformed/classes/classes.dex differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/results.bin b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/AndroidManifest.xml b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..f5c3691
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.core" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..d8f70c0
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,3 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
diff --git a/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
new file mode 100644
index 0000000..181db3b
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=unimodules-core
+mavenGroupId=org.unimodules
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/R.txt b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/jars/classes.jar b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..c908aaa
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/jars/classes.jar differ
diff --git a/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/proguard.txt b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/proguard.txt
new file mode 100644
index 0000000..bd9417b
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/.transforms/fda7290ade35fc5acae04492c7aeb70b/transformed/out/proguard.txt
@@ -0,0 +1,13 @@
+
+-keepclassmembers class * {
+  @org.unimodules.core.interfaces.ExpoProp *;
+}
+
+-keepclassmembers class * {
+  @org.unimodules.core.interfaces.ExpoMethod *;
+}
+
+-keep @org.unimodules.core.interfaces.DoNotStrip class *
+-keepclassmembers class * {
+  @org.unimodules.core.interfaces.DoNotStrip *;
+}
diff --git a/node_modules/@unimodules/core/android/build/generated/source/buildConfig/debug/org/unimodules/core/BuildConfig.java b/node_modules/@unimodules/core/android/build/generated/source/buildConfig/debug/org/unimodules/core/BuildConfig.java
new file mode 100644
index 0000000..6ff0623
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/generated/source/buildConfig/debug/org/unimodules/core/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package org.unimodules.core;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "org.unimodules.core";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/@unimodules/core/android/build/generated/source/buildConfig/release/org/unimodules/core/BuildConfig.java b/node_modules/@unimodules/core/android/build/generated/source/buildConfig/release/org/unimodules/core/BuildConfig.java
new file mode 100644
index 0000000..faec266
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/generated/source/buildConfig/release/org/unimodules/core/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package org.unimodules.core;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "org.unimodules.core";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..f5c3691
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.core" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..b1534db
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.core",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..f5c3691
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.core" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..a1df8ed
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.core",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/aar_main_jar/release/classes.jar b/node_modules/@unimodules/core/android/build/intermediates/aar_main_jar/release/classes.jar
new file mode 100644
index 0000000..f278ce7
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/aar_main_jar/release/classes.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/@unimodules/core/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/@unimodules/core/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/@unimodules/core/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/@unimodules/core/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/@unimodules/core/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/@unimodules/core/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/annotations_typedef_file/release/typedefs.txt b/node_modules/@unimodules/core/android/build/intermediates/annotations_typedef_file/release/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/@unimodules/core/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/@unimodules/core/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..1ce28dd
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/@unimodules/core/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..a6c4972
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/compile_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/@unimodules/core/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..0c0f063
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/@unimodules/core/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..0c0f063
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/compile_r_class_jar/release/R.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/@unimodules/core/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/@unimodules/core/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/@unimodules/core/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/@unimodules/core/android/build/intermediates/full_jar/release/full.jar b/node_modules/@unimodules/core/android/build/intermediates/full_jar/release/full.jar
new file mode 100644
index 0000000..c7bfefb
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/full_jar/release/full.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..906d2fe
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..1753105
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..c9aaabc
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..318d0f4
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..d48ce21
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/intermediates/shader_assets/debug/out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/debug/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..d5aeeb6
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:34:33 ICT 2022
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/packageDebugResources/merger.xml b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageDebugResources/merger.xml
new file mode 100644
index 0000000..23140d7
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/debug/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..77872b2
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/intermediates/shader_assets/release/out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/release/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..1142a1e
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:15:39 ICT 2022
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/packageReleaseResources/merger.xml b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..69bf863
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/incremental/packageReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/release/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/incremental/release-mergeJavaRes/merge-state b/node_modules/@unimodules/core/android/build/intermediates/incremental/release-mergeJavaRes/merge-state
new file mode 100644
index 0000000..1bb38cc
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/incremental/release-mergeJavaRes/merge-state differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ArgumentsHelper.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ArgumentsHelper.class
new file mode 100644
index 0000000..d498e9c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ArgumentsHelper.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/BasePackage.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/BasePackage.class
new file mode 100644
index 0000000..e9f6944
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/BasePackage.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/BuildConfig.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/BuildConfig.class
new file mode 100644
index 0000000..efa41c8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/BuildConfig.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ExportedModule$MethodInfo.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ExportedModule$MethodInfo.class
new file mode 100644
index 0000000..211e7d6
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ExportedModule$MethodInfo.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ExportedModule.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ExportedModule.class
new file mode 100644
index 0000000..d638dc1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ExportedModule.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/MapHelper.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/MapHelper.class
new file mode 100644
index 0000000..509bdfb
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/MapHelper.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ModuleRegistry.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ModuleRegistry.class
new file mode 100644
index 0000000..7122f78
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ModuleRegistry.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ModuleRegistryProvider.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ModuleRegistryProvider.class
new file mode 100644
index 0000000..6bc00de
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ModuleRegistryProvider.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/Promise.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/Promise.class
new file mode 100644
index 0000000..9068ffe
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/Promise.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ViewManager$PropSetterInfo.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ViewManager$PropSetterInfo.class
new file mode 100644
index 0000000..6023769
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ViewManager$PropSetterInfo.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ViewManager$ViewManagerType.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ViewManager$ViewManagerType.class
new file mode 100644
index 0000000..8fe6319
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ViewManager$ViewManagerType.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ViewManager.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ViewManager.class
new file mode 100644
index 0000000..6de94b3
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/ViewManager.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/arguments/MapArguments.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/arguments/MapArguments.class
new file mode 100644
index 0000000..aa8b112
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/arguments/MapArguments.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/arguments/ReadableArguments.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/arguments/ReadableArguments.class
new file mode 100644
index 0000000..3531972
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/arguments/ReadableArguments.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/CodedException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/CodedException.class
new file mode 100644
index 0000000..fdda7f0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/CodedException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/CodedRuntimeException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/CodedRuntimeException.class
new file mode 100644
index 0000000..441397c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/CodedRuntimeException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/CurrentActivityNotFoundException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/CurrentActivityNotFoundException.class
new file mode 100644
index 0000000..d8d39b9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/CurrentActivityNotFoundException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/InvalidArgumentException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/InvalidArgumentException.class
new file mode 100644
index 0000000..da24fce
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/InvalidArgumentException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/ModuleNotFoundException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/ModuleNotFoundException.class
new file mode 100644
index 0000000..1491a98
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/errors/ModuleNotFoundException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ActivityEventListener.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ActivityEventListener.class
new file mode 100644
index 0000000..a8bb66c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ActivityEventListener.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ActivityProvider.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ActivityProvider.class
new file mode 100644
index 0000000..8dbbf9d
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ActivityProvider.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Arguments.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Arguments.class
new file mode 100644
index 0000000..23319c6
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Arguments.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/CodedThrowable.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/CodedThrowable.class
new file mode 100644
index 0000000..0d3458b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/CodedThrowable.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Consumer.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Consumer.class
new file mode 100644
index 0000000..d4f88e8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Consumer.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/DoNotStrip.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/DoNotStrip.class
new file mode 100644
index 0000000..2d1cec5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/DoNotStrip.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ExpoMethod.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ExpoMethod.class
new file mode 100644
index 0000000..ceb809b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ExpoMethod.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ExpoProp.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ExpoProp.class
new file mode 100644
index 0000000..ff119c4
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/ExpoProp.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Function.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Function.class
new file mode 100644
index 0000000..5d65d65
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Function.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/InternalModule.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/InternalModule.class
new file mode 100644
index 0000000..28aab9f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/InternalModule.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/JavaScriptContextProvider.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/JavaScriptContextProvider.class
new file mode 100644
index 0000000..6927550
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/JavaScriptContextProvider.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/LifecycleEventListener.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/LifecycleEventListener.class
new file mode 100644
index 0000000..4ae49bc
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/LifecycleEventListener.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Package.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Package.class
new file mode 100644
index 0000000..1a1c83f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/Package.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/RegistryLifecycleListener.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/RegistryLifecycleListener.class
new file mode 100644
index 0000000..a8368b5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/RegistryLifecycleListener.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.class
new file mode 100644
index 0000000..386b87f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.class
new file mode 100644
index 0000000..0dda9bd
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/SingletonModule.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/SingletonModule.class
new file mode 100644
index 0000000..40a35cc
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/SingletonModule.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.class
new file mode 100644
index 0000000..2b883f2
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/EventEmitter$Event.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/EventEmitter$Event.class
new file mode 100644
index 0000000..5fcd8e9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/EventEmitter$Event.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/EventEmitter.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/EventEmitter.class
new file mode 100644
index 0000000..b361bcb
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/EventEmitter.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/KeepAwakeManager.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/KeepAwakeManager.class
new file mode 100644
index 0000000..f558218
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/KeepAwakeManager.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.class
new file mode 100644
index 0000000..b8f0611
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager$UIBlock.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager$UIBlock.class
new file mode 100644
index 0000000..13d121b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager$UIBlock.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager$ViewHolder.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager$ViewHolder.class
new file mode 100644
index 0000000..a4fdf2e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager$ViewHolder.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager.class
new file mode 100644
index 0000000..1d05431
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/interfaces/services/UIManager.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/utilities/FileUtilities.class b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/utilities/FileUtilities.class
new file mode 100644
index 0000000..978ccde
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/debug/classes/org/unimodules/core/utilities/FileUtilities.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ArgumentsHelper.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ArgumentsHelper.class
new file mode 100644
index 0000000..d498e9c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ArgumentsHelper.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/BasePackage.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/BasePackage.class
new file mode 100644
index 0000000..e9f6944
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/BasePackage.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/BuildConfig.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/BuildConfig.class
new file mode 100644
index 0000000..0590932
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/BuildConfig.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ExportedModule$MethodInfo.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ExportedModule$MethodInfo.class
new file mode 100644
index 0000000..211e7d6
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ExportedModule$MethodInfo.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ExportedModule.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ExportedModule.class
new file mode 100644
index 0000000..d638dc1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ExportedModule.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/MapHelper.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/MapHelper.class
new file mode 100644
index 0000000..509bdfb
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/MapHelper.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ModuleRegistry.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ModuleRegistry.class
new file mode 100644
index 0000000..7122f78
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ModuleRegistry.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ModuleRegistryProvider.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ModuleRegistryProvider.class
new file mode 100644
index 0000000..6bc00de
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ModuleRegistryProvider.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/Promise.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/Promise.class
new file mode 100644
index 0000000..9068ffe
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/Promise.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ViewManager$PropSetterInfo.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ViewManager$PropSetterInfo.class
new file mode 100644
index 0000000..6023769
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ViewManager$PropSetterInfo.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ViewManager$ViewManagerType.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ViewManager$ViewManagerType.class
new file mode 100644
index 0000000..8fe6319
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ViewManager$ViewManagerType.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ViewManager.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ViewManager.class
new file mode 100644
index 0000000..6de94b3
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/ViewManager.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/arguments/MapArguments.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/arguments/MapArguments.class
new file mode 100644
index 0000000..aa8b112
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/arguments/MapArguments.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/arguments/ReadableArguments.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/arguments/ReadableArguments.class
new file mode 100644
index 0000000..3531972
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/arguments/ReadableArguments.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/CodedException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/CodedException.class
new file mode 100644
index 0000000..fdda7f0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/CodedException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/CodedRuntimeException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/CodedRuntimeException.class
new file mode 100644
index 0000000..441397c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/CodedRuntimeException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/CurrentActivityNotFoundException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/CurrentActivityNotFoundException.class
new file mode 100644
index 0000000..d8d39b9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/CurrentActivityNotFoundException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/InvalidArgumentException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/InvalidArgumentException.class
new file mode 100644
index 0000000..da24fce
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/InvalidArgumentException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/ModuleNotFoundException.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/ModuleNotFoundException.class
new file mode 100644
index 0000000..1491a98
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/errors/ModuleNotFoundException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ActivityEventListener.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ActivityEventListener.class
new file mode 100644
index 0000000..a8bb66c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ActivityEventListener.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ActivityProvider.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ActivityProvider.class
new file mode 100644
index 0000000..8dbbf9d
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ActivityProvider.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Arguments.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Arguments.class
new file mode 100644
index 0000000..23319c6
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Arguments.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/CodedThrowable.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/CodedThrowable.class
new file mode 100644
index 0000000..0d3458b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/CodedThrowable.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Consumer.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Consumer.class
new file mode 100644
index 0000000..d4f88e8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Consumer.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/DoNotStrip.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/DoNotStrip.class
new file mode 100644
index 0000000..2d1cec5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/DoNotStrip.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ExpoMethod.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ExpoMethod.class
new file mode 100644
index 0000000..ceb809b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ExpoMethod.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ExpoProp.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ExpoProp.class
new file mode 100644
index 0000000..ff119c4
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/ExpoProp.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Function.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Function.class
new file mode 100644
index 0000000..5d65d65
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Function.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/InternalModule.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/InternalModule.class
new file mode 100644
index 0000000..28aab9f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/InternalModule.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/JavaScriptContextProvider.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/JavaScriptContextProvider.class
new file mode 100644
index 0000000..6927550
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/JavaScriptContextProvider.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/LifecycleEventListener.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/LifecycleEventListener.class
new file mode 100644
index 0000000..4ae49bc
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/LifecycleEventListener.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Package.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Package.class
new file mode 100644
index 0000000..1a1c83f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/Package.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/RegistryLifecycleListener.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/RegistryLifecycleListener.class
new file mode 100644
index 0000000..a8368b5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/RegistryLifecycleListener.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.class
new file mode 100644
index 0000000..386b87f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.class
new file mode 100644
index 0000000..0dda9bd
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/SingletonModule.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/SingletonModule.class
new file mode 100644
index 0000000..40a35cc
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/SingletonModule.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.class
new file mode 100644
index 0000000..2b883f2
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/EventEmitter$Event.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/EventEmitter$Event.class
new file mode 100644
index 0000000..5fcd8e9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/EventEmitter$Event.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/EventEmitter.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/EventEmitter.class
new file mode 100644
index 0000000..b361bcb
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/EventEmitter.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/KeepAwakeManager.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/KeepAwakeManager.class
new file mode 100644
index 0000000..f558218
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/KeepAwakeManager.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.class
new file mode 100644
index 0000000..b8f0611
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager$UIBlock.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager$UIBlock.class
new file mode 100644
index 0000000..13d121b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager$UIBlock.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager$ViewHolder.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager$ViewHolder.class
new file mode 100644
index 0000000..a4fdf2e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager$ViewHolder.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager.class
new file mode 100644
index 0000000..1d05431
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/interfaces/services/UIManager.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/utilities/FileUtilities.class b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/utilities/FileUtilities.class
new file mode 100644
index 0000000..978ccde
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/javac/release/classes/org/unimodules/core/utilities/FileUtilities.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/library_java_res/debug/res.jar b/node_modules/@unimodules/core/android/build/intermediates/library_java_res/debug/res.jar
new file mode 100644
index 0000000..a916e80
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/library_java_res/debug/res.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/library_java_res/release/res.jar b/node_modules/@unimodules/core/android/build/intermediates/library_java_res/release/res.jar
new file mode 100644
index 0000000..a40f36b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/library_java_res/release/res.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties b/node_modules/@unimodules/core/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
new file mode 100644
index 0000000..181db3b
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=unimodules-core
+mavenGroupId=org.unimodules
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/local_aar_for_lint/release/out.aar b/node_modules/@unimodules/core/android/build/intermediates/local_aar_for_lint/release/out.aar
new file mode 100644
index 0000000..097d7af
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/local_aar_for_lint/release/out.aar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/@unimodules/core/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/@unimodules/core/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/@unimodules/core/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/@unimodules/core/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/@unimodules/core/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..8645158
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="org.unimodules.core" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/@unimodules/core/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/@unimodules/core/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..8645158
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="org.unimodules.core" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/@unimodules/core/android/build/intermediates/merged_consumer_proguard_file/release/proguard.txt b/node_modules/@unimodules/core/android/build/intermediates/merged_consumer_proguard_file/release/proguard.txt
new file mode 100644
index 0000000..bd9417b
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/merged_consumer_proguard_file/release/proguard.txt
@@ -0,0 +1,13 @@
+
+-keepclassmembers class * {
+  @org.unimodules.core.interfaces.ExpoProp *;
+}
+
+-keepclassmembers class * {
+  @org.unimodules.core.interfaces.ExpoMethod *;
+}
+
+-keep @org.unimodules.core.interfaces.DoNotStrip class *
+-keepclassmembers class * {
+  @org.unimodules.core.interfaces.DoNotStrip *;
+}
diff --git a/node_modules/@unimodules/core/android/build/intermediates/merged_java_res/release/feature-unimodules-core.jar b/node_modules/@unimodules/core/android/build/intermediates/merged_java_res/release/feature-unimodules-core.jar
new file mode 100644
index 0000000..a527a43
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/merged_java_res/release/feature-unimodules-core.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/@unimodules/core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..f5c3691
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.core" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/@unimodules/core/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..f5c3691
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="org.unimodules.core" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/@unimodules/core/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/@unimodules/core/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/@unimodules/core/android/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..5d2636d
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.core",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/packaged_manifests/release/output-metadata.json b/node_modules/@unimodules/core/android/build/intermediates/packaged_manifests/release/output-metadata.json
new file mode 100644
index 0000000..a3bc449
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/packaged_manifests/release/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "org.unimodules.core",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/release/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/META-INF/unimodules-core_release.kotlin_module b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/META-INF/unimodules-core_release.kotlin_module
new file mode 100644
index 0000000..3a4e3bf
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/META-INF/unimodules-core_release.kotlin_module differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ArgumentsHelper.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ArgumentsHelper.class
new file mode 100644
index 0000000..394ee46
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ArgumentsHelper.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/BasePackage.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/BasePackage.class
new file mode 100644
index 0000000..a591534
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/BasePackage.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/BuildConfig.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/BuildConfig.class
new file mode 100644
index 0000000..b134045
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/BuildConfig.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ExportedModule$MethodInfo.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ExportedModule$MethodInfo.class
new file mode 100644
index 0000000..426a8f0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ExportedModule$MethodInfo.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ExportedModule.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ExportedModule.class
new file mode 100644
index 0000000..802414f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ExportedModule.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/MapHelper.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/MapHelper.class
new file mode 100644
index 0000000..6ea6be1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/MapHelper.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistry.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistry.class
new file mode 100644
index 0000000..4fba707
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistry.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.class
new file mode 100644
index 0000000..8b68447
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistryDelegate.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistryDelegate.class
new file mode 100644
index 0000000..10e7b35
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistryDelegate.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistryProvider.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistryProvider.class
new file mode 100644
index 0000000..8047575
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ModuleRegistryProvider.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/Promise.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/Promise.class
new file mode 100644
index 0000000..036604a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/Promise.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ViewManager$PropSetterInfo.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ViewManager$PropSetterInfo.class
new file mode 100644
index 0000000..201a73e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ViewManager$PropSetterInfo.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ViewManager$ViewManagerType.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ViewManager$ViewManagerType.class
new file mode 100644
index 0000000..2a7afdf
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ViewManager$ViewManagerType.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ViewManager.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ViewManager.class
new file mode 100644
index 0000000..b327bf2
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/ViewManager.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/arguments/MapArguments.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/arguments/MapArguments.class
new file mode 100644
index 0000000..51be78f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/arguments/MapArguments.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/arguments/ReadableArguments.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/arguments/ReadableArguments.class
new file mode 100644
index 0000000..355d8f1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/arguments/ReadableArguments.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/CodedException.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/CodedException.class
new file mode 100644
index 0000000..1294d79
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/CodedException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/CodedRuntimeException.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/CodedRuntimeException.class
new file mode 100644
index 0000000..b1b2dca
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/CodedRuntimeException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/CurrentActivityNotFoundException.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/CurrentActivityNotFoundException.class
new file mode 100644
index 0000000..b6a649c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/CurrentActivityNotFoundException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/InvalidArgumentException.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/InvalidArgumentException.class
new file mode 100644
index 0000000..8cfce5a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/InvalidArgumentException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/ModuleNotFoundException.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/ModuleNotFoundException.class
new file mode 100644
index 0000000..27f31bf
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/errors/ModuleNotFoundException.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ActivityEventListener.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ActivityEventListener.class
new file mode 100644
index 0000000..41950f6
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ActivityEventListener.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ActivityProvider.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ActivityProvider.class
new file mode 100644
index 0000000..c2b8997
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ActivityProvider.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Arguments.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Arguments.class
new file mode 100644
index 0000000..ea18dfa
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Arguments.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/CodedThrowable.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/CodedThrowable.class
new file mode 100644
index 0000000..c55886a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/CodedThrowable.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Consumer.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Consumer.class
new file mode 100644
index 0000000..877c6be
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Consumer.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/DoNotStrip.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/DoNotStrip.class
new file mode 100644
index 0000000..144e1cb
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/DoNotStrip.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ExpoMethod.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ExpoMethod.class
new file mode 100644
index 0000000..3de8767
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ExpoMethod.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ExpoProp.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ExpoProp.class
new file mode 100644
index 0000000..7bb13e7
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/ExpoProp.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Function.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Function.class
new file mode 100644
index 0000000..bed19bb
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Function.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/InternalModule.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/InternalModule.class
new file mode 100644
index 0000000..26e1bb7
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/InternalModule.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/JavaScriptContextProvider.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/JavaScriptContextProvider.class
new file mode 100644
index 0000000..8cb7536
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/JavaScriptContextProvider.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/LifecycleEventListener.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/LifecycleEventListener.class
new file mode 100644
index 0000000..48a43ac
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/LifecycleEventListener.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Package.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Package.class
new file mode 100644
index 0000000..30eba0e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/Package.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/RegistryLifecycleListener.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/RegistryLifecycleListener.class
new file mode 100644
index 0000000..ef8ae7f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/RegistryLifecycleListener.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.class
new file mode 100644
index 0000000..61bae1a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface$PlatformVersion.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.class
new file mode 100644
index 0000000..37da90b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/RuntimeEnvironmentInterface.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/SingletonModule.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/SingletonModule.class
new file mode 100644
index 0000000..8b2bd4b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/SingletonModule.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.class
new file mode 100644
index 0000000..bec0f94
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/EventEmitter$BaseEvent.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/EventEmitter$Event.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/EventEmitter$Event.class
new file mode 100644
index 0000000..4d0a1dc
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/EventEmitter$Event.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/EventEmitter.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/EventEmitter.class
new file mode 100644
index 0000000..039e189
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/EventEmitter.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/KeepAwakeManager.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/KeepAwakeManager.class
new file mode 100644
index 0000000..2299ab5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/KeepAwakeManager.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.class
new file mode 100644
index 0000000..373c1c2
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager$GroupUIBlock.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager$UIBlock.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager$UIBlock.class
new file mode 100644
index 0000000..8995155
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager$UIBlock.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager$ViewHolder.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager$ViewHolder.class
new file mode 100644
index 0000000..671b4ac
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager$ViewHolder.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager.class
new file mode 100644
index 0000000..d6c7edc
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/interfaces/services/UIManager.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/utilities/FileUtilities.class b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/utilities/FileUtilities.class
new file mode 100644
index 0000000..2f655c7
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_dir/release/org/unimodules/core/utilities/FileUtilities.class differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..b89806c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..f57aa3e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/intermediates/runtime_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/@unimodules/core/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/@unimodules/core/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..8bc8bfe
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1 @@
+org.unimodules.core
diff --git a/node_modules/@unimodules/core/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/@unimodules/core/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..8bc8bfe
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1 @@
+org.unimodules.core
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/build-history.bin b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/build-history.bin
new file mode 100644
index 0000000..e1510d9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/build-history.bin differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream
new file mode 100644
index 0000000..a7d94ec
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len
new file mode 100644
index 0000000..9c559e0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.values.at
new file mode 100644
index 0000000..e7e0b92
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i
new file mode 100644
index 0000000..d919733
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab
new file mode 100644
index 0000000..12ce6dc
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
new file mode 100644
index 0000000..c662689
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
new file mode 100644
index 0000000..826d10c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
new file mode 100644
index 0000000..fc0e221
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i
new file mode 100644
index 0000000..34382fa
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
new file mode 100644
index 0000000..e6d26a6
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
new file mode 100644
index 0000000..c662689
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..826d10c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
new file mode 100644
index 0000000..c0d55c0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
new file mode 100644
index 0000000..34382fa
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream
new file mode 100644
index 0000000..0d0e07b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream.len
new file mode 100644
index 0000000..5b8e1ac
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.values.at
new file mode 100644
index 0000000..6279138
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i
new file mode 100644
index 0000000..1d7da13
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab
new file mode 100644
index 0000000..e6d26a6
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
new file mode 100644
index 0000000..66d4816
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..7ab8050
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
new file mode 100644
index 0000000..3ffb83f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
new file mode 100644
index 0000000..ba18bf9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream
new file mode 100644
index 0000000..5fc422f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len
new file mode 100644
index 0000000..68d7fbd
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at
new file mode 100644
index 0000000..becc4b8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i
new file mode 100644
index 0000000..87fd055
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab
new file mode 100644
index 0000000..0812fbb
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream
new file mode 100644
index 0000000..c1b9ebd
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len
new file mode 100644
index 0000000..3f68fd9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at
new file mode 100644
index 0000000..7b18fa2
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i
new file mode 100644
index 0000000..aba9902
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab
new file mode 100644
index 0000000..dac19f2
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
new file mode 100644
index 0000000..ee43503
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
new file mode 100644
index 0000000..468508e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
new file mode 100644
index 0000000..9ce53c8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i
new file mode 100644
index 0000000..de1488f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/counters.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/counters.tab
new file mode 100644
index 0000000..166c057
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/counters.tab
@@ -0,0 +1,2 @@
+1
+0
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream
new file mode 100644
index 0000000..a7d94ec
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len
new file mode 100644
index 0000000..9c559e0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.values.at
new file mode 100644
index 0000000..5875372
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i
new file mode 100644
index 0000000..79901d8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab
new file mode 100644
index 0000000..8aad32b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream
new file mode 100644
index 0000000..08e7df1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len
new file mode 100644
index 0000000..b7da01d
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.values.at
new file mode 100644
index 0000000..e2f26e9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab
new file mode 100644
index 0000000..d4ef210
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream
new file mode 100644
index 0000000..65b4af5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream.len
new file mode 100644
index 0000000..d61ec06
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.len
new file mode 100644
index 0000000..62cf1e5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.values.at
new file mode 100644
index 0000000..6062bbf
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i
new file mode 100644
index 0000000..6aff285
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/last-build.bin b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/last-build.bin
new file mode 100644
index 0000000..ed684da
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileDebugKotlin/last-build.bin differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/build-history.bin b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/build-history.bin
new file mode 100644
index 0000000..417374f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/build-history.bin differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream
new file mode 100644
index 0000000..a7d94ec
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len
new file mode 100644
index 0000000..9c559e0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.values.at
new file mode 100644
index 0000000..0b5f95a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i
new file mode 100644
index 0000000..d919733
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab
new file mode 100644
index 0000000..12ce6dc
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
new file mode 100644
index 0000000..c662689
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
new file mode 100644
index 0000000..826d10c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
new file mode 100644
index 0000000..fc0e221
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i
new file mode 100644
index 0000000..34382fa
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
new file mode 100644
index 0000000..e6d26a6
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
new file mode 100644
index 0000000..c662689
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..826d10c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
new file mode 100644
index 0000000..c0d55c0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
new file mode 100644
index 0000000..34382fa
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream
new file mode 100644
index 0000000..0d0e07b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream.len
new file mode 100644
index 0000000..5b8e1ac
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.values.at
new file mode 100644
index 0000000..6279138
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i
new file mode 100644
index 0000000..1d7da13
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/inline-functions.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab
new file mode 100644
index 0000000..e6d26a6
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
new file mode 100644
index 0000000..66d4816
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..7ab8050
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
new file mode 100644
index 0000000..3ffb83f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
new file mode 100644
index 0000000..ba18bf9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream
new file mode 100644
index 0000000..5fc422f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len
new file mode 100644
index 0000000..68d7fbd
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at
new file mode 100644
index 0000000..becc4b8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i
new file mode 100644
index 0000000..87fd055
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab
new file mode 100644
index 0000000..c42ec2c
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream
new file mode 100644
index 0000000..c1b9ebd
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len
new file mode 100644
index 0000000..3f68fd9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at
new file mode 100644
index 0000000..a5973ad
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i
new file mode 100644
index 0000000..aba9902
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab
new file mode 100644
index 0000000..dac19f2
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
new file mode 100644
index 0000000..ee43503
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
new file mode 100644
index 0000000..468508e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len
new file mode 100644
index 0000000..01bdaa1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
new file mode 100644
index 0000000..9ce53c8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i
new file mode 100644
index 0000000..de1488f
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/counters.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/counters.tab
new file mode 100644
index 0000000..166c057
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/counters.tab
@@ -0,0 +1,2 @@
+1
+0
\ No newline at end of file
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream
new file mode 100644
index 0000000..a7d94ec
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len
new file mode 100644
index 0000000..9c559e0
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.values.at
new file mode 100644
index 0000000..5875372
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i
new file mode 100644
index 0000000..79901d8
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab
new file mode 100644
index 0000000..8aad32b
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream
new file mode 100644
index 0000000..08e7df1
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len
new file mode 100644
index 0000000..b7da01d
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.values.at
new file mode 100644
index 0000000..e2f26e9
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab
new file mode 100644
index 0000000..d4ef210
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream
new file mode 100644
index 0000000..65b4af5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream.len
new file mode 100644
index 0000000..d61ec06
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.len
new file mode 100644
index 0000000..62cf1e5
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.values.at b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.values.at
new file mode 100644
index 0000000..6062bbf
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.values.at differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i
new file mode 100644
index 0000000..6aff285
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i.len b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i.len differ
diff --git a/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/last-build.bin b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/last-build.bin
new file mode 100644
index 0000000..d9d03a4
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/kotlin/compileReleaseKotlin/last-build.bin differ
diff --git a/node_modules/@unimodules/core/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/@unimodules/core/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..b102d5f
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:1-4:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:11-40
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:1-4:12
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
diff --git a/node_modules/@unimodules/core/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/@unimodules/core/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..b102d5f
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:1-4:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:11-40
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml:2:1-4:12
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/@unimodules/core/android/src/main/AndroidManifest.xml
diff --git a/node_modules/@unimodules/core/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/@unimodules/core/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..2177761
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/@unimodules/core/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin b/node_modules/@unimodules/core/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..78a9fb3
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/@unimodules/core/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt b/node_modules/@unimodules/core/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
new file mode 100644
index 0000000..db868f9
--- /dev/null
+++ b/node_modules/@unimodules/core/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
@@ -0,0 +1,81 @@
+org/unimodules/core/BuildConfig.java
+ org.unimodules.core.BuildConfig
+org/unimodules/core/interfaces/InternalModule.java
+ org.unimodules.core.interfaces.InternalModule
+org/unimodules/core/ModuleRegistryProvider.java
+ org.unimodules.core.ModuleRegistryProvider
+org/unimodules/core/errors/CodedRuntimeException.java
+ org.unimodules.core.errors.CodedRuntimeException
+org/unimodules/core/interfaces/services/EventEmitter.java
+ org.unimodules.core.interfaces.services.EventEmitter
+ org.unimodules.core.interfaces.services.EventEmitter$BaseEvent
+ org.unimodules.core.interfaces.services.EventEmitter$Event
+org/unimodules/core/ModuleRegistry.java
+ org.unimodules.core.ModuleRegistry
+org/unimodules/core/utilities/FileUtilities.java
+ org.unimodules.core.utilities.FileUtilities
+org/unimodules/core/interfaces/Package.java
+ org.unimodules.core.interfaces.Package
+org/unimodules/core/arguments/MapArguments.java
+ org.unimodules.core.arguments.MapArguments
+org/unimodules/core/interfaces/CodedThrowable.java
+ org.unimodules.core.interfaces.CodedThrowable
+org/unimodules/core/interfaces/DoNotStrip.java
+ org.unimodules.core.interfaces.DoNotStrip
+org/unimodules/core/interfaces/LifecycleEventListener.java
+ org.unimodules.core.interfaces.LifecycleEventListener
+org/unimodules/core/interfaces/Consumer.java
+ org.unimodules.core.interfaces.Consumer
+org/unimodules/core/interfaces/RuntimeEnvironmentInterface.java
+ org.unimodules.core.interfaces.RuntimeEnvironmentInterface
+ org.unimodules.core.interfaces.RuntimeEnvironmentInterface$PlatformVersion
+org/unimodules/core/ViewManager.java
+ org.unimodules.core.ViewManager
+ org.unimodules.core.ViewManager$PropSetterInfo
+ org.unimodules.core.ViewManager$ViewManagerType
+org/unimodules/core/interfaces/ActivityEventListener.java
+ org.unimodules.core.interfaces.ActivityEventListener
+org/unimodules/core/errors/InvalidArgumentException.java
+ org.unimodules.core.errors.InvalidArgumentException
+org/unimodules/core/MapHelper.java
+ org.unimodules.core.MapHelper
+org/unimodules/core/interfaces/ActivityProvider.java
+ org.unimodules.core.interfaces.ActivityProvider
+org/unimodules/core/interfaces/JavaScriptContextProvider.java
+ org.unimodules.core.interfaces.JavaScriptContextProvider
+org/unimodules/core/arguments/ReadableArguments.java
+ org.unimodules.core.arguments.ReadableArguments
+org/unimodules/core/interfaces/services/KeepAwakeManager.java
+ org.unimodules.core.interfaces.services.KeepAwakeManager
+org/unimodules/core/errors/ModuleNotFoundException.java
+ org.unimodules.core.errors.ModuleNotFoundException
+org/unimodules/core/ArgumentsHelper.java
+ org.unimodules.core.ArgumentsHelper
+org/unimodules/core/ExportedModule.java
+ org.unimodules.core.ExportedModule
+ org.unimodules.core.ExportedModule$MethodInfo
+org/unimodules/core/interfaces/Function.java
+ org.unimodules.core.interfaces.Function
+org/unimodules/core/interfaces/RegistryLifecycleListener.java
+ org.unimodules.core.interfaces.RegistryLifecycleListener
+org/unimodules/core/Promise.java
+ org.unimodules.core.Promise
+org/unimodules/core/interfaces/services/UIManager.java
+ org.unimodules.core.interfaces.services.UIManager
+ org.unimodules.core.interfaces.services.UIManager$GroupUIBlock
+ org.unimodules.core.interfaces.services.UIManager$UIBlock
+ org.unimodules.core.interfaces.services.UIManager$ViewHolder
+org/unimodules/core/interfaces/ExpoMethod.java
+ org.unimodules.core.interfaces.ExpoMethod
+org/unimodules/core/BasePackage.java
+ org.unimodules.core.BasePackage
+org/unimodules/core/errors/CodedException.java
+ org.unimodules.core.errors.CodedException
+org/unimodules/core/interfaces/ExpoProp.java
+ org.unimodules.core.interfaces.ExpoProp
+org/unimodules/core/interfaces/Arguments.java
+ org.unimodules.core.interfaces.Arguments
+org/unimodules/core/errors/CurrentActivityNotFoundException.java
+ org.unimodules.core.errors.CurrentActivityNotFoundException
+org/unimodules/core/interfaces/SingletonModule.java
+ org.unimodules.core.interfaces.SingletonModule
diff --git a/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/debug/META-INF/unimodules-core_debug.kotlin_module b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/debug/META-INF/unimodules-core_debug.kotlin_module
new file mode 100644
index 0000000..3a4e3bf
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/debug/META-INF/unimodules-core_debug.kotlin_module differ
diff --git a/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/debug/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.class b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/debug/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.class
new file mode 100644
index 0000000..8b68447
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/debug/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.class differ
diff --git a/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/debug/org/unimodules/core/ModuleRegistryDelegate.class b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/debug/org/unimodules/core/ModuleRegistryDelegate.class
new file mode 100644
index 0000000..9fa8acd
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/debug/org/unimodules/core/ModuleRegistryDelegate.class differ
diff --git a/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/release/META-INF/unimodules-core_release.kotlin_module b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/release/META-INF/unimodules-core_release.kotlin_module
new file mode 100644
index 0000000..3a4e3bf
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/release/META-INF/unimodules-core_release.kotlin_module differ
diff --git a/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/release/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.class b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/release/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.class
new file mode 100644
index 0000000..8b68447
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/release/org/unimodules/core/ModuleRegistryDelegate$getFromModuleRegistry$1.class differ
diff --git a/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/release/org/unimodules/core/ModuleRegistryDelegate.class b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/release/org/unimodules/core/ModuleRegistryDelegate.class
new file mode 100644
index 0000000..10e7b35
Binary files /dev/null and b/node_modules/@unimodules/core/android/build/tmp/kotlin-classes/release/org/unimodules/core/ModuleRegistryDelegate.class differ
