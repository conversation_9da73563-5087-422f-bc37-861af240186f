diff --git a/node_modules/@react-native-picker/picker/ios/RNCPicker.h b/node_modules/@react-native-picker/picker/ios/RNCPicker.h
index cf9776d..1c6b6f0 100644
--- a/node_modules/@react-native-picker/picker/ios/RNCPicker.h
+++ b/node_modules/@react-native-picker/picker/ios/RNCPicker.h
@@ -19,9 +19,11 @@
 
 @property (nonatomic, strong) UIColor *color;
 @property (nonatomic, strong) UIFont *font;
+@property (nonatomic, strong) UIFont *font2;
 @property (nonatomic, assign) NSTextAlignment textAlign;
 
 @property (nonatomic, assign) NSInteger numberOfLines;
+@property (nonatomic, assign) BOOL customSecondLine;
 
 @property (nonatomic, copy) RCTBubblingEventBlock onChange;
 
diff --git a/node_modules/@react-native-picker/picker/ios/RNCPicker.m b/node_modules/@react-native-picker/picker/ios/RNCPicker.m
index abe0eee..73e4879 100644
--- a/node_modules/@react-native-picker/picker/ios/RNCPicker.m
+++ b/node_modules/@react-native-picker/picker/ios/RNCPicker.m
@@ -20,6 +20,7 @@ - (instancetype)initWithFrame:(CGRect)frame
   if ((self = [super initWithFrame:frame])) {
     _color = [UIColor blackColor];
     _font = [UIFont systemFontOfSize:21]; // TODO: selected title default should be 23.5
+    _font2 = [UIFont systemFontOfSize:21]; // TODO: selected title default should be 23.5
     _numberOfLines = 1;
     _selectedIndex = NSNotFound;
     _selectionColor = 0;
@@ -64,6 +65,19 @@ - (void) setFont:(UIFont *)font
   [self setNeedsLayout];
 }
 
+- (void) setFont2:(UIFont *)font
+{
+  _font2 = font;
+  [self reloadAllComponents];
+  [self setNeedsLayout];
+}
+
+- (void)setCustomSecondLine:(BOOL)customSecondLine
+{
+  _customSecondLine = customSecondLine;
+  [self reloadAllComponents];
+  [self setNeedsLayout];
+}
 #pragma mark - UIPickerViewDataSource protocol
 
 - (NSInteger)numberOfComponentsInPickerView:(__unused UIPickerView *)pickerView
@@ -95,6 +109,8 @@ - (UIView *)pickerView:(UIPickerView *)pickerView
           forComponent:(NSInteger)component
            reusingView:(UIView *)view
 {
+  NSArray *arrayOfComponents = [[self pickerView:pickerView titleForRow:row forComponent:component] componentsSeparatedByString:@"\n"];
+  Boolean isCustomSecondLine = [arrayOfComponents count] == 2 && _numberOfLines == 2 && _customSecondLine;
   if (!view) {
       CGFloat rowHeight = [pickerView rowSizeForComponent:component].height;
       CGFloat rowWidth = [pickerView rowSizeForComponent:component].width;
@@ -107,11 +123,29 @@ - (UIView *)pickerView:(UIPickerView *)pickerView
           }
       }];
     [view insertSubview:label atIndex:0];
+    if (isCustomSecondLine) {
+        RNCPickerLabel* label1 = [[RNCPickerLabel alloc] initWithFrame:(CGRect) {
+            CGPointMake(0, _font.lineHeight/2),
+            {
+                rowWidth,
+                rowHeight,
+            }
+        }];
+        label1.font = _font2;
+        label1.textColor = [RCTConvert UIColor:_items[row][@"textColor"]] ?: _color;
+        label1.textAlignment = _textAlign;
+        label1.text = arrayOfComponents[1];
+        [view insertSubview:label1 atIndex:1];
+    }
   }
 
   if (@available(iOS 14.0, *)) {
       if (_selectionColor) {
-          pickerView.subviews[1].backgroundColor = [RCTConvert UIColor:@(_selectionColor)];
+          if (isCustomSecondLine) {
+              pickerView.subviews[2].backgroundColor = [RCTConvert UIColor:@(_selectionColor)];
+          } else {
+              pickerView.subviews[1].backgroundColor = [RCTConvert UIColor:@(_selectionColor)];
+          }
       }
   }
     
@@ -121,7 +155,13 @@ - (UIView *)pickerView:(UIPickerView *)pickerView
   label.textColor = [RCTConvert UIColor:_items[row][@"textColor"]] ?: _color;
 
   label.textAlignment = _textAlign;
-  label.text = [self pickerView:pickerView titleForRow:row forComponent:component];
+  if (isCustomSecondLine) {
+      NSString *s = arrayOfComponents[0];
+      s = [s stringByAppendingString:@"\n"];
+      label.text =  s;
+  } else {
+      label.text =  [self pickerView:pickerView titleForRow:row forComponent:component];
+  }
   label.accessibilityIdentifier = _items[row][@"testID"];
     
   label.numberOfLines = _numberOfLines;
diff --git a/node_modules/@react-native-picker/picker/ios/RNCPickerManager.m b/node_modules/@react-native-picker/picker/ios/RNCPickerManager.m
index 5ee8937..1f9d232 100644
--- a/node_modules/@react-native-picker/picker/ios/RNCPickerManager.m
+++ b/node_modules/@react-native-picker/picker/ios/RNCPickerManager.m
@@ -27,6 +27,11 @@ - (UIView *)view
 RCT_EXPORT_VIEW_PROPERTY(color, UIColor)
 RCT_EXPORT_VIEW_PROPERTY(textAlign, NSTextAlignment)
 RCT_EXPORT_VIEW_PROPERTY(numberOfLines, NSInteger)
+RCT_EXPORT_VIEW_PROPERTY(customSecondLine, BOOL)
+RCT_CUSTOM_VIEW_PROPERTY(fontSize2, NSNumber, RNCPicker)
+{
+  view.font2 = [RCTFont updateFont:view.font withSize:json ?: @(defaultView.font.pointSize)];
+}
 RCT_CUSTOM_VIEW_PROPERTY(fontSize, NSNumber, RNCPicker)
 {
   view.font = [RCTFont updateFont:view.font withSize:json ?: @(defaultView.font.pointSize)];
@@ -42,6 +47,7 @@ - (UIView *)view
 RCT_CUSTOM_VIEW_PROPERTY(fontFamily, NSString, RNCPicker)
 {
   view.font = [RCTFont updateFont:view.font withFamily:json ?: defaultView.font.familyName];
+  view.font2 = [RCTFont updateFont:view.font2 withFamily:json ?: defaultView.font.familyName];
 }
 RCT_CUSTOM_VIEW_PROPERTY(themeVariant, NSString, RNCPicker)
 {
diff --git a/node_modules/@react-native-picker/picker/js/PickerIOS.ios.js b/node_modules/@react-native-picker/picker/js/PickerIOS.ios.js
index ece46ab..c24c11f 100644
--- a/node_modules/@react-native-picker/picker/js/PickerIOS.ios.js
+++ b/node_modules/@react-native-picker/picker/js/PickerIOS.ios.js
@@ -115,6 +115,8 @@ class PickerIOS extends React.Component<Props, State> {
           style={[styles.pickerIOS, this.props.itemStyle]}
           testID={this.props.testID}
           themeVariant={this.props.themeVariant}
+          fontSize2={this.props.fontSize2}
+          customSecondLine={this.props.customSecondLine}
         />
       </View>
     );
