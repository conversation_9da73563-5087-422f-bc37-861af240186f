diff --git a/node_modules/expo-screen-orientation/android/build.gradle b/node_modules/expo-screen-orientation/android/build.gradle
index 9a69d6c..0b96650 100644
--- a/node_modules/expo-screen-orientation/android/build.gradle
+++ b/node_modules/expo-screen-orientation/android/build.gradle
@@ -1,6 +1,6 @@
 apply plugin: 'com.android.library'
 apply plugin: 'kotlin-android'
-apply plugin: 'maven'
+apply plugin: 'maven-publish'
 
 group = 'host.exp.exponent'
 version = '3.3.0'
@@ -36,15 +36,6 @@ artifacts {
   archives androidSourcesJar
 }
 
-uploadArchives {
-  repositories {
-    mavenDeployer {
-      configuration = configurations.deployerJars
-      repository(url: mavenLocal().url)
-    }
-  }
-}
-
 android {
   compileSdkVersion safeExtGet("compileSdkVersion", 30)
 
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/02d128a287e6d370b8c6519532ed5e62/results.bin b/node_modules/expo-screen-orientation/android/build/.transforms/02d128a287e6d370b8c6519532ed5e62/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/02d128a287e6d370b8c6519532ed5e62/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/02d128a287e6d370b8c6519532ed5e62/transformed/classes/classes.dex b/node_modules/expo-screen-orientation/android/build/.transforms/02d128a287e6d370b8c6519532ed5e62/transformed/classes/classes.dex
new file mode 100644
index 0000000..f748f0b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/02d128a287e6d370b8c6519532ed5e62/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/149593678b0e49ef6851daf471ab022f/results.bin b/node_modules/expo-screen-orientation/android/build/.transforms/149593678b0e49ef6851daf471ab022f/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/149593678b0e49ef6851daf471ab022f/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/149593678b0e49ef6851daf471ab022f/transformed/classes/classes.dex b/node_modules/expo-screen-orientation/android/build/.transforms/149593678b0e49ef6851daf471ab022f/transformed/classes/classes.dex
new file mode 100644
index 0000000..121015f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/149593678b0e49ef6851daf471ab022f/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/results.bin b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/AndroidManifest.xml b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..1f5c110
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.screenorientation" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..d8f70c0
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,3 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
new file mode 100644
index 0000000..b60b0f0
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/META-INF/com/android/build/gradle/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=expo-screen-orientation
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/R.txt b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/jars/classes.jar b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..ae2f00d
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/24cddcd8dacb5f4880e033b82d446c35/transformed/out/jars/classes.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/results.bin b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/results.bin
new file mode 100644
index 0000000..9e53e72
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/results.bin
@@ -0,0 +1 @@
+o/release
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/desugar_graph.bin b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/desugar_graph.bin
new file mode 100644
index 0000000..601f245
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/desugar_graph.bin differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/BuildConfig.dex b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/BuildConfig.dex
new file mode 100644
index 0000000..78e6958
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/BuildConfig.dex differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/Orientation.dex b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/Orientation.dex
new file mode 100644
index 0000000..138bf72
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/Orientation.dex differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/ScreenOrientationModule.dex b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/ScreenOrientationModule.dex
new file mode 100644
index 0000000..cbb8f29
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/ScreenOrientationModule.dex differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/ScreenOrientationModuleKt.dex b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/ScreenOrientationModuleKt.dex
new file mode 100644
index 0000000..35fa1df
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/ScreenOrientationModuleKt.dex differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/ScreenOrientationPackage.dex b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/ScreenOrientationPackage.dex
new file mode 100644
index 0000000..de99cf5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/5575ffa885039a88700b9f091af68e3c/transformed/release/expo/modules/screenorientation/ScreenOrientationPackage.dex differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/74f8b547fc9c81127fad6eda1b9db536/results.bin b/node_modules/expo-screen-orientation/android/build/.transforms/74f8b547fc9c81127fad6eda1b9db536/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/74f8b547fc9c81127fad6eda1b9db536/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/74f8b547fc9c81127fad6eda1b9db536/transformed/classes/classes.dex b/node_modules/expo-screen-orientation/android/build/.transforms/74f8b547fc9c81127fad6eda1b9db536/transformed/classes/classes.dex
new file mode 100644
index 0000000..ddf5262
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/74f8b547fc9c81127fad6eda1b9db536/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/7d479a4f83bb6930bf86109a77d2e319/results.bin b/node_modules/expo-screen-orientation/android/build/.transforms/7d479a4f83bb6930bf86109a77d2e319/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/7d479a4f83bb6930bf86109a77d2e319/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/results.bin b/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/AndroidManifest.xml b/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..1f5c110
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.screenorientation" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/R.txt b/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/jars/classes.jar b/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..7fb427b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/edf4e63689cce9585d2051a9bb699353/transformed/out/jars/classes.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/efc7e5e6f66442db80242258726dddfc/results.bin b/node_modules/expo-screen-orientation/android/build/.transforms/efc7e5e6f66442db80242258726dddfc/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/.transforms/efc7e5e6f66442db80242258726dddfc/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/expo-screen-orientation/android/build/.transforms/efc7e5e6f66442db80242258726dddfc/transformed/classes/classes.dex b/node_modules/expo-screen-orientation/android/build/.transforms/efc7e5e6f66442db80242258726dddfc/transformed/classes/classes.dex
new file mode 100644
index 0000000..32956f0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/.transforms/efc7e5e6f66442db80242258726dddfc/transformed/classes/classes.dex differ
diff --git a/node_modules/expo-screen-orientation/android/build/generated/source/buildConfig/debug/expo/modules/screenorientation/BuildConfig.java b/node_modules/expo-screen-orientation/android/build/generated/source/buildConfig/debug/expo/modules/screenorientation/BuildConfig.java
new file mode 100644
index 0000000..ac29f53
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/generated/source/buildConfig/debug/expo/modules/screenorientation/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package expo.modules.screenorientation;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "expo.modules.screenorientation";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/expo-screen-orientation/android/build/generated/source/buildConfig/release/expo/modules/screenorientation/BuildConfig.java b/node_modules/expo-screen-orientation/android/build/generated/source/buildConfig/release/expo/modules/screenorientation/BuildConfig.java
new file mode 100644
index 0000000..138764a
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/generated/source/buildConfig/release/expo/modules/screenorientation/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package expo.modules.screenorientation;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "expo.modules.screenorientation";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..1f5c110
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.screenorientation" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..9124869
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules.screenorientation",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..1f5c110
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.screenorientation" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..ccb6968
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules.screenorientation",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/aar_main_jar/release/classes.jar b/node_modules/expo-screen-orientation/android/build/intermediates/aar_main_jar/release/classes.jar
new file mode 100644
index 0000000..7fb427b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/aar_main_jar/release/classes.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/expo-screen-orientation/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/expo-screen-orientation/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..8c9c699
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,4 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/expo-screen-orientation/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/expo-screen-orientation/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/annotations_typedef_file/release/typedefs.txt b/node_modules/expo-screen-orientation/android/build/intermediates/annotations_typedef_file/release/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/expo-screen-orientation/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..ab8b984
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/expo-screen-orientation/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..5406dd7
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/compile_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/expo-screen-orientation/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..3193388
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/expo-screen-orientation/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..3193388
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/compile_r_class_jar/release/R.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/expo-screen-orientation/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/expo-screen-orientation/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/full_jar/release/full.jar b/node_modules/expo-screen-orientation/android/build/intermediates/full_jar/release/full.jar
new file mode 100644
index 0000000..8357191
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/full_jar/release/full.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..8d93e21
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..e8d31be
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..27b06b1
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..2875b45
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..a243940
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/intermediates/shader_assets/debug/out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/debug/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..ed842ca
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:34:32 ICT 2022
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageDebugResources/merger.xml b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageDebugResources/merger.xml
new file mode 100644
index 0000000..b09f75b
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/generated/res/rs/debug"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/debug/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..beea9e3
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/assets"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/intermediates/shader_assets/release/out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/release/assets"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..6483d4a
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Oct 12 17:16:21 ICT 2022
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageReleaseResources/merger.xml b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..20d7d6d
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/packageReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/res"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/generated/res/rs/release"/><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/release/res"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/incremental/release-mergeJavaRes/merge-state b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/release-mergeJavaRes/merge-state
new file mode 100644
index 0000000..8870f07
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/incremental/release-mergeJavaRes/merge-state differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/javac/debug/classes/expo/modules/screenorientation/BuildConfig.class b/node_modules/expo-screen-orientation/android/build/intermediates/javac/debug/classes/expo/modules/screenorientation/BuildConfig.class
new file mode 100644
index 0000000..871bacf
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/javac/debug/classes/expo/modules/screenorientation/BuildConfig.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/javac/release/classes/expo/modules/screenorientation/BuildConfig.class b/node_modules/expo-screen-orientation/android/build/intermediates/javac/release/classes/expo/modules/screenorientation/BuildConfig.class
new file mode 100644
index 0000000..7131895
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/javac/release/classes/expo/modules/screenorientation/BuildConfig.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/library_java_res/debug/res.jar b/node_modules/expo-screen-orientation/android/build/intermediates/library_java_res/debug/res.jar
new file mode 100644
index 0000000..706e0c8
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/library_java_res/debug/res.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/library_java_res/release/res.jar b/node_modules/expo-screen-orientation/android/build/intermediates/library_java_res/release/res.jar
new file mode 100644
index 0000000..89508ca
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/library_java_res/release/res.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties b/node_modules/expo-screen-orientation/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
new file mode 100644
index 0000000..b60b0f0
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=expo-screen-orientation
+mavenGroupId=host.exp.exponent
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/local_aar_for_lint/release/out.aar b/node_modules/expo-screen-orientation/android/build/intermediates/local_aar_for_lint/release/out.aar
new file mode 100644
index 0000000..5eee692
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/local_aar_for_lint/release/out.aar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/expo-screen-orientation/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/expo-screen-orientation/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/expo-screen-orientation/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..2b758e5
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="expo.modules.screenorientation" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/expo-screen-orientation/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..2b758e5
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,11 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="expo.modules.screenorientation" >
+4
+5    <uses-sdk
+6        android:minSdkVersion="21"
+6-->/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+7        android:targetSdkVersion="31" />
+7-->/Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+8
+9</manifest>
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/merged_java_res/release/feature-expo-screen-orientation.jar b/node_modules/expo-screen-orientation/android/build/intermediates/merged_java_res/release/feature-expo-screen-orientation.jar
new file mode 100644
index 0000000..a8f856d
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/merged_java_res/release/feature-expo-screen-orientation.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/expo-screen-orientation/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..1f5c110
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.screenorientation" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/expo-screen-orientation/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..1f5c110
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="expo.modules.screenorientation" >
+
+    <uses-sdk
+        android:minSdkVersion="21"
+        android:targetSdkVersion="31" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/expo-screen-orientation/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/expo-screen-orientation/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/expo-screen-orientation/android/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..1712b51
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules.screenorientation",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/packaged_manifests/release/output-metadata.json b/node_modules/expo-screen-orientation/android/build/intermediates/packaged_manifests/release/output-metadata.json
new file mode 100644
index 0000000..510397e
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/packaged_manifests/release/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "expo.modules.screenorientation",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/release/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/META-INF/expo-screen-orientation_release.kotlin_module b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/META-INF/expo-screen-orientation_release.kotlin_module
new file mode 100644
index 0000000..f0a14a0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/META-INF/expo-screen-orientation_release.kotlin_module differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/BuildConfig.class b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/BuildConfig.class
new file mode 100644
index 0000000..761a693
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/BuildConfig.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/Orientation.class b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/Orientation.class
new file mode 100644
index 0000000..bfaff60
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/Orientation.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/ScreenOrientationModule.class b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/ScreenOrientationModule.class
new file mode 100644
index 0000000..481e55f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/ScreenOrientationModule.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/ScreenOrientationModuleKt.class b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/ScreenOrientationModuleKt.class
new file mode 100644
index 0000000..32e0659
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/ScreenOrientationModuleKt.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/ScreenOrientationPackage.class b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/ScreenOrientationPackage.class
new file mode 100644
index 0000000..6b857cb
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_dir/release/expo/modules/screenorientation/ScreenOrientationPackage.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..08b485d
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..c572e28
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/intermediates/runtime_library_classes_jar/release/classes.jar differ
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/expo-screen-orientation/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..aeacc2b
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1 @@
+expo.modules.screenorientation
diff --git a/node_modules/expo-screen-orientation/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/expo-screen-orientation/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..aeacc2b
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1 @@
+expo.modules.screenorientation
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/build-history.bin b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/build-history.bin
new file mode 100644
index 0000000..346cc51
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/build-history.bin differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab
new file mode 100644
index 0000000..02c4e0d
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream
new file mode 100644
index 0000000..44ea98c
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len
new file mode 100644
index 0000000..5d40ee5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.values.at
new file mode 100644
index 0000000..ef42240
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i
new file mode 100644
index 0000000..dc80f5a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/inputs/source-to-output.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab
new file mode 100644
index 0000000..c9ca6f2
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
new file mode 100644
index 0000000..e10b83a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
new file mode 100644
index 0000000..8d116c5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
new file mode 100644
index 0000000..fe23964
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i
new file mode 100644
index 0000000..0be5ed0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
new file mode 100644
index 0000000..ea950cc
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
new file mode 100644
index 0000000..e10b83a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..8d116c5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
new file mode 100644
index 0000000..287c79f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
new file mode 100644
index 0000000..0be5ed0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab
new file mode 100644
index 0000000..d4a74a7
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
new file mode 100644
index 0000000..7bc4f85
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..68b143b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
new file mode 100644
index 0000000..93a595b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
new file mode 100644
index 0000000..970a093
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
new file mode 100644
index 0000000..57cc84c
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream
new file mode 100644
index 0000000..5340cac
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len
new file mode 100644
index 0000000..00880dd
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.values.at
new file mode 100644
index 0000000..46d6744
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i
new file mode 100644
index 0000000..361763e
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab
new file mode 100644
index 0000000..4c7dc2e
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream
new file mode 100644
index 0000000..f3b7138
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len
new file mode 100644
index 0000000..77d97e9
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.len
new file mode 100644
index 0000000..ec8f944
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at
new file mode 100644
index 0000000..2abe1ea
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i
new file mode 100644
index 0000000..d6d3c09
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab
new file mode 100644
index 0000000..fd035e0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
new file mode 100644
index 0000000..b2202b2
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
new file mode 100644
index 0000000..5d40ee5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
new file mode 100644
index 0000000..89a8799
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i
new file mode 100644
index 0000000..da00901
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab
new file mode 100644
index 0000000..68588d3
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream
new file mode 100644
index 0000000..629d82d
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len
new file mode 100644
index 0000000..39be582
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.len
new file mode 100644
index 0000000..93a595b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.values.at
new file mode 100644
index 0000000..9f48565
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i
new file mode 100644
index 0000000..5d4a74f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab
new file mode 100644
index 0000000..b5a8db1
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream
new file mode 100644
index 0000000..e10b83a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len
new file mode 100644
index 0000000..8d116c5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.values.at
new file mode 100644
index 0000000..15b473a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i
new file mode 100644
index 0000000..0be5ed0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/counters.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/counters.tab
new file mode 100644
index 0000000..c393a51
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/counters.tab
@@ -0,0 +1,2 @@
+3
+0
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab
new file mode 100644
index 0000000..d9b323b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream
new file mode 100644
index 0000000..b2202b2
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len
new file mode 100644
index 0000000..5d40ee5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.values.at
new file mode 100644
index 0000000..9f383b5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i
new file mode 100644
index 0000000..f3cda61
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/file-to-id.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab
new file mode 100644
index 0000000..c4c7e2e
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream
new file mode 100644
index 0000000..636f34a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len
new file mode 100644
index 0000000..29ce11c
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.values.at
new file mode 100644
index 0000000..287c79f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab_i
new file mode 100644
index 0000000..e9905b3
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/id-to-file.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab
new file mode 100644
index 0000000..a2c2199
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream
new file mode 100644
index 0000000..6f55d29
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream.len
new file mode 100644
index 0000000..5232a92
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.len
new file mode 100644
index 0000000..1f1ecbb
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.values.at
new file mode 100644
index 0000000..c32db10
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i
new file mode 100644
index 0000000..3713ad1
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/caches-jvm/lookups/lookups.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/last-build.bin b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/last-build.bin
new file mode 100644
index 0000000..683cb3c
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileDebugKotlin/last-build.bin differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/build-history.bin b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/build-history.bin
new file mode 100644
index 0000000..fe8f5de
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/build-history.bin differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab
new file mode 100644
index 0000000..6b20bae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream
new file mode 100644
index 0000000..44ea98c
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len
new file mode 100644
index 0000000..5d40ee5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.values.at
new file mode 100644
index 0000000..b1b2600
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i
new file mode 100644
index 0000000..dc80f5a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/inputs/source-to-output.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab
new file mode 100644
index 0000000..c9ca6f2
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream
new file mode 100644
index 0000000..e10b83a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len
new file mode 100644
index 0000000..8d116c5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at
new file mode 100644
index 0000000..fe23964
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i
new file mode 100644
index 0000000..0be5ed0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-attributes.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab
new file mode 100644
index 0000000..ea950cc
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream
new file mode 100644
index 0000000..e10b83a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..8d116c5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at
new file mode 100644
index 0000000..287c79f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i
new file mode 100644
index 0000000..0be5ed0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab
new file mode 100644
index 0000000..d4a74a7
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream
new file mode 100644
index 0000000..7bc4f85
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len
new file mode 100644
index 0000000..68b143b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len
new file mode 100644
index 0000000..93a595b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at
new file mode 100644
index 0000000..970a093
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i
new file mode 100644
index 0000000..57cc84c
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab
new file mode 100644
index 0000000..bdf584a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream
new file mode 100644
index 0000000..5340cac
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len
new file mode 100644
index 0000000..00880dd
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.len
new file mode 100644
index 0000000..2a17e6e
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.values.at
new file mode 100644
index 0000000..46d6744
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i
new file mode 100644
index 0000000..361763e
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/package-parts.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab
new file mode 100644
index 0000000..c899f4d
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream
new file mode 100644
index 0000000..f3b7138
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len
new file mode 100644
index 0000000..77d97e9
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.len
new file mode 100644
index 0000000..ec8f944
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at
new file mode 100644
index 0000000..33fa10f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i
new file mode 100644
index 0000000..d6d3c09
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/proto.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab
new file mode 100644
index 0000000..fd035e0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream
new file mode 100644
index 0000000..b2202b2
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len
new file mode 100644
index 0000000..5d40ee5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at
new file mode 100644
index 0000000..89a8799
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i
new file mode 100644
index 0000000..da00901
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab
new file mode 100644
index 0000000..68588d3
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream
new file mode 100644
index 0000000..629d82d
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len
new file mode 100644
index 0000000..39be582
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.len
new file mode 100644
index 0000000..93a595b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.values.at
new file mode 100644
index 0000000..9f48565
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i
new file mode 100644
index 0000000..5d4a74f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/subtypes.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab
new file mode 100644
index 0000000..b5a8db1
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream
new file mode 100644
index 0000000..e10b83a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len
new file mode 100644
index 0000000..8d116c5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.values.at
new file mode 100644
index 0000000..15b473a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i
new file mode 100644
index 0000000..0be5ed0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/jvm/kotlin/supertypes.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/counters.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/counters.tab
new file mode 100644
index 0000000..c393a51
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/counters.tab
@@ -0,0 +1,2 @@
+3
+0
\ No newline at end of file
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab
new file mode 100644
index 0000000..d9b323b
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream
new file mode 100644
index 0000000..b2202b2
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len
new file mode 100644
index 0000000..5d40ee5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.values.at
new file mode 100644
index 0000000..9f383b5
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i
new file mode 100644
index 0000000..f3cda61
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/file-to-id.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab
new file mode 100644
index 0000000..c4c7e2e
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream
new file mode 100644
index 0000000..636f34a
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len
new file mode 100644
index 0000000..29ce11c
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.len
new file mode 100644
index 0000000..a9f80ae
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.values.at
new file mode 100644
index 0000000..287c79f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab_i
new file mode 100644
index 0000000..e9905b3
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/id-to-file.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab
new file mode 100644
index 0000000..a2c2199
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream
new file mode 100644
index 0000000..6f55d29
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream.len
new file mode 100644
index 0000000..5232a92
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.keystream.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.len
new file mode 100644
index 0000000..1f1ecbb
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.values.at b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.values.at
new file mode 100644
index 0000000..c32db10
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab.values.at differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i
new file mode 100644
index 0000000..3713ad1
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i.len b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i.len
new file mode 100644
index 0000000..131e265
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/caches-jvm/lookups/lookups.tab_i.len differ
diff --git a/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/last-build.bin b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/last-build.bin
new file mode 100644
index 0000000..f7aad73
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/kotlin/compileReleaseKotlin/last-build.bin differ
diff --git a/node_modules/expo-screen-orientation/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/expo-screen-orientation/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..81f7d48
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:1-2:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:1-2:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:1-2:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:11-51
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:1-2:12
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
diff --git a/node_modules/expo-screen-orientation/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/expo-screen-orientation/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..81f7d48
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,25 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:1-2:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:1-2:12
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:1-2:12
+	package
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:11-51
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml:1:1-2:12
+uses-sdk
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		ADDED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
+		INJECTED from /Users/<USER>/mytmplusapp/node_modules/expo-screen-orientation/android/src/main/AndroidManifest.xml
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/expo-screen-orientation/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..e79e082
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin b/node_modules/expo-screen-orientation/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..f368eb2
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt b/node_modules/expo-screen-orientation/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
new file mode 100644
index 0000000..b918722
--- /dev/null
+++ b/node_modules/expo-screen-orientation/android/build/tmp/compileReleaseJavaWithJavac/source-classes-mapping.txt
@@ -0,0 +1,2 @@
+expo/modules/screenorientation/BuildConfig.java
+ expo.modules.screenorientation.BuildConfig
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/META-INF/expo-screen-orientation_debug.kotlin_module b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/META-INF/expo-screen-orientation_debug.kotlin_module
new file mode 100644
index 0000000..f0a14a0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/META-INF/expo-screen-orientation_debug.kotlin_module differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/Orientation.class b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/Orientation.class
new file mode 100644
index 0000000..3c33b71
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/Orientation.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/ScreenOrientationModule.class b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/ScreenOrientationModule.class
new file mode 100644
index 0000000..8255bbe
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/ScreenOrientationModule.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/ScreenOrientationModuleKt.class b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/ScreenOrientationModuleKt.class
new file mode 100644
index 0000000..2be1ccf
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/ScreenOrientationModuleKt.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/ScreenOrientationPackage.class b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/ScreenOrientationPackage.class
new file mode 100644
index 0000000..b9329ed
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/debug/expo/modules/screenorientation/ScreenOrientationPackage.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/META-INF/expo-screen-orientation_release.kotlin_module b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/META-INF/expo-screen-orientation_release.kotlin_module
new file mode 100644
index 0000000..f0a14a0
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/META-INF/expo-screen-orientation_release.kotlin_module differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/Orientation.class b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/Orientation.class
new file mode 100644
index 0000000..bfaff60
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/Orientation.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/ScreenOrientationModule.class b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/ScreenOrientationModule.class
new file mode 100644
index 0000000..481e55f
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/ScreenOrientationModule.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/ScreenOrientationModuleKt.class b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/ScreenOrientationModuleKt.class
new file mode 100644
index 0000000..32e0659
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/ScreenOrientationModuleKt.class differ
diff --git a/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/ScreenOrientationPackage.class b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/ScreenOrientationPackage.class
new file mode 100644
index 0000000..6b857cb
Binary files /dev/null and b/node_modules/expo-screen-orientation/android/build/tmp/kotlin-classes/release/expo/modules/screenorientation/ScreenOrientationPackage.class differ
