require_relative '../node_modules/react-native/scripts/react_native_pods'

# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

plugin 'cocoapods-patch'

target 'TaylorMade' do
  config = use_native_modules!
  # ENV['RCT_NEW_ARCH_ENABLED'] = '1'
  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => false
  )

  permissions_path = '../node_modules/react-native-permissions/ios'

  pod 'react-native-webview', :path => '../node_modules/react-native-webview'
  pod 'Permission-LocationAlways', :path => "#{permissions_path}/LocationAlways"
  pod 'Permission-LocationWhenInUse', :path => "#{permissions_path}/LocationWhenInUse"
  pod 'Permission-Camera', :path => "#{permissions_path}/Camera"
  pod 'Permission-PhotoLibraryAddOnly', :path => "#{permissions_path}/PhotoLibraryAddOnly"
  pod 'Permission-Microphone', :path => "#{permissions_path}/Microphone"
  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-google-maps', :path => rn_maps_path
  pod 'RSClipperWrapper', :git => 'https://github.com/rusty1s/RSClipperWrapper.git'
  pod 'Permission-Notifications', :path => "#{permissions_path}/Notifications"
  pod 'Permission-PhotoLibrary', :path => "#{permissions_path}/PhotoLibrary"

  pod 'CodePush', :path => '../node_modules/react-native-code-push'

  pod 'react-native-version-check', :path => '../node_modules/react-native-version-check'

  pod "KlaviyoSwift", '~> 2.2.1'
  
  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
       config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.1'
       config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = "arm64"
      end
      if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
        target.build_configurations.each do |config|
            config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        end
      end

    end
    react_native_post_install(
      installer, 
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
      )
  end
end

workspace 'TaylorMade.xcworkspace'

def available_pods_watch
    
#    pod 'WatchActiviyIndicator'
    pod 'AFNetworking'
    pod 'JSONModel'
#    pod 'Realm', '~> 2.8.3'
#    pod 'Realm'
    pod 'Mantle'
    pod 'YOChartImageKit', '~> 1.1'
    
end

target 'MyTMWatch WatchKit Extension' do
    platform :watchos, '4.0'

    available_pods_watch
end


target 'NotificationServiceExtension' do
  pod 'KlaviyoSwiftExtension', '2.1.0-beta1'
end