// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		41D15A8F55DAF4B51F23882F /* libPods-MyTMWatch WatchKit Extension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B2955E4BDD55B318A48D6BD1 /* libPods-MyTMWatch WatchKit Extension.a */; };
		6635D6A48A3A67DCC7CC4329 /* libPods-TaylorMade.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 1592A196DF96064F4E941DC8 /* libPods-TaylorMade.a */; };
		69B17CAA4C224E20B0BFB5CF /* SF-Pro.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7530263CB229425EBE3E1C06 /* SF-Pro.ttf */; };
		7724BA3926AA313D00A256D9 /* MyTaylorMade.entitlements in Resources */ = {isa = PBXBuildFile; fileRef = 777DAC502652EACD009FC1A4 /* MyTaylorMade.entitlements */; };
		777DAC4F2652EABB009FC1A4 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 777DAC4E2652EABB009FC1A4 /* StoreKit.framework */; };
		77C74553276BEE8E0054F4BB /* File.swift in Sources */ = {isa = PBXBuildFile; fileRef = 77C74552276BEE8E0054F4BB /* File.swift */; };
		77CEEEF6264F1AA500F32FB1 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 77CEEEF5264F1AA500F32FB1 /* GoogleService-Info.plist */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		C9FD95F8B969BD88E6689E7D /* libPods-NotificationServiceExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5929BD80CE7C87340F619298 /* libPods-NotificationServiceExtension.a */; };
		D93841F42AD52C4400B4E3E7 /* NotificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = D93841F32AD52C4400B4E3E7 /* NotificationService.swift */; };
		D93841F82AD52C4400B4E3E7 /* NotificationServiceExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = D93841F12AD52C4400B4E3E7 /* NotificationServiceExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		E725168C2A497B51005A15B2 /* KlaviyoEventEmitter.m in Sources */ = {isa = PBXBuildFile; fileRef = E725168B2A497B51005A15B2 /* KlaviyoEventEmitter.m */; };
		E72DE2B7289BBCDB00A93733 /* MRPAddRoundClassicInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE233289BBCDB00A93733 /* MRPAddRoundClassicInterfaceController.m */; };
		E72DE2B8289BBCDB00A93733 /* MRPClassicRoundStatsInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE236289BBCDB00A93733 /* MRPClassicRoundStatsInterfaceController.m */; };
		E72DE2B9289BBCDB00A93733 /* MRPClassicScorecardInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE239289BBCDB00A93733 /* MRPClassicScorecardInterfaceController.m */; };
		E72DE2BA289BBCDB00A93733 /* MRPClassicScorecardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE23B289BBCDB00A93733 /* MRPClassicScorecardCell.m */; };
		E72DE2BB289BBCDB00A93733 /* MRPClassicFooterScoreCardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE23C289BBCDB00A93733 /* MRPClassicFooterScoreCardCell.m */; };
		E72DE2BC289BBCDB00A93733 /* MRPHoleCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE23F289BBCDB00A93733 /* MRPHoleCell.m */; };
		E72DE2BD289BBCDB00A93733 /* MRPScorecardHeaderCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE240289BBCDB00A93733 /* MRPScorecardHeaderCell.m */; };
		E72DE2BE289BBCDB00A93733 /* MRPNearByCourseCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE241289BBCDB00A93733 /* MRPNearByCourseCell.m */; };
		E72DE2BF289BBCDB00A93733 /* MRPEndHoleCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE243289BBCDB00A93733 /* MRPEndHoleCell.m */; };
		E72DE2C0289BBCDB00A93733 /* MRPTeeCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE244289BBCDB00A93733 /* MRPTeeCell.m */; };
		E72DE2C1289BBCDB00A93733 /* MRPPutterClubCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE249289BBCDB00A93733 /* MRPPutterClubCell.m */; };
		E72DE2C2289BBCDB00A93733 /* MRPListClubCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE24B289BBCDB00A93733 /* MRPListClubCell.m */; };
		E72DE2C3289BBCDB00A93733 /* MRPScorecardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE24C289BBCDB00A93733 /* MRPScorecardCell.m */; };
		E72DE2C4289BBCDB00A93733 /* MRPStrokeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE253289BBCDB00A93733 /* MRPStrokeModel.m */; };
		E72DE2C5289BBCDB00A93733 /* MRPLocation2DModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE254289BBCDB00A93733 /* MRPLocation2DModel.m */; };
		E72DE2C6289BBCDB00A93733 /* MRPRoundLieModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE257289BBCDB00A93733 /* MRPRoundLieModel.m */; };
		E72DE2C7289BBCDB00A93733 /* MRPPoint.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE258289BBCDB00A93733 /* MRPPoint.m */; };
		E72DE2C8289BBCDB00A93733 /* MRPPropertyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE25A289BBCDB00A93733 /* MRPPropertyModel.m */; };
		E72DE2C9289BBCDB00A93733 /* MRPTeeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE25B289BBCDB00A93733 /* MRPTeeModel.m */; };
		E72DE2CA289BBCDB00A93733 /* MRPHolePlayerMetadataModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE25D289BBCDB00A93733 /* MRPHolePlayerMetadataModel.m */; };
		E72DE2CB289BBCDB00A93733 /* MRPHoleModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE25E289BBCDB00A93733 /* MRPHoleModel.m */; };
		E72DE2CC289BBCDB00A93733 /* MRPCourseModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE260289BBCDB00A93733 /* MRPCourseModel.m */; };
		E72DE2CD289BBCDB00A93733 /* MRPGeometryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE262289BBCDB00A93733 /* MRPGeometryModel.m */; };
		E72DE2CE289BBCDB00A93733 /* MRPRoundModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE263289BBCDB00A93733 /* MRPRoundModel.m */; };
		E72DE2CF289BBCDB00A93733 /* MRPRealmStringModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE264289BBCDB00A93733 /* MRPRealmStringModel.m */; };
		E72DE2D0289BBCDB00A93733 /* MRPFeatureModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE266289BBCDB00A93733 /* MRPFeatureModel.m */; };
		E72DE2D1289BBCDB00A93733 /* MRPFeaturesModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE267289BBCDB00A93733 /* MRPFeaturesModel.m */; };
		E72DE2D2289BBCDB00A93733 /* MRPLocationsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE26A289BBCDB00A93733 /* MRPLocationsModel.m */; };
		E72DE2D3289BBCDB00A93733 /* MRPClubsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE26C289BBCDB00A93733 /* MRPClubsModel.m */; };
		E72DE2D4289BBCDB00A93733 /* MRPTeeboxBoundaryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE26F289BBCDB00A93733 /* MRPTeeboxBoundaryModel.m */; };
		E72DE2D5289BBCDB00A93733 /* MRPCourseConditionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE271289BBCDB00A93733 /* MRPCourseConditionModel.m */; };
		E72DE2D6289BBCDB00A93733 /* MRPRoundPlayerMetadataModel.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE273289BBCDB00A93733 /* MRPRoundPlayerMetadataModel.m */; };
		E72DE2D7289BBCDB00A93733 /* MRPWatchApiHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE278289BBCDB00A93733 /* MRPWatchApiHandler.m */; };
		E72DE2D8289BBCDB00A93733 /* MRPWatchRoundManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE27D289BBCDB00A93733 /* MRPWatchRoundManager.m */; };
		E72DE2D9289BBCDB00A93733 /* MRPWatchBaseBackgrounder.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE27E289BBCDB00A93733 /* MRPWatchBaseBackgrounder.m */; };
		E72DE2DA289BBCDB00A93733 /* MRPRoundData.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE27F289BBCDB00A93733 /* MRPRoundData.m */; };
		E72DE2DB289BBCDB00A93733 /* MRPWatchLocationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE281289BBCDB00A93733 /* MRPWatchLocationManager.m */; };
		E72DE2DC289BBCDB00A93733 /* MRPHoleInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE283289BBCDB00A93733 /* MRPHoleInterfaceController.m */; };
		E72DE2DD289BBCDB00A93733 /* MRPPlayRoundInfoInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE286289BBCDB00A93733 /* MRPPlayRoundInfoInterfaceController.m */; };
		E72DE2DE289BBCDB00A93733 /* MRPAboutInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE287289BBCDB00A93733 /* MRPAboutInterfaceController.m */; };
		E72DE2DF289BBCDB00A93733 /* MRPScorecardInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE288289BBCDB00A93733 /* MRPScorecardInterfaceController.m */; };
		E72DE2E0289BBCDB00A93733 /* MRPListTeeInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE28B289BBCDB00A93733 /* MRPListTeeInterfaceController.m */; };
		E72DE2E1289BBCDB00A93733 /* MRPConfirmAddClubInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE28F289BBCDB00A93733 /* MRPConfirmAddClubInterfaceController.m */; };
		E72DE2E2289BBCDB00A93733 /* MRPErrorInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE290289BBCDB00A93733 /* MRPErrorInterfaceController.m */; };
		E72DE2E3289BBCDB00A93733 /* MRPErrorLocationInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE291289BBCDB00A93733 /* MRPErrorLocationInterfaceController.m */; };
		E72DE2E4289BBCDB00A93733 /* MRPConfirmInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE294289BBCDB00A93733 /* MRPConfirmInterfaceController.m */; };
		E72DE2E5289BBCDB00A93733 /* MRPSummaryInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE295289BBCDB00A93733 /* MRPSummaryInterfaceController.m */; };
		E72DE2E6289BBCDB00A93733 /* MRPAddShotInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE298289BBCDB00A93733 /* MRPAddShotInterfaceController.m */; };
		E72DE2E7289BBCDB00A93733 /* MRPListClubInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE29B289BBCDB00A93733 /* MRPListClubInterfaceController.m */; };
		E72DE2E8289BBCDB00A93733 /* MRPHomeInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE29C289BBCDB00A93733 /* MRPHomeInterfaceController.m */; };
		E72DE2E9289BBCDB00A93733 /* MRPListModeInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE29E289BBCDB00A93733 /* MRPListModeInterfaceController.m */; };
		E72DE2EA289BBCDB00A93733 /* MRPSelectScoreInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE29F289BBCDB00A93733 /* MRPSelectScoreInterfaceController.m */; };
		E72DE2EB289BBCDB00A93733 /* MRPListTypeInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE2A2289BBCDB00A93733 /* MRPListTypeInterfaceController.m */; };
		E72DE2EC289BBCDB00A93733 /* MRPNearByCourseInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE2A5289BBCDB00A93733 /* MRPNearByCourseInterfaceController.m */; };
		E72DE2ED289BBCDB00A93733 /* MRPResumeRoundInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE2A6289BBCDB00A93733 /* MRPResumeRoundInterfaceController.m */; };
		E72DE2EE289BBCDB00A93733 /* WKInterfaceController+Animation.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE2A9289BBCDB00A93733 /* WKInterfaceController+Animation.m */; };
		E72DE2EF289BBCDB00A93733 /* WKInterfaceLabel+Animation.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE2AA289BBCDB00A93733 /* WKInterfaceLabel+Animation.m */; };
		E72DE2F0289BBCDB00A93733 /* MRPWatchUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE2AD289BBCDB00A93733 /* MRPWatchUtils.m */; };
		E72DE2F1289BBCDB00A93733 /* WKInterfaceGroup+Indicator.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE2B1289BBCDB00A93733 /* WKInterfaceGroup+Indicator.m */; };
		E72DE2F2289BBCDB00A93733 /* MRPBaseInterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE2B6289BBCDB00A93733 /* MRPBaseInterfaceController.m */; };
		E72DE30D289BC0E200A93733 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E72DE30C289BC0E200A93733 /* SystemConfiguration.framework */; };
		E72DE320289BC1A400A93733 /* HMAC.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE314289BC1A400A93733 /* HMAC.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		E72DE321289BC1A400A93733 /* HMAC.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE314289BC1A400A93733 /* HMAC.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		E72DE322289BC1A400A93733 /* GTMBase64.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE319289BC1A400A93733 /* GTMBase64.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		E72DE323289BC1A400A93733 /* GTMBase64.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE319289BC1A400A93733 /* GTMBase64.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		E72DE328289BC1F000A93733 /* MRPScorecardInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE326289BC1F000A93733 /* MRPScorecardInfo.m */; };
		E72DE329289BC1F000A93733 /* MRPScorecardInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE326289BC1F000A93733 /* MRPScorecardInfo.m */; };
		E72DE32A289BC1F000A93733 /* MRPIconFont.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE327289BC1F000A93733 /* MRPIconFont.m */; };
		E72DE32B289BC1F000A93733 /* MRPIconFont.m in Sources */ = {isa = PBXBuildFile; fileRef = E72DE327289BC1F000A93733 /* MRPIconFont.m */; };
		E72DE330289BC6D100A93733 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = E72DE32F289BC6D100A93733 /* Assets.xcassets */; };
		E7345D6E291BB1DD00FAE5D7 /* SF-Pro.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7530263CB229425EBE3E1C06 /* SF-Pro.ttf */; };
		E7345D6F291BB1DE00FAE5D7 /* SF-Pro.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7530263CB229425EBE3E1C06 /* SF-Pro.ttf */; };
		E7345D70291BB1E200FAE5D7 /* SF-Pro-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F95DD0C927F449AF00489D4B /* SF-Pro-Italic.ttf */; };
		E7345D71291BB1E200FAE5D7 /* SF-Pro-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F95DD0C927F449AF00489D4B /* SF-Pro-Italic.ttf */; };
		E73EEBDB289B8A28001E4EDB /* MyTMWatch WatchKit Extension.appex in Embed App Extensions */ = {isa = PBXBuildFile; fileRef = E73EEBDA289B8A28001E4EDB /* MyTMWatch WatchKit Extension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		E73EEBE1289B8A28001E4EDB /* InterfaceController.m in Sources */ = {isa = PBXBuildFile; fileRef = E73EEBE0289B8A28001E4EDB /* InterfaceController.m */; };
		E73EEBE4289B8A28001E4EDB /* ExtensionDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = E73EEBE3289B8A28001E4EDB /* ExtensionDelegate.m */; };
		E73EEBEC289B8A29001E4EDB /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = E73EEBEB289B8A29001E4EDB /* Assets.xcassets */; };
		E73EEC07289B8A29001E4EDB /* Staging_MyTMWatch.app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = E73EEBCF289B8A27001E4EDB /* Staging_MyTMWatch.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		E77BDD9028A520AA005B946E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = E72DE32F289BC6D100A93733 /* Assets.xcassets */; };
		E77BDD9728A524AF005B946E /* Interface.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E77BDD9528A524AF005B946E /* Interface.storyboard */; };
		E77BDD9828A524AF005B946E /* Interface.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E77BDD9528A524AF005B946E /* Interface.storyboard */; };
		E7B6F5C828978704002B127F /* AdSupport.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E7B6F5C728978704002B127F /* AdSupport.framework */; };
		E7DE56B5291CA67500EA081D /* NewYork-Regular-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F957F9DB27F6F23300AE30D2 /* NewYork-Regular-Italic.ttf */; };
		E7DE56B6291CA67B00EA081D /* NewYork-Regular-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F957F9DB27F6F23300AE30D2 /* NewYork-Regular-Italic.ttf */; };
		E7DE56B7291CA67E00EA081D /* DINNextLTPro-BoldCondensed.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9280BF1530EF446C8A333924 /* DINNextLTPro-BoldCondensed.ttf */; };
		E7DE56B8291CA67E00EA081D /* DINNextLTPro-BoldCondensed.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9280BF1530EF446C8A333924 /* DINNextLTPro-BoldCondensed.ttf */; };
		E7DE56C6291CA6E900EA081D /* TMMRPSprint2.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56B9291CA6E700EA081D /* TMMRPSprint2.ttf */; };
		E7DE56C7291CA6E900EA081D /* TMMRPSprint2.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56B9291CA6E700EA081D /* TMMRPSprint2.ttf */; };
		E7DE56C8291CA6E900EA081D /* TMMRPSprint5.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BA291CA6E700EA081D /* TMMRPSprint5.ttf */; };
		E7DE56C9291CA6E900EA081D /* TMMRPSprint5.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BA291CA6E700EA081D /* TMMRPSprint5.ttf */; };
		E7DE56CA291CA6E900EA081D /* icomoon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BB291CA6E700EA081D /* icomoon.ttf */; };
		E7DE56CB291CA6E900EA081D /* icomoon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BB291CA6E700EA081D /* icomoon.ttf */; };
		E7DE56CE291CA6E900EA081D /* ClubHaus-Oblique.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BD291CA6E800EA081D /* ClubHaus-Oblique.ttf */; };
		E7DE56CF291CA6E900EA081D /* ClubHaus-Oblique.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BD291CA6E800EA081D /* ClubHaus-Oblique.ttf */; };
		E7DE56D0291CA6E900EA081D /* ClubHaus-BoldOblique.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BE291CA6E800EA081D /* ClubHaus-BoldOblique.ttf */; };
		E7DE56D1291CA6E900EA081D /* ClubHaus-BoldOblique.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BE291CA6E800EA081D /* ClubHaus-BoldOblique.ttf */; };
		E7DE56D2291CA6E900EA081D /* DIN-Condensed-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BF291CA6E800EA081D /* DIN-Condensed-Bold.ttf */; };
		E7DE56D3291CA6E900EA081D /* DIN-Condensed-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BF291CA6E800EA081D /* DIN-Condensed-Bold.ttf */; };
		E7DE56D4291CA6E900EA081D /* ICOMOON2.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C0291CA6E800EA081D /* ICOMOON2.ttf */; };
		E7DE56D5291CA6E900EA081D /* ICOMOON2.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C0291CA6E800EA081D /* ICOMOON2.ttf */; };
		E7DE56D6291CA6E900EA081D /* Roboto-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C1291CA6E800EA081D /* Roboto-Bold.ttf */; };
		E7DE56D7291CA6E900EA081D /* Roboto-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C1291CA6E800EA081D /* Roboto-Bold.ttf */; };
		E7DE56DA291CA6E900EA081D /* Roboto-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C3291CA6E900EA081D /* Roboto-Regular.ttf */; };
		E7DE56DB291CA6E900EA081D /* Roboto-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C3291CA6E900EA081D /* Roboto-Regular.ttf */; };
		E7DE56DC291CA6E900EA081D /* ClubHaus-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C4291CA6E900EA081D /* ClubHaus-Regular.ttf */; };
		E7DE56DD291CA6E900EA081D /* ClubHaus-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C4291CA6E900EA081D /* ClubHaus-Regular.ttf */; };
		E7DE56DE291CA6E900EA081D /* ClubHaus-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C5291CA6E900EA081D /* ClubHaus-Bold.ttf */; };
		E7DE56DF291CA6E900EA081D /* ClubHaus-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C5291CA6E900EA081D /* ClubHaus-Bold.ttf */; };
		F91301262977E7A100E4ABC0 /* RCTKlaviyoModule.m in Sources */ = {isa = PBXBuildFile; fileRef = F91301202977E17500E4ABC0 /* RCTKlaviyoModule.m */; };
		F938A3A02ACFB7E9005AECCB /* DINNext79-Heavy.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3802ACFB7E7005AECCB /* DINNext79-Heavy.ttf */; };
		F938A3A12ACFB7E9005AECCB /* DINNext79-Heavy.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3802ACFB7E7005AECCB /* DINNext79-Heavy.ttf */; };
		F938A3A22ACFB7E9005AECCB /* DINNext79-Heavy.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3802ACFB7E7005AECCB /* DINNext79-Heavy.ttf */; };
		F938A3A32ACFB7E9005AECCB /* DINNext79-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3812ACFB7E7005AECCB /* DINNext79-Italic.ttf */; };
		F938A3A42ACFB7E9005AECCB /* DINNext79-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3812ACFB7E7005AECCB /* DINNext79-Italic.ttf */; };
		F938A3A52ACFB7E9005AECCB /* DINNext79-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3812ACFB7E7005AECCB /* DINNext79-Italic.ttf */; };
		F938A3B22ACFB7E9005AECCB /* DINNext79-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3862ACFB7E8005AECCB /* DINNext79-Regular.ttf */; };
		F938A3B32ACFB7E9005AECCB /* DINNext79-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3862ACFB7E8005AECCB /* DINNext79-Regular.ttf */; };
		F938A3B42ACFB7E9005AECCB /* DINNext79-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3862ACFB7E8005AECCB /* DINNext79-Regular.ttf */; };
		F938A3CD2ACFB7E9005AECCB /* DINNext79-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A38F2ACFB7E8005AECCB /* DINNext79-Bold.ttf */; };
		F938A3CE2ACFB7E9005AECCB /* DINNext79-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A38F2ACFB7E8005AECCB /* DINNext79-Bold.ttf */; };
		F938A3CF2ACFB7E9005AECCB /* DINNext79-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A38F2ACFB7E8005AECCB /* DINNext79-Bold.ttf */; };
		F938A3E22ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3962ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf */; };
		F938A3E32ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3962ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf */; };
		F938A3E42ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3962ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf */; };
		F93967E72CE1C8C3001857E4 /* DINNext79-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A38F2ACFB7E8005AECCB /* DINNext79-Bold.ttf */; };
		F93967E82CE1C8C3001857E4 /* DINNext79-Heavy.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3802ACFB7E7005AECCB /* DINNext79-Heavy.ttf */; };
		F93967E92CE1C8C3001857E4 /* DINNext79-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3962ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf */; };
		F93967EA2CE1C8C3001857E4 /* DINNext79-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3862ACFB7E8005AECCB /* DINNext79-Regular.ttf */; };
		F93967EB2CE1C8C3001857E4 /* DINNext79-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F938A3812ACFB7E7005AECCB /* DINNext79-Italic.ttf */; };
		F95144912ACEB39000ECDB54 /* SFMonoHeavy.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144852ACEB38F00ECDB54 /* SFMonoHeavy.otf */; };
		F95144922ACEB39000ECDB54 /* SFMonoHeavy.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144852ACEB38F00ECDB54 /* SFMonoHeavy.otf */; };
		F95144932ACEB39000ECDB54 /* SFMonoHeavy.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144852ACEB38F00ECDB54 /* SFMonoHeavy.otf */; };
		F95144942ACEB39000ECDB54 /* SFMonoBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144862ACEB38F00ECDB54 /* SFMonoBold.otf */; };
		F95144952ACEB39000ECDB54 /* SFMonoBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144862ACEB38F00ECDB54 /* SFMonoBold.otf */; };
		F95144962ACEB39000ECDB54 /* SFMonoBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144862ACEB38F00ECDB54 /* SFMonoBold.otf */; };
		F95144972ACEB39000ECDB54 /* SFMonoMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = ********2ACEB39000ECDB54 /* SFMonoMedium.otf */; };
		F95144982ACEB39000ECDB54 /* SFMonoMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = ********2ACEB39000ECDB54 /* SFMonoMedium.otf */; };
		F95144992ACEB39000ECDB54 /* SFMonoMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = ********2ACEB39000ECDB54 /* SFMonoMedium.otf */; };
		F951449A2ACEB39000ECDB54 /* SFMonoMediumItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144882ACEB39000ECDB54 /* SFMonoMediumItalic.otf */; };
		F951449B2ACEB39000ECDB54 /* SFMonoMediumItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144882ACEB39000ECDB54 /* SFMonoMediumItalic.otf */; };
		F951449C2ACEB39000ECDB54 /* SFMonoMediumItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144882ACEB39000ECDB54 /* SFMonoMediumItalic.otf */; };
		F951449D2ACEB39000ECDB54 /* SFMonoLightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144892ACEB39000ECDB54 /* SFMonoLightItalic.otf */; };
		F951449E2ACEB39000ECDB54 /* SFMonoLightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144892ACEB39000ECDB54 /* SFMonoLightItalic.otf */; };
		F951449F2ACEB39000ECDB54 /* SFMonoLightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144892ACEB39000ECDB54 /* SFMonoLightItalic.otf */; };
		F95144A02ACEB39000ECDB54 /* SFMonoBoldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448A2ACEB39000ECDB54 /* SFMonoBoldItalic.otf */; };
		F95144A12ACEB39000ECDB54 /* SFMonoBoldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448A2ACEB39000ECDB54 /* SFMonoBoldItalic.otf */; };
		F95144A22ACEB39000ECDB54 /* SFMonoBoldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448A2ACEB39000ECDB54 /* SFMonoBoldItalic.otf */; };
		F95144A32ACEB39000ECDB54 /* SFMonoRegular.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448B2ACEB39000ECDB54 /* SFMonoRegular.otf */; };
		F95144A42ACEB39000ECDB54 /* SFMonoRegular.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448B2ACEB39000ECDB54 /* SFMonoRegular.otf */; };
		F95144A52ACEB39000ECDB54 /* SFMonoRegular.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448B2ACEB39000ECDB54 /* SFMonoRegular.otf */; };
		F95144A62ACEB39000ECDB54 /* SFMonoSemibold.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448C2ACEB39000ECDB54 /* SFMonoSemibold.otf */; };
		F95144A72ACEB39000ECDB54 /* SFMonoSemibold.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448C2ACEB39000ECDB54 /* SFMonoSemibold.otf */; };
		F95144A82ACEB39000ECDB54 /* SFMonoSemibold.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448C2ACEB39000ECDB54 /* SFMonoSemibold.otf */; };
		F95144A92ACEB39000ECDB54 /* SFMonoLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448D2ACEB39000ECDB54 /* SFMonoLight.otf */; };
		F95144AA2ACEB39000ECDB54 /* SFMonoLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448D2ACEB39000ECDB54 /* SFMonoLight.otf */; };
		F95144AB2ACEB39000ECDB54 /* SFMonoLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448D2ACEB39000ECDB54 /* SFMonoLight.otf */; };
		F95144AC2ACEB39000ECDB54 /* SFMonoRegularItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448E2ACEB39000ECDB54 /* SFMonoRegularItalic.otf */; };
		F95144AD2ACEB39000ECDB54 /* SFMonoRegularItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448E2ACEB39000ECDB54 /* SFMonoRegularItalic.otf */; };
		F95144AE2ACEB39000ECDB54 /* SFMonoRegularItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448E2ACEB39000ECDB54 /* SFMonoRegularItalic.otf */; };
		F95144AF2ACEB39000ECDB54 /* SFMonoHeavyItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448F2ACEB39000ECDB54 /* SFMonoHeavyItalic.otf */; };
		F95144B02ACEB39000ECDB54 /* SFMonoHeavyItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448F2ACEB39000ECDB54 /* SFMonoHeavyItalic.otf */; };
		F95144B12ACEB39000ECDB54 /* SFMonoHeavyItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448F2ACEB39000ECDB54 /* SFMonoHeavyItalic.otf */; };
		F95144B22ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144902ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf */; };
		F95144B32ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144902ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf */; };
		F95144B42ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144902ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf */; };
		F957F9DC27F6F23300AE30D2 /* NewYork-Regular-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F957F9DB27F6F23300AE30D2 /* NewYork-Regular-Italic.ttf */; };
		F95DD0CA27F449AF00489D4B /* SF-Pro-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F95DD0C927F449AF00489D4B /* SF-Pro-Italic.ttf */; };
		F97F413C2CD9D5F200DBE529 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F97F413B2CD9D5F200DBE529 /* WidgetKit.framework */; };
		F97F413E2CD9D5F200DBE529 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F97F413D2CD9D5F200DBE529 /* SwiftUI.framework */; };
		F97F41412CD9D5F200DBE529 /* PlayHoleWidgetBundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97F41402CD9D5F200DBE529 /* PlayHoleWidgetBundle.swift */; };
		F97F41432CD9D5F200DBE529 /* PlayHoleWidgetLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97F41422CD9D5F200DBE529 /* PlayHoleWidgetLiveActivity.swift */; };
		F97F41492CD9D5F300DBE529 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = F97F41482CD9D5F300DBE529 /* Assets.xcassets */; };
		F97F414D2CD9D5F300DBE529 /* PlayHoleWidgetExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = F97F413A2CD9D5F200DBE529 /* PlayHoleWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		F994378E2CE25B4E001887EB /* Roboto-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C3291CA6E900EA081D /* Roboto-Regular.ttf */; };
		F994378F2CE25B4E001887EB /* SFMonoLightItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144892ACEB39000ECDB54 /* SFMonoLightItalic.otf */; };
		F99437902CE25B4E001887EB /* SFMonoMedium.otf in Resources */ = {isa = PBXBuildFile; fileRef = ********2ACEB39000ECDB54 /* SFMonoMedium.otf */; };
		F99437912CE25B4E001887EB /* SFMonoRegularItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448E2ACEB39000ECDB54 /* SFMonoRegularItalic.otf */; };
		F99437922CE25B4E001887EB /* ClubHaus-BoldOblique.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BE291CA6E800EA081D /* ClubHaus-BoldOblique.ttf */; };
		F99437932CE25B4E001887EB /* SFMonoHeavy.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144852ACEB38F00ECDB54 /* SFMonoHeavy.otf */; };
		F99437942CE25B4E001887EB /* SFMonoHeavyItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448F2ACEB39000ECDB54 /* SFMonoHeavyItalic.otf */; };
		F99437952CE25B4E001887EB /* SFMonoLight.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448D2ACEB39000ECDB54 /* SFMonoLight.otf */; };
		F99437962CE25B4E001887EB /* DIN-Condensed-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BF291CA6E800EA081D /* DIN-Condensed-Bold.ttf */; };
		F99437972CE25B4E001887EB /* SFMonoSemiboldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144902ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf */; };
		F99437982CE25B4E001887EB /* NewYork-Regular-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F957F9DB27F6F23300AE30D2 /* NewYork-Regular-Italic.ttf */; };
		F99437992CE25B4E001887EB /* SF-Pro.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7530263CB229425EBE3E1C06 /* SF-Pro.ttf */; };
		F994379A2CE25B4E001887EB /* ShadowsIntoLight-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F9DA18B32B1F130100C6A07A /* ShadowsIntoLight-Regular.ttf */; };
		F994379B2CE25B4E001887EB /* ClubHaus-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C5291CA6E900EA081D /* ClubHaus-Bold.ttf */; };
		F994379C2CE25B4E001887EB /* SFMonoMediumItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144882ACEB39000ECDB54 /* SFMonoMediumItalic.otf */; };
		F994379D2CE25B4E001887EB /* Roboto-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C1291CA6E800EA081D /* Roboto-Bold.ttf */; };
		F994379E2CE25B4E001887EB /* SFMonoBold.otf in Resources */ = {isa = PBXBuildFile; fileRef = F95144862ACEB38F00ECDB54 /* SFMonoBold.otf */; };
		F994379F2CE25B4E001887EB /* SFMonoBoldItalic.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448A2ACEB39000ECDB54 /* SFMonoBoldItalic.otf */; };
		F99437A02CE25B4E001887EB /* SFMonoSemibold.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448C2ACEB39000ECDB54 /* SFMonoSemibold.otf */; };
		F99437A12CE25B4E001887EB /* ClubHaus-Oblique.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BD291CA6E800EA081D /* ClubHaus-Oblique.ttf */; };
		F99437A22CE25B4E001887EB /* ClubHaus-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C4291CA6E900EA081D /* ClubHaus-Regular.ttf */; };
		F99437A32CE25B4E001887EB /* ICOMOON2.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56C0291CA6E800EA081D /* ICOMOON2.ttf */; };
		F99437A42CE25B4E001887EB /* SF-Pro-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F95DD0C927F449AF00489D4B /* SF-Pro-Italic.ttf */; };
		F99437A52CE25B4E001887EB /* DINNextLTPro-BoldCondensed.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9280BF1530EF446C8A333924 /* DINNextLTPro-BoldCondensed.ttf */; };
		F99437A62CE25B4E001887EB /* TMMRPSprint5.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BA291CA6E700EA081D /* TMMRPSprint5.ttf */; };
		F99437A72CE25B4E001887EB /* icomoon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56BB291CA6E700EA081D /* icomoon.ttf */; };
		F99437A82CE25B4E001887EB /* SFMonoRegular.otf in Resources */ = {isa = PBXBuildFile; fileRef = F951448B2ACEB39000ECDB54 /* SFMonoRegular.otf */; };
		F99437A92CE25B4E001887EB /* TMMRPSprint2.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E7DE56B9291CA6E700EA081D /* TMMRPSprint2.ttf */; };
		F9C6CCEA28B492EE00C70EA2 /* AppCenter-Config.plist in Resources */ = {isa = PBXBuildFile; fileRef = F9C6CCE928B492EE00C70EA2 /* AppCenter-Config.plist */; };
		F9DA18B42B1F130100C6A07A /* ShadowsIntoLight-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = F9DA18B32B1F130100C6A07A /* ShadowsIntoLight-Regular.ttf */; };
		F9DF78772BA5AAF900CDD77B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F9DF78762BA5AAF900CDD77B /* PrivacyInfo.xcprivacy */; };
		F9DF78792BA5AB7000CDD77B /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = F9DF78782BA5AB7000CDD77B /* PrivacyInfo.xcprivacy */; };
		F9F498972CDB660200C9F218 /* PlayHoleWidgetModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = F9F498952CDB660200C9F218 /* PlayHoleWidgetModule.swift */; };
		F9F4989C2CDB6DDC00C9F218 /* PlayHoleWidgetModule.m in Sources */ = {isa = PBXBuildFile; fileRef = F9F4989B2CDB6DDC00C9F218 /* PlayHoleWidgetModule.m */; };
		F9F498A42CDB7D3C00C9F218 /* PlayHoleWidgetModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = F9F498952CDB660200C9F218 /* PlayHoleWidgetModule.swift */; };
		F9F498A52CDB82BB00C9F218 /* PlayHoleWidgetLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = F97F41422CD9D5F200DBE529 /* PlayHoleWidgetLiveActivity.swift */; };
		FB79A5DDD99C474F81AD8E9C /* DINNextLTPro-BoldCondensed.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 9280BF1530EF446C8A333924 /* DINNextLTPro-BoldCondensed.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		D93841F62AD52C4400B4E3E7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = D93841F02AD52C4400B4E3E7;
			remoteInfo = NotificationServiceExtension;
		};
		E73EEBDC289B8A28001E4EDB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E73EEBD9289B8A28001E4EDB;
			remoteInfo = "MyTMWatch WatchKit Extension";
		};
		E73EEC05289B8A29001E4EDB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E73EEBCE289B8A27001E4EDB;
			remoteInfo = MyTMWatch;
		};
		F97F414B2CD9D5F300DBE529 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F97F41392CD9D5F200DBE529;
			remoteInfo = PlayHoleWidgetExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		D93841E82AD510E200B4E3E7 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				F97F414D2CD9D5F300DBE529 /* PlayHoleWidgetExtension.appex in Embed Foundation Extensions */,
				D93841F82AD52C4400B4E3E7 /* NotificationServiceExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		E73EEC0C289B8A29001E4EDB /* Embed App Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				E73EEBDB289B8A28001E4EDB /* MyTMWatch WatchKit Extension.appex in Embed App Extensions */,
			);
			name = "Embed App Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		E73EEC11289B8A29001E4EDB /* Embed Watch Content */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				E73EEC07289B8A29001E4EDB /* Staging_MyTMWatch.app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		06F08DC055CB716550EB80B6 /* Pods-MyTMWatch WatchKit Extension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MyTMWatch WatchKit Extension.debug.xcconfig"; path = "Target Support Files/Pods-MyTMWatch WatchKit Extension/Pods-MyTMWatch WatchKit Extension.debug.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* TaylorMade.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TaylorMade.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = MyTaylorMade/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = MyTaylorMade/AppDelegate.m; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = MyTaylorMade/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = MyTaylorMade/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = MyTaylorMade/main.m; sourceTree = "<group>"; };
		14FDB9ADCB297A09E0CF5BF4 /* Pods-MyTaylorMade.staging.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MyTaylorMade.staging.xcconfig"; path = "Target Support Files/Pods-MyTaylorMade/Pods-MyTaylorMade.staging.xcconfig"; sourceTree = "<group>"; };
		1592A196DF96064F4E941DC8 /* libPods-TaylorMade.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-TaylorMade.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		1919B0D59BF2B3F83805A63C /* Pods-TaylorMade.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TaylorMade.release.xcconfig"; path = "Target Support Files/Pods-TaylorMade/Pods-TaylorMade.release.xcconfig"; sourceTree = "<group>"; };
		4099872AB8AA866150756D53 /* Pods-MyTaylorMade.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MyTaylorMade.release.xcconfig"; path = "Target Support Files/Pods-MyTaylorMade/Pods-MyTaylorMade.release.xcconfig"; sourceTree = "<group>"; };
		5772BDBB86A8EB8A4EA0E481 /* Pods-MyTMWatch WatchKit Extension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MyTMWatch WatchKit Extension.release.xcconfig"; path = "Target Support Files/Pods-MyTMWatch WatchKit Extension/Pods-MyTMWatch WatchKit Extension.release.xcconfig"; sourceTree = "<group>"; };
		5929BD80CE7C87340F619298 /* libPods-NotificationServiceExtension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-NotificationServiceExtension.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		7063FB291E1803C1DA83C8E0 /* Pods-MyTaylorMade.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MyTaylorMade.debug.xcconfig"; path = "Target Support Files/Pods-MyTaylorMade/Pods-MyTaylorMade.debug.xcconfig"; sourceTree = "<group>"; };
		7530263CB229425EBE3E1C06 /* SF-Pro.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro.ttf"; path = "../assets/fonts/SF-Pro.ttf"; sourceTree = "<group>"; };
		7724BA3826A8D97900A256D9 /* myTaylorMadeStaging.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = myTaylorMadeStaging.entitlements; path = MyTaylorMade/myTaylorMadeStaging.entitlements; sourceTree = "<group>"; };
		777DAC4E2652EABB009FC1A4 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		777DAC502652EACD009FC1A4 /* MyTaylorMade.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = MyTaylorMade.entitlements; path = MyTaylorMade/MyTaylorMade.entitlements; sourceTree = "<group>"; };
		77C74552276BEE8E0054F4BB /* File.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = File.swift; sourceTree = "<group>"; };
		77C74554276BF02F0054F4BB /* MyTaylorMade-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = "MyTaylorMade-Bridging-Header.h"; path = "MyTaylorMade/MyTaylorMade-Bridging-Header.h"; sourceTree = "<group>"; };
		77CEEEF5264F1AA500F32FB1 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = MyTaylorMade/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8AB19EA950507E3563145074 /* Pods-TaylorMade.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TaylorMade.debug.xcconfig"; path = "Target Support Files/Pods-TaylorMade/Pods-TaylorMade.debug.xcconfig"; sourceTree = "<group>"; };
		9280BF1530EF446C8A333924 /* DINNextLTPro-BoldCondensed.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "DINNextLTPro-BoldCondensed.ttf"; path = "../assets/fonts/DINNextLTPro-BoldCondensed.ttf"; sourceTree = "<group>"; };
		B09F82085BFCA6B5F336AA55 /* Pods-MyTMWatch WatchKit Extension.staging.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MyTMWatch WatchKit Extension.staging.xcconfig"; path = "Target Support Files/Pods-MyTMWatch WatchKit Extension/Pods-MyTMWatch WatchKit Extension.staging.xcconfig"; sourceTree = "<group>"; };
		B2955E4BDD55B318A48D6BD1 /* libPods-MyTMWatch WatchKit Extension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-MyTMWatch WatchKit Extension.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		C2D0B05DEDCE958E030BF608 /* Pods-NotificationServiceExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.debug.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.debug.xcconfig"; sourceTree = "<group>"; };
		D93841F12AD52C4400B4E3E7 /* NotificationServiceExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = NotificationServiceExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		D93841F32AD52C4400B4E3E7 /* NotificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationService.swift; sourceTree = "<group>"; };
		D93841F52AD52C4400B4E3E7 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		D9871F192B997FBF00838D94 /* TaylorMade-Bridgin-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "TaylorMade-Bridgin-Header.h"; sourceTree = "<group>"; };
		DC2A08BCE19AB0472DC803F4 /* Pods-NotificationServiceExtension.staging.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.staging.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.staging.xcconfig"; sourceTree = "<group>"; };
		E3920F4999FCEB33DCEFDE09 /* Pods-NotificationServiceExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-NotificationServiceExtension.release.xcconfig"; path = "Target Support Files/Pods-NotificationServiceExtension/Pods-NotificationServiceExtension.release.xcconfig"; sourceTree = "<group>"; };
		E725168A2A497B51005A15B2 /* KlaviyoEventEmitter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = KlaviyoEventEmitter.h; sourceTree = "<group>"; };
		E725168B2A497B51005A15B2 /* KlaviyoEventEmitter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KlaviyoEventEmitter.m; sourceTree = "<group>"; };
		E72DE232289BBCDB00A93733 /* MRPAddRoundClassicInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPAddRoundClassicInterfaceController.h; sourceTree = "<group>"; };
		E72DE233289BBCDB00A93733 /* MRPAddRoundClassicInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPAddRoundClassicInterfaceController.m; sourceTree = "<group>"; };
		E72DE235289BBCDB00A93733 /* MRPClassicRoundStatsInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPClassicRoundStatsInterfaceController.h; sourceTree = "<group>"; };
		E72DE236289BBCDB00A93733 /* MRPClassicRoundStatsInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPClassicRoundStatsInterfaceController.m; sourceTree = "<group>"; };
		E72DE238289BBCDB00A93733 /* MRPClassicScorecardCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPClassicScorecardCell.h; sourceTree = "<group>"; };
		E72DE239289BBCDB00A93733 /* MRPClassicScorecardInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPClassicScorecardInterfaceController.m; sourceTree = "<group>"; };
		E72DE23A289BBCDB00A93733 /* MRPClassicFooterScoreCardCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPClassicFooterScoreCardCell.h; sourceTree = "<group>"; };
		E72DE23B289BBCDB00A93733 /* MRPClassicScorecardCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPClassicScorecardCell.m; sourceTree = "<group>"; };
		E72DE23C289BBCDB00A93733 /* MRPClassicFooterScoreCardCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPClassicFooterScoreCardCell.m; sourceTree = "<group>"; };
		E72DE23D289BBCDB00A93733 /* MRPClassicScorecardInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPClassicScorecardInterfaceController.h; sourceTree = "<group>"; };
		E72DE23F289BBCDB00A93733 /* MRPHoleCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPHoleCell.m; sourceTree = "<group>"; };
		E72DE240289BBCDB00A93733 /* MRPScorecardHeaderCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPScorecardHeaderCell.m; sourceTree = "<group>"; };
		E72DE241289BBCDB00A93733 /* MRPNearByCourseCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPNearByCourseCell.m; sourceTree = "<group>"; };
		E72DE242289BBCDB00A93733 /* MRPPutterClubCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPPutterClubCell.h; sourceTree = "<group>"; };
		E72DE243289BBCDB00A93733 /* MRPEndHoleCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPEndHoleCell.m; sourceTree = "<group>"; };
		E72DE244289BBCDB00A93733 /* MRPTeeCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPTeeCell.m; sourceTree = "<group>"; };
		E72DE245289BBCDB00A93733 /* MRPScorecardCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPScorecardCell.h; sourceTree = "<group>"; };
		E72DE246289BBCDB00A93733 /* MRPListClubCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPListClubCell.h; sourceTree = "<group>"; };
		E72DE247289BBCDB00A93733 /* MRPHoleCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPHoleCell.h; sourceTree = "<group>"; };
		E72DE248289BBCDB00A93733 /* MRPScorecardHeaderCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPScorecardHeaderCell.h; sourceTree = "<group>"; };
		E72DE249289BBCDB00A93733 /* MRPPutterClubCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPPutterClubCell.m; sourceTree = "<group>"; };
		E72DE24A289BBCDB00A93733 /* MRPNearByCourseCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPNearByCourseCell.h; sourceTree = "<group>"; };
		E72DE24B289BBCDB00A93733 /* MRPListClubCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPListClubCell.m; sourceTree = "<group>"; };
		E72DE24C289BBCDB00A93733 /* MRPScorecardCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPScorecardCell.m; sourceTree = "<group>"; };
		E72DE24D289BBCDB00A93733 /* MRPTeeCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPTeeCell.h; sourceTree = "<group>"; };
		E72DE24E289BBCDB00A93733 /* MRPEndHoleCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPEndHoleCell.h; sourceTree = "<group>"; };
		E72DE250289BBCDB00A93733 /* MRPWatchApiHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPWatchApiHandler.h; sourceTree = "<group>"; };
		E72DE252289BBCDB00A93733 /* MRPLocationsModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPLocationsModel.h; sourceTree = "<group>"; };
		E72DE253289BBCDB00A93733 /* MRPStrokeModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPStrokeModel.m; sourceTree = "<group>"; };
		E72DE254289BBCDB00A93733 /* MRPLocation2DModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPLocation2DModel.m; sourceTree = "<group>"; };
		E72DE255289BBCDB00A93733 /* MRPFeaturesModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPFeaturesModel.h; sourceTree = "<group>"; };
		E72DE256289BBCDB00A93733 /* MRPFeatureModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPFeatureModel.h; sourceTree = "<group>"; };
		E72DE257289BBCDB00A93733 /* MRPRoundLieModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPRoundLieModel.m; sourceTree = "<group>"; };
		E72DE258289BBCDB00A93733 /* MRPPoint.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPPoint.m; sourceTree = "<group>"; };
		E72DE259289BBCDB00A93733 /* MRPClubsModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPClubsModel.h; sourceTree = "<group>"; };
		E72DE25A289BBCDB00A93733 /* MRPPropertyModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPPropertyModel.m; sourceTree = "<group>"; };
		E72DE25B289BBCDB00A93733 /* MRPTeeModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPTeeModel.m; sourceTree = "<group>"; };
		E72DE25C289BBCDB00A93733 /* MRPRoundPlayerMetadataModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPRoundPlayerMetadataModel.h; sourceTree = "<group>"; };
		E72DE25D289BBCDB00A93733 /* MRPHolePlayerMetadataModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPHolePlayerMetadataModel.m; sourceTree = "<group>"; };
		E72DE25E289BBCDB00A93733 /* MRPHoleModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPHoleModel.m; sourceTree = "<group>"; };
		E72DE25F289BBCDB00A93733 /* MRPCourseConditionModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPCourseConditionModel.h; sourceTree = "<group>"; };
		E72DE260289BBCDB00A93733 /* MRPCourseModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPCourseModel.m; sourceTree = "<group>"; };
		E72DE261289BBCDB00A93733 /* MRPTeeboxBoundaryModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPTeeboxBoundaryModel.h; sourceTree = "<group>"; };
		E72DE262289BBCDB00A93733 /* MRPGeometryModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPGeometryModel.m; sourceTree = "<group>"; };
		E72DE263289BBCDB00A93733 /* MRPRoundModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPRoundModel.m; sourceTree = "<group>"; };
		E72DE264289BBCDB00A93733 /* MRPRealmStringModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPRealmStringModel.m; sourceTree = "<group>"; };
		E72DE265289BBCDB00A93733 /* MRPRoundLieModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPRoundLieModel.h; sourceTree = "<group>"; };
		E72DE266289BBCDB00A93733 /* MRPFeatureModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPFeatureModel.m; sourceTree = "<group>"; };
		E72DE267289BBCDB00A93733 /* MRPFeaturesModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPFeaturesModel.m; sourceTree = "<group>"; };
		E72DE268289BBCDB00A93733 /* MRPLocation2DModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPLocation2DModel.h; sourceTree = "<group>"; };
		E72DE269289BBCDB00A93733 /* MRPStrokeModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPStrokeModel.h; sourceTree = "<group>"; };
		E72DE26A289BBCDB00A93733 /* MRPLocationsModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPLocationsModel.m; sourceTree = "<group>"; };
		E72DE26B289BBCDB00A93733 /* MRPPropertyModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPPropertyModel.h; sourceTree = "<group>"; };
		E72DE26C289BBCDB00A93733 /* MRPClubsModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPClubsModel.m; sourceTree = "<group>"; };
		E72DE26D289BBCDB00A93733 /* MRPPoint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPPoint.h; sourceTree = "<group>"; };
		E72DE26E289BBCDB00A93733 /* MRPGeometryModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPGeometryModel.h; sourceTree = "<group>"; };
		E72DE26F289BBCDB00A93733 /* MRPTeeboxBoundaryModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPTeeboxBoundaryModel.m; sourceTree = "<group>"; };
		E72DE270289BBCDB00A93733 /* MRPCourseModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPCourseModel.h; sourceTree = "<group>"; };
		E72DE271289BBCDB00A93733 /* MRPCourseConditionModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPCourseConditionModel.m; sourceTree = "<group>"; };
		E72DE272289BBCDB00A93733 /* MRPHoleModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPHoleModel.h; sourceTree = "<group>"; };
		E72DE273289BBCDB00A93733 /* MRPRoundPlayerMetadataModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPRoundPlayerMetadataModel.m; sourceTree = "<group>"; };
		E72DE274289BBCDB00A93733 /* MRPHolePlayerMetadataModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPHolePlayerMetadataModel.h; sourceTree = "<group>"; };
		E72DE275289BBCDB00A93733 /* MRPTeeModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPTeeModel.h; sourceTree = "<group>"; };
		E72DE276289BBCDB00A93733 /* MRPRealmStringModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPRealmStringModel.h; sourceTree = "<group>"; };
		E72DE277289BBCDB00A93733 /* MRPRoundModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPRoundModel.h; sourceTree = "<group>"; };
		E72DE278289BBCDB00A93733 /* MRPWatchApiHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPWatchApiHandler.m; sourceTree = "<group>"; };
		E72DE27A289BBCDB00A93733 /* MRPWatchBaseBackgrounder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPWatchBaseBackgrounder.h; sourceTree = "<group>"; };
		E72DE27B289BBCDB00A93733 /* MRPRoundData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPRoundData.h; sourceTree = "<group>"; };
		E72DE27C289BBCDB00A93733 /* MRPWatchLocationManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPWatchLocationManager.h; sourceTree = "<group>"; };
		E72DE27D289BBCDB00A93733 /* MRPWatchRoundManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPWatchRoundManager.m; sourceTree = "<group>"; };
		E72DE27E289BBCDB00A93733 /* MRPWatchBaseBackgrounder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPWatchBaseBackgrounder.m; sourceTree = "<group>"; };
		E72DE27F289BBCDB00A93733 /* MRPRoundData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPRoundData.m; sourceTree = "<group>"; };
		E72DE280289BBCDB00A93733 /* MRPWatchRoundManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPWatchRoundManager.h; sourceTree = "<group>"; };
		E72DE281289BBCDB00A93733 /* MRPWatchLocationManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPWatchLocationManager.m; sourceTree = "<group>"; };
		E72DE283289BBCDB00A93733 /* MRPHoleInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPHoleInterfaceController.m; sourceTree = "<group>"; };
		E72DE284289BBCDB00A93733 /* MRPHomeInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPHomeInterfaceController.h; sourceTree = "<group>"; };
		E72DE285289BBCDB00A93733 /* MRPListClubInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPListClubInterfaceController.h; sourceTree = "<group>"; };
		E72DE286289BBCDB00A93733 /* MRPPlayRoundInfoInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPPlayRoundInfoInterfaceController.m; sourceTree = "<group>"; };
		E72DE287289BBCDB00A93733 /* MRPAboutInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPAboutInterfaceController.m; sourceTree = "<group>"; };
		E72DE288289BBCDB00A93733 /* MRPScorecardInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPScorecardInterfaceController.m; sourceTree = "<group>"; };
		E72DE289289BBCDB00A93733 /* MRPSelectScoreInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPSelectScoreInterfaceController.h; sourceTree = "<group>"; };
		E72DE28A289BBCDB00A93733 /* MRPListModeInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPListModeInterfaceController.h; sourceTree = "<group>"; };
		E72DE28B289BBCDB00A93733 /* MRPListTeeInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPListTeeInterfaceController.m; sourceTree = "<group>"; };
		E72DE28C289BBCDB00A93733 /* MRPListTypeInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPListTypeInterfaceController.h; sourceTree = "<group>"; };
		E72DE28E289BBCDB00A93733 /* MRPErrorLocationInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPErrorLocationInterfaceController.h; sourceTree = "<group>"; };
		E72DE28F289BBCDB00A93733 /* MRPConfirmAddClubInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPConfirmAddClubInterfaceController.m; sourceTree = "<group>"; };
		E72DE290289BBCDB00A93733 /* MRPErrorInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPErrorInterfaceController.m; sourceTree = "<group>"; };
		E72DE291289BBCDB00A93733 /* MRPErrorLocationInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPErrorLocationInterfaceController.m; sourceTree = "<group>"; };
		E72DE292289BBCDB00A93733 /* MRPErrorInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPErrorInterfaceController.h; sourceTree = "<group>"; };
		E72DE293289BBCDB00A93733 /* MRPConfirmAddClubInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPConfirmAddClubInterfaceController.h; sourceTree = "<group>"; };
		E72DE294289BBCDB00A93733 /* MRPConfirmInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPConfirmInterfaceController.m; sourceTree = "<group>"; };
		E72DE295289BBCDB00A93733 /* MRPSummaryInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPSummaryInterfaceController.m; sourceTree = "<group>"; };
		E72DE296289BBCDB00A93733 /* MRPResumeRoundInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPResumeRoundInterfaceController.h; sourceTree = "<group>"; };
		E72DE297289BBCDB00A93733 /* MRPNearByCourseInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPNearByCourseInterfaceController.h; sourceTree = "<group>"; };
		E72DE298289BBCDB00A93733 /* MRPAddShotInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPAddShotInterfaceController.m; sourceTree = "<group>"; };
		E72DE299289BBCDB00A93733 /* MRPPlayRoundInfoInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPPlayRoundInfoInterfaceController.h; sourceTree = "<group>"; };
		E72DE29A289BBCDB00A93733 /* MRPAboutInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPAboutInterfaceController.h; sourceTree = "<group>"; };
		E72DE29B289BBCDB00A93733 /* MRPListClubInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPListClubInterfaceController.m; sourceTree = "<group>"; };
		E72DE29C289BBCDB00A93733 /* MRPHomeInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPHomeInterfaceController.m; sourceTree = "<group>"; };
		E72DE29D289BBCDB00A93733 /* MRPHoleInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPHoleInterfaceController.h; sourceTree = "<group>"; };
		E72DE29E289BBCDB00A93733 /* MRPListModeInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPListModeInterfaceController.m; sourceTree = "<group>"; };
		E72DE29F289BBCDB00A93733 /* MRPSelectScoreInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPSelectScoreInterfaceController.m; sourceTree = "<group>"; };
		E72DE2A0289BBCDB00A93733 /* MRPScorecardInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPScorecardInterfaceController.h; sourceTree = "<group>"; };
		E72DE2A1289BBCDB00A93733 /* MRPConfirmInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPConfirmInterfaceController.h; sourceTree = "<group>"; };
		E72DE2A2289BBCDB00A93733 /* MRPListTypeInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPListTypeInterfaceController.m; sourceTree = "<group>"; };
		E72DE2A3289BBCDB00A93733 /* MRPListTeeInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPListTeeInterfaceController.h; sourceTree = "<group>"; };
		E72DE2A4289BBCDB00A93733 /* MRPAddShotInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPAddShotInterfaceController.h; sourceTree = "<group>"; };
		E72DE2A5289BBCDB00A93733 /* MRPNearByCourseInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPNearByCourseInterfaceController.m; sourceTree = "<group>"; };
		E72DE2A6289BBCDB00A93733 /* MRPResumeRoundInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPResumeRoundInterfaceController.m; sourceTree = "<group>"; };
		E72DE2A7289BBCDB00A93733 /* MRPSummaryInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPSummaryInterfaceController.h; sourceTree = "<group>"; };
		E72DE2A9289BBCDB00A93733 /* WKInterfaceController+Animation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "WKInterfaceController+Animation.m"; sourceTree = "<group>"; };
		E72DE2AA289BBCDB00A93733 /* WKInterfaceLabel+Animation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "WKInterfaceLabel+Animation.m"; sourceTree = "<group>"; };
		E72DE2AB289BBCDB00A93733 /* MRPWatchUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPWatchUtils.h; sourceTree = "<group>"; };
		E72DE2AC289BBCDB00A93733 /* WKInterfaceController+Animation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "WKInterfaceController+Animation.h"; sourceTree = "<group>"; };
		E72DE2AD289BBCDB00A93733 /* MRPWatchUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPWatchUtils.m; sourceTree = "<group>"; };
		E72DE2AE289BBCDB00A93733 /* WKInterfaceLabel+Animation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "WKInterfaceLabel+Animation.h"; sourceTree = "<group>"; };
		E72DE2B1289BBCDB00A93733 /* WKInterfaceGroup+Indicator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "WKInterfaceGroup+Indicator.m"; sourceTree = "<group>"; };
		E72DE2B2289BBCDB00A93733 /* WKInterfaceGroup+Indicator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "WKInterfaceGroup+Indicator.h"; sourceTree = "<group>"; };
		E72DE2B4289BBCDB00A93733 /* MRPBaseInterfaceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPBaseInterfaceController.h; sourceTree = "<group>"; };
		E72DE2B5289BBCDB00A93733 /* MRPConstantsWatch.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPConstantsWatch.h; sourceTree = "<group>"; };
		E72DE2B6289BBCDB00A93733 /* MRPBaseInterfaceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPBaseInterfaceController.m; sourceTree = "<group>"; };
		E72DE2F3289BBD3600A93733 /* MRPConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPConstants.h; sourceTree = "<group>"; };
		E72DE30C289BC0E200A93733 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		E72DE314289BC1A400A93733 /* HMAC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HMAC.m; sourceTree = "<group>"; };
		E72DE315289BC1A400A93733 /* HMAC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HMAC.h; sourceTree = "<group>"; };
		E72DE316289BC1A400A93733 /* GTMBase64.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GTMBase64.h; sourceTree = "<group>"; };
		E72DE318289BC1A400A93733 /* GTMDefines.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GTMDefines.h; sourceTree = "<group>"; };
		E72DE319289BC1A400A93733 /* GTMBase64.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GTMBase64.m; sourceTree = "<group>"; };
		E72DE324289BC1F000A93733 /* MRPIconFont.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPIconFont.h; sourceTree = "<group>"; };
		E72DE325289BC1F000A93733 /* MRPScorecardInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MRPScorecardInfo.h; sourceTree = "<group>"; };
		E72DE326289BC1F000A93733 /* MRPScorecardInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPScorecardInfo.m; sourceTree = "<group>"; };
		E72DE327289BC1F000A93733 /* MRPIconFont.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MRPIconFont.m; sourceTree = "<group>"; };
		E72DE32F289BC6D100A93733 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Assets.xcassets; path = myTMWatch/Assets.xcassets; sourceTree = SOURCE_ROOT; };
		E73EEBCF289B8A27001E4EDB /* Staging_MyTMWatch.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Staging_MyTMWatch.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E73EEBDA289B8A28001E4EDB /* MyTMWatch WatchKit Extension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = "MyTMWatch WatchKit Extension.appex"; sourceTree = BUILT_PRODUCTS_DIR; };
		E73EEBDF289B8A28001E4EDB /* InterfaceController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InterfaceController.h; sourceTree = "<group>"; };
		E73EEBE0289B8A28001E4EDB /* InterfaceController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InterfaceController.m; sourceTree = "<group>"; };
		E73EEBE2289B8A28001E4EDB /* ExtensionDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ExtensionDelegate.h; sourceTree = "<group>"; };
		E73EEBE3289B8A28001E4EDB /* ExtensionDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ExtensionDelegate.m; sourceTree = "<group>"; };
		E73EEBEB289B8A29001E4EDB /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		E73EEBED289B8A29001E4EDB /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		E73EEBF7289B8A29001E4EDB /* MyTMWatchTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyTMWatchTests.m; sourceTree = "<group>"; };
		E73EEC01289B8A29001E4EDB /* MyTMWatchUITests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyTMWatchUITests.m; sourceTree = "<group>"; };
		E73EEC03289B8A29001E4EDB /* MyTMWatchUITestsLaunchTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyTMWatchUITestsLaunchTests.m; sourceTree = "<group>"; };
		E77BDD9628A524AF005B946E /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = myTMWatch/Base.lproj/Interface.storyboard; sourceTree = SOURCE_ROOT; };
		E7B6F5C728978704002B127F /* AdSupport.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AdSupport.framework; path = System/Library/Frameworks/AdSupport.framework; sourceTree = SDKROOT; };
		E7DE56B9291CA6E700EA081D /* TMMRPSprint2.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = TMMRPSprint2.ttf; sourceTree = "<group>"; };
		E7DE56BA291CA6E700EA081D /* TMMRPSprint5.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = TMMRPSprint5.ttf; sourceTree = "<group>"; };
		E7DE56BB291CA6E700EA081D /* icomoon.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = icomoon.ttf; sourceTree = "<group>"; };
		E7DE56BD291CA6E800EA081D /* ClubHaus-Oblique.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "ClubHaus-Oblique.ttf"; sourceTree = "<group>"; };
		E7DE56BE291CA6E800EA081D /* ClubHaus-BoldOblique.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "ClubHaus-BoldOblique.ttf"; sourceTree = "<group>"; };
		E7DE56BF291CA6E800EA081D /* DIN-Condensed-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "DIN-Condensed-Bold.ttf"; sourceTree = "<group>"; };
		E7DE56C0291CA6E800EA081D /* ICOMOON2.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = ICOMOON2.ttf; sourceTree = "<group>"; };
		E7DE56C1291CA6E800EA081D /* Roboto-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Bold.ttf"; sourceTree = "<group>"; };
		E7DE56C3291CA6E900EA081D /* Roboto-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Roboto-Regular.ttf"; sourceTree = "<group>"; };
		E7DE56C4291CA6E900EA081D /* ClubHaus-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "ClubHaus-Regular.ttf"; sourceTree = "<group>"; };
		E7DE56C5291CA6E900EA081D /* ClubHaus-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "ClubHaus-Bold.ttf"; sourceTree = "<group>"; };
		E7E76B2F2921E774003D5F2B /* MyTMWatch WatchKit Extension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "MyTMWatch WatchKit Extension.entitlements"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		ED6D2EBFA54B3D7D7E3DBD02 /* Pods-TaylorMade.staging.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TaylorMade.staging.xcconfig"; path = "Target Support Files/Pods-TaylorMade/Pods-TaylorMade.staging.xcconfig"; sourceTree = "<group>"; };
		F913011F2977E15100E4ABC0 /* RCTKlaviyoModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RCTKlaviyoModule.h; sourceTree = "<group>"; };
		F91301202977E17500E4ABC0 /* RCTKlaviyoModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RCTKlaviyoModule.m; sourceTree = "<group>"; };
		F938A3802ACFB7E7005AECCB /* DINNext79-Heavy.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "DINNext79-Heavy.ttf"; path = "../assets/fonts/DINNext79-Heavy.ttf"; sourceTree = "<group>"; };
		F938A3812ACFB7E7005AECCB /* DINNext79-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "DINNext79-Italic.ttf"; path = "../assets/fonts/DINNext79-Italic.ttf"; sourceTree = "<group>"; };
		F938A3862ACFB7E8005AECCB /* DINNext79-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "DINNext79-Regular.ttf"; path = "../assets/fonts/DINNext79-Regular.ttf"; sourceTree = "<group>"; };
		F938A38F2ACFB7E8005AECCB /* DINNext79-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "DINNext79-Bold.ttf"; path = "../assets/fonts/DINNext79-Bold.ttf"; sourceTree = "<group>"; };
		F938A3962ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "DINNext79-BoldItalic.ttf"; path = "../assets/fonts/DINNext79-BoldItalic.ttf"; sourceTree = "<group>"; };
		F94733DB296C14BA001AFD7F /* libKlaviyoSwift.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libKlaviyoSwift.a; sourceTree = BUILT_PRODUCTS_DIR; };
		F95144852ACEB38F00ECDB54 /* SFMonoHeavy.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoHeavy.otf; path = ../assets/fonts/SFMonoHeavy.otf; sourceTree = "<group>"; };
		F95144862ACEB38F00ECDB54 /* SFMonoBold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoBold.otf; path = ../assets/fonts/SFMonoBold.otf; sourceTree = "<group>"; };
		********2ACEB39000ECDB54 /* SFMonoMedium.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoMedium.otf; path = ../assets/fonts/SFMonoMedium.otf; sourceTree = "<group>"; };
		F95144882ACEB39000ECDB54 /* SFMonoMediumItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoMediumItalic.otf; path = ../assets/fonts/SFMonoMediumItalic.otf; sourceTree = "<group>"; };
		F95144892ACEB39000ECDB54 /* SFMonoLightItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoLightItalic.otf; path = ../assets/fonts/SFMonoLightItalic.otf; sourceTree = "<group>"; };
		F951448A2ACEB39000ECDB54 /* SFMonoBoldItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoBoldItalic.otf; path = ../assets/fonts/SFMonoBoldItalic.otf; sourceTree = "<group>"; };
		F951448B2ACEB39000ECDB54 /* SFMonoRegular.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoRegular.otf; path = ../assets/fonts/SFMonoRegular.otf; sourceTree = "<group>"; };
		F951448C2ACEB39000ECDB54 /* SFMonoSemibold.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoSemibold.otf; path = ../assets/fonts/SFMonoSemibold.otf; sourceTree = "<group>"; };
		F951448D2ACEB39000ECDB54 /* SFMonoLight.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoLight.otf; path = ../assets/fonts/SFMonoLight.otf; sourceTree = "<group>"; };
		F951448E2ACEB39000ECDB54 /* SFMonoRegularItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoRegularItalic.otf; path = ../assets/fonts/SFMonoRegularItalic.otf; sourceTree = "<group>"; };
		F951448F2ACEB39000ECDB54 /* SFMonoHeavyItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoHeavyItalic.otf; path = ../assets/fonts/SFMonoHeavyItalic.otf; sourceTree = "<group>"; };
		F95144902ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf */ = {isa = PBXFileReference; lastKnownFileType = file; name = SFMonoSemiboldItalic.otf; path = ../assets/fonts/SFMonoSemiboldItalic.otf; sourceTree = "<group>"; };
		F957F9DB27F6F23300AE30D2 /* NewYork-Regular-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "NewYork-Regular-Italic.ttf"; path = "../assets/fonts/NewYork-Regular-Italic.ttf"; sourceTree = "<group>"; };
		F95DD0C927F449AF00489D4B /* SF-Pro-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = "SF-Pro-Italic.ttf"; path = "../assets/fonts/SF-Pro-Italic.ttf"; sourceTree = "<group>"; };
		F97F413A2CD9D5F200DBE529 /* PlayHoleWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = PlayHoleWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		F97F413B2CD9D5F200DBE529 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		F97F413D2CD9D5F200DBE529 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		F97F41402CD9D5F200DBE529 /* PlayHoleWidgetBundle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayHoleWidgetBundle.swift; sourceTree = "<group>"; };
		F97F41422CD9D5F200DBE529 /* PlayHoleWidgetLiveActivity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayHoleWidgetLiveActivity.swift; sourceTree = "<group>"; };
		F97F41482CD9D5F300DBE529 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		F97F414A2CD9D5F300DBE529 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F9C6CCE928B492EE00C70EA2 /* AppCenter-Config.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "AppCenter-Config.plist"; sourceTree = "<group>"; };
		F9DA18B32B1F130100C6A07A /* ShadowsIntoLight-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "ShadowsIntoLight-Regular.ttf"; sourceTree = "<group>"; };
		F9DF78762BA5AAF900CDD77B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		F9DF78782BA5AB7000CDD77B /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = "myTMWatch WatchKit Extension/PrivacyInfo.xcprivacy"; sourceTree = SOURCE_ROOT; };
		F9F498952CDB660200C9F218 /* PlayHoleWidgetModule.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayHoleWidgetModule.swift; sourceTree = "<group>"; };
		F9F4989B2CDB6DDC00C9F218 /* PlayHoleWidgetModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PlayHoleWidgetModule.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E72DE30D289BC0E200A93733 /* SystemConfiguration.framework in Frameworks */,
				777DAC4F2652EABB009FC1A4 /* StoreKit.framework in Frameworks */,
				E7B6F5C828978704002B127F /* AdSupport.framework in Frameworks */,
				6635D6A48A3A67DCC7CC4329 /* libPods-TaylorMade.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2E224E6AA9A14CC7DDAF6431 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D93841EE2AD52C4400B4E3E7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				C9FD95F8B969BD88E6689E7D /* libPods-NotificationServiceExtension.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E73EEBD7289B8A28001E4EDB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				41D15A8F55DAF4B51F23882F /* libPods-MyTMWatch WatchKit Extension.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F97F41372CD9D5F200DBE529 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F97F413E2CD9D5F200DBE529 /* SwiftUI.framework in Frameworks */,
				F97F413C2CD9D5F200DBE529 /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* MyTaylorMade */ = {
			isa = PBXGroup;
			children = (
				F913011E2977E11B00E4ABC0 /* Klaviyo */,
				F9C6CCE928B492EE00C70EA2 /* AppCenter-Config.plist */,
				E72DE30F289BC1A400A93733 /* OC */,
				77C74554276BF02F0054F4BB /* MyTaylorMade-Bridging-Header.h */,
				7724BA3826A8D97900A256D9 /* myTaylorMadeStaging.entitlements */,
				777DAC502652EACD009FC1A4 /* MyTaylorMade.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				77C74552276BEE8E0054F4BB /* File.swift */,
				D9871F192B997FBF00838D94 /* TaylorMade-Bridgin-Header.h */,
				F9DF78762BA5AAF900CDD77B /* PrivacyInfo.xcprivacy */,
				F9F4989B2CDB6DDC00C9F218 /* PlayHoleWidgetModule.m */,
			);
			name = MyTaylorMade;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F94733DB296C14BA001AFD7F /* libKlaviyoSwift.a */,
				E7B6F5C728978704002B127F /* AdSupport.framework */,
				E72DE30C289BC0E200A93733 /* SystemConfiguration.framework */,
				777DAC4E2652EABB009FC1A4 /* StoreKit.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				B2955E4BDD55B318A48D6BD1 /* libPods-MyTMWatch WatchKit Extension.a */,
				5929BD80CE7C87340F619298 /* libPods-NotificationServiceExtension.a */,
				1592A196DF96064F4E941DC8 /* libPods-TaylorMade.a */,
				F97F413B2CD9D5F200DBE529 /* WidgetKit.framework */,
				F97F413D2CD9D5F200DBE529 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				77CEEEF5264F1AA500F32FB1 /* GoogleService-Info.plist */,
				13B07FAE1A68108700A75B9A /* MyTaylorMade */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				E73EEBD0289B8A27001E4EDB /* MyTMWatch */,
				E73EEBDE289B8A28001E4EDB /* MyTMWatch WatchKit Extension */,
				E73EEBF6289B8A29001E4EDB /* MyTMWatchTests */,
				E73EEC00289B8A29001E4EDB /* MyTMWatchUITests */,
				D93841F22AD52C4400B4E3E7 /* NotificationServiceExtension */,
				F97F413F2CD9D5F200DBE529 /* PlayHoleWidget */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				D709598F5AC7942828DA497E /* Pods */,
				EFEBF0F751DB4DA1A5A485AC /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* TaylorMade.app */,
				E73EEBCF289B8A27001E4EDB /* Staging_MyTMWatch.app */,
				E73EEBDA289B8A28001E4EDB /* MyTMWatch WatchKit Extension.appex */,
				D93841F12AD52C4400B4E3E7 /* NotificationServiceExtension.appex */,
				F97F413A2CD9D5F200DBE529 /* PlayHoleWidgetExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		D709598F5AC7942828DA497E /* Pods */ = {
			isa = PBXGroup;
			children = (
				7063FB291E1803C1DA83C8E0 /* Pods-MyTaylorMade.debug.xcconfig */,
				4099872AB8AA866150756D53 /* Pods-MyTaylorMade.release.xcconfig */,
				14FDB9ADCB297A09E0CF5BF4 /* Pods-MyTaylorMade.staging.xcconfig */,
				06F08DC055CB716550EB80B6 /* Pods-MyTMWatch WatchKit Extension.debug.xcconfig */,
				5772BDBB86A8EB8A4EA0E481 /* Pods-MyTMWatch WatchKit Extension.release.xcconfig */,
				B09F82085BFCA6B5F336AA55 /* Pods-MyTMWatch WatchKit Extension.staging.xcconfig */,
				C2D0B05DEDCE958E030BF608 /* Pods-NotificationServiceExtension.debug.xcconfig */,
				E3920F4999FCEB33DCEFDE09 /* Pods-NotificationServiceExtension.release.xcconfig */,
				DC2A08BCE19AB0472DC803F4 /* Pods-NotificationServiceExtension.staging.xcconfig */,
				8AB19EA950507E3563145074 /* Pods-TaylorMade.debug.xcconfig */,
				1919B0D59BF2B3F83805A63C /* Pods-TaylorMade.release.xcconfig */,
				ED6D2EBFA54B3D7D7E3DBD02 /* Pods-TaylorMade.staging.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		D93841F22AD52C4400B4E3E7 /* NotificationServiceExtension */ = {
			isa = PBXGroup;
			children = (
				D93841F32AD52C4400B4E3E7 /* NotificationService.swift */,
				D93841F52AD52C4400B4E3E7 /* Info.plist */,
			);
			path = NotificationServiceExtension;
			sourceTree = "<group>";
		};
		E72DE22F289BBCDB00A93733 /* Source */ = {
			isa = PBXGroup;
			children = (
				E72DE230289BBCDB00A93733 /* ClassicRound */,
				E72DE23E289BBCDB00A93733 /* Cells */,
				E72DE24F289BBCDB00A93733 /* Networking */,
				E72DE279289BBCDB00A93733 /* Managers */,
				E72DE282289BBCDB00A93733 /* Controllers */,
				E72DE2A8289BBCDB00A93733 /* Helper */,
				E72DE2AF289BBCDB00A93733 /* ThirdParty */,
				E72DE2B3289BBCDB00A93733 /* Base */,
			);
			name = Source;
			path = "myTMWatch WatchKit Extension/Source";
			sourceTree = SOURCE_ROOT;
		};
		E72DE230289BBCDB00A93733 /* ClassicRound */ = {
			isa = PBXGroup;
			children = (
				E72DE231289BBCDB00A93733 /* AddRound */,
				E72DE234289BBCDB00A93733 /* RoundStats */,
				E72DE237289BBCDB00A93733 /* ScoreCard */,
			);
			path = ClassicRound;
			sourceTree = "<group>";
		};
		E72DE231289BBCDB00A93733 /* AddRound */ = {
			isa = PBXGroup;
			children = (
				E72DE232289BBCDB00A93733 /* MRPAddRoundClassicInterfaceController.h */,
				E72DE233289BBCDB00A93733 /* MRPAddRoundClassicInterfaceController.m */,
			);
			path = AddRound;
			sourceTree = "<group>";
		};
		E72DE234289BBCDB00A93733 /* RoundStats */ = {
			isa = PBXGroup;
			children = (
				E72DE235289BBCDB00A93733 /* MRPClassicRoundStatsInterfaceController.h */,
				E72DE236289BBCDB00A93733 /* MRPClassicRoundStatsInterfaceController.m */,
			);
			path = RoundStats;
			sourceTree = "<group>";
		};
		E72DE237289BBCDB00A93733 /* ScoreCard */ = {
			isa = PBXGroup;
			children = (
				E72DE238289BBCDB00A93733 /* MRPClassicScorecardCell.h */,
				E72DE239289BBCDB00A93733 /* MRPClassicScorecardInterfaceController.m */,
				E72DE23A289BBCDB00A93733 /* MRPClassicFooterScoreCardCell.h */,
				E72DE23B289BBCDB00A93733 /* MRPClassicScorecardCell.m */,
				E72DE23C289BBCDB00A93733 /* MRPClassicFooterScoreCardCell.m */,
				E72DE23D289BBCDB00A93733 /* MRPClassicScorecardInterfaceController.h */,
			);
			path = ScoreCard;
			sourceTree = "<group>";
		};
		E72DE23E289BBCDB00A93733 /* Cells */ = {
			isa = PBXGroup;
			children = (
				E72DE23F289BBCDB00A93733 /* MRPHoleCell.m */,
				E72DE240289BBCDB00A93733 /* MRPScorecardHeaderCell.m */,
				E72DE241289BBCDB00A93733 /* MRPNearByCourseCell.m */,
				E72DE242289BBCDB00A93733 /* MRPPutterClubCell.h */,
				E72DE243289BBCDB00A93733 /* MRPEndHoleCell.m */,
				E72DE244289BBCDB00A93733 /* MRPTeeCell.m */,
				E72DE245289BBCDB00A93733 /* MRPScorecardCell.h */,
				E72DE246289BBCDB00A93733 /* MRPListClubCell.h */,
				E72DE247289BBCDB00A93733 /* MRPHoleCell.h */,
				E72DE248289BBCDB00A93733 /* MRPScorecardHeaderCell.h */,
				E72DE249289BBCDB00A93733 /* MRPPutterClubCell.m */,
				E72DE24A289BBCDB00A93733 /* MRPNearByCourseCell.h */,
				E72DE24B289BBCDB00A93733 /* MRPListClubCell.m */,
				E72DE24C289BBCDB00A93733 /* MRPScorecardCell.m */,
				E72DE24D289BBCDB00A93733 /* MRPTeeCell.h */,
				E72DE24E289BBCDB00A93733 /* MRPEndHoleCell.h */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		E72DE24F289BBCDB00A93733 /* Networking */ = {
			isa = PBXGroup;
			children = (
				E72DE250289BBCDB00A93733 /* MRPWatchApiHandler.h */,
				E72DE251289BBCDB00A93733 /* Model */,
				E72DE278289BBCDB00A93733 /* MRPWatchApiHandler.m */,
			);
			path = Networking;
			sourceTree = "<group>";
		};
		E72DE251289BBCDB00A93733 /* Model */ = {
			isa = PBXGroup;
			children = (
				E72DE252289BBCDB00A93733 /* MRPLocationsModel.h */,
				E72DE253289BBCDB00A93733 /* MRPStrokeModel.m */,
				E72DE254289BBCDB00A93733 /* MRPLocation2DModel.m */,
				E72DE255289BBCDB00A93733 /* MRPFeaturesModel.h */,
				E72DE256289BBCDB00A93733 /* MRPFeatureModel.h */,
				E72DE257289BBCDB00A93733 /* MRPRoundLieModel.m */,
				E72DE258289BBCDB00A93733 /* MRPPoint.m */,
				E72DE259289BBCDB00A93733 /* MRPClubsModel.h */,
				E72DE25A289BBCDB00A93733 /* MRPPropertyModel.m */,
				E72DE25B289BBCDB00A93733 /* MRPTeeModel.m */,
				E72DE25C289BBCDB00A93733 /* MRPRoundPlayerMetadataModel.h */,
				E72DE25D289BBCDB00A93733 /* MRPHolePlayerMetadataModel.m */,
				E72DE25E289BBCDB00A93733 /* MRPHoleModel.m */,
				E72DE25F289BBCDB00A93733 /* MRPCourseConditionModel.h */,
				E72DE260289BBCDB00A93733 /* MRPCourseModel.m */,
				E72DE261289BBCDB00A93733 /* MRPTeeboxBoundaryModel.h */,
				E72DE262289BBCDB00A93733 /* MRPGeometryModel.m */,
				E72DE263289BBCDB00A93733 /* MRPRoundModel.m */,
				E72DE264289BBCDB00A93733 /* MRPRealmStringModel.m */,
				E72DE265289BBCDB00A93733 /* MRPRoundLieModel.h */,
				E72DE266289BBCDB00A93733 /* MRPFeatureModel.m */,
				E72DE267289BBCDB00A93733 /* MRPFeaturesModel.m */,
				E72DE268289BBCDB00A93733 /* MRPLocation2DModel.h */,
				E72DE269289BBCDB00A93733 /* MRPStrokeModel.h */,
				E72DE26A289BBCDB00A93733 /* MRPLocationsModel.m */,
				E72DE26B289BBCDB00A93733 /* MRPPropertyModel.h */,
				E72DE26C289BBCDB00A93733 /* MRPClubsModel.m */,
				E72DE26D289BBCDB00A93733 /* MRPPoint.h */,
				E72DE26E289BBCDB00A93733 /* MRPGeometryModel.h */,
				E72DE26F289BBCDB00A93733 /* MRPTeeboxBoundaryModel.m */,
				E72DE270289BBCDB00A93733 /* MRPCourseModel.h */,
				E72DE271289BBCDB00A93733 /* MRPCourseConditionModel.m */,
				E72DE272289BBCDB00A93733 /* MRPHoleModel.h */,
				E72DE273289BBCDB00A93733 /* MRPRoundPlayerMetadataModel.m */,
				E72DE274289BBCDB00A93733 /* MRPHolePlayerMetadataModel.h */,
				E72DE275289BBCDB00A93733 /* MRPTeeModel.h */,
				E72DE276289BBCDB00A93733 /* MRPRealmStringModel.h */,
				E72DE277289BBCDB00A93733 /* MRPRoundModel.h */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		E72DE279289BBCDB00A93733 /* Managers */ = {
			isa = PBXGroup;
			children = (
				E72DE27A289BBCDB00A93733 /* MRPWatchBaseBackgrounder.h */,
				E72DE27B289BBCDB00A93733 /* MRPRoundData.h */,
				E72DE27C289BBCDB00A93733 /* MRPWatchLocationManager.h */,
				E72DE27D289BBCDB00A93733 /* MRPWatchRoundManager.m */,
				E72DE27E289BBCDB00A93733 /* MRPWatchBaseBackgrounder.m */,
				E72DE27F289BBCDB00A93733 /* MRPRoundData.m */,
				E72DE280289BBCDB00A93733 /* MRPWatchRoundManager.h */,
				E72DE281289BBCDB00A93733 /* MRPWatchLocationManager.m */,
			);
			path = Managers;
			sourceTree = "<group>";
		};
		E72DE282289BBCDB00A93733 /* Controllers */ = {
			isa = PBXGroup;
			children = (
				E72DE283289BBCDB00A93733 /* MRPHoleInterfaceController.m */,
				E72DE284289BBCDB00A93733 /* MRPHomeInterfaceController.h */,
				E72DE285289BBCDB00A93733 /* MRPListClubInterfaceController.h */,
				E72DE286289BBCDB00A93733 /* MRPPlayRoundInfoInterfaceController.m */,
				E72DE287289BBCDB00A93733 /* MRPAboutInterfaceController.m */,
				E72DE288289BBCDB00A93733 /* MRPScorecardInterfaceController.m */,
				E72DE289289BBCDB00A93733 /* MRPSelectScoreInterfaceController.h */,
				E72DE28A289BBCDB00A93733 /* MRPListModeInterfaceController.h */,
				E72DE28B289BBCDB00A93733 /* MRPListTeeInterfaceController.m */,
				E72DE28C289BBCDB00A93733 /* MRPListTypeInterfaceController.h */,
				E72DE28D289BBCDB00A93733 /* Alert */,
				E72DE294289BBCDB00A93733 /* MRPConfirmInterfaceController.m */,
				E72DE295289BBCDB00A93733 /* MRPSummaryInterfaceController.m */,
				E72DE296289BBCDB00A93733 /* MRPResumeRoundInterfaceController.h */,
				E72DE297289BBCDB00A93733 /* MRPNearByCourseInterfaceController.h */,
				E72DE298289BBCDB00A93733 /* MRPAddShotInterfaceController.m */,
				E72DE299289BBCDB00A93733 /* MRPPlayRoundInfoInterfaceController.h */,
				E72DE29A289BBCDB00A93733 /* MRPAboutInterfaceController.h */,
				E72DE29B289BBCDB00A93733 /* MRPListClubInterfaceController.m */,
				E72DE29C289BBCDB00A93733 /* MRPHomeInterfaceController.m */,
				E72DE29D289BBCDB00A93733 /* MRPHoleInterfaceController.h */,
				E72DE29E289BBCDB00A93733 /* MRPListModeInterfaceController.m */,
				E72DE29F289BBCDB00A93733 /* MRPSelectScoreInterfaceController.m */,
				E72DE2A0289BBCDB00A93733 /* MRPScorecardInterfaceController.h */,
				E72DE2A1289BBCDB00A93733 /* MRPConfirmInterfaceController.h */,
				E72DE2A2289BBCDB00A93733 /* MRPListTypeInterfaceController.m */,
				E72DE2A3289BBCDB00A93733 /* MRPListTeeInterfaceController.h */,
				E72DE2A4289BBCDB00A93733 /* MRPAddShotInterfaceController.h */,
				E72DE2A5289BBCDB00A93733 /* MRPNearByCourseInterfaceController.m */,
				E72DE2A6289BBCDB00A93733 /* MRPResumeRoundInterfaceController.m */,
				E72DE2A7289BBCDB00A93733 /* MRPSummaryInterfaceController.h */,
			);
			path = Controllers;
			sourceTree = "<group>";
		};
		E72DE28D289BBCDB00A93733 /* Alert */ = {
			isa = PBXGroup;
			children = (
				E72DE28E289BBCDB00A93733 /* MRPErrorLocationInterfaceController.h */,
				E72DE28F289BBCDB00A93733 /* MRPConfirmAddClubInterfaceController.m */,
				E72DE290289BBCDB00A93733 /* MRPErrorInterfaceController.m */,
				E72DE291289BBCDB00A93733 /* MRPErrorLocationInterfaceController.m */,
				E72DE292289BBCDB00A93733 /* MRPErrorInterfaceController.h */,
				E72DE293289BBCDB00A93733 /* MRPConfirmAddClubInterfaceController.h */,
			);
			path = Alert;
			sourceTree = "<group>";
		};
		E72DE2A8289BBCDB00A93733 /* Helper */ = {
			isa = PBXGroup;
			children = (
				E72DE2A9289BBCDB00A93733 /* WKInterfaceController+Animation.m */,
				E72DE2AA289BBCDB00A93733 /* WKInterfaceLabel+Animation.m */,
				E72DE2AB289BBCDB00A93733 /* MRPWatchUtils.h */,
				E72DE2AC289BBCDB00A93733 /* WKInterfaceController+Animation.h */,
				E72DE2AD289BBCDB00A93733 /* MRPWatchUtils.m */,
				E72DE2AE289BBCDB00A93733 /* WKInterfaceLabel+Animation.h */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		E72DE2AF289BBCDB00A93733 /* ThirdParty */ = {
			isa = PBXGroup;
			children = (
				E72DE2B0289BBCDB00A93733 /* Indicator */,
			);
			path = ThirdParty;
			sourceTree = "<group>";
		};
		E72DE2B0289BBCDB00A93733 /* Indicator */ = {
			isa = PBXGroup;
			children = (
				E72DE2B1289BBCDB00A93733 /* WKInterfaceGroup+Indicator.m */,
				E72DE2B2289BBCDB00A93733 /* WKInterfaceGroup+Indicator.h */,
			);
			path = Indicator;
			sourceTree = "<group>";
		};
		E72DE2B3289BBCDB00A93733 /* Base */ = {
			isa = PBXGroup;
			children = (
				E72DE2F3289BBD3600A93733 /* MRPConstants.h */,
				E72DE2B4289BBCDB00A93733 /* MRPBaseInterfaceController.h */,
				E72DE2B5289BBCDB00A93733 /* MRPConstantsWatch.h */,
				E72DE2B6289BBCDB00A93733 /* MRPBaseInterfaceController.m */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		E72DE30F289BC1A400A93733 /* OC */ = {
			isa = PBXGroup;
			children = (
				E72DE311289BC1A400A93733 /* SDKs iGolf Connect */,
				E72DE31A289BC1A400A93733 /* Base */,
			);
			path = OC;
			sourceTree = "<group>";
		};
		E72DE311289BC1A400A93733 /* SDKs iGolf Connect */ = {
			isa = PBXGroup;
			children = (
				E72DE313289BC1A400A93733 /* HMAC */,
				E72DE316289BC1A400A93733 /* GTMBase64.h */,
				E72DE318289BC1A400A93733 /* GTMDefines.h */,
				E72DE319289BC1A400A93733 /* GTMBase64.m */,
			);
			path = "SDKs iGolf Connect";
			sourceTree = "<group>";
		};
		E72DE313289BC1A400A93733 /* HMAC */ = {
			isa = PBXGroup;
			children = (
				E72DE314289BC1A400A93733 /* HMAC.m */,
				E72DE315289BC1A400A93733 /* HMAC.h */,
			);
			path = HMAC;
			sourceTree = "<group>";
		};
		E72DE31A289BC1A400A93733 /* Base */ = {
			isa = PBXGroup;
			children = (
				E72DE324289BC1F000A93733 /* MRPIconFont.h */,
				E72DE327289BC1F000A93733 /* MRPIconFont.m */,
				E72DE325289BC1F000A93733 /* MRPScorecardInfo.h */,
				E72DE326289BC1F000A93733 /* MRPScorecardInfo.m */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		E73EEBD0289B8A27001E4EDB /* MyTMWatch */ = {
			isa = PBXGroup;
			children = (
				E77BDD9528A524AF005B946E /* Interface.storyboard */,
				E72DE32F289BC6D100A93733 /* Assets.xcassets */,
			);
			path = MyTMWatch;
			sourceTree = "<group>";
		};
		E73EEBDE289B8A28001E4EDB /* MyTMWatch WatchKit Extension */ = {
			isa = PBXGroup;
			children = (
				E7E76B2F2921E774003D5F2B /* MyTMWatch WatchKit Extension.entitlements */,
				E72DE22F289BBCDB00A93733 /* Source */,
				E73EEBDF289B8A28001E4EDB /* InterfaceController.h */,
				E73EEBE0289B8A28001E4EDB /* InterfaceController.m */,
				E73EEBE2289B8A28001E4EDB /* ExtensionDelegate.h */,
				E73EEBE3289B8A28001E4EDB /* ExtensionDelegate.m */,
				E73EEBEB289B8A29001E4EDB /* Assets.xcassets */,
				E73EEBED289B8A29001E4EDB /* Info.plist */,
				F9DF78782BA5AB7000CDD77B /* PrivacyInfo.xcprivacy */,
			);
			path = "MyTMWatch WatchKit Extension";
			sourceTree = "<group>";
		};
		E73EEBF6289B8A29001E4EDB /* MyTMWatchTests */ = {
			isa = PBXGroup;
			children = (
				E73EEBF7289B8A29001E4EDB /* MyTMWatchTests.m */,
			);
			path = MyTMWatchTests;
			sourceTree = "<group>";
		};
		E73EEC00289B8A29001E4EDB /* MyTMWatchUITests */ = {
			isa = PBXGroup;
			children = (
				E73EEC01289B8A29001E4EDB /* MyTMWatchUITests.m */,
				E73EEC03289B8A29001E4EDB /* MyTMWatchUITestsLaunchTests.m */,
			);
			path = MyTMWatchUITests;
			sourceTree = "<group>";
		};
		EFEBF0F751DB4DA1A5A485AC /* Resources */ = {
			isa = PBXGroup;
			children = (
				F9DA18B32B1F130100C6A07A /* ShadowsIntoLight-Regular.ttf */,
				F938A38F2ACFB7E8005AECCB /* DINNext79-Bold.ttf */,
				F938A3962ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf */,
				F938A3802ACFB7E7005AECCB /* DINNext79-Heavy.ttf */,
				F938A3812ACFB7E7005AECCB /* DINNext79-Italic.ttf */,
				F938A3862ACFB7E8005AECCB /* DINNext79-Regular.ttf */,
				F95144862ACEB38F00ECDB54 /* SFMonoBold.otf */,
				F951448A2ACEB39000ECDB54 /* SFMonoBoldItalic.otf */,
				F95144852ACEB38F00ECDB54 /* SFMonoHeavy.otf */,
				F951448F2ACEB39000ECDB54 /* SFMonoHeavyItalic.otf */,
				F951448D2ACEB39000ECDB54 /* SFMonoLight.otf */,
				F95144892ACEB39000ECDB54 /* SFMonoLightItalic.otf */,
				********2ACEB39000ECDB54 /* SFMonoMedium.otf */,
				F95144882ACEB39000ECDB54 /* SFMonoMediumItalic.otf */,
				F951448B2ACEB39000ECDB54 /* SFMonoRegular.otf */,
				F951448E2ACEB39000ECDB54 /* SFMonoRegularItalic.otf */,
				F951448C2ACEB39000ECDB54 /* SFMonoSemibold.otf */,
				F95144902ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf */,
				E7DE56C5291CA6E900EA081D /* ClubHaus-Bold.ttf */,
				E7DE56BE291CA6E800EA081D /* ClubHaus-BoldOblique.ttf */,
				E7DE56BD291CA6E800EA081D /* ClubHaus-Oblique.ttf */,
				E7DE56C4291CA6E900EA081D /* ClubHaus-Regular.ttf */,
				E7DE56BF291CA6E800EA081D /* DIN-Condensed-Bold.ttf */,
				E7DE56BB291CA6E700EA081D /* icomoon.ttf */,
				E7DE56C0291CA6E800EA081D /* ICOMOON2.ttf */,
				E7DE56C1291CA6E800EA081D /* Roboto-Bold.ttf */,
				E7DE56C3291CA6E900EA081D /* Roboto-Regular.ttf */,
				E7DE56B9291CA6E700EA081D /* TMMRPSprint2.ttf */,
				E7DE56BA291CA6E700EA081D /* TMMRPSprint5.ttf */,
				F957F9DB27F6F23300AE30D2 /* NewYork-Regular-Italic.ttf */,
				F95DD0C927F449AF00489D4B /* SF-Pro-Italic.ttf */,
				9280BF1530EF446C8A333924 /* DINNextLTPro-BoldCondensed.ttf */,
				7530263CB229425EBE3E1C06 /* SF-Pro.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		F913011E2977E11B00E4ABC0 /* Klaviyo */ = {
			isa = PBXGroup;
			children = (
				F913011F2977E15100E4ABC0 /* RCTKlaviyoModule.h */,
				F91301202977E17500E4ABC0 /* RCTKlaviyoModule.m */,
				E725168A2A497B51005A15B2 /* KlaviyoEventEmitter.h */,
				E725168B2A497B51005A15B2 /* KlaviyoEventEmitter.m */,
			);
			path = Klaviyo;
			sourceTree = "<group>";
		};
		F97F413F2CD9D5F200DBE529 /* PlayHoleWidget */ = {
			isa = PBXGroup;
			children = (
				F97F41402CD9D5F200DBE529 /* PlayHoleWidgetBundle.swift */,
				F97F41422CD9D5F200DBE529 /* PlayHoleWidgetLiveActivity.swift */,
				F9F498952CDB660200C9F218 /* PlayHoleWidgetModule.swift */,
				F97F41482CD9D5F300DBE529 /* Assets.xcassets */,
				F97F414A2CD9D5F300DBE529 /* Info.plist */,
			);
			path = PlayHoleWidget;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* TaylorMade */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "TaylorMade" */;
			buildPhases = (
				88AD6A4B5FBEE09294091DC2 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				D93841E82AD510E200B4E3E7 /* Embed Foundation Extensions */,
				E73EEC11289B8A29001E4EDB /* Embed Watch Content */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				1DCDED218FCA6507D6C14C91 /* [CP] Copy Pods Resources */,
				C8EFA5066372B033F126A74E /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
				E73EEC06289B8A29001E4EDB /* PBXTargetDependency */,
				D93841F72AD52C4400B4E3E7 /* PBXTargetDependency */,
				F97F414C2CD9D5F300DBE529 /* PBXTargetDependency */,
			);
			name = TaylorMade;
			productName = myTaylorMade;
			productReference = 13B07F961A680F5B00A75B9A /* TaylorMade.app */;
			productType = "com.apple.product-type.application";
		};
		D93841F02AD52C4400B4E3E7 /* NotificationServiceExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D93841F92AD52C4400B4E3E7 /* Build configuration list for PBXNativeTarget "NotificationServiceExtension" */;
			buildPhases = (
				4C1759AB101779394C85E32C /* [CP] Check Pods Manifest.lock */,
				D93841ED2AD52C4400B4E3E7 /* Sources */,
				D93841EE2AD52C4400B4E3E7 /* Frameworks */,
				D93841EF2AD52C4400B4E3E7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NotificationServiceExtension;
			productName = NotificationServiceExtension;
			productReference = D93841F12AD52C4400B4E3E7 /* NotificationServiceExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		E73EEBCE289B8A27001E4EDB /* MyTMWatch */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E73EEC0D289B8A29001E4EDB /* Build configuration list for PBXNativeTarget "MyTMWatch" */;
			buildPhases = (
				E73EEBCD289B8A27001E4EDB /* Resources */,
				E73EEC0C289B8A29001E4EDB /* Embed App Extensions */,
				2E224E6AA9A14CC7DDAF6431 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				E73EEBDD289B8A28001E4EDB /* PBXTargetDependency */,
			);
			name = MyTMWatch;
			productName = MyTMWatch;
			productReference = E73EEBCF289B8A27001E4EDB /* Staging_MyTMWatch.app */;
			productType = "com.apple.product-type.application.watchapp2";
		};
		E73EEBD9289B8A28001E4EDB /* MyTMWatch WatchKit Extension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E73EEC08289B8A29001E4EDB /* Build configuration list for PBXNativeTarget "MyTMWatch WatchKit Extension" */;
			buildPhases = (
				5025F6704753DFA0F524D635 /* [CP] Check Pods Manifest.lock */,
				E73EEBD6289B8A28001E4EDB /* Sources */,
				E73EEBD7289B8A28001E4EDB /* Frameworks */,
				E73EEBD8289B8A28001E4EDB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "MyTMWatch WatchKit Extension";
			productName = "MyTMWatch WatchKit Extension";
			productReference = E73EEBDA289B8A28001E4EDB /* MyTMWatch WatchKit Extension.appex */;
			productType = "com.apple.product-type.watchkit2-extension";
		};
		F97F41392CD9D5F200DBE529 /* PlayHoleWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F97F41512CD9D5F300DBE529 /* Build configuration list for PBXNativeTarget "PlayHoleWidgetExtension" */;
			buildPhases = (
				F97F41362CD9D5F200DBE529 /* Sources */,
				F97F41372CD9D5F200DBE529 /* Frameworks */,
				F97F41382CD9D5F200DBE529 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PlayHoleWidgetExtension;
			productName = PlayHoleWidgetExtension;
			productReference = F97F413A2CD9D5F200DBE529 /* PlayHoleWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1530;
				LastUpgradeCheck = 1320;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1250;
					};
					D93841F02AD52C4400B4E3E7 = {
						CreatedOnToolsVersion = 14.1;
					};
					E73EEBCE289B8A27001E4EDB = {
						CreatedOnToolsVersion = 13.4.1;
					};
					E73EEBD9289B8A28001E4EDB = {
						CreatedOnToolsVersion = 13.4.1;
					};
					F97F41392CD9D5F200DBE529 = {
						CreatedOnToolsVersion = 15.3;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "TaylorMade" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* TaylorMade */,
				E73EEBCE289B8A27001E4EDB /* MyTMWatch */,
				E73EEBD9289B8A28001E4EDB /* MyTMWatch WatchKit Extension */,
				D93841F02AD52C4400B4E3E7 /* NotificationServiceExtension */,
				F97F41392CD9D5F200DBE529 /* PlayHoleWidgetExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7724BA3926AA313D00A256D9 /* MyTaylorMade.entitlements in Resources */,
				F95144AC2ACEB39000ECDB54 /* SFMonoRegularItalic.otf in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				F9C6CCEA28B492EE00C70EA2 /* AppCenter-Config.plist in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				F95144A02ACEB39000ECDB54 /* SFMonoBoldItalic.otf in Resources */,
				F95144942ACEB39000ECDB54 /* SFMonoBold.otf in Resources */,
				F938A3A32ACFB7E9005AECCB /* DINNext79-Italic.ttf in Resources */,
				77CEEEF6264F1AA500F32FB1 /* GoogleService-Info.plist in Resources */,
				F951449A2ACEB39000ECDB54 /* SFMonoMediumItalic.otf in Resources */,
				F95144AF2ACEB39000ECDB54 /* SFMonoHeavyItalic.otf in Resources */,
				F938A3A02ACFB7E9005AECCB /* DINNext79-Heavy.ttf in Resources */,
				F95144972ACEB39000ECDB54 /* SFMonoMedium.otf in Resources */,
				FB79A5DDD99C474F81AD8E9C /* DINNextLTPro-BoldCondensed.ttf in Resources */,
				69B17CAA4C224E20B0BFB5CF /* SF-Pro.ttf in Resources */,
				F9DA18B42B1F130100C6A07A /* ShadowsIntoLight-Regular.ttf in Resources */,
				F951449D2ACEB39000ECDB54 /* SFMonoLightItalic.otf in Resources */,
				F95144A92ACEB39000ECDB54 /* SFMonoLight.otf in Resources */,
				F95144A32ACEB39000ECDB54 /* SFMonoRegular.otf in Resources */,
				F938A3B22ACFB7E9005AECCB /* DINNext79-Regular.ttf in Resources */,
				F95144B22ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf in Resources */,
				F95144912ACEB39000ECDB54 /* SFMonoHeavy.otf in Resources */,
				F9DF78772BA5AAF900CDD77B /* PrivacyInfo.xcprivacy in Resources */,
				F95DD0CA27F449AF00489D4B /* SF-Pro-Italic.ttf in Resources */,
				F957F9DC27F6F23300AE30D2 /* NewYork-Regular-Italic.ttf in Resources */,
				F95144A62ACEB39000ECDB54 /* SFMonoSemibold.otf in Resources */,
				F938A3E22ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf in Resources */,
				F938A3CD2ACFB7E9005AECCB /* DINNext79-Bold.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D93841EF2AD52C4400B4E3E7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E73EEBCD289B8A27001E4EDB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F938A3E32ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf in Resources */,
				F938A3A42ACFB7E9005AECCB /* DINNext79-Italic.ttf in Resources */,
				F951449E2ACEB39000ECDB54 /* SFMonoLightItalic.otf in Resources */,
				F95144952ACEB39000ECDB54 /* SFMonoBold.otf in Resources */,
				F938A3CE2ACFB7E9005AECCB /* DINNext79-Bold.ttf in Resources */,
				F938A3A12ACFB7E9005AECCB /* DINNext79-Heavy.ttf in Resources */,
				E77BDD9728A524AF005B946E /* Interface.storyboard in Resources */,
				E7DE56D6291CA6E900EA081D /* Roboto-Bold.ttf in Resources */,
				E7DE56D0291CA6E900EA081D /* ClubHaus-BoldOblique.ttf in Resources */,
				F95144A72ACEB39000ECDB54 /* SFMonoSemibold.otf in Resources */,
				E7DE56C8291CA6E900EA081D /* TMMRPSprint5.ttf in Resources */,
				F95144A12ACEB39000ECDB54 /* SFMonoBoldItalic.otf in Resources */,
				E7DE56D4291CA6E900EA081D /* ICOMOON2.ttf in Resources */,
				F95144B02ACEB39000ECDB54 /* SFMonoHeavyItalic.otf in Resources */,
				F95144982ACEB39000ECDB54 /* SFMonoMedium.otf in Resources */,
				E7345D6E291BB1DD00FAE5D7 /* SF-Pro.ttf in Resources */,
				F951449B2ACEB39000ECDB54 /* SFMonoMediumItalic.otf in Resources */,
				E7DE56CA291CA6E900EA081D /* icomoon.ttf in Resources */,
				F95144922ACEB39000ECDB54 /* SFMonoHeavy.otf in Resources */,
				E7DE56B7291CA67E00EA081D /* DINNextLTPro-BoldCondensed.ttf in Resources */,
				E72DE330289BC6D100A93733 /* Assets.xcassets in Resources */,
				F95144AA2ACEB39000ECDB54 /* SFMonoLight.otf in Resources */,
				E7DE56C6291CA6E900EA081D /* TMMRPSprint2.ttf in Resources */,
				E7DE56B5291CA67500EA081D /* NewYork-Regular-Italic.ttf in Resources */,
				E7DE56CE291CA6E900EA081D /* ClubHaus-Oblique.ttf in Resources */,
				E7DE56DC291CA6E900EA081D /* ClubHaus-Regular.ttf in Resources */,
				F95144B32ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf in Resources */,
				E7DE56DE291CA6E900EA081D /* ClubHaus-Bold.ttf in Resources */,
				F95144A42ACEB39000ECDB54 /* SFMonoRegular.otf in Resources */,
				E7DE56DA291CA6E900EA081D /* Roboto-Regular.ttf in Resources */,
				E7345D70291BB1E200FAE5D7 /* SF-Pro-Italic.ttf in Resources */,
				F95144AD2ACEB39000ECDB54 /* SFMonoRegularItalic.otf in Resources */,
				F938A3B32ACFB7E9005AECCB /* DINNext79-Regular.ttf in Resources */,
				E7DE56D2291CA6E900EA081D /* DIN-Condensed-Bold.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E73EEBD8289B8A28001E4EDB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F951449F2ACEB39000ECDB54 /* SFMonoLightItalic.otf in Resources */,
				E7DE56D1291CA6E900EA081D /* ClubHaus-BoldOblique.ttf in Resources */,
				E77BDD9028A520AA005B946E /* Assets.xcassets in Resources */,
				E7DE56DF291CA6E900EA081D /* ClubHaus-Bold.ttf in Resources */,
				F95144AE2ACEB39000ECDB54 /* SFMonoRegularItalic.otf in Resources */,
				F95144A52ACEB39000ECDB54 /* SFMonoRegular.otf in Resources */,
				F95144A22ACEB39000ECDB54 /* SFMonoBoldItalic.otf in Resources */,
				F95144B12ACEB39000ECDB54 /* SFMonoHeavyItalic.otf in Resources */,
				E7345D6F291BB1DE00FAE5D7 /* SF-Pro.ttf in Resources */,
				F95144962ACEB39000ECDB54 /* SFMonoBold.otf in Resources */,
				F95144A82ACEB39000ECDB54 /* SFMonoSemibold.otf in Resources */,
				E7DE56C9291CA6E900EA081D /* TMMRPSprint5.ttf in Resources */,
				E7DE56DB291CA6E900EA081D /* Roboto-Regular.ttf in Resources */,
				E7DE56B8291CA67E00EA081D /* DINNextLTPro-BoldCondensed.ttf in Resources */,
				F938A3A22ACFB7E9005AECCB /* DINNext79-Heavy.ttf in Resources */,
				F951449C2ACEB39000ECDB54 /* SFMonoMediumItalic.otf in Resources */,
				E77BDD9828A524AF005B946E /* Interface.storyboard in Resources */,
				E7DE56D3291CA6E900EA081D /* DIN-Condensed-Bold.ttf in Resources */,
				F95144932ACEB39000ECDB54 /* SFMonoHeavy.otf in Resources */,
				F938A3B42ACFB7E9005AECCB /* DINNext79-Regular.ttf in Resources */,
				F938A3A52ACFB7E9005AECCB /* DINNext79-Italic.ttf in Resources */,
				F938A3E42ACFB7E9005AECCB /* DINNext79-BoldItalic.ttf in Resources */,
				E7DE56CB291CA6E900EA081D /* icomoon.ttf in Resources */,
				E7DE56B6291CA67B00EA081D /* NewYork-Regular-Italic.ttf in Resources */,
				E73EEBEC289B8A29001E4EDB /* Assets.xcassets in Resources */,
				E7DE56D5291CA6E900EA081D /* ICOMOON2.ttf in Resources */,
				F95144992ACEB39000ECDB54 /* SFMonoMedium.otf in Resources */,
				F95144AB2ACEB39000ECDB54 /* SFMonoLight.otf in Resources */,
				E7DE56DD291CA6E900EA081D /* ClubHaus-Regular.ttf in Resources */,
				E7DE56CF291CA6E900EA081D /* ClubHaus-Oblique.ttf in Resources */,
				F9DF78792BA5AB7000CDD77B /* PrivacyInfo.xcprivacy in Resources */,
				E7DE56C7291CA6E900EA081D /* TMMRPSprint2.ttf in Resources */,
				F938A3CF2ACFB7E9005AECCB /* DINNext79-Bold.ttf in Resources */,
				E7DE56D7291CA6E900EA081D /* Roboto-Bold.ttf in Resources */,
				F95144B42ACEB39000ECDB54 /* SFMonoSemiboldItalic.otf in Resources */,
				E7345D71291BB1E200FAE5D7 /* SF-Pro-Italic.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F97F41382CD9D5F200DBE529 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F994378E2CE25B4E001887EB /* Roboto-Regular.ttf in Resources */,
				F994378F2CE25B4E001887EB /* SFMonoLightItalic.otf in Resources */,
				F99437902CE25B4E001887EB /* SFMonoMedium.otf in Resources */,
				F99437912CE25B4E001887EB /* SFMonoRegularItalic.otf in Resources */,
				F99437922CE25B4E001887EB /* ClubHaus-BoldOblique.ttf in Resources */,
				F99437932CE25B4E001887EB /* SFMonoHeavy.otf in Resources */,
				F99437942CE25B4E001887EB /* SFMonoHeavyItalic.otf in Resources */,
				F99437952CE25B4E001887EB /* SFMonoLight.otf in Resources */,
				F99437962CE25B4E001887EB /* DIN-Condensed-Bold.ttf in Resources */,
				F99437972CE25B4E001887EB /* SFMonoSemiboldItalic.otf in Resources */,
				F99437982CE25B4E001887EB /* NewYork-Regular-Italic.ttf in Resources */,
				F99437992CE25B4E001887EB /* SF-Pro.ttf in Resources */,
				F994379A2CE25B4E001887EB /* ShadowsIntoLight-Regular.ttf in Resources */,
				F994379B2CE25B4E001887EB /* ClubHaus-Bold.ttf in Resources */,
				F994379C2CE25B4E001887EB /* SFMonoMediumItalic.otf in Resources */,
				F994379D2CE25B4E001887EB /* Roboto-Bold.ttf in Resources */,
				F994379E2CE25B4E001887EB /* SFMonoBold.otf in Resources */,
				F994379F2CE25B4E001887EB /* SFMonoBoldItalic.otf in Resources */,
				F99437A02CE25B4E001887EB /* SFMonoSemibold.otf in Resources */,
				F99437A12CE25B4E001887EB /* ClubHaus-Oblique.ttf in Resources */,
				F99437A22CE25B4E001887EB /* ClubHaus-Regular.ttf in Resources */,
				F99437A32CE25B4E001887EB /* ICOMOON2.ttf in Resources */,
				F99437A42CE25B4E001887EB /* SF-Pro-Italic.ttf in Resources */,
				F99437A52CE25B4E001887EB /* DINNextLTPro-BoldCondensed.ttf in Resources */,
				F99437A62CE25B4E001887EB /* TMMRPSprint5.ttf in Resources */,
				F99437A72CE25B4E001887EB /* icomoon.ttf in Resources */,
				F99437A82CE25B4E001887EB /* SFMonoRegular.otf in Resources */,
				F99437A92CE25B4E001887EB /* TMMRPSprint2.ttf in Resources */,
				F93967E72CE1C8C3001857E4 /* DINNext79-Bold.ttf in Resources */,
				F93967E82CE1C8C3001857E4 /* DINNext79-Heavy.ttf in Resources */,
				F93967E92CE1C8C3001857E4 /* DINNext79-BoldItalic.ttf in Resources */,
				F93967EA2CE1C8C3001857E4 /* DINNext79-Regular.ttf in Resources */,
				F93967EB2CE1C8C3001857E4 /* DINNext79-Italic.ttf in Resources */,
				F97F41492CD9D5F300DBE529 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nexport NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh\n";
		};
		1DCDED218FCA6507D6C14C91 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TaylorMade/Pods-TaylorMade-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TaylorMade/Pods-TaylorMade-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TaylorMade/Pods-TaylorMade-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4C1759AB101779394C85E32C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-NotificationServiceExtension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		5025F6704753DFA0F524D635 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MyTMWatch WatchKit Extension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		88AD6A4B5FBEE09294091DC2 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-TaylorMade-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		C8EFA5066372B033F126A74E /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    _JSON_OUTPUT_BASE64=$(python -c 'import json,sys,base64;print(base64.b64encode(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"').read())['${_JSON_ROOT}'])))' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F91301262977E7A100E4ABC0 /* RCTKlaviyoModule.m in Sources */,
				E725168C2A497B51005A15B2 /* KlaviyoEventEmitter.m in Sources */,
				F9F498A42CDB7D3C00C9F218 /* PlayHoleWidgetModule.swift in Sources */,
				E72DE328289BC1F000A93733 /* MRPScorecardInfo.m in Sources */,
				F9F498A52CDB82BB00C9F218 /* PlayHoleWidgetLiveActivity.swift in Sources */,
				F9F4989C2CDB6DDC00C9F218 /* PlayHoleWidgetModule.m in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				77C74553276BEE8E0054F4BB /* File.swift in Sources */,
				E72DE320289BC1A400A93733 /* HMAC.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				E72DE322289BC1A400A93733 /* GTMBase64.m in Sources */,
				E72DE32A289BC1F000A93733 /* MRPIconFont.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D93841ED2AD52C4400B4E3E7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D93841F42AD52C4400B4E3E7 /* NotificationService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E73EEBD6289B8A28001E4EDB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E72DE2C8289BBCDB00A93733 /* MRPPropertyModel.m in Sources */,
				E72DE2CD289BBCDB00A93733 /* MRPGeometryModel.m in Sources */,
				E72DE2C0289BBCDB00A93733 /* MRPTeeCell.m in Sources */,
				E72DE2F0289BBCDB00A93733 /* MRPWatchUtils.m in Sources */,
				E72DE321289BC1A400A93733 /* HMAC.m in Sources */,
				E72DE2DC289BBCDB00A93733 /* MRPHoleInterfaceController.m in Sources */,
				E72DE2C7289BBCDB00A93733 /* MRPPoint.m in Sources */,
				E73EEBE4289B8A28001E4EDB /* ExtensionDelegate.m in Sources */,
				E72DE2EA289BBCDB00A93733 /* MRPSelectScoreInterfaceController.m in Sources */,
				E72DE2E2289BBCDB00A93733 /* MRPErrorInterfaceController.m in Sources */,
				E72DE2D5289BBCDB00A93733 /* MRPCourseConditionModel.m in Sources */,
				E72DE2EC289BBCDB00A93733 /* MRPNearByCourseInterfaceController.m in Sources */,
				E72DE2CB289BBCDB00A93733 /* MRPHoleModel.m in Sources */,
				E72DE2CE289BBCDB00A93733 /* MRPRoundModel.m in Sources */,
				E72DE2E7289BBCDB00A93733 /* MRPListClubInterfaceController.m in Sources */,
				E72DE2E4289BBCDB00A93733 /* MRPConfirmInterfaceController.m in Sources */,
				E72DE2E0289BBCDB00A93733 /* MRPListTeeInterfaceController.m in Sources */,
				E72DE2E5289BBCDB00A93733 /* MRPSummaryInterfaceController.m in Sources */,
				E72DE2CF289BBCDB00A93733 /* MRPRealmStringModel.m in Sources */,
				E73EEBE1289B8A28001E4EDB /* InterfaceController.m in Sources */,
				E72DE2BA289BBCDB00A93733 /* MRPClassicScorecardCell.m in Sources */,
				E72DE2E6289BBCDB00A93733 /* MRPAddShotInterfaceController.m in Sources */,
				E72DE2F2289BBCDB00A93733 /* MRPBaseInterfaceController.m in Sources */,
				E72DE32B289BC1F000A93733 /* MRPIconFont.m in Sources */,
				E72DE2C3289BBCDB00A93733 /* MRPScorecardCell.m in Sources */,
				E72DE2BD289BBCDB00A93733 /* MRPScorecardHeaderCell.m in Sources */,
				E72DE329289BC1F000A93733 /* MRPScorecardInfo.m in Sources */,
				E72DE2C4289BBCDB00A93733 /* MRPStrokeModel.m in Sources */,
				E72DE2BE289BBCDB00A93733 /* MRPNearByCourseCell.m in Sources */,
				E72DE2D9289BBCDB00A93733 /* MRPWatchBaseBackgrounder.m in Sources */,
				E72DE2D1289BBCDB00A93733 /* MRPFeaturesModel.m in Sources */,
				E72DE323289BC1A400A93733 /* GTMBase64.m in Sources */,
				E72DE2EB289BBCDB00A93733 /* MRPListTypeInterfaceController.m in Sources */,
				E72DE2BB289BBCDB00A93733 /* MRPClassicFooterScoreCardCell.m in Sources */,
				E72DE2D0289BBCDB00A93733 /* MRPFeatureModel.m in Sources */,
				E72DE2BF289BBCDB00A93733 /* MRPEndHoleCell.m in Sources */,
				E72DE2CA289BBCDB00A93733 /* MRPHolePlayerMetadataModel.m in Sources */,
				E72DE2B9289BBCDB00A93733 /* MRPClassicScorecardInterfaceController.m in Sources */,
				E72DE2B8289BBCDB00A93733 /* MRPClassicRoundStatsInterfaceController.m in Sources */,
				E72DE2EE289BBCDB00A93733 /* WKInterfaceController+Animation.m in Sources */,
				E72DE2C1289BBCDB00A93733 /* MRPPutterClubCell.m in Sources */,
				E72DE2CC289BBCDB00A93733 /* MRPCourseModel.m in Sources */,
				E72DE2DD289BBCDB00A93733 /* MRPPlayRoundInfoInterfaceController.m in Sources */,
				E72DE2E1289BBCDB00A93733 /* MRPConfirmAddClubInterfaceController.m in Sources */,
				E72DE2DA289BBCDB00A93733 /* MRPRoundData.m in Sources */,
				E72DE2E8289BBCDB00A93733 /* MRPHomeInterfaceController.m in Sources */,
				E72DE2F1289BBCDB00A93733 /* WKInterfaceGroup+Indicator.m in Sources */,
				E72DE2D4289BBCDB00A93733 /* MRPTeeboxBoundaryModel.m in Sources */,
				E72DE2C5289BBCDB00A93733 /* MRPLocation2DModel.m in Sources */,
				E72DE2EF289BBCDB00A93733 /* WKInterfaceLabel+Animation.m in Sources */,
				E72DE2C9289BBCDB00A93733 /* MRPTeeModel.m in Sources */,
				E72DE2D2289BBCDB00A93733 /* MRPLocationsModel.m in Sources */,
				E72DE2B7289BBCDB00A93733 /* MRPAddRoundClassicInterfaceController.m in Sources */,
				E72DE2D8289BBCDB00A93733 /* MRPWatchRoundManager.m in Sources */,
				E72DE2DB289BBCDB00A93733 /* MRPWatchLocationManager.m in Sources */,
				E72DE2D6289BBCDB00A93733 /* MRPRoundPlayerMetadataModel.m in Sources */,
				E72DE2C2289BBCDB00A93733 /* MRPListClubCell.m in Sources */,
				E72DE2DE289BBCDB00A93733 /* MRPAboutInterfaceController.m in Sources */,
				E72DE2E3289BBCDB00A93733 /* MRPErrorLocationInterfaceController.m in Sources */,
				E72DE2BC289BBCDB00A93733 /* MRPHoleCell.m in Sources */,
				E72DE2C6289BBCDB00A93733 /* MRPRoundLieModel.m in Sources */,
				E72DE2DF289BBCDB00A93733 /* MRPScorecardInterfaceController.m in Sources */,
				E72DE2D3289BBCDB00A93733 /* MRPClubsModel.m in Sources */,
				E72DE2E9289BBCDB00A93733 /* MRPListModeInterfaceController.m in Sources */,
				E72DE2D7289BBCDB00A93733 /* MRPWatchApiHandler.m in Sources */,
				E72DE2ED289BBCDB00A93733 /* MRPResumeRoundInterfaceController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F97F41362CD9D5F200DBE529 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F97F41432CD9D5F200DBE529 /* PlayHoleWidgetLiveActivity.swift in Sources */,
				F9F498972CDB660200C9F218 /* PlayHoleWidgetModule.swift in Sources */,
				F97F41412CD9D5F200DBE529 /* PlayHoleWidgetBundle.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		D93841F72AD52C4400B4E3E7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = D93841F02AD52C4400B4E3E7 /* NotificationServiceExtension */;
			targetProxy = D93841F62AD52C4400B4E3E7 /* PBXContainerItemProxy */;
		};
		E73EEBDD289B8A28001E4EDB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E73EEBD9289B8A28001E4EDB /* MyTMWatch WatchKit Extension */;
			targetProxy = E73EEBDC289B8A28001E4EDB /* PBXContainerItemProxy */;
		};
		E73EEC06289B8A29001E4EDB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E73EEBCE289B8A27001E4EDB /* MyTMWatch */;
			targetProxy = E73EEC05289B8A29001E4EDB /* PBXContainerItemProxy */;
		};
		F97F414C2CD9D5F300DBE529 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F97F41392CD9D5F200DBE529 /* PlayHoleWidgetExtension */;
			targetProxy = F97F414B2CD9D5F300DBE529 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		E77BDD9528A524AF005B946E /* Interface.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				E77BDD9628A524AF005B946E /* Base */,
			);
			name = Interface.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 8AB19EA950507E3563145074 /* Pods-TaylorMade.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = MyTaylorMade/MyTaylorMade.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 329;
				CURRENT_SCHEME_NAME = 1;
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386 arm64";
				INFOPLIST_FILE = "$(SRCROOT)/MyTaylorMade/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios;
				PRODUCT_NAME = TaylorMade;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/MyTaylorMade/MyTaylorMade-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1919B0D59BF2B3F83805A63C /* Pods-TaylorMade.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = MyTaylorMade/MyTaylorMade.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 329;
				CURRENT_SCHEME_NAME = 3;
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386 arm64";
				INFOPLIST_FILE = "$(SRCROOT)/MyTaylorMade/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios;
				PRODUCT_NAME = TaylorMade;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/MyTaylorMade/MyTaylorMade-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				Multi_Deployment_Config = "$(BUILD_DIR)/Release$(EFFECTIVE_PLATFORM_NAME)";
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = false;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				Multi_Deployment_Config = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				USE_HERMES = false;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		D93841FA2AD52C4400B4E3E7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C2D0B05DEDCE958E030BF608 /* Pods-NotificationServiceExtension.debug.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		D93841FB2AD52C4400B4E3E7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E3920F4999FCEB33DCEFDE09 /* Pods-NotificationServiceExtension.release.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		D93841FC2AD52C4400B4E3E7 /* Staging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DC2A08BCE19AB0472DC803F4 /* Pods-NotificationServiceExtension.staging.xcconfig */;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = NotificationServiceExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = NotificationServiceExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.NotificationServiceExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Staging;
		};
		DFCD6A492616795400EAF93C /* Staging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				Multi_Deployment_Config = "$(BUILD_DIR)/Release$(EFFECTIVE_PLATFORM_NAME)";
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = false;
				VALIDATE_PRODUCT = YES;
			};
			name = Staging;
		};
		DFCD6A4A2616795400EAF93C /* Staging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ED6D2EBFA54B3D7D7E3DBD02 /* Pods-TaylorMade.staging.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = MyTaylorMade/myTaylorMadeStaging.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 329;
				CURRENT_SCHEME_NAME = 2;
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386 arm64";
				INFOPLIST_FILE = "$(SRCROOT)/MyTaylorMade/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios;
				PRODUCT_NAME = TaylorMade;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/MyTaylorMade/MyTaylorMade-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Staging;
		};
		E73EEC09289B8A29001E4EDB /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 06F08DC055CB716550EB80B6 /* Pods-MyTMWatch WatchKit Extension.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_COMPLICATION_NAME = Complication;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "MyTMWatch WatchKit Extension/MyTMWatch WatchKit Extension.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386 arm64";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "MyTMWatch WatchKit Extension/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Staging_TaylorMade WatchKit Extension";
				INFOPLIST_KEY_CLKComplicationPrincipalClass = ComplicationController;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Turn locations services on to enable GPS and shot tracking functionality.";
				INFOPLIST_KEY_NSLocationAlwaysUsageDescription = "Turn locations services on to enable GPS and shot tracking functionality.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "To take full advantage of this app on the golf course, we recommend turning your location services on.";
				INFOPLIST_KEY_WKExtensionDelegateClassName = ExtensionDelegate;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.watchkitapp.watchkitextension;
				PRODUCT_NAME = "${TARGET_NAME}";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Debug;
		};
		E73EEC0A289B8A29001E4EDB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5772BDBB86A8EB8A4EA0E481 /* Pods-MyTMWatch WatchKit Extension.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_COMPLICATION_NAME = Complication;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "MyTMWatch WatchKit Extension/MyTMWatch WatchKit Extension.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386 arm64";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "MyTMWatch WatchKit Extension/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Staging_TaylorMade WatchKit Extension";
				INFOPLIST_KEY_CLKComplicationPrincipalClass = ComplicationController;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Turn locations services on to enable GPS and shot tracking functionality.";
				INFOPLIST_KEY_NSLocationAlwaysUsageDescription = "Turn locations services on to enable GPS and shot tracking functionality.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "To take full advantage of this app on the golf course, we recommend turning your location services on.";
				INFOPLIST_KEY_WKExtensionDelegateClassName = ExtensionDelegate;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.watchkitapp.watchkitextension;
				PRODUCT_NAME = "${TARGET_NAME}";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Release;
		};
		E73EEC0B289B8A29001E4EDB /* Staging */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B09F82085BFCA6B5F336AA55 /* Pods-MyTMWatch WatchKit Extension.staging.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_COMPLICATION_NAME = Complication;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = "MyTMWatch WatchKit Extension/MyTMWatch WatchKit Extension.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386 arm64";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "MyTMWatch WatchKit Extension/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Staging_TaylorMade WatchKit Extension";
				INFOPLIST_KEY_CLKComplicationPrincipalClass = ComplicationController;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "Turn locations services on to enable GPS and shot tracking functionality.";
				INFOPLIST_KEY_NSLocationAlwaysUsageDescription = "Turn locations services on to enable GPS and shot tracking functionality.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "To take full advantage of this app on the golf course, we recommend turning your location services on.";
				INFOPLIST_KEY_WKExtensionDelegateClassName = ExtensionDelegate;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.watchkitapp.watchkitextension;
				PRODUCT_NAME = "${TARGET_NAME}";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Staging;
		};
		E73EEC0E289B8A29001E4EDB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386 arm64";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				IBSC_MODULE = MyTMWatch_WatchKit_Extension;
				INFOPLIST_KEY_CFBundleDisplayName = Staging_TaylorMade;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = com.taylormadegolf.mytaylormadeplus.ios;
				MARKETING_VERSION = 3.0.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.watchkitapp;
				PRODUCT_NAME = Staging_MyTMWatch;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Debug;
		};
		E73EEC0F289B8A29001E4EDB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386 arm64";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				IBSC_MODULE = MyTMWatch_WatchKit_Extension;
				INFOPLIST_KEY_CFBundleDisplayName = Staging_TaylorMade;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = com.taylormadegolf.mytaylormadeplus.ios;
				MARKETING_VERSION = 3.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.watchkitapp;
				PRODUCT_NAME = Staging_MyTMWatch;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Release;
		};
		E73EEC10289B8A29001E4EDB /* Staging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "i386 arm64";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				IBSC_MODULE = MyTMWatch_WatchKit_Extension;
				INFOPLIST_KEY_CFBundleDisplayName = Staging_TaylorMade;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = com.taylormadegolf.mytaylormadeplus.ios;
				MARKETING_VERSION = 3.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.watchkitapp;
				PRODUCT_NAME = Staging_MyTMWatch;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 4.0;
			};
			name = Staging;
		};
		F97F414E2CD9D5F300DBE529 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PlayHoleWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PlayHoleWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.PlayHoleWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F97F414F2CD9D5F300DBE529 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PlayHoleWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PlayHoleWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.PlayHoleWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F97F41502CD9D5F300DBE529 /* Staging */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 329;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L3QTG9Q2V8;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PlayHoleWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = PlayHoleWidget;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.taylormadegolf.mytaylormadeplus.ios.PlayHoleWidget;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Staging;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "TaylorMade" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
				DFCD6A4A2616795400EAF93C /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "TaylorMade" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
				DFCD6A492616795400EAF93C /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D93841F92AD52C4400B4E3E7 /* Build configuration list for PBXNativeTarget "NotificationServiceExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D93841FA2AD52C4400B4E3E7 /* Debug */,
				D93841FB2AD52C4400B4E3E7 /* Release */,
				D93841FC2AD52C4400B4E3E7 /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E73EEC08289B8A29001E4EDB /* Build configuration list for PBXNativeTarget "MyTMWatch WatchKit Extension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E73EEC09289B8A29001E4EDB /* Debug */,
				E73EEC0A289B8A29001E4EDB /* Release */,
				E73EEC0B289B8A29001E4EDB /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E73EEC0D289B8A29001E4EDB /* Build configuration list for PBXNativeTarget "MyTMWatch" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E73EEC0E289B8A29001E4EDB /* Debug */,
				E73EEC0F289B8A29001E4EDB /* Release */,
				E73EEC10289B8A29001E4EDB /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F97F41512CD9D5F300DBE529 /* Build configuration list for PBXNativeTarget "PlayHoleWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F97F414E2CD9D5F300DBE529 /* Debug */,
				F97F414F2CD9D5F300DBE529 /* Release */,
				F97F41502CD9D5F300DBE529 /* Staging */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
