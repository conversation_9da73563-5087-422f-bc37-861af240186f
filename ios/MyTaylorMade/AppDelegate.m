#import "AppDelegate.h"

#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTLinkingManager.h>
#import "RNSplashScreen.h"
#import <Firebase.h>
#import <RNBranch/RNBranch.h>
#import <Branch/Branch.h>
#import <GoogleMaps/GoogleMaps.h>
#import <AppCenterReactNative.h>
#import <AppCenterReactNativeAnalytics.h>
#import <AppCenterReactNativeCrashes.h>
#import <AppCenterReactNativeShared/AppCenterReactNativeShared.h>
#import <CodePush/CodePush.h>
#import <FIROptions.h>
#import <UserNotifications/UNUserNotificationCenter.h>
#import "KlaviyoEventEmitter.h"
#import <React/RCTBridgeModule.h>
#import "TaylorMade-Swift.h"

@import KlaviyoSwift;
@import UserNotifications;

NSString *const appCenterSecret = @"df13027d-53bc-4a83-8cc8-ad0468d31031";
NSString *const klaviyoPublicKey = @"Jij9SV";
NSString *const branchKey = @"key_test_goWAlMlQUquZss1qsM9kaoapwEgkFqH1";
NSString *const firebaseApiKey = @"AIzaSyDyEiFKthlPWDadb38m79C3dQaBkwC0-uk";
NSString *const codePushKey_dev = @"PDkboYEn8ofcDSclDdvkxcv6yiqpn3d4pTyZ3";
NSString *const codePushKey_stg = @"cbjgoDuAGQN-BcCDm1NyHYYPUB4LSqd2dZlrT";
NSString *const codePushKey_prod = @"JJyyjgKQVJnFXSY7ublUCCdh7ZMTMKVgCuFZU";

@interface AppDelegate () <RCTBridgeDelegate>

 
@end

@implementation AppDelegate

KlaviyoEventEmitter *klaviyoEventEmitter = NULL;

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  // Đặt tên module RN
  self.moduleName = @"myTaylorMade";
  self.initialProps = @{};

  // AppCenter
  [AppCenterReactNativeShared setStartAutomatically:YES];
  [AppCenterReactNativeShared setAppSecret:appCenterSecret];
  [AppCenterReactNative register];
  [AppCenterReactNativeAnalytics registerWithInitiallyEnabled:true];
  [AppCenterReactNativeCrashes registerWithAutomaticProcessing];

  NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
  [Klaviyo setupWithPublicAPIKeyWithApiKey:klaviyoPublicKey];
  [Branch setBranchKey:branchKey error:nil];
  
  klaviyoEventEmitter = [KlaviyoEventEmitter allocWithZone: nil];
  NSString *schemeCode = [infoDictionary objectForKey:@"SCM"];
  NSString *codePushKey = @"";
  switch([schemeCode integerValue]){
     case 1:
      codePushKey = codePushKey_dev;
        break;
     case 2:
      codePushKey = codePushKey_stg;
        break;
    case 3:
      codePushKey = codePushKey_prod;
      break;
     default :
      break;
  }
 
  [CodePush setDeploymentKey:codePushKey];
  
  NSMutableArray *newArguments = [NSMutableArray arrayWithArray:[[NSProcessInfo processInfo] arguments]];
  [newArguments addObject:@"-FIRDebugEnabled"];
  [NSUserDefaults.standardUserDefaults setBool:YES forKey:@"/google/firebase/debug_mode"];
  [NSUserDefaults.standardUserDefaults setBool:YES forKey:@"/google/measurement/debug_mode"];
  [[NSProcessInfo processInfo] setValue:[newArguments copy] forKey:@"arguments"];
  [RNBranch initSessionWithLaunchOptions:launchOptions isReferrable:YES];
  
  [FIROptions defaultOptions].APIKey = firebaseApiKey;
  FIROptions *options = [FIROptions defaultOptions];
  [FIRApp configureWithOptions:options];

  RCTBridge *bridge = [[RCTBridge alloc] initWithDelegate:self launchOptions:launchOptions];
  RCTRootView *rootView = [[RCTRootView alloc] initWithBridge:bridge
                                                   moduleName:@"myTaylorMade"
                                            initialProperties:nil];

  if (@available(iOS 13.0, *)) {
      rootView.backgroundColor = [UIColor systemBackgroundColor];
  } else {
      rootView.backgroundColor = [UIColor whiteColor];
  }
  [UNUserNotificationCenter currentNotificationCenter].delegate = self;
  [application registerForRemoteNotifications];

  NSDictionary *remoteNoti =[launchOptions objectForKey:UIApplicationLaunchOptionsRemoteNotificationKey];
       if (remoteNoti) {
         [[Klaviyo sharedInstance] handlePushSkipDeepLinkWithUserInfo:remoteNoti];
       }
  [UIApplication sharedApplication].applicationIconBadgeNumber = 0;
  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
  UIViewController *rootViewController = [UIViewController new];
  rootViewController.view = rootView;
  self.window.rootViewController = rootViewController;
  [self.window makeKeyAndVisible];
  [RNSplashScreen show];
  [GMSServices provideAPIKey:@"AIzaSyBAhrAPVCw4Frbrt64fLn5FEk22DkZ2AIA"];
  [super application:application didFinishLaunchingWithOptions:launchOptions];
  return YES;
}

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@"index"];
#else
  return [CodePush bundleURL];
#endif
}

- (void)userNotificationCenter:(UNUserNotificationCenter *)center
       willPresentNotification:(UNNotification *)notification
         withCompletionHandler:
             (void (^)(UNNotificationPresentationOptions options))completionHandler {
  UNNotificationPresentationOptions options = UNNotificationPresentationOptionAlert;
  if (@available(iOS 14.0, *)) {
    options = UNNotificationPresentationOptionList | UNNotificationPresentationOptionBanner;
  }
  completionHandler(options);
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options
{
  return [RNBranch application:app openURL:url options:options];
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
  return [RNBranch continueUserActivity:userActivity];
}


// Old way
- (void)session: (nonnull WCSession *)session didReceiveMessage:(nonnull NSDictionary<NSString *,id> *)message replyHandler:(nonnull void (^)(NSDictionary<NSString *,id> * __nonnull))replyHandler {


}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo
    fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))handler {
  if(application.applicationState == UIApplicationStateInactive || application.applicationState ==  UIApplicationStateBackground){
    [[Klaviyo sharedInstance] handlePushSkipDeepLinkWithUserInfo:userInfo];
  }

}

// Handle notification messages after display notification is tapped by the user.
- (void)userNotificationCenter:(UNUserNotificationCenter *)center
didReceiveNotificationResponse:(UNNotificationResponse *)response
         withCompletionHandler:(void(^)(void))completionHandler {
  
  NSDictionary *userInfo = response.notification.request.content.userInfo;
  if(userInfo){
    [[Klaviyo sharedInstance] handlePushSkipDeepLinkWithUserInfo:userInfo];
    [klaviyoEventEmitter sendPushNotificationWithData:userInfo];
  }

  [UIApplication sharedApplication].applicationIconBadgeNumber = [UIApplication sharedApplication].applicationIconBadgeNumber - 1;

}

@end

