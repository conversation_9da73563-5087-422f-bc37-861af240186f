<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Staging_TaylorMade</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>None</string>
			<key>CFBundleURLName</key>
			<string>auth0</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>myTaylorMade</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>mytaylormadeplus</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FirebaseAutomaticScreenReportingEnabled</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>itms-apps</string>
		<string>mailto</string>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>12.0</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to use your camera</string>
	<key>NSContactsUsageDescription</key>
	<string>$(PRODUCT_NAME) would like access to your contacts</string>
	<key>NSExceptionDomains</key>
	<dict>
		<key>localhost</key>
		<dict>
			<key>NSExceptionAllowsInsecureHTTPLoads</key>
			<true/>
		</dict>
		<key>staging.taylormadegolf.com</key>
		<dict>
			<key>NSExceptionAllowsInsecureHTTPLoads</key>
			<true/>
			<key>NSIncludesSubdomains</key>
			<true/>
		</dict>
	</dict>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We use your location to search for golf courses nearby and provide you with detailed yardages via built-in GPS.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We use your location to search for golf courses nearby and provide you with detailed yardages via built-in GPS.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your location to search for golf courses nearby and provide you with detailed yardages via built-in GPS.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to use your microphone (for videos)</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>$(PRODUCT_NAME) would like to save photos to your photo gallery</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>$(PRODUCT_NAME) would like access to your photo gallery</string>
	<key>NSSupportsLiveActivities</key>
	<true/>
	<key>RCTNewArchEnabled</key>
	<false/>
	<key>SCM</key>
	<string>$(CURRENT_SCHEME_NAME)</string>
	<key>UIAppFonts</key>
	<array>
		<string>ShadowsIntoLight-Regular.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>DINNextLTPro-BoldCondensed.ttf</string>
		<string>SF-Pro.ttf</string>
		<string>Fontisto.ttf</string>
		<string>SF-Pro-Italic.ttf</string>
		<string>NewYork-Regular-Italic.ttf</string>
		<string>SF-Pro.ttf</string>
		<string>SFMonoBold.otf</string>
		<string>SFMonoBoldItalic.otf</string>
		<string>SFMonoHeavy.otf</string>
		<string>SFMonoHeavyItalic.otf</string>
		<string>SFMonoLight.otf</string>
		<string>SFMonoLightItalic.otf</string>
		<string>SFMonoMedium.otf</string>
		<string>SFMonoMediumItalic.otf</string>
		<string>SFMonoRegular.otf</string>
		<string>SFMonoRegularItalic.otf</string>
		<string>SFMonoSemibold.otf</string>
		<string>SFMonoRegular.otf</string>
		<string>SFMonoSemiboldItalic.otf</string>
		<string>DINNext79-Bold.ttf</string>
		<string>DINNext79-BoldItalic.ttf</string>
		<string>DINNext79-Heavy.ttf</string>
		<string>DINNext79-Italic.ttf</string>
		<string>DINNext79-Regular.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>location</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array/>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>branch_app_domain</key>
	<string>3sshf.test-app.link</string>
	<key>branch_universal_link_domains</key>
	<array>
		<string>3sshf.test-app.link</string>
		<string>3sshf-alternate.test-app.link</string>
		<string>3sshf.app.link</string>
		<string>3sshf-alternate.app.link</string>
	</array>
</dict>
</plist>
