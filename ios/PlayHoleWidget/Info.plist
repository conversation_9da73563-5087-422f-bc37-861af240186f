<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSExtension</key>
	<dict>
		<key>NSExtensionPointIdentifier</key>
		<string>com.apple.widgetkit-extension</string>
	</dict>
	<key>RCTNewArchEnabled</key>
	<false/>
	<key>UIAppFonts</key>
	<array>
		<string>ShadowsIntoLight-Regular.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
		<string>DINNextLTPro-BoldCondensed.ttf</string>
		<string>SF-Pro.ttf</string>
		<string>Fontisto.ttf</string>
		<string>SF-Pro-Italic.ttf</string>
		<string>NewYork-Regular-Italic.ttf</string>
		<string>SF-Pro.ttf</string>
		<string>SFMonoBold.otf</string>
		<string>SFMonoBoldItalic.otf</string>
		<string>SFMonoHeavy.otf</string>
		<string>SFMonoHeavyItalic.otf</string>
		<string>SFMonoLight.otf</string>
		<string>SFMonoLightItalic.otf</string>
		<string>SFMonoMedium.otf</string>
		<string>SFMonoMediumItalic.otf</string>
		<string>SFMonoRegular.otf</string>
		<string>SFMonoRegularItalic.otf</string>
		<string>SFMonoSemibold.otf</string>
		<string>SFMonoRegular.otf</string>
		<string>SFMonoSemiboldItalic.otf</string>
		<string>DINNext79-Bold.ttf</string>
		<string>DINNext79-BoldItalic.ttf</string>
		<string>DINNext79-Heavy.ttf</string>
		<string>DINNext79-Italic.ttf</string>
		<string>DINNext79-Regular.ttf</string>
	</array>
</dict>
</plist>
