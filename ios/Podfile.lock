PODS:
  - A0Auth0 (2.13.3):
    - React-Core
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AnyCodable-FlightSchool (0.6.7)
  - appcenter-analytics (4.4.5):
    - AppCenter/Analytics (~> 4.0)
    - AppCenterReactNativeShared (~> 4.0)
    - React-Core
  - appcenter-core (4.4.5):
    - AppCenterReactNativeShared (~> 4.0)
    - React-Core
  - appcenter-crashes (4.4.5):
    - AppCenter/Crashes (~> 4.0)
    - AppCenterReactNativeShared (~> 4.0)
    - React-Core
  - AppCenter/Analytics (4.4.3):
    - AppCenter/Core
  - AppCenter/Core (4.4.3)
  - AppCenter/Crashes (4.4.3):
    - AppCenter/Core
  - AppCenterReactNativeShared (4.4.5):
    - AppCenter/Core (= 4.4.3)
  - AppsFlyerFramework (6.9.1):
    - AppsFlyerFramework/Main (= 6.9.1)
  - AppsFlyerFramework/Main (6.9.1)
  - Base64 (1.1.2)
  - BEMCheckBox (1.4.1)
  - boost (1.76.0)
  - Branch (1.43.2)
  - BVLinearGradient (2.8.3):
    - React-Core
  - Charts (4.1.0):
    - Charts/Core (= 4.1.0)
  - Charts/Core (4.1.0):
    - SwiftAlgorithms (~> 1.0)
  - CodePush (7.0.5):
    - Base64 (~> 1.1)
    - JWT (~> 3.0.0-beta.12)
    - React-Core
    - SSZipArchive (~> 2.2.2)
  - DoubleConversion (1.1.6)
  - EXConstants (11.0.2):
    - ExpoModulesCore
    - UMCore
  - EXFileSystem (11.1.3):
    - ExpoModulesCore
    - UMCore
  - EXImageLoader (2.2.0):
    - ExpoModulesCore
    - React-Core
    - UMCore
  - ExpoModulesCore (0.2.0):
    - ExpoModulesCore/Core (= 0.2.0)
    - ExpoModulesCore/Interfaces (= 0.2.0)
    - UMCore
  - ExpoModulesCore/Core (0.2.0):
    - UMCore
  - ExpoModulesCore/Interfaces (0.2.0):
    - ExpoModulesCore/Core
    - UMCore
  - EXScreenOrientation (3.3.0):
    - React-Core
    - UMCore
  - FBLazyVector (0.70.2)
  - FBReactNativeSpec (0.70.2):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.2)
    - RCTTypeSafety (= 0.70.2)
    - React-Core (= 0.70.2)
    - React-jsi (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - Firebase/Analytics (8.10.0):
    - Firebase/Core
  - Firebase/Core (8.10.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 8.10.0)
  - Firebase/CoreOnly (8.10.0):
    - FirebaseCore (= 8.10.0)
  - Firebase/Messaging (8.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 8.10.0)
  - FirebaseAnalytics (8.10.0):
    - FirebaseAnalytics/AdIdSupport (= 8.10.0)
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.6)
    - GoogleUtilities/MethodSwizzler (~> 7.6)
    - GoogleUtilities/Network (~> 7.6)
    - "GoogleUtilities/NSData+zlib (~> 7.6)"
    - nanopb (~> 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (8.10.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleAppMeasurement (= 8.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.6)
    - GoogleUtilities/MethodSwizzler (~> 7.6)
    - GoogleUtilities/Network (~> 7.6)
    - "GoogleUtilities/NSData+zlib (~> 7.6)"
    - nanopb (~> 2.30908.0)
  - FirebaseCore (8.10.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.6)
    - GoogleUtilities/Logger (~> 7.6)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseMessaging (8.10.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.6)
    - GoogleUtilities/Environment (~> 7.6)
    - GoogleUtilities/Reachability (~> 7.6)
    - GoogleUtilities/UserDefaults (~> 7.6)
    - nanopb (~> 2.30908.0)
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.1.0):
    - Google-Maps-iOS-Utils/Clustering (= 4.1.0)
    - Google-Maps-iOS-Utils/Geometry (= 4.1.0)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.1.0)
    - Google-Maps-iOS-Utils/Heatmap (= 4.1.0)
    - Google-Maps-iOS-Utils/QuadTree (= 4.1.0)
    - GoogleMaps
  - Google-Maps-iOS-Utils/Clustering (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/Geometry (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/GeometryUtils (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/Heatmap (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/QuadTree (4.1.0):
    - GoogleMaps
  - GoogleAppMeasurement (8.10.0):
    - GoogleAppMeasurement/AdIdSupport (= 8.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.6)
    - GoogleUtilities/MethodSwizzler (~> 7.6)
    - GoogleUtilities/Network (~> 7.6)
    - "GoogleUtilities/NSData+zlib (~> 7.6)"
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (8.10.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 8.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.6)
    - GoogleUtilities/MethodSwizzler (~> 7.6)
    - GoogleUtilities/Network (~> 7.6)
    - "GoogleUtilities/NSData+zlib (~> 7.6)"
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (8.10.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.6)
    - GoogleUtilities/MethodSwizzler (~> 7.6)
    - GoogleUtilities/Network (~> 7.6)
    - "GoogleUtilities/NSData+zlib (~> 7.6)"
    - nanopb (~> 2.30908.0)
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (7.0.0):
    - GoogleMaps/Maps (= 7.0.0)
  - GoogleMaps/Base (7.0.0)
  - GoogleMaps/Maps (7.0.0):
    - GoogleMaps/Base
  - GoogleUtilities/AppDelegateSwizzler (7.12.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.12.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.12.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.12.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.12.0)"
  - GoogleUtilities/Reachability (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.12.0):
    - GoogleUtilities/Logger
  - JSONModel (1.8.0)
  - JWT (3.0.0-beta.14):
    - Base64 (~> 1.1.2)
  - KlaviyoSwift (2.2.1):
    - AnyCodable-FlightSchool
  - KlaviyoSwiftExtension (2.1.0-beta1)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - Mute (0.6.1)
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - Permission-Camera (3.6.1):
    - RNPermissions
  - Permission-LocationAlways (3.6.1):
    - RNPermissions
  - Permission-LocationWhenInUse (3.6.1):
    - RNPermissions
  - Permission-Microphone (3.6.1):
    - RNPermissions
  - Permission-Notifications (3.6.1):
    - RNPermissions
  - Permission-PhotoLibrary (3.6.1):
    - RNPermissions
  - Permission-PhotoLibraryAddOnly (3.6.1):
    - RNPermissions
  - PromisesObjC (2.3.1)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.70.2)
  - RCTTypeSafety (0.70.2):
    - FBLazyVector (= 0.70.2)
    - RCTRequired (= 0.70.2)
    - React-Core (= 0.70.2)
  - React (0.70.2):
    - React-Core (= 0.70.2)
    - React-Core/DevSupport (= 0.70.2)
    - React-Core/RCTWebSocket (= 0.70.2)
    - React-RCTActionSheet (= 0.70.2)
    - React-RCTAnimation (= 0.70.2)
    - React-RCTBlob (= 0.70.2)
    - React-RCTImage (= 0.70.2)
    - React-RCTLinking (= 0.70.2)
    - React-RCTNetwork (= 0.70.2)
    - React-RCTSettings (= 0.70.2)
    - React-RCTText (= 0.70.2)
    - React-RCTVibration (= 0.70.2)
  - React-bridging (0.70.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi (= 0.70.2)
  - React-callinvoker (0.70.2)
  - React-Codegen (0.70.2):
    - FBReactNativeSpec (= 0.70.2)
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.70.2)
    - RCTTypeSafety (= 0.70.2)
    - React-Core (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - React-Core (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.2)
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/CoreModulesHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/Default (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/DevSupport (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.2)
    - React-Core/RCTWebSocket (= 0.70.2)
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-jsinspector (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTBlobHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTImageHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTTextHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-Core/RCTWebSocket (0.70.2):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.70.2)
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsiexecutor (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - Yoga
  - React-CoreModules (0.70.2):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.2)
    - React-Codegen (= 0.70.2)
    - React-Core/CoreModulesHeaders (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-RCTImage (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - React-cxxreact (0.70.2):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-jsinspector (= 0.70.2)
    - React-logger (= 0.70.2)
    - React-perflogger (= 0.70.2)
    - React-runtimeexecutor (= 0.70.2)
  - React-jsi (0.70.2):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-jsi/Default (= 0.70.2)
  - React-jsi/Default (0.70.2):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.70.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-perflogger (= 0.70.2)
  - React-jsinspector (0.70.2)
  - React-logger (0.70.2):
    - glog
  - react-native-appsflyer (6.9.4):
    - AppsFlyerFramework (= 6.9.1)
    - React
  - react-native-babylon (1.6.1):
    - React
  - react-native-blur (4.3.2):
    - React-Core
  - react-native-branch (5.6.2):
    - Branch (= 1.43.2)
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-charts-wrapper (0.5.11):
    - Charts (= 4.1.0)
    - React
    - SwiftyJSON (= 5.0)
  - react-native-config (1.4.12):
    - react-native-config/App (= 1.4.12)
  - react-native-config/App (1.4.12):
    - React-Core
  - react-native-contacts (7.0.5):
    - React-Core
  - react-native-create-thumbnail (1.6.4):
    - React-Core
  - react-native-date-picker (3.4.3):
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-google-maps (1.3.2):
    - Google-Maps-iOS-Utils (= 4.1.0)
    - GoogleMaps (= 7.0.0)
    - React-Core
  - react-native-image-picker (4.10.3):
    - React-Core
  - react-native-in-app-review (4.3.5):
    - React-Core
  - react-native-mail (6.1.1):
    - React-Core
  - react-native-maps (1.3.2):
    - React-Core
  - react-native-netinfo (9.3.7):
    - React-Core
  - react-native-orientation-locker (1.5.0):
    - React-Core
  - react-native-pager-view (5.4.25):
    - React-Core
  - react-native-safe-area-context (3.4.1):
    - React-Core
  - react-native-select-contact (1.6.3):
    - React-Core
  - react-native-slider (3.0.3):
    - React
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-version-check (3.4.6):
    - React-Core
  - react-native-video (5.2.1):
    - React-Core
    - react-native-video/Video (= 5.2.1)
  - react-native-video/Video (5.2.1):
    - React-Core
  - react-native-view-shot (3.5.0):
    - React-Core
  - react-native-volume-manager (1.10.0):
    - Mute
    - React-Core
  - react-native-webview (11.26.0):
    - React-Core
  - React-perflogger (0.70.2)
  - React-RCTActionSheet (0.70.2):
    - React-Core/RCTActionSheetHeaders (= 0.70.2)
  - React-RCTAnimation (0.70.2):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.2)
    - React-Codegen (= 0.70.2)
    - React-Core/RCTAnimationHeaders (= 0.70.2)
    - React-jsi (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - React-RCTBlob (0.70.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.2)
    - React-Core/RCTBlobHeaders (= 0.70.2)
    - React-Core/RCTWebSocket (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-RCTNetwork (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - React-RCTImage (0.70.2):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.2)
    - React-Codegen (= 0.70.2)
    - React-Core/RCTImageHeaders (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-RCTNetwork (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - React-RCTLinking (0.70.2):
    - React-Codegen (= 0.70.2)
    - React-Core/RCTLinkingHeaders (= 0.70.2)
    - React-jsi (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - React-RCTNetwork (0.70.2):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.2)
    - React-Codegen (= 0.70.2)
    - React-Core/RCTNetworkHeaders (= 0.70.2)
    - React-jsi (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - React-RCTSettings (0.70.2):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.70.2)
    - React-Codegen (= 0.70.2)
    - React-Core/RCTSettingsHeaders (= 0.70.2)
    - React-jsi (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - React-RCTText (0.70.2):
    - React-Core/RCTTextHeaders (= 0.70.2)
  - React-RCTVibration (0.70.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.70.2)
    - React-Core/RCTVibrationHeaders (= 0.70.2)
    - React-jsi (= 0.70.2)
    - ReactCommon/turbomodule/core (= 0.70.2)
  - React-runtimeexecutor (0.70.2):
    - React-jsi (= 0.70.2)
  - React-TestFairy (2.60.0):
    - React
    - TestFairy (= 1.29.9)
  - ReactCommon/turbomodule/core (0.70.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-bridging (= 0.70.2)
    - React-callinvoker (= 0.70.2)
    - React-Core (= 0.70.2)
    - React-cxxreact (= 0.70.2)
    - React-jsi (= 0.70.2)
    - React-logger (= 0.70.2)
    - React-perflogger (= 0.70.2)
  - ReactNativeART (1.2.0):
    - React
  - RealmJS (11.0.0-rc.2):
    - React
  - RNCAsyncStorage (1.17.11):
    - React-Core
  - RNCCheckbox (0.5.14):
    - BEMCheckBox (~> 1.4)
    - React-Core
  - RNCMaskedView (0.2.9):
    - React-Core
  - RNCPicker (2.4.10):
    - React-Core
  - RNDeviceInfo (10.3.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBAnalytics (13.1.1):
    - Firebase/Analytics (= 8.10.0)
    - React-Core
    - RNFBApp
  - RNFBApp (13.1.1):
    - Firebase/CoreOnly (= 8.10.0)
    - React-Core
  - RNFBMessaging (13.1.1):
    - Firebase/Messaging (= 8.10.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.9.0):
    - React-Core
  - RNKeychain (7.0.0):
    - React-Core
  - RNLocalize (2.2.4):
    - React-Core
  - RNPermissions (3.6.1):
    - React-Core
  - RNReanimated (2.14.1):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.18.2):
    - React-Core
    - React-RCTImage
  - RNSecureRandom (1.0.1):
    - React
  - RNShare (7.9.1):
    - React-Core
  - RNSVG (12.5.1):
    - React-Core
  - RNVectorIcons (9.2.0):
    - React-Core
  - RNWatch (1.1.0):
    - React
  - RSClipperWrapper (1.3)
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SSZipArchive (2.2.3)
  - SwiftAlgorithms (1.0.0)
  - SwiftyJSON (5.0.0)
  - TestFairy (1.29.9)
  - UMAppLoader (2.2.0)
  - UMCore (7.1.2)
  - UMReactNativeAdapter (6.3.9):
    - ExpoModulesCore
    - React-Core
    - UMCore
  - UMTaskManagerInterface (6.2.0):
    - UMCore
  - YOChartImageKit (1.2.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - A0Auth0 (from `../node_modules/react-native-auth0`)
  - AFNetworking
  - appcenter-analytics (from `../node_modules/appcenter-analytics`)
  - appcenter-core (from `../node_modules/appcenter`)
  - appcenter-crashes (from `../node_modules/appcenter-crashes`)
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - CodePush (from `../node_modules/react-native-code-push`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXFileSystem (from `../node_modules/expo-file-system/ios`)
  - EXImageLoader (from `../node_modules/expo-image-loader/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core/ios`)
  - EXScreenOrientation (from `../node_modules/expo-screen-orientation/ios`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - JSONModel
  - KlaviyoSwift (~> 2.2.1)
  - KlaviyoSwiftExtension (= 2.1.0-beta1)
  - Mantle
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera`)
  - Permission-LocationAlways (from `../node_modules/react-native-permissions/ios/LocationAlways`)
  - Permission-LocationWhenInUse (from `../node_modules/react-native-permissions/ios/LocationWhenInUse`)
  - Permission-Microphone (from `../node_modules/react-native-permissions/ios/Microphone`)
  - Permission-Notifications (from `../node_modules/react-native-permissions/ios/Notifications`)
  - Permission-PhotoLibrary (from `../node_modules/react-native-permissions/ios/PhotoLibrary`)
  - Permission-PhotoLibraryAddOnly (from `../node_modules/react-native-permissions/ios/PhotoLibraryAddOnly`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-bridging (from `../node_modules/react-native/ReactCommon`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-appsflyer (from `../node_modules/react-native-appsflyer`)
  - "react-native-babylon (from `../node_modules/@babylonjs/react-native-iosandroid-0-70`)"
  - "react-native-blur (from `../node_modules/@react-native-community/blur`)"
  - react-native-branch (from `../node_modules/react-native-branch`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-charts-wrapper (from `../node_modules/react-native-charts-wrapper`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-contacts (from `../node_modules/react-native-contacts`)
  - react-native-create-thumbnail (from `../node_modules/react-native-create-thumbnail`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-google-maps (from `../node_modules/react-native-maps`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-in-app-review (from `../node_modules/react-native-in-app-review`)
  - react-native-mail (from `../node_modules/react-native-mail`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-orientation-locker (from `../node_modules/react-native-orientation-locker`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-select-contact (from `../node_modules/react-native-select-contact`)
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-version-check (from `../node_modules/react-native-version-check`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-view-shot (from `../node_modules/react-native-view-shot`)
  - react-native-volume-manager (from `../node_modules/react-native-volume-manager`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-TestFairy (from `../node_modules/react-native-testfairy`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "ReactNativeART (from `../node_modules/@react-native-community/art`)"
  - RealmJS (from `../node_modules/realm`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../node_modules/@react-native-community/checkbox`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBAnalytics (from `../node_modules/@react-native-firebase/analytics`)"
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNKeychain (from `../node_modules/react-native-keychain`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSecureRandom (from `../node_modules/react-native-securerandom`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - RNWatch (from `../node_modules/react-native-watch-connectivity`)
  - RSClipperWrapper (from `https://github.com/rusty1s/RSClipperWrapper.git`)
  - UMAppLoader (from `../node_modules/unimodules-app-loader/ios`)
  - "UMCore (from `../node_modules/@unimodules/core/ios`)"
  - "UMReactNativeAdapter (from `../node_modules/@unimodules/react-native-adapter/ios`)"
  - UMTaskManagerInterface (from `../node_modules/unimodules-task-manager-interface/ios`)
  - YOChartImageKit (~> 1.1)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AFNetworking
    - AnyCodable-FlightSchool
    - AppCenter
    - AppCenterReactNativeShared
    - AppsFlyerFramework
    - Base64
    - BEMCheckBox
    - Branch
    - Charts
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseMessaging
    - fmt
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - JSONModel
    - JWT
    - KlaviyoSwift
    - KlaviyoSwiftExtension
    - libwebp
    - Mantle
    - Mute
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - SSZipArchive
    - SwiftAlgorithms
    - SwiftyJSON
    - TestFairy
    - YOChartImageKit

EXTERNAL SOURCES:
  A0Auth0:
    :path: "../node_modules/react-native-auth0"
  appcenter-analytics:
    :path: "../node_modules/appcenter-analytics"
  appcenter-core:
    :path: "../node_modules/appcenter"
  appcenter-crashes:
    :path: "../node_modules/appcenter-crashes"
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  CodePush:
    :path: "../node_modules/react-native-code-push"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  EXImageLoader:
    :path: "../node_modules/expo-image-loader/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core/ios"
  EXScreenOrientation:
    :path: "../node_modules/expo-screen-orientation/ios"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera"
  Permission-LocationAlways:
    :path: "../node_modules/react-native-permissions/ios/LocationAlways"
  Permission-LocationWhenInUse:
    :path: "../node_modules/react-native-permissions/ios/LocationWhenInUse"
  Permission-Microphone:
    :path: "../node_modules/react-native-permissions/ios/Microphone"
  Permission-Notifications:
    :path: "../node_modules/react-native-permissions/ios/Notifications"
  Permission-PhotoLibrary:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibrary"
  Permission-PhotoLibraryAddOnly:
    :path: "../node_modules/react-native-permissions/ios/PhotoLibraryAddOnly"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-bridging:
    :path: "../node_modules/react-native/ReactCommon"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-appsflyer:
    :path: "../node_modules/react-native-appsflyer"
  react-native-babylon:
    :path: "../node_modules/@babylonjs/react-native-iosandroid-0-70"
  react-native-blur:
    :path: "../node_modules/@react-native-community/blur"
  react-native-branch:
    :path: "../node_modules/react-native-branch"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-charts-wrapper:
    :path: "../node_modules/react-native-charts-wrapper"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-contacts:
    :path: "../node_modules/react-native-contacts"
  react-native-create-thumbnail:
    :path: "../node_modules/react-native-create-thumbnail"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-google-maps:
    :path: "../node_modules/react-native-maps"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-in-app-review:
    :path: "../node_modules/react-native-in-app-review"
  react-native-mail:
    :path: "../node_modules/react-native-mail"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-orientation-locker:
    :path: "../node_modules/react-native-orientation-locker"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-select-contact:
    :path: "../node_modules/react-native-select-contact"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-version-check:
    :path: "../node_modules/react-native-version-check"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-view-shot:
    :path: "../node_modules/react-native-view-shot"
  react-native-volume-manager:
    :path: "../node_modules/react-native-volume-manager"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-TestFairy:
    :path: "../node_modules/react-native-testfairy"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  ReactNativeART:
    :path: "../node_modules/@react-native-community/art"
  RealmJS:
    :path: "../node_modules/realm"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../node_modules/@react-native-community/checkbox"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBAnalytics:
    :path: "../node_modules/@react-native-firebase/analytics"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNKeychain:
    :path: "../node_modules/react-native-keychain"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSecureRandom:
    :path: "../node_modules/react-native-securerandom"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  RNWatch:
    :path: "../node_modules/react-native-watch-connectivity"
  RSClipperWrapper:
    :git: https://github.com/rusty1s/RSClipperWrapper.git
  UMAppLoader:
    :path: "../node_modules/unimodules-app-loader/ios"
  UMCore:
    :path: "../node_modules/@unimodules/core/ios"
  UMReactNativeAdapter:
    :path: "../node_modules/@unimodules/react-native-adapter/ios"
  UMTaskManagerInterface:
    :path: "../node_modules/unimodules-task-manager-interface/ios"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

CHECKOUT OPTIONS:
  RSClipperWrapper:
    :commit: 304dc7168bbc98015c6fd7b043b6ed788856884e
    :git: https://github.com/rusty1s/RSClipperWrapper.git

SPEC CHECKSUMS:
  A0Auth0: 49dea639a21d98bb8e3ae204a0d768c666bf7369
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  AnyCodable-FlightSchool: 261cbe76757802b17d471b9059b21e6fa5edf57b
  AppCenter: 3fd04aa1b166e16fdb03ec81dabe488aece83fbd
  appcenter-analytics: 1aea9ada8922d2e2df3e069a28eda21157c9d30d
  appcenter-core: c593b2e4ac28d6a21b6a217c4a5e6b3de7b0b00f
  appcenter-crashes: d9ae1c9296974c1c433600fa2f320e0ba1f12bdf
  AppCenterReactNativeShared: f395caeabde0dc3a11609dbcb737d0f14cd40e79
  AppsFlyerFramework: dc9aa675faa8e1a2ed40fb295d3c5408ee2b972a
  Base64: cecfb41a004124895a7bcee567a89bae5a89d49b
  BEMCheckBox: 5ba6e37ade3d3657b36caecc35c8b75c6c2b1a4e
  boost: a7c83b31436843459a1961bfd74b96033dc77234
  Branch: 4ac024cb3c29b0ef628048694db3c4cfa679beb0
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  Charts: ce0768268078eee0336f122c3c4ca248e4e204c5
  CodePush: ef496b6fd053012e985e3d4a0ab9f9dbf2739eac
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  EXConstants: 4cb52b6d8f636c767104a44bf7db3873e9c01a6f
  EXFileSystem: 0a04aba8da751b9ac954065911bcf166503f8267
  EXImageLoader: d3531a3fe530b22925c19977cb53bb43e3821fe6
  ExpoModulesCore: 2734852616127a6c1fc23012197890a6f3763dc7
  EXScreenOrientation: 09fe6b6b87899ae0c9320255bda7b7513cdfc8ec
  FBLazyVector: 0507edc21c06f1650c591f0981c846445469373b
  FBReactNativeSpec: 698ef8604615cfa7ae2119e9ca4ed7687a6ae62e
  Firebase: 44213362f1dcc52555b935dc925ed35ac55f1b20
  FirebaseAnalytics: 319c9b3b1bdd400d60e2f415dff0c5f6959e6760
  FirebaseCore: 04186597c095da37d90ff9fd3e53bc61a1ff2440
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  FirebaseInstallations: 40bd9054049b2eae9a2c38ef1c3dd213df3605cd
  FirebaseMessaging: b0aeba17332ee1ee610662b4d1e02a86db82f08f
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  Google-Maps-iOS-Utils: 3343332b18dfd5be8f1f44edd7d481ace3da4d9a
  GoogleAppMeasurement: a3311dbcf3ea651e5a070fe8559b57c174ada081
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleMaps: 6e9c923ca035989709fcb5771544fda4cc5fa2a4
  GoogleUtilities: 0759d1a57ebb953965c2dfe0ba4c82e95ccc2e34
  JSONModel: 02ab723958366a3fd27da57ea2af2113658762e9
  JWT: ef71dfb03e1f842081e64dc42eef0e164f35d251
  KlaviyoSwift: 6c5ab3e43aec911be84849452bc114ae079df6c2
  KlaviyoSwiftExtension: c7c0f09c4e507790de083bfc1085a8cacb1be12d
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  Mute: 20135a96076f140cc82bfc8b810e2d6150d8ec7e
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  Permission-Camera: bf6791b17c7f614b6826019fcfdcc286d3a107f6
  Permission-LocationAlways: 8d99b025c9f73c696e0cdb367e42525f2e9a26f2
  Permission-LocationWhenInUse: 3ba99e45c852763f730eabecec2870c2382b7bd4
  Permission-Microphone: 48212dd4d28025d9930d583e3c7a56da7268665c
  Permission-Notifications: 150484ae586eb9be4e32217582a78350a9bb31c3
  Permission-PhotoLibrary: 5b34ca67279f7201ae109cef36f9806a6596002d
  Permission-PhotoLibraryAddOnly: 6dba8924024e239af2e282daa8a88967679b4983
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  RCT-Folly: 0080d0a6ebf2577475bda044aa59e2ca1f909cda
  RCTRequired: d4033a367d0bfd1f23f67b501f8cdabf9afe617e
  RCTTypeSafety: b112b2ccc59309a65284280c0a53baf1ce4b5860
  React: 04474547a4729eef1fb378ca42f302f4b3219eb8
  React-bridging: 1c8695b292b4a9baaca3960f6166d9766e20492d
  React-callinvoker: 4d91e2db7773ee3fcea2d3a5c6beb52a5bfd4d71
  React-Codegen: 33356335c6f3b0869cb4434055fdec219139f635
  React-Core: 634b8aa20e1dad445425ee9581f4719bcfd1b19b
  React-CoreModules: 746825283de4b54dcb4fd88703ff516297a5f60d
  React-cxxreact: f8d2686d98b5ffed1b1de3aa62e1f81db4903153
  React-jsi: 198b9b3e0a85e68cb6898265400fd8bf34cacda4
  React-jsiexecutor: 53bd208e5c27939c6e6365528393445a596a9a2b
  React-jsinspector: 26c42646ab0bb69e29e837e23754fe7121eeaf94
  React-logger: 1bfd109a0ffa4c0989bbfac0c2d8c4abe4637faa
  react-native-appsflyer: 78c2c6233c6c8ce6fe7f4a4f0553b1ea0b23ee89
  react-native-babylon: 8686d6a93acff6fd2da64f899c8a7e44ec07f05d
  react-native-blur: cfdad7b3c01d725ab62a8a729f42ea463998afa2
  react-native-branch: 4e42fda662d96893afbbd02839806931398e3d2e
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-charts-wrapper: 4461b72f48e737a490e968bf2468cc0fd2281681
  react-native-config: 5e3e0c7798522b1a0d7641f7ffa5363044e16397
  react-native-contacts: 0aa99963d8f29099d4725cd0d2f8b6ef5f0d9a2d
  react-native-create-thumbnail: e022bcdcba8a0b4529a50d3fa1a832ec921be39d
  react-native-date-picker: 201b481c94dcb7678f4712477ad026dd7793305b
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-google-maps: 035ad2f9b4974f22af3d4d4c6993960a181b0aaf
  react-native-image-picker: 60f4246eb5bb7187fc15638a8c1f13abd3820695
  react-native-in-app-review: cf4a29c5a1d6bb28dd0d15f1fb1faa25e265bfb5
  react-native-mail: 8fdcd3aef007c33a6877a18eb4cf7447a1d4ce4a
  react-native-maps: 085f614cf14d3637b2048bb9752da5b1c27c2886
  react-native-netinfo: 2517ad504b3d303e90d7a431b0fcaef76d207983
  react-native-orientation-locker: 851f6510d8046ea2f14aa169b1e01fcd309a94ba
  react-native-pager-view: da490aa1f902c9a5aeecf0909cc975ad0e92e53e
  react-native-safe-area-context: 9e40fb181dac02619414ba1294d6c2a807056ab9
  react-native-select-contact: f88ccd9dbee6c4d1c32507291b431cf558a2ed42
  react-native-slider: e99fc201cefe81270fc9d81714a7a0f5e566b168
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  react-native-version-check: dc483031cdf65d9e947aea2d6a41142d010c7e59
  react-native-video: c26780b224543c62d5e1b2a7244a5cd1b50e8253
  react-native-view-shot: 792829857bbb23a9c8acdad9a640554bdee397a3
  react-native-volume-manager: 3c7d8047841b6831730dea7bf25250522388b4f4
  react-native-webview: 994b9f8fbb504d6314dc40d83f94f27c6831b3bf
  React-perflogger: 6009895616a455781293950bbd63d53cfc7ffbc5
  React-RCTActionSheet: 5e90aa5712af18bfc86c2c6d97d4dbe0e5451c1d
  React-RCTAnimation: 50c44d6501f8bfb2fe885e544501f8798b4ff3d6
  React-RCTBlob: 3cc08e7112dd7b77faf3fa481ba22ca2bba5f20a
  React-RCTImage: ca8335860b5f64c383ad27f52a28d85089d49b7a
  React-RCTLinking: 297cd91bdbf427efc861fc7943e6d683e61860fa
  React-RCTNetwork: 8a197bff6f1dc5353484507a4cdcd47e9356316f
  React-RCTSettings: d3db1f1e61a5ad8deb50f44f5cb6c7c3ef32b3ac
  React-RCTText: c2c05ab3dbfb1cf5855b14802f392148970e48da
  React-RCTVibration: 89e2cbea456ac5ec623943661d00e4dc45fe74b9
  React-runtimeexecutor: 80065f60af4f4b05603661070c8622bb3740bf16
  React-TestFairy: dd24c6e7d580bcaaebd35a9d756c1ee301363ca3
  ReactCommon: 1209130f460e4aa9d255ddc75fa0a827ebf93dfb
  ReactNativeART: 78edc68dd4a1e675338cd0cd113319cf3a65f2ab
  RealmJS: 1a0478d2c2d98da875057b716c69c8d03eef2b3a
  RNCAsyncStorage: 8616bd5a58af409453ea4e1b246521bb76578d60
  RNCCheckbox: 38989bbd3d7d536adf24ba26c6b3e6cefe0bea6f
  RNCMaskedView: 949696f25ec596bfc697fc88e6f95cf0c79669b6
  RNCPicker: 0bc2f0a29abcca7b7ed44a2d036aac9ab6d25700
  RNDeviceInfo: 4701f0bf2a06b34654745053db0ce4cb0c53ada7
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFBAnalytics: 9337a84539ea8497409a8c1db1c301dc1b2b3858
  RNFBApp: 6e20f07b316346d96737191f377777f61f51a8d2
  RNFBMessaging: eb8d1b08795c8718706007ed0743732d00e1acc4
  RNGestureHandler: 071d7a9ad81e8b83fe7663b303d132406a7d8f39
  RNKeychain: f75b8c8b2f17d3b2aa1f25b4a0ac5b83d947ff8f
  RNLocalize: 0df7970cfc60389f00eb62fd7c097dc75af3fb4f
  RNPermissions: dcdb7b99796bbeda6975a6e79ad519c41b251b1c
  RNReanimated: 8125b2324ba03108136ab5dd506fc49b2454da24
  RNScreens: 34cc502acf1b916c582c60003dc3089fa01dc66d
  RNSecureRandom: 07efbdf2cd99efe13497433668e54acd7df49fef
  RNShare: a5dc3b9c53ddc73e155b8cd9a94c70c91913c43c
  RNSVG: d7d7bc8229af3842c9cfc3a723c815a52cdd1105
  RNVectorIcons: fcc2f6cb32f5735b586e66d14103a74ce6ad61f8
  RNWatch: fd30ca40a5b5ef58dcbc195638e68219bc455236
  RSClipperWrapper: e8105534f0db6b1e03d8b92a100ea438a67a6679
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  SwiftAlgorithms: 38dda4731d19027fdeee1125f973111bf3386b53
  SwiftyJSON: 36413e04c44ee145039d332b4f4e2d3e8d6c4db7
  TestFairy: 34488ae6a253b9b7f67da8651f3ef9c6b5bae1ad
  UMAppLoader: 21af63390e55c82e037fb9752d93114a80ecf16e
  UMCore: ce3a4faa010239063b8343895b29a6d97b01069d
  UMReactNativeAdapter: d03cefd0e4e4179ab8c490408589f1c8a6c8b785
  UMTaskManagerInterface: 2be431101b73604e64fbfffcf759336f9d8fccbb
  YOChartImageKit: ************************************5fbe
  Yoga: 043f8eb97345d0171f27fead4d1849cacf0472a5

PODFILE CHECKSUM: ed3b3f0146f0118d29eb413984283ef1aa1ed24e

COCOAPODS: 1.16.2
