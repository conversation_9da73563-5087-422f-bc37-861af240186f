<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="horizontal">

    <com.aigestudio.wheelpicker.widgets.WheelYearPicker
        android:id="@+id/wheel_date_picker_year"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/wheel_date_picker_year_tv"
        android:layout_width="wrap_content"
        android:textColor="#536D8A"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/WheelMargins"
        android:layout_marginStart="@dimen/WheelMargins"
        android:text="@string/Year" />

    <com.aigestudio.wheelpicker.widgets.WheelMonthPicker
        android:id="@+id/wheel_date_picker_month"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:textColor="#536D8A"
        android:id="@+id/wheel_date_picker_month_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/WheelMargins"
        android:layout_marginStart="@dimen/WheelMargins"
        android:text="@string/Month" />

    <com.aigestudio.wheelpicker.widgets.WheelDayPicker
        android:id="@+id/wheel_date_picker_day"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:textColor="#536D8A"
        android:id="@+id/wheel_date_picker_day_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/WheelMargins"
        android:text="@string/Day" />
</LinearLayout>