<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="WheelPicker">
        <attr name="wheel_data" format="reference" />
        <attr name="wheel_selected_item_position" format="integer" />
        <attr name="wheel_item_text_size" format="dimension" />
        <attr name="wheel_item_text_color" format="color" />
        <attr name="wheel_selected_item_text_color" format="color" />
        <attr name="wheel_same_width" format="boolean" />
        <attr name="wheel_maximum_width_text" format="string" />
        <attr name="wheel_maximum_width_text_position" format="integer" />
        <attr name="wheel_visible_item_count" format="integer" />
        <attr name="wheel_item_space" format="dimension" />
        <attr name="wheel_cyclic" format="boolean" />
        <attr name="wheel_indicator" format="boolean" />
        <attr name="wheel_indicator_color" format="color" />
        <attr name="wheel_indicator_size" format="dimension" />
        <attr name="wheel_curtain" format="boolean" />
        <attr name="wheel_curtain_color" format="color" />
        <attr name="wheel_atmospheric" format="boolean" />
        <attr name="wheel_curved" format="boolean" />
        <attr name="wheel_item_align" format="enum">
            <enum name="center" value="0" />
            <enum name="left" value="1" />
            <enum name="right" value="2" />
        </attr>
        <attr name="wheel_font_path" format="string" />
    </declare-styleable>
</resources>