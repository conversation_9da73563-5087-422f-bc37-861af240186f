apply plugin: 'com.android.library'
android {
    compileSdkVersion rootProject.ext.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets { 
        main { 
            assets.srcDirs = ['src/main/assets', 'src/main/assets/']
            java.srcDirs = ['src/main/java']
            res.srcDirs = ['src/main/res']
        } 
     }
}
dependencies {
    implementation 'com.google.code.gson:gson:2.8.2'
}