{"name": "@gregfrench/react-native-wheel-picker", "version": "1.2.18", "description": "React native cross platform picker.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/<PERSON><PERSON><PERSON>/react-native-wheel-picker.git"}, "keywords": ["react-native", "picker", "wheel"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/lesliesam/react-native-wheel-picker/issues"}, "homepage": "https://github.com/<PERSON><PERSON><PERSON>/react-native-wheel-picker#readme", "peerDependencies": {"prop-types": "^15.6.2", "react": ">=16.0", "react-native": ">=0.57"}, "dependencies": {"@react-native-picker/picker": "^1.9.4"}}