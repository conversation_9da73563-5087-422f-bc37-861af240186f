import React, {useEffect, useState} from 'react';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';
// import * as ScreenOrientation from 'expo-screen-orientation';
import {Platform, NativeModules} from 'react-native';

console.log(
  'New Architecture Enabled:',
  Platform.constants?.reactNativeVersion?.newArchEnabled ?? false,
);
import EncryptionGate from 'components/EncryptionGate';
import StoreGate from 'components/StoreGate';
console.log('🟨 Before import Navigation');
import Navigation from './src/navigation';
console.log('🟩 After import Navigation');
import codePush from 'react-native-code-push';
import AppUpdateModal from 'components/AppUpdateModal';
import { t } from 'i18next';
import useAppState from 'hooks/useAppState';
import {clearCompletedLoyaltyActions} from 'utils/asyncStorage';
let codePushOptions = {checkFrequency: codePush.CheckFrequency.MANUAL};
console.log('✅ App.js loaded');

import 'moment/locale/en-ca';
import ErrorBoundary from 'components/ErrorBoundary';

function App() {
  const [isCodePushNeedUpdate, setCodePushNeedUpdate] = useState(false);
  const [updateModalVisible, setUpdateModalVisible] = useState(false);

  const checkUpdateFromCodePush = async () => {
    const isNeedUpdate = await codePush.checkForUpdate();
    const downloadProgress = ({totalBytes, receivedBytes}) => {
      console.log('progress', receivedBytes / totalBytes);
    };
    const syncStatusChanged = status => {
      if (status === codePush.SyncStatus.INSTALLING_UPDATE) {
        // console.log('status', status);
        if (isNeedUpdate.isMandatory) {
          setUpdateModalVisible(true);
        }
        // alert('Downloading new version done!');
      }
    };
    if (isNeedUpdate) {
      setCodePushNeedUpdate(true);
      codePush.sync(
        {
          // updateDialog: true,
          installMode: codePush.InstallMode.ON_NEXT_RESTART,
          mandatoryInstallMode: codePush.InstallMode.ON_NEXT_RESTART,
        },
        syncStatusChanged,
        downloadProgress,
      );
    }
  };
  useEffect(() => {
    // ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
    clearCompletedLoyaltyActions();
    checkUpdateFromCodePush();
  }, []);

  useAppState({
    onForeground: () => {
      checkUpdateFromCodePush();
    },
  });

  const restartApp = () => {
    try {
      codePush.restartApp(false);
    } catch (error) {
      console.log('err restartApp', error);
    }
  };

  return (
    <SafeAreaProvider>
      <EncryptionGate>
        {encryptionKey => (
          <StoreGate encryptionKey={encryptionKey}>
            {(store, persistor) => (
              <Provider store={store}>
                <PersistGate loading={null} persistor={persistor}>
                  <ErrorBoundary>
                  <Navigation />
                  <AppUpdateModal
                    visible={updateModalVisible}
                    title={'app.force_update_version.modal.title'}
                    description={t(
                      'app.force_update_version.modal.description_v2',
                    )}
                    button1={
                      isCodePushNeedUpdate
                        ? 'app.force_update_version.modal.button_update'
                        : null
                    }
                    button1Action={restartApp}
                  />
                  </ErrorBoundary>
                </PersistGate>
              </Provider>
            )}
          </StoreGate>
        )}
      </EncryptionGate>
    </SafeAreaProvider>
  );
}
export default codePush(codePushOptions)(App);
